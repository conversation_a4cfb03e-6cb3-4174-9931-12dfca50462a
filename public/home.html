

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BoGuani - Messenger of Value</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-purple': '#2D1B4E',
                        'medium-purple': '#3D2A5F',
                        'light-purple': '#4E3A70',
                        'dark-gray': '#1E1E24',
                        'medium-gray': '#2D2D34',
                        'gold': '#D4AF37',
                        'gold-light': '#F2D675'
                    },
                    fontFamily: {
                        'sans': ['Montserrat', 'sans-serif'],
                        'serif': ['Georgia', 'serif']
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
            background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
            font-family: 'Montserrat', sans-serif;
            min-height: 100vh;
        }

        .hero-gradient {
            background: linear-gradient(135deg,
                rgba(30, 30, 36, 0.95) 0%,
                rgba(45, 27, 78, 0.9) 25%,
                rgba(61, 42, 95, 0.85) 50%,
                rgba(78, 58, 112, 0.8) 75%,
                rgba(45, 27, 78, 0.9) 100%);
        }

        .section-gradient {
            background: linear-gradient(135deg,
                rgba(30, 30, 36, 0.8) 0%,
                rgba(45, 27, 78, 0.7) 50%,
                rgba(61, 42, 95, 0.6) 100%);
        }

        .card-gradient {
            background: linear-gradient(135deg,
                rgba(61, 42, 95, 0.4) 0%,
                rgba(78, 58, 112, 0.3) 50%,
                rgba(45, 27, 78, 0.4) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .gold-gradient {
            background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .gold-border {
            border: 2px solid transparent;
            background: linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box,
                        linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
            transition: all 0.4s ease;
            background: linear-gradient(135deg,
                rgba(61, 42, 95, 0.6) 0%,
                rgba(78, 58, 112, 0.4) 50%,
                rgba(45, 27, 78, 0.6) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px -10px rgba(212, 175, 55, 0.3);
            background: linear-gradient(135deg,
                rgba(61, 42, 95, 0.8) 0%,
                rgba(78, 58, 112, 0.6) 50%,
                rgba(45, 27, 78, 0.8) 100%);
        }

        .btn-hover {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-hover:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
        }

        .btn-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-hover:hover::before {
            left: 100%;
        }

        .hero-pattern {
            background-image:
                radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%),
                url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .nav-gradient {
            background: linear-gradient(135deg,
                rgba(30, 30, 36, 0.95) 0%,
                rgba(45, 27, 78, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(212, 175, 55, 0.2);
        }

        .testimonial-card {
            background: linear-gradient(135deg,
                rgba(61, 42, 95, 0.5) 0%,
                rgba(78, 58, 112, 0.3) 50%,
                rgba(45, 27, 78, 0.5) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(212, 175, 55, 0.2);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(212, 175, 55, 0.2);
        }
    </style>
</head>
<body class="text-white min-h-screen">
    <div class="hero-pattern min-h-screen">
        <!-- Navigation -->
        <nav class="nav-gradient fixed w-full z-10">
            <div class="container mx-auto px-6 py-4 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="text-gold text-3xl mr-3">
                        <i class="fas fa-comment-dollar"></i>
                    </div>
                    <span class="font-bold text-2xl gold-gradient">BoGuani</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="hover:text-gold transition-all duration-300 font-medium">Features</a>
                    <a href="#about" class="hover:text-gold transition-all duration-300 font-medium">About</a>
                    <a href="#download" class="hover:text-gold transition-all duration-300 font-medium">Download</a>
                    <a href="/privacy" class="hover:text-gold transition-all duration-300 font-medium">Privacy</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-white focus:outline-none p-2">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="md:hidden hidden section-gradient border-t border-gold border-opacity-20">
                <div class="container mx-auto px-6 py-4 flex flex-col space-y-4">
                    <a href="#features" class="hover:text-gold transition-all duration-300 font-medium">Features</a>
                    <a href="#about" class="hover:text-gold transition-all duration-300 font-medium">About</a>
                    <a href="#download" class="hover:text-gold transition-all duration-300 font-medium">Download</a>
                    <a href="/privacy" class="hover:text-gold transition-all duration-300 font-medium">Privacy</a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <header class="hero-gradient pt-32 pb-24 px-6 min-h-screen flex items-center">
            <div class="container mx-auto flex flex-col md:flex-row items-center relative">
                <!-- Floating background elements -->
                <div class="absolute top-10 left-10 w-32 h-32 bg-gold opacity-5 rounded-full blur-3xl animate-pulse"></div>
                <div class="absolute bottom-20 right-20 w-40 h-40 bg-gold opacity-3 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>

                <div class="md:w-1/2 mb-12 md:mb-0 relative z-10">
                    <div class="mb-6">
                        <h1 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                            <span class="gold-gradient drop-shadow-lg">BoGuani</span>
                        </h1>
                        <h2 class="text-3xl md:text-4xl font-semibold mb-4 text-gray-100">Messenger of Value</h2>
                        <p class="text-2xl mb-8 text-gold-light italic font-medium">Where Words Carry Worth</p>
                    </div>

                    <div class="card-gradient p-6 rounded-2xl mb-8">
                        <p class="mb-6 text-gray-200 text-lg leading-relaxed">
                            Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
                            Send messages that matter, share moments that count, and transfer value instantly -
                            all protected by sacred-level encryption.
                        </p>
                        <div class="text-center">
                            <p class="text-2xl font-bold gold-gradient">"Speak Gold. Share Value."</p>
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-4 justify-center md:justify-start">
                        <a href="/auth" class="bg-gradient-to-r from-gold to-gold-light text-dark-gray px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center shadow-lg">
                            <i class="fas fa-globe mr-3"></i> Open BoGuani Web Version
                        </a>
                        <a href="#features" class="gold-border bg-transparent px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center">
                            <i class="fas fa-play mr-3 text-gold"></i> <span class="text-gold">Watch Demo</span>
                        </a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center relative z-10">
                    <div class="relative w-80 h-96">
                        <!-- App mockup -->
                        <div class="absolute inset-0 card-gradient rounded-3xl gold-border shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-500">
                            <div class="section-gradient p-4 border-b border-gold border-opacity-40">
                                <div class="flex justify-between items-center">
                                    <div class="text-gold text-xl">
                                        <i class="fas fa-comment-dollar"></i>
                                    </div>
                                    <p class="font-bold text-lg gold-gradient">BoGuani</p>
                                    <div class="text-gold">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 h-full bg-gradient-to-b from-transparent to-dark-purple to-opacity-20">
                                <div class="flex flex-col h-full space-y-3">
                                    <div class="card-gradient p-3 rounded-xl self-start max-w-[75%] border border-light-purple border-opacity-30">
                                        <p class="text-sm text-gray-200">Hey! Can you send me 20 for dinner tonight?</p>
                                    </div>
                                    <div class="bg-gradient-to-r from-gold to-gold-light bg-opacity-20 p-3 rounded-xl self-end max-w-[75%] border border-gold border-opacity-40">
                                        <p class="text-sm text-gray-100">Sure! Sending it now with a special message.</p>
                                    </div>
                                    <div class="bg-gradient-to-r from-gold to-gold-light bg-opacity-30 p-3 rounded-xl self-end max-w-[75%] border border-gold border-opacity-50">
                                        <div class="flex items-center">
                                            <div class="text-gold mr-2">
                                                <i class="fas fa-coins"></i>
                                            </div>
                                            <p class="text-sm font-semibold text-gray-100">$20.00 sent - Enjoy dinner!</p>
                                        </div>
                                    </div>
                                    <div class="card-gradient p-3 rounded-xl self-start max-w-[75%] border border-light-purple border-opacity-30">
                                        <p class="text-sm text-gray-200">Thanks! Value received. 🙏</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Enhanced decorative elements -->
                        <div class="absolute -bottom-8 -right-8 w-48 h-48 bg-gradient-to-r from-gold to-gold-light opacity-10 rounded-full blur-3xl animate-pulse"></div>
                        <div class="absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-r from-gold to-gold-light opacity-15 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s;"></div>
                        <div class="absolute top-1/2 -right-4 w-16 h-16 bg-gold opacity-20 rounded-full blur-xl animate-bounce" style="animation-delay: 3s;"></div>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Features Section -->
    <section id="features" class="py-20 section-gradient relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-0 left-1/4 w-64 h-64 bg-gold opacity-5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 right-1/4 w-48 h-48 bg-gold opacity-3 rounded-full blur-3xl"></div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-20">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Key Features</h2>
                <div class="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto mb-4"></div>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">Experience the future of value-based communication</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card p-8 rounded-2xl relative overflow-hidden group">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold to-gold-light opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="text-gold text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-light">256-bit End-to-End Encryption</h3>
                        <p class="text-gray-300 leading-relaxed">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>
                    </div>
                </div>

                <div class="feature-card p-8 rounded-2xl relative overflow-hidden group">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold to-gold-light opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="text-gold text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-light">Instant Money Transfers</h3>
                        <p class="text-gray-300 leading-relaxed">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
                    </div>
                </div>

                <div class="feature-card p-8 rounded-2xl relative overflow-hidden group">
                    <div class="absolute inset-0 bg-gradient-to-br from-gold to-gold-light opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="text-gold text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-gold-light">24/7 Global Support</h3>
                        <p class="text-gray-300 leading-relaxed">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="py-20 hero-gradient relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-1/2 left-0 w-72 h-72 bg-gold opacity-5 rounded-full blur-3xl transform -translate-y-1/2"></div>
        <div class="absolute top-1/2 right-0 w-72 h-72 bg-gold opacity-5 rounded-full blur-3xl transform -translate-y-1/2"></div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-20">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Get BoGuani Now</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-6">Experience the revolution in value-based messaging. Available on all major platforms.</p>
                <div class="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto"></div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
                <a href="/downloads/ios" class="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                    <i class="fab fa-apple text-5xl mb-4 text-gold group-hover:scale-110 transition-transform duration-300"></i>
                    <div>
                        <p class="text-sm text-gray-400 mb-1">Download for</p>
                        <p class="font-bold text-lg text-gold-light">iOS</p>
                        <p class="text-xs text-gray-500 mt-2">Coming Soon</p>
                    </div>
                </a>

                <a href="/downloads/android" class="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                    <i class="fab fa-android text-5xl mb-4 text-gold group-hover:scale-110 transition-transform duration-300"></i>
                    <div>
                        <p class="text-sm text-gray-400 mb-1">Download for</p>
                        <p class="font-bold text-lg text-gold-light">Android</p>
                        <p class="text-xs text-gray-500 mt-2">Coming Soon</p>
                    </div>
                </a>

                <a href="/downloads/desktop" class="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                    <i class="fas fa-desktop text-5xl mb-4 text-gold group-hover:scale-110 transition-transform duration-300"></i>
                    <div>
                        <p class="text-sm text-gray-400 mb-1">Download</p>
                        <p class="font-bold text-lg text-gold-light">Desktop App</p>
                        <p class="text-xs text-gray-500 mt-2">Coming Soon</p>
                    </div>
                </a>

                <a href="/auth" class="bg-gradient-to-br from-gold to-gold-light text-dark-gray hover:from-gold-light hover:to-gold transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group shadow-lg">
                    <i class="fas fa-globe text-5xl mb-4 text-dark-gray group-hover:scale-110 transition-transform duration-300"></i>
                    <div>
                        <p class="text-sm text-dark-gray opacity-80 mb-1">Open</p>
                        <p class="font-bold text-lg text-dark-gray">Web Version</p>
                        <p class="text-xs text-dark-gray opacity-70 mt-2">Available Now</p>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 bg-dark-purple">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="relative">
                        <svg class="w-full max-w-md mx-auto" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#D4AF37" fill-opacity="0.2" d="M45.7,-51.9C59.9,-41.5,72.3,-27.7,76.3,-11.5C80.3,4.7,75.9,23.4,65.1,36.6C54.3,49.8,37.1,57.5,19.3,63.3C1.6,69.1,-16.8,73,-32.5,67.5C-48.2,62,-61.3,47.1,-68.3,29.8C-75.3,12.5,-76.2,-7.2,-69.8,-23.6C-63.3,-40,-49.5,-53.1,-34.7,-63C-19.9,-72.9,-3.9,-79.7,9.9,-76.8C23.8,-73.9,31.5,-62.3,45.7,-51.9Z" transform="translate(100 100)" />
                            <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="#D4AF37" font-size="24" font-weight="bold">Taíno Wisdom</text>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-gold text-6xl opacity-20">
                                <i class="fas fa-comment-dollar"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 md:pl-10">
                    <h2 class="text-3xl font-bold mb-6">Our Story</h2>
                    <div class="w-20 h-1 bg-gold mb-6"></div>
                    <p class="mb-4 text-gray-300">
                        BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. The name "BoGuani" combines elements from their language representing both "message" and "worth."
                    </p>
                    <p class="mb-6 text-gray-300">
                        We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.
                    </p>
                    <div class="flex items-center">
                        <div class="h-px bg-gold flex-grow"></div>
                        <p class="px-4 text-gold italic">"Speak Gold. Share Value."</p>
                        <div class="h-px bg-gold flex-grow"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 section-gradient relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-0 right-1/3 w-56 h-56 bg-gold opacity-4 rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 left-1/3 w-40 h-40 bg-gold opacity-6 rounded-full blur-3xl"></div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-20">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 gold-gradient">What Our Users Say</h2>
                <div class="w-24 h-1 bg-gradient-to-r from-gold to-gold-light mx-auto mb-4"></div>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto">Real experiences from our valued community</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="testimonial-card p-8 rounded-2xl relative overflow-hidden">
                    <div class="text-gold text-5xl absolute -top-3 -left-1 opacity-80">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-8 mb-8 text-gray-300 text-lg leading-relaxed italic">BoGuani has completely changed how I think about messaging. Being able to send value along with my words makes every conversation more meaningful.</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-gold to-gold-light flex items-center justify-center text-dark-purple font-bold text-lg shadow-lg">M</div>
                        <div class="ml-4">
                            <p class="font-bold text-lg text-gold-light">Maria L.</p>
                            <p class="text-sm text-gray-400">Entrepreneur</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card p-8 rounded-2xl relative overflow-hidden">
                    <div class="text-gold text-5xl absolute -top-3 -left-1 opacity-80">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-8 mb-8 text-gray-300 text-lg leading-relaxed italic">The security features are impressive. I feel confident sending money through BoGuani knowing that my transactions are protected by top-tier encryption.</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-gold to-gold-light flex items-center justify-center text-dark-purple font-bold text-lg shadow-lg">J</div>
                        <div class="ml-4">
                            <p class="font-bold text-lg text-gold-light">James T.</p>
                            <p class="text-sm text-gray-400">Security Analyst</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card p-8 rounded-2xl relative overflow-hidden">
                    <div class="text-gold text-5xl absolute -top-3 -left-1 opacity-80">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-8 mb-8 text-gray-300 text-lg leading-relaxed italic">I use BoGuani daily for both personal and business communications. The ability to instantly transfer value has streamlined so many of my interactions.</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-gold to-gold-light flex items-center justify-center text-dark-purple font-bold text-lg shadow-lg">S</div>
                        <div class="ml-4">
                            <p class="font-bold text-lg text-gold-light">Sarah K.</p>
                            <p class="text-sm text-gray-400">Digital Nomad</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-b from-dark-purple to-dark-gray">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Communications?</h2>
            <p class="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Join thousands of users who are already experiencing the power of value-based messaging with BoGuani.</p>
            
            <div class="flex flex-wrap justify-center gap-6">
                <a href="/auth" class="bg-gold text-dark-gray px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i class="fas fa-globe mr-2"></i> Open Web Version
                </a>
                <a href="#" class="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i class="fas fa-download mr-2 text-gold"></i> <span class="text-gold">Download App</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="section-gradient py-16 relative overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-0 left-1/2 w-96 h-96 bg-gold opacity-3 rounded-full blur-3xl transform -translate-x-1/2"></div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="flex flex-col md:flex-row justify-between items-start mb-12">
                <div class="mb-8 md:mb-0">
                    <div class="flex items-center mb-4">
                        <div class="text-gold text-3xl mr-3">
                            <i class="fas fa-comment-dollar"></i>
                        </div>
                        <span class="font-bold text-2xl gold-gradient">BoGuani</span>
                    </div>
                    <p class="text-gray-300 text-lg mb-4">Messenger of Value</p>
                    <p class="text-gray-400 max-w-sm">Where Words Carry Worth - Experience the future of value-based communication.</p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div>
                        <h3 class="font-bold mb-4 text-gold-light text-lg">Product</h3>
                        <ul class="space-y-3 text-gray-400">
                            <li><a href="#features" class="hover:text-gold transition-all duration-300">Features</a></li>
                            <li><a href="/downloads" class="hover:text-gold transition-all duration-300">Downloads</a></li>
                            <li><a href="/auth" class="hover:text-gold transition-all duration-300">Web App</a></li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="font-bold mb-4 text-gold-light text-lg">Company</h3>
                        <ul class="space-y-3 text-gray-400">
                            <li><a href="#about" class="hover:text-gold transition-all duration-300">About</a></li>
                            <li><a href="/contact" class="hover:text-gold transition-all duration-300">Contact</a></li>
                            <li><a href="/support" class="hover:text-gold transition-all duration-300">Support</a></li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="font-bold mb-4 text-gold-light text-lg">Resources</h3>
                        <ul class="space-y-3 text-gray-400">
                            <li><a href="/help" class="hover:text-gold transition-all duration-300">Help Center</a></li>
                            <li><a href="/guides" class="hover:text-gold transition-all duration-300">Guides</a></li>
                            <li><a href="/api" class="hover:text-gold transition-all duration-300">API Docs</a></li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="font-bold mb-4 text-gold-light text-lg">Legal</h3>
                        <ul class="space-y-3 text-gray-400">
                            <li><a href="/privacy" class="hover:text-gold transition-all duration-300">Privacy</a></li>
                            <li><a href="/terms" class="hover:text-gold transition-all duration-300">Terms</a></li>
                            <li><a href="/security" class="hover:text-gold transition-all duration-300">Security</a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="flex justify-center mb-8">
                <div class="flex space-x-6">
                    <a href="https://twitter.com/boguani" class="w-12 h-12 rounded-full card-gradient flex items-center justify-center hover:bg-opacity-80 transition-all duration-300 group">
                        <i class="fab fa-twitter text-gold text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </a>
                    <a href="https://facebook.com/boguani" class="w-12 h-12 rounded-full card-gradient flex items-center justify-center hover:bg-opacity-80 transition-all duration-300 group">
                        <i class="fab fa-facebook-f text-gold text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </a>
                    <a href="https://instagram.com/boguani" class="w-12 h-12 rounded-full card-gradient flex items-center justify-center hover:bg-opacity-80 transition-all duration-300 group">
                        <i class="fab fa-instagram text-gold text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </a>
                    <a href="https://linkedin.com/company/boguani" class="w-12 h-12 rounded-full card-gradient flex items-center justify-center hover:bg-opacity-80 transition-all duration-300 group">
                        <i class="fab fa-linkedin-in text-gold text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="border-t border-gold border-opacity-20 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 mb-4 md:mb-0">© 2024 BoGuani. All rights reserved.</p>
                <div class="flex space-x-8 text-gray-400">
                    <a href="/privacy" class="hover:text-gold transition-all duration-300">Privacy Policy</a>
                    <a href="/terms" class="hover:text-gold transition-all duration-300">Terms of Service</a>
                    <a href="/cookies" class="hover:text-gold transition-all duration-300">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'957a1395d437a04f',t:'MTc1MTI0ODg5NC4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
