{"name": "nexuspay", "private": true, "version": "0.0.1", "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite dev --port 5000", "build": "vite build", "preview": "vite preview", "start": "node build", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "postinstall": "svelte-kit sync"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/libsodium-wrappers": "^0.7.14", "@types/node": "^24.0.7", "@types/uuid": "^10.0.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "^5.0.0", "vite": "^6.2.6"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "@sveltejs/adapter-node": "^5.2.12", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "dotenv": "^17.0.0", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "firebase-tools": "^14.9.0", "jsonwebtoken": "^9.0.2", "libsodium-wrappers": "^0.7.15", "motion": "^12.19.2", "plaid": "^36.0.0", "postcss": "^8.5.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.4.17", "twilio": "^5.7.1", "uuid": "^11.1.0", "zod": "^3.25.67"}}