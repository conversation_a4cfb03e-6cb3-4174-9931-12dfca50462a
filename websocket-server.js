import { WebSocketServer } from 'ws';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'boguani-secret-key';
const PORT = 5003; // Use different port to avoid conflicts

// Store connected clients
const clients = new Map();

// Create WebSocket server
const wss = new WebSocketServer({ 
  port: PORT,
  path: '/ws'
});

console.log(`🔌 WebSocket server running on ws://localhost:${PORT}/ws`);

wss.on('connection', (ws, req) => {
  console.log('📱 New WebSocket connection');
  
  let userId = null;
  let isAuthenticated = false;

  ws.on('message', async (data) => {
    try {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'auth':
          try {
            const decoded = jwt.verify(message.token, JWT_SECRET);
            userId = decoded.userId;
            isAuthenticated = true;
            clients.set(userId, ws);
            
            ws.send(JSON.stringify({
              type: 'auth_success',
              userId
            }));
            
            console.log(`✅ User authenticated: ${userId}`);
          } catch (error) {
            ws.send(JSON.stringify({
              type: 'auth_error',
              error: 'Invalid token'
            }));
          }
          break;

        case 'send_message':
          if (!isAuthenticated) {
            ws.send(JSON.stringify({
              type: 'error',
              error: 'Not authenticated'
            }));
            return;
          }

          // Broadcast message to all connected clients
          const messageData = {
            type: 'message',
            message: {
              ...message.message,
              status: 'delivered',
              timestamp: new Date().toISOString()
            }
          };

          // Send to all clients (in real app, send only to chat participants)
          clients.forEach((clientWs, clientId) => {
            if (clientWs.readyState === 1) { // WebSocket.OPEN
              clientWs.send(JSON.stringify(messageData));
            }
          });

          console.log(`📨 Message sent from ${userId}: ${message.message.content}`);
          break;

        case 'call_offer':
          if (!isAuthenticated) return;
          
          // Forward call offer to recipient
          const recipientWs = clients.get(message.to);
          if (recipientWs && recipientWs.readyState === 1) {
            recipientWs.send(JSON.stringify({
              type: 'call_offer',
              from: {
                id: userId,
                name: message.fromName || 'Unknown',
                phoneNumber: message.fromPhone || 'Unknown'
              },
              offer: message.offer,
              callType: message.callType
            }));
          }
          break;

        case 'call_answer':
          if (!isAuthenticated) return;
          
          // Forward answer to caller
          clients.forEach((clientWs, clientId) => {
            if (clientId !== userId && clientWs.readyState === 1) {
              clientWs.send(JSON.stringify({
                type: 'call_answer',
                answer: message.answer
              }));
            }
          });
          break;

        case 'ice_candidate':
          if (!isAuthenticated) return;
          
          // Forward ICE candidate to other participants
          clients.forEach((clientWs, clientId) => {
            if (clientId !== userId && clientWs.readyState === 1) {
              clientWs.send(JSON.stringify({
                type: 'ice_candidate',
                candidate: message.candidate
              }));
            }
          });
          break;

        case 'call_end':
        case 'call_declined':
          if (!isAuthenticated) return;
          
          // Forward call end/decline to all participants
          clients.forEach((clientWs, clientId) => {
            if (clientId !== userId && clientWs.readyState === 1) {
              clientWs.send(JSON.stringify({
                type: message.type
              }));
            }
          });
          break;

        case 'typing':
          if (!isAuthenticated) return;
          
          // Forward typing indicator
          clients.forEach((clientWs, clientId) => {
            if (clientId !== userId && clientWs.readyState === 1) {
              clientWs.send(JSON.stringify({
                type: 'typing',
                userId,
                isTyping: message.isTyping,
                chatId: message.chatId
              }));
            }
          });
          break;

        default:
          console.log('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error processing message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        error: 'Invalid message format'
      }));
    }
  });

  ws.on('close', () => {
    if (userId) {
      clients.delete(userId);
      console.log(`👋 User disconnected: ${userId}`);
      
      // Notify other clients that user went offline
      clients.forEach((clientWs) => {
        if (clientWs.readyState === 1) {
          clientWs.send(JSON.stringify({
            type: 'user_offline',
            userId
          }));
        }
      });
    }
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to BoGuani WebSocket server'
  }));
});

// Handle server shutdown gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down WebSocket server...');
  wss.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down WebSocket server...');
  wss.close(() => {
    console.log('✅ WebSocket server closed');
    process.exit(0);
  });
});

export default wss;
