const CACHE_NAME = 'boguani-v1.0.0';
const STATIC_CACHE = 'boguani-static-v1.0.0';
const DYNAMIC_CACHE = 'boguani-dynamic-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
  '/',
  '/auth',
  '/chat',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('📦 Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('✅ Static files cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Failed to cache static files:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('🗑️ Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip WebSocket and external requests
  if (url.protocol === 'ws:' || url.protocol === 'wss:' || !url.origin.includes(self.location.origin)) {
    return;
  }

  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        // Return cached version if available
        if (cachedResponse) {
          console.log('📦 Serving from cache:', request.url);
          return cachedResponse;
        }

        // Otherwise fetch from network
        return fetch(request)
          .then((response) => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Cache dynamic content
            caches.open(DYNAMIC_CACHE)
              .then((cache) => {
                console.log('💾 Caching dynamic content:', request.url);
                cache.put(request, responseToCache);
              });

            return response;
          })
          .catch((error) => {
            console.error('🌐 Network request failed:', error);
            
            // Return offline page for navigation requests
            if (request.destination === 'document') {
              return caches.match('/offline.html') || new Response(
                '<!DOCTYPE html><html><head><title>BoGuani - Offline</title><meta name="viewport" content="width=device-width,initial-scale=1"><style>body{font-family:system-ui;text-align:center;padding:2rem;background:#1a1a1a;color:#fff}h1{color:#D4AF37}</style></head><body><h1>BoGuani</h1><p>You are currently offline</p><p>Please check your internet connection</p></body></html>',
                { headers: { 'Content-Type': 'text/html' } }
              );
            }

            throw error;
          });
      })
  );
});

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('🔄 Background sync triggered:', event.tag);
  
  if (event.tag === 'send-message') {
    event.waitUntil(syncMessages());
  } else if (event.tag === 'send-payment') {
    event.waitUntil(syncPayments());
  }
});

// Push notifications
self.addEventListener('push', (event) => {
  console.log('📱 Push notification received');
  
  const options = {
    body: 'You have a new message',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/chat'
    },
    actions: [
      {
        action: 'open',
        title: 'Open Chat',
        icon: '/icons/action-open.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/action-dismiss.png'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.message || options.body;
    options.data = { ...options.data, ...data };
  }

  event.waitUntil(
    self.registration.showNotification('BoGuani', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('🔔 Notification clicked:', event.action);
  
  event.notification.close();

  if (event.action === 'open' || !event.action) {
    event.waitUntil(
      clients.openWindow(event.notification.data?.url || '/chat')
    );
  }
});

// Helper functions
async function syncMessages() {
  try {
    // Get pending messages from IndexedDB
    const pendingMessages = await getPendingMessages();
    
    for (const message of pendingMessages) {
      try {
        await fetch('/api/messages/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${message.token}`
          },
          body: JSON.stringify(message.data)
        });
        
        // Remove from pending queue
        await removePendingMessage(message.id);
        console.log('✅ Synced message:', message.id);
      } catch (error) {
        console.error('❌ Failed to sync message:', error);
      }
    }
  } catch (error) {
    console.error('❌ Message sync failed:', error);
  }
}

async function syncPayments() {
  try {
    // Get pending payments from IndexedDB
    const pendingPayments = await getPendingPayments();
    
    for (const payment of pendingPayments) {
      try {
        await fetch('/api/payments/send', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${payment.token}`
          },
          body: JSON.stringify(payment.data)
        });
        
        // Remove from pending queue
        await removePendingPayment(payment.id);
        console.log('✅ Synced payment:', payment.id);
      } catch (error) {
        console.error('❌ Failed to sync payment:', error);
      }
    }
  } catch (error) {
    console.error('❌ Payment sync failed:', error);
  }
}

// IndexedDB helpers (simplified for demo)
async function getPendingMessages() {
  // In a real app, you'd use IndexedDB to store pending messages
  return [];
}

async function removePendingMessage(id) {
  // Remove message from IndexedDB
}

async function getPendingPayments() {
  // In a real app, you'd use IndexedDB to store pending payments
  return [];
}

async function removePendingPayment(id) {
  // Remove payment from IndexedDB
}

console.log('🎉 BoGuani Service Worker loaded successfully');
