(()=>{var e={};e.id=365,e.ids=[365],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3150:(e,t,a)=>{Promise.resolve().then(a.bind(a,5795))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5288:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=a(5239),s=a(8088),o=a(8170),l=a.n(o),n=a(893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(t,i);let d={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,8305)),"/Volumes/Apps/Websites/ChatPay/src/app/auth/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Volumes/Apps/Websites/ChatPay/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Volumes/Apps/Websites/ChatPay/src/app/auth/page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5795:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var r=a(687),s=a(6180),o=a.n(s),l=a(3210),n=a(6189),i=a(5814),d=a.n(i);function c(){let e=(0,n.useRouter)(),[t,a]=(0,l.useState)("phone"),[s,i]=(0,l.useState)("+1"),[c,x]=(0,l.useState)(""),[m,u]=(0,l.useState)(""),[p,f]=(0,l.useState)(""),[h,g]=(0,l.useState)(""),[b,j]=(0,l.useState)(""),[y,w]=(0,l.useState)(!1),[v,N]=(0,l.useState)(!1),k=async()=>{if(!c.trim())return void j("Please enter your phone number");w(!0),j("");try{await new Promise(e=>setTimeout(e,2e3)),a("otp"),w(!1)}catch{j("Failed to send verification code. Please try again."),w(!1)}},F=async()=>{if(!m.trim()||6!==m.length)return void j("Please enter a valid 6-digit code");w(!0),j("");try{await new Promise(e=>setTimeout(e,1500)),a("profile"),w(!1)}catch{j("Invalid verification code. Please try again."),w(!1)}},C=async()=>{if(!p.trim()||!h.trim())return void j("Please fill in all fields");w(!0),j("");try{await new Promise(e=>setTimeout(e,2e3)),w(!1),N(!0),setTimeout(()=>{e.push("/chat")},1500)}catch{j("Failed to create profile. Please try again."),w(!1)}},P=()=>{"otp"===t?a("phone"):"profile"===t&&a("otp"),j("")};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o(),{id:"c8e67e6c32d144f",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:-moz-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:-o-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:linear-gradient(135deg,#111827 0%,#1F2937 50%,#374151 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.feature-card{-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.feature-card:hover{-webkit-transform:translatey(-5px);-moz-transform:translatey(-5px);-ms-transform:translatey(-5px);-o-transform:translatey(-5px);transform:translatey(-5px);-webkit-box-shadow:0 10px 25px -5px rgba(0,0,0,.3);-moz-box-shadow:0 10px 25px -5px rgba(0,0,0,.3);box-shadow:0 10px 25px -5px rgba(0,0,0,.3)}.btn-hover:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 5px 15px rgba(212,175,55,.3);-moz-box-shadow:0 5px 15px rgba(212,175,55,.3);box-shadow:0 5px 15px rgba(212,175,55,.3)}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}.auth-card{background:-webkit-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-moz-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-o-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:linear-gradient(135deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.3)}.auth-input{background:rgba(17,24,39,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.5);-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.auth-input:focus{border-color:#D4AF37;-webkit-box-shadow:0 0 0 3px rgba(212,175,55,.1);-moz-box-shadow:0 0 0 3px rgba(212,175,55,.1);box-shadow:0 0 0 3px rgba(212,175,55,.1);background:rgba(17,24,39,.9)}.professional-shadow{-webkit-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);-moz-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2)}"}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900",children:(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f min-h-screen flex items-center justify-center px-6",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f w-full max-w-md",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f text-center mb-12",children:[(0,r.jsx)(d(),{href:"/",className:"inline-block",children:(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300",children:(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-comment-dollar text-3xl text-purple-900"})})}),(0,r.jsx)("h1",{className:"jsx-c8e67e6c32d144f text-4xl md:text-5xl font-bold mb-4 gold-gradient",children:"Welcome to BoGuani"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-xl text-gray-300 mb-2",children:"Messenger of Value"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 italic",children:'"Speak Gold. Share Value."'})]}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f auth-card p-8 rounded-2xl professional-shadow",children:v?(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f text-center py-8",children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-check text-2xl text-purple-900"})}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-4 gold-gradient",children:"Welcome to BoGuani!"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-300 mb-6",children:"Your account has been created successfully."}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-sm text-gray-400 mt-4",children:"Redirecting to chat..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f flex justify-center mb-8",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex space-x-4",children:[(0,r.jsx)("div",{className:`jsx-c8e67e6c32d144f w-3 h-3 rounded-full ${"phone"===t?"bg-yellow-400":"bg-gray-600"}`}),(0,r.jsx)("div",{className:`jsx-c8e67e6c32d144f w-3 h-3 rounded-full ${"otp"===t?"bg-yellow-400":"bg-gray-600"}`}),(0,r.jsx)("div",{className:`jsx-c8e67e6c32d144f w-3 h-3 rounded-full ${"profile"===t?"bg-yellow-400":"bg-gray-600"}`})]})}),b&&(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex items-center",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-exclamation-triangle text-red-400 mr-3"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-red-200 text-sm",children:b})]})}),"phone"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Enter Your Phone"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:"We'll send you a verification code"})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),k()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"countryCode",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Country"}),(0,r.jsx)("select",{id:"countryCode",value:s,onChange:e=>i(e.target.value),className:"jsx-c8e67e6c32d144f w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400",children:[{code:"+1",country:"United States",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"+1",country:"Canada",flag:"\uD83C\uDDE8\uD83C\uDDE6"},{code:"+44",country:"United Kingdom",flag:"\uD83C\uDDEC\uD83C\uDDE7"},{code:"+33",country:"France",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"+49",country:"Germany",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"+81",country:"Japan",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"+86",country:"China",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"+91",country:"India",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"+55",country:"Brazil",flag:"\uD83C\uDDE7\uD83C\uDDF7"},{code:"+61",country:"Australia",flag:"\uD83C\uDDE6\uD83C\uDDFA"}].map((e,t)=>(0,r.jsxs)("option",{value:e.code,className:"jsx-c8e67e6c32d144f",children:[e.flag," ",e.code," (",e.country,")"]},t))})]}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"phone",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex space-x-2",children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-20 px-3 py-3 auth-input rounded-lg text-center text-gray-300",children:s}),(0,r.jsx)("input",{type:"tel",id:"phone",value:c,onChange:e=>x(e.target.value),placeholder:"1234567890",required:!0,className:"jsx-c8e67e6c32d144f flex-1 px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]})]}),(0,r.jsx)("button",{type:"submit",disabled:y,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Sending Code..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-paper-plane mr-2"}),"Send Verification Code"]})})]})]}),"otp"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsxs)("button",{onClick:P,className:"jsx-c8e67e6c32d144f text-yellow-400 hover:text-yellow-200 transition-colors mb-4",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-arrow-left mr-2"}),"Back"]}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Enter Verification Code"}),(0,r.jsxs)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:["We sent a 6-digit code to ",s,c]})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),F()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"otp",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Verification Code"}),(0,r.jsx)("input",{type:"text",id:"otp",value:m,onChange:e=>u(e.target.value),placeholder:"123456",maxLength:6,required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none"})]}),(0,r.jsx)("button",{type:"submit",disabled:y,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Verifying..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-check mr-2"}),"Verify Code"]})})]})]}),"profile"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsxs)("button",{onClick:P,className:"jsx-c8e67e6c32d144f text-yellow-400 hover:text-yellow-200 transition-colors mb-4",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-arrow-left mr-2"}),"Back"]}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Create Your Profile"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:"Tell us a bit about yourself"})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),C()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"name",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Full Name"}),(0,r.jsx)("input",{type:"text",id:"name",value:p,onChange:e=>f(e.target.value),placeholder:"John Doe",required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"username",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Username"}),(0,r.jsx)("input",{type:"text",id:"username",value:h,onChange:e=>g(e.target.value),placeholder:"johndoe",required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]}),(0,r.jsx)("button",{type:"submit",disabled:y,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Creating Profile..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-user-plus mr-2"}),"Complete Setup"]})})]})]})]})}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f text-center mt-8",children:(0,r.jsxs)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:["By continuing, you agree to our"," ",(0,r.jsx)(d(),{href:"/terms",className:"text-yellow-400 hover:text-yellow-200 transition-colors",children:"Terms"})," and"," ",(0,r.jsx)(d(),{href:"/privacy",className:"text-yellow-400 hover:text-yellow-200 transition-colors",children:"Privacy Policy"})]})})]})})})]})}},6189:(e,t,a)=>{"use strict";var r=a(5773);a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},7878:(e,t,a)=>{Promise.resolve().then(a.bind(a,8305))},8305:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/ChatPay/src/app/auth/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/ChatPay/src/app/auth/page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,938,658,814,279],()=>a(5288));module.exports=r})();