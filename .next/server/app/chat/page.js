(()=>{var e={};e.id=457,e.ids=[457],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3240:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=t(5239),r=t(8088),i=t(8170),l=t.n(i),n=t(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c={children:["",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8973)),"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Volumes/Apps/Websites/ChatPay/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/chat/page",pathname:"/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3430:(e,s,t)=>{Promise.resolve().then(t.bind(t,8973))},3873:e=>{"use strict";e.exports=require("path")},4384:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var a=t(687),r=t(6180),i=t.n(r),l=t(3210),n=t(6189),o=t(6001),c=t(2157),d=t(2789),x=t(2743),m=t(1279),h=t(8171),u=t(2582);class b extends l.Component{getSnapshotBeforeUpdate(e){let s=this.props.childRef.current;if(s&&e.isPresent&&!this.props.isPresent){let e=s.offsetParent,t=(0,h.s)(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=s.offsetHeight||0,a.width=s.offsetWidth||0,a.top=s.offsetTop,a.left=s.offsetLeft,a.right=t-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function g({children:e,isPresent:s,anchorX:t,root:r}){let i=(0,l.useId)(),n=(0,l.useRef)(null),o=(0,l.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,l.useContext)(u.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:a,top:l,left:d,right:x}=o.current;if(s||!n.current||!e||!a)return;let m="left"===t?`left: ${d}`:`right: ${x}`;n.current.dataset.motionPopId=i;let h=document.createElement("style");c&&(h.nonce=c);let u=r??document.head;return u.appendChild(h),h.sheet&&h.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${m}px !important;
            top: ${l}px !important;
          }
        `),()=>{u.removeChild(h),u.contains(h)&&u.removeChild(h)}},[s]),(0,a.jsx)(b,{isPresent:s,childRef:n,sizeRef:o,children:l.cloneElement(e,{ref:n})})}let f=({children:e,initial:s,isPresent:t,onExitComplete:r,custom:i,presenceAffectsLayout:n,mode:o,anchorX:c,root:x})=>{let h=(0,d.M)(p),u=(0,l.useId)(),b=!0,f=(0,l.useMemo)(()=>(b=!1,{id:u,initial:s,isPresent:t,custom:i,onExitComplete:e=>{for(let s of(h.set(e,!0),h.values()))if(!s)return;r&&r()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[t,h,r]);return n&&b&&(f={...f}),(0,l.useMemo)(()=>{h.forEach((e,s)=>h.set(s,!1))},[t]),l.useEffect(()=>{t||h.size||!r||r()},[t]),"popLayout"===o&&(e=(0,a.jsx)(g,{isPresent:t,anchorX:c,root:x,children:e})),(0,a.jsx)(m.t.Provider,{value:f,children:e})};function p(){return new Map}var j=t(6044);let y=e=>e.key||"";function v(e){let s=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&s.push(e)}),s}let N=({children:e,custom:s,initial:t=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:n="sync",propagate:o=!1,anchorX:m="left",root:h})=>{let[u,b]=(0,j.xQ)(o),g=(0,l.useMemo)(()=>v(e),[e]),p=o&&!u?[]:g.map(y),N=(0,l.useRef)(!0),w=(0,l.useRef)(g),k=(0,d.M)(()=>new Map),[C,S]=(0,l.useState)(g),[P,A]=(0,l.useState)(g);(0,x.E)(()=>{N.current=!1,w.current=g;for(let e=0;e<P.length;e++){let s=y(P[e]);p.includes(s)?k.delete(s):!0!==k.get(s)&&k.set(s,!1)}},[P,p.length,p.join("-")]);let $=[];if(g!==C){let e=[...g];for(let s=0;s<P.length;s++){let t=P[s],a=y(t);p.includes(a)||(e.splice(s,0,t),$.push(t))}return"wait"===n&&$.length&&(e=$),A(v(e)),S(g),null}let{forceRender:D}=(0,l.useContext)(c.L);return(0,a.jsx)(a.Fragment,{children:P.map(e=>{let l=y(e),c=(!o||!!u)&&(g===P||p.includes(l));return(0,a.jsx)(f,{isPresent:c,initial:(!N.current||!!t)&&void 0,custom:s,presenceAffectsLayout:i,mode:n,root:h,onExitComplete:c?void 0:()=>{if(!k.has(l))return;k.set(l,!0);let e=!0;k.forEach(s=>{s||(e=!1)}),e&&(D?.(),A(w.current),o&&b?.(),r&&r())},anchorX:m,children:e},l)})})};function w({isOpen:e,onClose:s,currentUser:t,bankBalance:r,connectedBank:i}){let[n,c]=(0,l.useState)("profile"),[d,x]=(0,l.useState)({name:t?.name||"",username:t?.username||"",phone:t?.phone||"",email:"<EMAIL>",status:t?.status||"",bio:"Messenger of Value",avatar:t?.avatar||""}),[m,h]=(0,l.useState)({enterToSend:!0,readReceipts:!0,typingIndicators:!0,lastSeen:!0,mediaAutoDownload:!0,soundEnabled:!0,vibrationEnabled:!0,messagePreview:!0,groupNotifications:!0,archiveChats:!1}),[u,b]=(0,l.useState)({instantTransfers:!0,paymentNotifications:!0,transactionLimits:{daily:5e3,monthly:25e3},autoSave:!1,savingsGoal:1e3,requirePinForPayments:!0,biometricAuth:!0}),[g,f]=(0,l.useState)({twoFactorAuth:!0,biometricLogin:!0,sessionTimeout:30,deviceVerification:!0,encryptionLevel:"military",backupMessages:!0,screenLock:!0,incognitoMode:!1}),[p,j]=(0,l.useState)({pushNotifications:!0,emailNotifications:!1,smsNotifications:!1,soundAlerts:!0,vibration:!0,showPreviews:!0,quietHours:{enabled:!1,start:"22:00",end:"07:00"},priorityContacts:[]}),[y,v]=(0,l.useState)({profileVisibility:"contacts",lastSeenVisibility:"contacts",statusVisibility:"everyone",readReceiptSharing:!0,blockUnknownNumbers:!1,dataCollection:!1,analyticsSharing:!1,locationSharing:!1}),[w,k]=(0,l.useState)({theme:"dark",accentColor:"gold",fontSize:"medium",chatWallpaper:"default",bubbleStyle:"transparent",animationsEnabled:!0,compactMode:!1,highContrast:!1});return e?(0,a.jsx)(N,{children:(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:s,children:(0,a.jsx)(o.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] border border-gray-600/50 professional-shadow overflow-hidden",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"w-64 bg-gradient-to-b from-gray-900/90 to-gray-800/90 border-r border-gray-600/30 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white gold-gradient",children:"Settings"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,a.jsx)("nav",{className:"space-y-2",children:[{id:"profile",name:"Profile",icon:"fas fa-user"},{id:"chat",name:"Chat",icon:"fas fa-comments"},{id:"banking",name:"Banking",icon:"fas fa-university"},{id:"security",name:"Security",icon:"fas fa-shield-alt"},{id:"notifications",name:"Notifications",icon:"fas fa-bell"},{id:"privacy",name:"Privacy",icon:"fas fa-lock"},{id:"appearance",name:"Appearance",icon:"fas fa-palette"},{id:"advanced",name:"Advanced",icon:"fas fa-cogs"}].map(e=>(0,a.jsxs)("button",{onClick:()=>c(e.id),className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${n===e.id?"bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 text-yellow-400":"text-gray-400 hover:text-white hover:bg-gray-700/50"}`,children:[(0,a.jsx)("i",{className:`${e.icon} text-lg`}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]},e.id))})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-8",children:["profile"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your personal information and profile appearance"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Profile Picture"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-gray-900 font-bold text-2xl professional-shadow",children:d.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-6 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300",children:"Upload Photo"}),(0,a.jsx)("button",{className:"block text-gray-400 hover:text-white transition-colors text-sm",children:"Remove Photo"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Personal Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:d.name,onChange:e=>x({...d,name:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),(0,a.jsx)("input",{type:"text",value:d.username,onChange:e=>x({...d,username:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:d.phone,onChange:e=>x({...d,phone:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:d.email,onChange:e=>x({...d,email:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Status & Bio"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Message"}),(0,a.jsx)("input",{type:"text",value:d.status,onChange:e=>x({...d,status:e.target.value}),placeholder:"Available for chat",className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),(0,a.jsx)("textarea",{value:d.bio,onChange:e=>x({...d,bio:e.target.value}),placeholder:"Tell others about yourself...",rows:3,className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none resize-none"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-8 py-3 rounded-xl font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})})]}),"chat"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Chat Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize your messaging experience and preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Message Behavior"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enter to Send"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Press Enter to send messages (Shift+Enter for new line)"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,enterToSend:!m.enterToSend}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.enterToSend?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.enterToSend?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Read Receipts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you've read messages"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,readReceipts:!m.readReceipts}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.readReceipts?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.readReceipts?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Typing Indicators"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you're typing to others"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,typingIndicators:!m.typingIndicators}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.typingIndicators?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.typingIndicators?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Last Seen"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show your last seen status to contacts"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,lastSeen:!m.lastSeen}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.lastSeen?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.lastSeen?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Media & Files"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Download Media"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically download photos and videos"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,mediaAutoDownload:!m.mediaAutoDownload}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.mediaAutoDownload?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.mediaAutoDownload?"translate-x-6":"translate-x-1"}`})})]})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Chat Notifications"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for new messages"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,soundEnabled:!m.soundEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.soundEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.soundEnabled?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Vibration"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Vibrate for new messages"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,vibrationEnabled:!m.vibrationEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.vibrationEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.vibrationEnabled?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Message Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>h({...m,messagePreview:!m.messagePreview}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.messagePreview?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.messagePreview?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"banking"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Banking & Payments"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your financial settings and payment preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Connected Bank Accounts"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/20",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-university text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:i}),(0,a.jsx)("p",{className:"text-green-400 text-sm",children:"Primary Account • Verified"}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Balance: $",r.toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-edit"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-400 transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,a.jsxs)("button",{className:"w-full p-4 border-2 border-dashed border-gray-600 rounded-xl text-gray-400 hover:text-white hover:border-yellow-400 transition-all duration-300",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Add New Bank Account"]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Payment Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Instant Transfers"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable instant money transfers (small fee may apply)"})]}),(0,a.jsx)("button",{onClick:()=>b({...u,instantTransfers:!u.instantTransfers}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.instantTransfers?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.instantTransfers?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Payment Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Get notified for all payment activities"})]}),(0,a.jsx)("button",{onClick:()=>b({...u,paymentNotifications:!u.paymentNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.paymentNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.paymentNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Require PIN for Payments"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Require PIN verification for all payments"})]}),(0,a.jsx)("button",{onClick:()=>b({...u,requirePinForPayments:!u.requirePinForPayments}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.requirePinForPayments?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.requirePinForPayments?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint/face ID for payments"})]}),(0,a.jsx)("button",{onClick:()=>b({...u,biometricAuth:!u.biometricAuth}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.biometricAuth?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.biometricAuth?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Transaction Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Daily Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:u.transactionLimits.daily,onChange:e=>b({...u,transactionLimits:{...u.transactionLimits,daily:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Monthly Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:u.transactionLimits.monthly,onChange:e=>b({...u,transactionLimits:{...u.transactionLimits,monthly:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Savings & Goals"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Save"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically save spare change from transactions"})]}),(0,a.jsx)("button",{onClick:()=>b({...u,autoSave:!u.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.autoSave?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.autoSave?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Savings Goal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:u.savingsGoal,onChange:e=>b({...u,savingsGoal:parseInt(e.target.value)}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none",placeholder:"1000"})]})]})]})]})]}),"security"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Security & Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Protect your account with advanced security features"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Authentication"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,twoFactorAuth:!g.twoFactorAuth}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.twoFactorAuth?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.twoFactorAuth?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Login"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint or face ID to log in"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,biometricLogin:!g.biometricLogin}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.biometricLogin?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.biometricLogin?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Device Verification"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Verify new devices before allowing access"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,deviceVerification:!g.deviceVerification}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.deviceVerification?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.deviceVerification?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsxs)("select",{value:g.sessionTimeout,onChange:e=>f({...g,sessionTimeout:parseInt(e.target.value)}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:15,children:"15 minutes"}),(0,a.jsx)("option",{value:30,children:"30 minutes"}),(0,a.jsx)("option",{value:60,children:"1 hour"}),(0,a.jsx)("option",{value:120,children:"2 hours"}),(0,a.jsx)("option",{value:0,children:"Never"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Encryption & Data"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Encryption Level"}),(0,a.jsxs)("select",{value:g.encryptionLevel,onChange:e=>f({...g,encryptionLevel:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"standard",children:"Standard (128-bit)"}),(0,a.jsx)("option",{value:"enhanced",children:"Enhanced (256-bit)"}),(0,a.jsx)("option",{value:"military",children:"Military Grade (AES-256)"})]}),(0,a.jsxs)("p",{className:"text-green-400 text-sm mt-2 flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"Current: Military Grade AES-256 Encryption"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Backup Messages"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Securely backup your messages to the cloud"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,backupMessages:!g.backupMessages}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.backupMessages?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.backupMessages?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Screen Lock"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Lock app when switching to other apps"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,screenLock:!g.screenLock}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.screenLock?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.screenLock?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Incognito Mode"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Hide message previews and disable screenshots"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,incognitoMode:!g.incognitoMode}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${g.incognitoMode?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${g.incognitoMode?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Security Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-key text-blue-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Change Password"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-green-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Manage Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-history text-yellow-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Login History"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt text-red-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Sign Out All Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]})]}),"notifications"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Notifications"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control how and when you receive notifications"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"General"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive notifications on this device"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,pushNotifications:!p.pushNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.pushNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.pushNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive important updates via email"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,emailNotifications:!p.emailNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.emailNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.emailNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Alerts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for notifications"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,soundAlerts:!p.soundAlerts}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.soundAlerts?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.soundAlerts?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Show Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,showPreviews:!p.showPreviews}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.showPreviews?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.showPreviews?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Quiet Hours"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enable Quiet Hours"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Mute notifications during specified hours"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,quietHours:{...p.quietHours,enabled:!p.quietHours.enabled}}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.quietHours.enabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.quietHours.enabled?"translate-x-6":"translate-x-1"}`})})]}),p.quietHours.enabled&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Start Time"}),(0,a.jsx)("input",{type:"time",value:p.quietHours.start,onChange:e=>j({...p,quietHours:{...p.quietHours,start:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"End Time"}),(0,a.jsx)("input",{type:"time",value:p.quietHours.end,onChange:e=>j({...p,quietHours:{...p.quietHours,end:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]})]})]})]})]}),"privacy"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control who can see your information and activity"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Visibility"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Profile Visibility"}),(0,a.jsxs)("select",{value:y.profileVisibility,onChange:e=>v({...y,profileVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Last Seen"}),(0,a.jsxs)("select",{value:y.lastSeenVisibility,onChange:e=>v({...y,lastSeenVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Visibility"}),(0,a.jsxs)("select",{value:y.statusVisibility,onChange:e=>v({...y,statusVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data & Analytics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Data Collection"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow collection of usage data for improvements"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,dataCollection:!y.dataCollection}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.dataCollection?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.dataCollection?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Analytics Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Share anonymous analytics data"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,analyticsSharing:!y.analyticsSharing}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.analyticsSharing?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.analyticsSharing?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Location Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow location sharing in messages"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,locationSharing:!y.locationSharing}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.locationSharing?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.locationSharing?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"appearance"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Appearance"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize the look and feel of BoGuani"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"dark"}),className:`p-4 rounded-xl border-2 transition-all ${"dark"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Dark"})]}),(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"light"}),className:`p-4 rounded-xl border-2 transition-all ${"light"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Light"})]}),(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"auto"}),className:`p-4 rounded-xl border-2 transition-all ${"auto"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-r from-gray-900 via-gray-500 to-gray-100 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Auto"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Customization"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Accent Color"}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-3",children:["gold","blue","green","purple","red","orange"].map(e=>(0,a.jsx)("button",{onClick:()=>k({...w,accentColor:e}),className:`w-12 h-12 rounded-full border-2 transition-all ${w.accentColor===e?"border-white scale-110":"border-gray-600"} ${"gold"===e?"bg-yellow-400":"blue"===e?"bg-blue-500":"green"===e?"bg-green-500":"purple"===e?"bg-purple-500":"red"===e?"bg-red-500":"bg-orange-500"}`},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Font Size"}),(0,a.jsxs)("select",{value:w.fontSize,onChange:e=>k({...w,fontSize:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"}),(0,a.jsx)("option",{value:"extra-large",children:"Extra Large"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Message Bubble Style"}),(0,a.jsxs)("select",{value:w.bubbleStyle,onChange:e=>k({...w,bubbleStyle:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"transparent",children:"Transparent"}),(0,a.jsx)("option",{value:"solid",children:"Solid"}),(0,a.jsx)("option",{value:"gradient",children:"Gradient"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Animations"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable smooth animations and transitions"})]}),(0,a.jsx)("button",{onClick:()=>k({...w,animationsEnabled:!w.animationsEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${w.animationsEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${w.animationsEnabled?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"advanced"===n&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Advanced"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Advanced settings and developer options"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data Management"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-download text-blue-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Export Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Download your messages and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-upload text-green-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Import Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Import messages from other apps"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-broom text-yellow-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Clear Cache"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Free up storage space"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Developer Options"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 rounded-xl border border-purple-400/30 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-code text-purple-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"API Settings"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Configure API endpoints and keys"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-600/20 to-indigo-500/20 rounded-xl border border-indigo-400/30 hover:border-indigo-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-bug text-indigo-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Debug Logs"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"View and export debug information"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-teal-600/20 to-teal-500/20 rounded-xl border border-teal-400/30 hover:border-teal-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-flask text-teal-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Beta Features"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Enable experimental features"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Account Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-orange-600/20 to-orange-500/20 rounded-xl border border-orange-400/30 hover:border-orange-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-user-times text-orange-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Deactivate Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Temporarily disable your account"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-trash text-red-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Delete Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Permanently delete your account and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"App Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Version"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"1.0.0"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Build"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"2024.01.15"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Platform"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Web"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Encryption"}),(0,a.jsxs)("span",{className:"text-green-400 font-medium flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"AES-256 Active"]})]})]})]})]})]})})]})})})}):null}let k=[{id:"1",name:"John Doe",username:"johndoe",phone:"+1234567890",isOnline:!0,lastSeen:"2 min ago",avatar:"JD",status:"Available for chat"},{id:"2",name:"Jane Smith",username:"janesmith",phone:"+1234567891",isOnline:!1,lastSeen:"1 hour ago",avatar:"JS",status:"Busy with work"},{id:"3",name:"Mike Johnson",username:"mikej",phone:"+1234567892",isOnline:!0,lastSeen:"now",avatar:"MJ",status:"Ready to receive payments"},{id:"4",name:"Sarah Wilson",username:"sarahw",phone:"+1234567893",isOnline:!1,lastSeen:"30 min ago",avatar:"SW",status:"At the gym"}];function C(){let e=(0,n.useRouter)(),[s,t]=(0,l.useState)(null),[r,c]=(0,l.useState)(null),[d,x]=(0,l.useState)([]),[m,h]=(0,l.useState)([]),[u,b]=(0,l.useState)(""),[g,f]=(0,l.useState)(!1),[p,j]=(0,l.useState)(""),[y,v]=(0,l.useState)(!0),[k,C]=(0,l.useState)(""),[S,P]=(0,l.useState)(!1),[A,$]=(0,l.useState)(!1),[D,E]=(0,l.useState)({isActive:!1,type:"voice"}),[M,F]=(0,l.useState)(!1),[L,V]=(0,l.useState)(null),[z]=(0,l.useState)(2847.32),[I]=(0,l.useState)("Chase ****1234"),[B,R]=(0,l.useState)(!1),T=(0,l.useRef)(null),q=(0,l.useRef)(null),H=(0,l.useRef)(null),G=e=>{if(e?.preventDefault(),!u.trim()||!r||!s)return;let t={id:Date.now().toString(),senderId:s.id,text:u,timestamp:new Date,status:"sending",type:"text"},a=[...d,t];x(a),b(""),setTimeout(()=>{x(e=>e.map(e=>e.id===t.id?{...e,status:"delivered"}:e))},1e3);let i=m.map(e=>e.id===r.id?{...e,lastMessage:u,timestamp:new Date,unreadCount:0,messages:[...a]}:e);h(i),c(i.find(e=>e.id===r.id)||null)};if(y||!s)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading chat..."})]})});let O=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),_=e=>e.toLocaleDateString([],{weekday:"long",year:"numeric",month:"long",day:"numeric"}),U=e=>{switch(e){case"sending":return(0,a.jsx)("span",{className:"text-gray-400",children:"\uD83D\uDD52"});case"sent":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓"});case"delivered":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓✓"});case"read":return(0,a.jsx)("span",{className:"text-blue-500",children:"✓✓"});default:return null}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i(),{id:"7110b046d83e55cc",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-moz-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-o-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:linear-gradient(135deg,#1E1E24 0%,#2D1B4E 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}.message-bubble{-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(212,175,55,.2)}.message-bubble:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 8px 25px rgba(0,0,0,.3);-moz-box-shadow:0 8px 25px rgba(0,0,0,.3);box-shadow:0 8px 25px rgba(0,0,0,.3);border-color:rgba(212,175,55,.4)}.message-bubble-sent{background:rgba(212,175,55,.15);border-color:rgba(212,175,55,.3)}.message-bubble-received{background:rgba(45,27,78,.4);border-color:rgba(107,114,128,.3)}.chat-input{background:rgba(17,24,39,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.5);-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.chat-input:focus{border-color:#D4AF37;-webkit-box-shadow:0 0 0 3px rgba(212,175,55,.1);-moz-box-shadow:0 0 0 3px rgba(212,175,55,.1);box-shadow:0 0 0 3px rgba(212,175,55,.1);background:rgba(17,24,39,.9)}.sidebar-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-o-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:linear-gradient(180deg,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-right:1px solid rgba(75,85,99,.3)}.chat-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-o-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:linear-gradient(180deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}.encryption-indicator{-webkit-animation:pulse 2s infinite;-moz-animation:pulse 2s infinite;-o-animation:pulse 2s infinite;animation:pulse 2s infinite}@-webkit-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-moz-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-o-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}.bank-balance{background:-webkit-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-moz-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-o-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:linear-gradient(135deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);border:1px solid rgba(34,197,94,.3)}.professional-shadow{-webkit-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);-moz-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2)}"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow",children:s.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsx)("div",{title:"End-to-End Encrypted",className:"jsx-7110b046d83e55cc absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:s.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:["@",s.username]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"E2E Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>F(!0),title:"Settings",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-lg"})}),(0,a.jsx)("button",{onClick:()=>{t(null),h([]),x([]),c(null),e.push("/auth")},title:"Logout",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-sign-out-alt text-lg"})})]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search conversations...",value:k,onChange:e=>C(e.target.value),className:"jsx-7110b046d83e55cc w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"}),(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"}),k&&(0,a.jsx)("button",{onClick:()=>C(""),className:"jsx-7110b046d83e55cc absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",children:m.map(e=>(0,a.jsx)(o.P.div,{className:`p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ${r?.id===e.id?"bg-gray-800/70 border-l-4 border-l-yellow-400":""}`,onClick:()=>{c(e),x(e.messages)},whileHover:{x:6},transition:{duration:.3,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:e.participant.avatar||e.participant.name.split(" ").map(e=>e[0]).join("")}),e.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"}),e.unreadCount>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold",children:e.unreadCount})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3 flex-1",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:e.participant.name}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:O(e.timestamp)})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-400 truncate",children:e.lastMessage}),e.participant.status&&(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 italic mt-1",children:e.participant.status})]})]})},e.id))}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc bank-balance rounded-xl p-4 backdrop-blur-lg",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm font-medium text-gray-300",children:I})]}),(0,a.jsx)("button",{onClick:()=>F(!0),title:"Manage Bank Account",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-sm"})})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:"Available Balance"}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xl font-bold text-green-400",children:["$",z.toLocaleString("en-US",{minimumFractionDigits:2})]})]}),(0,a.jsxs)("button",{onClick:()=>f(!0),className:"jsx-7110b046d83e55cc bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send"]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mt-2 flex items-center text-xs text-gray-500",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1 text-green-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Secured by Plaid & 256-bit AES encryption"})]})]})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex flex-col chat-panel",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:r.participant.avatar||r.participant.name.split(" ").map(e=>e[0]).join("")}),r.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:r.participant.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:r.participant.isOnline?r.participant.isTyping?"Typing...":"Online":`Last seen ${r.participant.lastSeen}`}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>E({isActive:!0,type:"voice",participant:r.participant}),title:"Voice Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone text-lg"})}),(0,a.jsx)("button",{onClick:()=>{E({isActive:!0,type:"video",participant:r.participant}),R(!0)},title:"Video Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-video text-lg"})}),(0,a.jsx)("button",{onClick:()=>F(!0),title:"User Info",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-info-circle text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto p-4 space-y-4",children:[d.length>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center mb-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:_(d[0].timestamp)})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc space-y-4",children:d.map((e,t)=>{let r=e.senderId===s.id,i=0===t||new Date(e.timestamp).toDateString()!==new Date(d[t-1].timestamp).toDateString();return(0,a.jsxs)(o.P.div,{className:"space-y-1",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[i&&0!==t&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center my-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:_(e.timestamp)})}),(0,a.jsx)("div",{className:`jsx-7110b046d83e55cc flex ${r?"justify-end":"justify-start"}`,children:(0,a.jsxs)("div",{className:`jsx-7110b046d83e55cc max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${r?"message-bubble-sent text-white rounded-tr-none":"message-bubble-received text-white rounded-tl-none"}`,children:["payment"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-white text-sm"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm font-semibold text-green-400",children:["Payment Sent: $",e.amount?.toFixed(2)," ",e.currency]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 mt-1",children:"Secured by Plaid • Instant Transfer"})]})]}),"image"===e.type&&e.imageUrl&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-2",children:(0,a.jsx)("img",{src:e.imageUrl,alt:"Shared image",onClick:()=>window.open(e.imageUrl,"_blank"),className:"jsx-7110b046d83e55cc rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"})}),"voice"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg",children:[(0,a.jsx)("button",{className:"jsx-7110b046d83e55cc w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-play text-sm"})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 h-2 bg-gray-600/50 rounded-full",children:(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full"})}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 font-medium",children:e.duration})]}),("text"===e.type||"payment"===e.type)&&(0,a.jsx)("p",{className:`jsx-7110b046d83e55cc ${"payment"===e.type?"text-sm":""} leading-relaxed`,children:e.text}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-end mt-2 space-x-2",children:[(0,a.jsx)("span",{className:`jsx-7110b046d83e55cc text-xs ${r?"text-gray-400":"text-gray-500"} font-medium`,children:O(e.timestamp)}),r&&(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc ml-1",children:U(e.status)}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock text-xs"})})]})]})})]},e.id)})}),(0,a.jsx)("div",{ref:T,className:"jsx-7110b046d83e55cc"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow",children:[L&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 font-semibold",children:["Replying to ",L.senderId===s.id?"yourself":r.participant.name]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-300 truncate",children:L.text})]}),(0,a.jsx)("button",{onClick:()=>V(null),className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)(N,{children:A&&(0,a.jsxs)(o.P.div,{className:"mb-3 flex space-x-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},children:[(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-image text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Photo"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-file text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Document"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-map-marker-alt text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Location"})]})]})}),(0,a.jsxs)("form",{onSubmit:G,className:"jsx-7110b046d83e55cc flex items-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>$(!A),className:"jsx-7110b046d83e55cc p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-plus text-lg"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 relative",children:[(0,a.jsx)("textarea",{value:u,onChange:e=>b(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),G(e))},placeholder:"Type a message...",rows:1,style:{minHeight:"48px",maxHeight:"120px"},className:"jsx-7110b046d83e55cc w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc absolute right-3 bottom-3 flex items-center space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>P(!S),className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-smile text-lg"})}),(0,a.jsx)("button",{type:"button",title:"Voice Message",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-blue-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-microphone text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>f(!0),title:"Send Money via Plaid",className:"jsx-7110b046d83e55cc p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-lg"})}),(0,a.jsx)("button",{type:"submit",disabled:!u.trim(),className:`jsx-7110b046d83e55cc p-3 rounded-full transition-all duration-300 professional-shadow ${u.trim()?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane text-lg"})})]})]})]})]}):(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center p-8",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-comment-dollar text-3xl text-purple-900"})}),(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-medium text-white mb-2",children:"Welcome to BoGuani"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-300",children:"Select a conversation to start messaging"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-yellow-400 text-sm mt-2 italic",children:'"Speak Gold. Share Value."'})]})})}),(0,a.jsx)(N,{children:D.isActive&&(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center text-white",children:["video"===D.type&&B&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-8",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow",children:[(0,a.jsx)("video",{ref:H,autoPlay:!0,playsInline:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden",children:(0,a.jsx)("video",{ref:q,autoPlay:!0,playsInline:!0,muted:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-3xl font-bold",children:D.participant?.avatar||D.participant?.name.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc text-3xl font-semibold mb-2",children:D.participant?.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-green-400 text-sm",children:"End-to-End Encrypted"})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-400 mb-8 text-lg",children:"video"===D.type?"Video calling...":"Voice calling..."}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-center space-x-6",children:[(0,a.jsx)("button",{onClick:()=>E(e=>({...e,isMuted:!e.isMuted})),className:`jsx-7110b046d83e55cc w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${D.isMuted?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"}`,children:(0,a.jsx)("i",{className:`jsx-7110b046d83e55cc fas ${D.isMuted?"fa-microphone-slash":"fa-microphone"} text-xl`})}),"video"===D.type&&(0,a.jsx)("button",{onClick:()=>E(e=>({...e,isVideoOff:!e.isVideoOff})),className:`jsx-7110b046d83e55cc w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${D.isVideoOff?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"}`,children:(0,a.jsx)("i",{className:`jsx-7110b046d83e55cc fas ${D.isVideoOff?"fa-video-slash":"fa-video"} text-xl`})}),(0,a.jsx)("button",{onClick:()=>{E({isActive:!1,type:"voice"}),R(!1)},className:"jsx-7110b046d83e55cc w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone-slash text-xl"})})]})]})})})]}),(0,a.jsx)(w,{isOpen:M,onClose:()=>F(!1),currentUser:s,bankBalance:z,connectedBank:I}),g&&(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsx)(o.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-semibold text-white gold-gradient",children:"Send Payment"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mt-1",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2 text-sm"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:"Secured by Plaid"})]})]}),(0,a.jsx)("button",{onClick:()=>{f(!1),j("")},className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times text-xl"})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-gray-300",children:I})]}),(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm text-green-400 font-semibold",children:["$",z.toLocaleString()]})]})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mb-6",children:[(0,a.jsx)("label",{htmlFor:"amount",className:"jsx-7110b046d83e55cc block text-sm font-medium text-gray-300 mb-2",children:"Amount (USD)"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-yellow-400 text-lg font-semibold",children:"$"})}),(0,a.jsx)("input",{type:"number",name:"amount",id:"amount",value:p,onChange:e=>j(e.target.value),placeholder:"0.00",step:"0.01",min:"0.01",className:"jsx-7110b046d83e55cc w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mt-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:["Sending to: ",r?.participant.name]}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-bolt mr-1"}),"Instant Transfer"]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{f(!1),j("")},className:"jsx-7110b046d83e55cc px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{if(!p||!r||!s)return;let e=[...d,{id:Date.now().toString(),senderId:s.id,text:`Payment of $${p}`,timestamp:new Date,status:"sent",type:"payment",amount:parseFloat(p),currency:"USD"}];x(e),f(!1),j("");let t=m.map(s=>s.id===r.id?{...s,lastMessage:`Payment of $${p}`,timestamp:new Date,unreadCount:0,messages:e}:s);h(t),c(t.find(e=>e.id===r.id)||null)},disabled:!p||0>=parseFloat(p),className:`jsx-7110b046d83e55cc px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ${p&&parseFloat(p)>0?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send $",p||"0.00"]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"256-bit AES encryption • FDIC insured • Instant settlement"})]})})]})})})]})}Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),k[0],k[1],Date.now()},6189:(e,s,t)=>{"use strict";var a=t(5773);t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6990:(e,s,t)=>{Promise.resolve().then(t.bind(t,4384))},8973:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,938,658,1,279],()=>t(3240));module.exports=a})();