exports.id=279,exports.ids=[279],exports.modules={440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var s=n(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},1135:()=>{},2903:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},4431:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>o});var s=n(7413),r=n(1980),i=n.n(r);n(1135);let o={title:"BoGuani - Messenger of Value",description:"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",keywords:"messaging, payments, secure chat, money transfer, encrypted messaging, BoGuani",authors:[{name:"BoGuani Team"}],openGraph:{title:"BoGuani - Messenger of Value",description:"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",type:"website"}};function l({children:e}){return(0,s.jsxs)("html",{lang:"en",className:i().variable,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",integrity:"sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==",crossOrigin:"anonymous",referrerPolicy:"no-referrer"}),(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),(0,s.jsx)("meta",{name:"theme-color",content:"#2D1B4E"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,s.jsx)("body",{className:`${i().variable} antialiased`,children:e})]})}},5839:()=>{},5913:(e,t,n)=>{"use strict";n(6397);var s=n(3210),r=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),i="undefined"!=typeof process&&process.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,n=t.name,s=void 0===n?"stylesheet":n,r=t.optimizeForSpeed,l=void 0===r?i:r;u(o(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",u("boolean"==typeof l,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=l,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;u(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){return u(o(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},n.replaceRule=function(e,t){this._optimizeForSpeed;var n=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(s){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}return e},n.deleteRule=function(e){this._serverSheet.deleteRule(e)},n.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},n.cssRules=function(){return this._serverSheet.cssRules},n.makeStyleTag=function(e,t,n){t&&u(o(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var r=document.head||document.getElementsByTagName("head")[0];return n?r.insertBefore(s,n):r.appendChild(s),s},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var n=0;n<t.length;n++){var s=t[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,t),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var a=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},c={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),s=e+n;return c[s]||(c[s]="jsx-"+a(e+"-"+n)),c[s]}function d(e,t){var n=e+(t=t.replace(/\/style/gi,"\\/style"));return c[n]||(c[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[n]}var m=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,s=void 0===n?null:n,r=t.optimizeForSpeed,i=void 0!==r&&r;this._sheet=s||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),s&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var n=this.getIdAndRules(e),s=n.styleId,r=n.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var i=r.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=i,this._instancesCounts[s]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var s=this._fromServer&&this._fromServer[n];s?(s.parentNode.removeChild(s),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],s=e[1];return r.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,s=e.id;if(n){var r=h(s,n);return{styleId:r,rules:Array.isArray(t)?t.map(function(e){return d(r,e)}):[d(r,t)]}}return{styleId:h(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),f=s.createContext(null);f.displayName="StyleSheetContext";r.default.useInsertionEffect||r.default.useLayoutEffect;var p=void 0;function v(e){var t=p||s.useContext(f);return t&&t.add(e),null}v.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=v},6180:(e,t,n)=>{"use strict";e.exports=n(5913).style},6397:()=>{},6615:()=>{},8436:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))}};