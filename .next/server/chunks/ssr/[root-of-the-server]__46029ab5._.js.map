{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/components/SettingsModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface SettingsModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  currentUser: any;\n  bankBalance: number;\n  connectedBank: string;\n}\n\ntype SettingsTab = 'profile' | 'chat' | 'banking' | 'security' | 'notifications' | 'privacy' | 'appearance' | 'advanced';\n\nexport default function SettingsModal({ isOpen, onClose, currentUser, bankBalance, connectedBank }: SettingsModalProps) {\n  const [activeTab, setActiveTab] = useState<SettingsTab>('profile');\n  const [profileData, setProfileData] = useState({\n    name: currentUser?.name || '',\n    username: currentUser?.username || '',\n    phone: currentUser?.phone || '',\n    email: '<EMAIL>',\n    status: currentUser?.status || '',\n    bio: 'Messenger of Value',\n    avatar: currentUser?.avatar || ''\n  });\n\n  const [chatSettings, setChatSettings] = useState({\n    enterToSend: true,\n    readReceipts: true,\n    typingIndicators: true,\n    lastSeen: true,\n    mediaAutoDownload: true,\n    soundEnabled: true,\n    vibrationEnabled: true,\n    messagePreview: true,\n    groupNotifications: true,\n    archiveChats: false\n  });\n\n  const [bankingSettings, setBankingSettings] = useState({\n    instantTransfers: true,\n    paymentNotifications: true,\n    transactionLimits: {\n      daily: 5000,\n      monthly: 25000\n    },\n    autoSave: false,\n    savingsGoal: 1000,\n    requirePinForPayments: true,\n    biometricAuth: true\n  });\n\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorAuth: true,\n    biometricLogin: true,\n    sessionTimeout: 30,\n    deviceVerification: true,\n    encryptionLevel: 'military',\n    backupMessages: true,\n    screenLock: true,\n    incognitoMode: false\n  });\n\n  const [notificationSettings, setNotificationSettings] = useState({\n    pushNotifications: true,\n    emailNotifications: false,\n    smsNotifications: false,\n    soundAlerts: true,\n    vibration: true,\n    showPreviews: true,\n    quietHours: {\n      enabled: false,\n      start: '22:00',\n      end: '07:00'\n    },\n    priorityContacts: []\n  });\n\n  const [privacySettings, setPrivacySettings] = useState({\n    profileVisibility: 'contacts',\n    lastSeenVisibility: 'contacts',\n    statusVisibility: 'everyone',\n    readReceiptSharing: true,\n    blockUnknownNumbers: false,\n    dataCollection: false,\n    analyticsSharing: false,\n    locationSharing: false\n  });\n\n  const [appearanceSettings, setAppearanceSettings] = useState({\n    theme: 'dark',\n    accentColor: 'gold',\n    fontSize: 'medium',\n    chatWallpaper: 'default',\n    bubbleStyle: 'transparent',\n    animationsEnabled: true,\n    compactMode: false,\n    highContrast: false\n  });\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: 'fas fa-user' },\n    { id: 'chat', name: 'Chat', icon: 'fas fa-comments' },\n    { id: 'banking', name: 'Banking', icon: 'fas fa-university' },\n    { id: 'security', name: 'Security', icon: 'fas fa-shield-alt' },\n    { id: 'notifications', name: 'Notifications', icon: 'fas fa-bell' },\n    { id: 'privacy', name: 'Privacy', icon: 'fas fa-lock' },\n    { id: 'appearance', name: 'Appearance', icon: 'fas fa-palette' },\n    { id: 'advanced', name: 'Advanced', icon: 'fas fa-cogs' }\n  ];\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div \n        className=\"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        onClick={onClose}\n      >\n        <motion.div \n          className=\"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] border border-gray-600/50 professional-shadow overflow-hidden\"\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          onClick={(e) => e.stopPropagation()}\n        >\n          <div className=\"flex h-full\">\n            {/* Sidebar */}\n            <div className=\"w-64 bg-gradient-to-b from-gray-900/90 to-gray-800/90 border-r border-gray-600/30 p-6\">\n              <div className=\"flex items-center justify-between mb-8\">\n                <h2 className=\"text-2xl font-bold text-white gold-gradient\">Settings</h2>\n                <button \n                  onClick={onClose}\n                  className=\"text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-700/50\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n              \n              <nav className=\"space-y-2\">\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id as SettingsTab)}\n                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${\n                      activeTab === tab.id\n                        ? 'bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 text-yellow-400'\n                        : 'text-gray-400 hover:text-white hover:bg-gray-700/50'\n                    }`}\n                  >\n                    <i className={`${tab.icon} text-lg`}></i>\n                    <span className=\"font-medium\">{tab.name}</span>\n                  </button>\n                ))}\n              </nav>\n            </div>\n\n            {/* Main Content */}\n            <div className=\"flex-1 overflow-y-auto\">\n              <div className=\"p-8\">\n                {/* Profile Settings */}\n                {activeTab === 'profile' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Profile Settings</h3>\n                      <p className=\"text-gray-400\">Manage your personal information and profile appearance</p>\n                    </div>\n\n                    {/* Profile Picture */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-4\">Profile Picture</h4>\n                      <div className=\"flex items-center space-x-6\">\n                        <div className=\"w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-gray-900 font-bold text-2xl professional-shadow\">\n                          {profileData.name.split(' ').map((n: string) => n[0]).join('')}\n                        </div>\n                        <div className=\"space-y-3\">\n                          <button className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-6 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300\">\n                            Upload Photo\n                          </button>\n                          <button className=\"block text-gray-400 hover:text-white transition-colors text-sm\">\n                            Remove Photo\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Personal Information */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Personal Information</h4>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Full Name</label>\n                          <input\n                            type=\"text\"\n                            value={profileData.name}\n                            onChange={(e) => setProfileData({...profileData, name: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Username</label>\n                          <input\n                            type=\"text\"\n                            value={profileData.username}\n                            onChange={(e) => setProfileData({...profileData, username: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Phone Number</label>\n                          <input\n                            type=\"tel\"\n                            value={profileData.phone}\n                            onChange={(e) => setProfileData({...profileData, phone: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Email Address</label>\n                          <input\n                            type=\"email\"\n                            value={profileData.email}\n                            onChange={(e) => setProfileData({...profileData, email: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Status & Bio */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Status & Bio</h4>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Status Message</label>\n                          <input\n                            type=\"text\"\n                            value={profileData.status}\n                            onChange={(e) => setProfileData({...profileData, status: e.target.value})}\n                            placeholder=\"Available for chat\"\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Bio</label>\n                          <textarea\n                            value={profileData.bio}\n                            onChange={(e) => setProfileData({...profileData, bio: e.target.value})}\n                            placeholder=\"Tell others about yourself...\"\n                            rows={3}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none resize-none\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Save Button */}\n                    <div className=\"flex justify-end\">\n                      <button className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-8 py-3 rounded-xl font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow\">\n                        <i className=\"fas fa-save mr-2\"></i>\n                        Save Changes\n                      </button>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Chat Settings */}\n                {activeTab === 'chat' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Chat Settings</h3>\n                      <p className=\"text-gray-400\">Customize your messaging experience and preferences</p>\n                    </div>\n\n                    {/* Message Behavior */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Message Behavior</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Enter to Send</h5>\n                            <p className=\"text-gray-400 text-sm\">Press Enter to send messages (Shift+Enter for new line)</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, enterToSend: !chatSettings.enterToSend})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.enterToSend ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.enterToSend ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Read Receipts</h5>\n                            <p className=\"text-gray-400 text-sm\">Show when you've read messages</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, readReceipts: !chatSettings.readReceipts})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.readReceipts ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.readReceipts ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Typing Indicators</h5>\n                            <p className=\"text-gray-400 text-sm\">Show when you're typing to others</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, typingIndicators: !chatSettings.typingIndicators})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.typingIndicators ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.typingIndicators ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Last Seen</h5>\n                            <p className=\"text-gray-400 text-sm\">Show your last seen status to contacts</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, lastSeen: !chatSettings.lastSeen})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.lastSeen ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.lastSeen ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Media & Files */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Media & Files</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Auto-Download Media</h5>\n                            <p className=\"text-gray-400 text-sm\">Automatically download photos and videos</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, mediaAutoDownload: !chatSettings.mediaAutoDownload})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.mediaAutoDownload ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.mediaAutoDownload ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Notifications */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Chat Notifications</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Sound Notifications</h5>\n                            <p className=\"text-gray-400 text-sm\">Play sound for new messages</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, soundEnabled: !chatSettings.soundEnabled})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.soundEnabled ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.soundEnabled ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Vibration</h5>\n                            <p className=\"text-gray-400 text-sm\">Vibrate for new messages</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, vibrationEnabled: !chatSettings.vibrationEnabled})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.vibrationEnabled ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.vibrationEnabled ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Message Previews</h5>\n                            <p className=\"text-gray-400 text-sm\">Show message content in notifications</p>\n                          </div>\n                          <button\n                            onClick={() => setChatSettings({...chatSettings, messagePreview: !chatSettings.messagePreview})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              chatSettings.messagePreview ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              chatSettings.messagePreview ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Banking Settings */}\n                {activeTab === 'banking' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Banking & Payments</h3>\n                      <p className=\"text-gray-400\">Manage your financial settings and payment preferences</p>\n                    </div>\n\n                    {/* Connected Accounts */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Connected Bank Accounts</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/20\">\n                          <div className=\"flex items-center space-x-4\">\n                            <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center\">\n                              <i className=\"fas fa-university text-white\"></i>\n                            </div>\n                            <div>\n                              <h5 className=\"text-white font-medium\">{connectedBank}</h5>\n                              <p className=\"text-green-400 text-sm\">Primary Account • Verified</p>\n                              <p className=\"text-gray-400 text-sm\">Balance: ${bankBalance.toLocaleString()}</p>\n                            </div>\n                          </div>\n                          <div className=\"flex space-x-2\">\n                            <button className=\"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700/50\">\n                              <i className=\"fas fa-edit\"></i>\n                            </button>\n                            <button className=\"text-gray-400 hover:text-red-400 transition-colors p-2 rounded-lg hover:bg-gray-700/50\">\n                              <i className=\"fas fa-trash\"></i>\n                            </button>\n                          </div>\n                        </div>\n\n                        <button className=\"w-full p-4 border-2 border-dashed border-gray-600 rounded-xl text-gray-400 hover:text-white hover:border-yellow-400 transition-all duration-300\">\n                          <i className=\"fas fa-plus mr-2\"></i>\n                          Add New Bank Account\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Payment Settings */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Payment Settings</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Instant Transfers</h5>\n                            <p className=\"text-gray-400 text-sm\">Enable instant money transfers (small fee may apply)</p>\n                          </div>\n                          <button\n                            onClick={() => setBankingSettings({...bankingSettings, instantTransfers: !bankingSettings.instantTransfers})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              bankingSettings.instantTransfers ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              bankingSettings.instantTransfers ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Payment Notifications</h5>\n                            <p className=\"text-gray-400 text-sm\">Get notified for all payment activities</p>\n                          </div>\n                          <button\n                            onClick={() => setBankingSettings({...bankingSettings, paymentNotifications: !bankingSettings.paymentNotifications})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              bankingSettings.paymentNotifications ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              bankingSettings.paymentNotifications ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Require PIN for Payments</h5>\n                            <p className=\"text-gray-400 text-sm\">Require PIN verification for all payments</p>\n                          </div>\n                          <button\n                            onClick={() => setBankingSettings({...bankingSettings, requirePinForPayments: !bankingSettings.requirePinForPayments})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              bankingSettings.requirePinForPayments ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              bankingSettings.requirePinForPayments ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Biometric Authentication</h5>\n                            <p className=\"text-gray-400 text-sm\">Use fingerprint/face ID for payments</p>\n                          </div>\n                          <button\n                            onClick={() => setBankingSettings({...bankingSettings, biometricAuth: !bankingSettings.biometricAuth})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              bankingSettings.biometricAuth ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              bankingSettings.biometricAuth ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Transaction Limits */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Transaction Limits</h4>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Daily Limit</label>\n                          <div className=\"relative\">\n                            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400\">$</span>\n                            <input\n                              type=\"number\"\n                              value={bankingSettings.transactionLimits.daily}\n                              onChange={(e) => setBankingSettings({\n                                ...bankingSettings,\n                                transactionLimits: {...bankingSettings.transactionLimits, daily: parseInt(e.target.value)}\n                              })}\n                              className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                            />\n                          </div>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Monthly Limit</label>\n                          <div className=\"relative\">\n                            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400\">$</span>\n                            <input\n                              type=\"number\"\n                              value={bankingSettings.transactionLimits.monthly}\n                              onChange={(e) => setBankingSettings({\n                                ...bankingSettings,\n                                transactionLimits: {...bankingSettings.transactionLimits, monthly: parseInt(e.target.value)}\n                              })}\n                              className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                            />\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Savings */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Savings & Goals</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Auto-Save</h5>\n                            <p className=\"text-gray-400 text-sm\">Automatically save spare change from transactions</p>\n                          </div>\n                          <button\n                            onClick={() => setBankingSettings({...bankingSettings, autoSave: !bankingSettings.autoSave})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              bankingSettings.autoSave ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              bankingSettings.autoSave ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Savings Goal</label>\n                          <div className=\"relative\">\n                            <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400\">$</span>\n                            <input\n                              type=\"number\"\n                              value={bankingSettings.savingsGoal}\n                              onChange={(e) => setBankingSettings({...bankingSettings, savingsGoal: parseInt(e.target.value)})}\n                              className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n                              placeholder=\"1000\"\n                            />\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Security Settings */}\n                {activeTab === 'security' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Security & Privacy</h3>\n                      <p className=\"text-gray-400\">Protect your account with advanced security features</p>\n                    </div>\n\n                    {/* Authentication */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Authentication</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Two-Factor Authentication</h5>\n                            <p className=\"text-gray-400 text-sm\">Add an extra layer of security to your account</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, twoFactorAuth: !securitySettings.twoFactorAuth})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.twoFactorAuth ? 'bg-green-500' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.twoFactorAuth ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Biometric Login</h5>\n                            <p className=\"text-gray-400 text-sm\">Use fingerprint or face ID to log in</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, biometricLogin: !securitySettings.biometricLogin})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.biometricLogin ? 'bg-green-500' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.biometricLogin ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Device Verification</h5>\n                            <p className=\"text-gray-400 text-sm\">Verify new devices before allowing access</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, deviceVerification: !securitySettings.deviceVerification})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.deviceVerification ? 'bg-green-500' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.deviceVerification ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Session Timeout (minutes)</label>\n                          <select\n                            value={securitySettings.sessionTimeout}\n                            onChange={(e) => setSecuritySettings({...securitySettings, sessionTimeout: parseInt(e.target.value)})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value={15}>15 minutes</option>\n                            <option value={30}>30 minutes</option>\n                            <option value={60}>1 hour</option>\n                            <option value={120}>2 hours</option>\n                            <option value={0}>Never</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Encryption */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Encryption & Data</h4>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Encryption Level</label>\n                          <select\n                            value={securitySettings.encryptionLevel}\n                            onChange={(e) => setSecuritySettings({...securitySettings, encryptionLevel: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"standard\">Standard (128-bit)</option>\n                            <option value=\"enhanced\">Enhanced (256-bit)</option>\n                            <option value=\"military\">Military Grade (AES-256)</option>\n                          </select>\n                          <p className=\"text-green-400 text-sm mt-2 flex items-center\">\n                            <i className=\"fas fa-shield-alt mr-2\"></i>\n                            Current: Military Grade AES-256 Encryption\n                          </p>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Backup Messages</h5>\n                            <p className=\"text-gray-400 text-sm\">Securely backup your messages to the cloud</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, backupMessages: !securitySettings.backupMessages})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.backupMessages ? 'bg-green-500' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.backupMessages ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Screen Lock</h5>\n                            <p className=\"text-gray-400 text-sm\">Lock app when switching to other apps</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, screenLock: !securitySettings.screenLock})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.screenLock ? 'bg-green-500' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.screenLock ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Incognito Mode</h5>\n                            <p className=\"text-gray-400 text-sm\">Hide message previews and disable screenshots</p>\n                          </div>\n                          <button\n                            onClick={() => setSecuritySettings({...securitySettings, incognitoMode: !securitySettings.incognitoMode})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              securitySettings.incognitoMode ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              securitySettings.incognitoMode ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Security Actions */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Security Actions</h4>\n                      <div className=\"space-y-3\">\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-key text-blue-400 mr-3\"></i>\n                            <span className=\"text-white font-medium\">Change Password</span>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-mobile-alt text-green-400 mr-3\"></i>\n                            <span className=\"text-white font-medium\">Manage Devices</span>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-history text-yellow-400 mr-3\"></i>\n                            <span className=\"text-white font-medium\">Login History</span>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-sign-out-alt text-red-400 mr-3\"></i>\n                            <span className=\"text-white font-medium\">Sign Out All Devices</span>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Notifications Settings */}\n                {activeTab === 'notifications' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Notifications</h3>\n                      <p className=\"text-gray-400\">Control how and when you receive notifications</p>\n                    </div>\n\n                    {/* General Notifications */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">General</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Push Notifications</h5>\n                            <p className=\"text-gray-400 text-sm\">Receive notifications on this device</p>\n                          </div>\n                          <button\n                            onClick={() => setNotificationSettings({...notificationSettings, pushNotifications: !notificationSettings.pushNotifications})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              notificationSettings.pushNotifications ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              notificationSettings.pushNotifications ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Email Notifications</h5>\n                            <p className=\"text-gray-400 text-sm\">Receive important updates via email</p>\n                          </div>\n                          <button\n                            onClick={() => setNotificationSettings({...notificationSettings, emailNotifications: !notificationSettings.emailNotifications})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              notificationSettings.emailNotifications ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              notificationSettings.emailNotifications ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Sound Alerts</h5>\n                            <p className=\"text-gray-400 text-sm\">Play sound for notifications</p>\n                          </div>\n                          <button\n                            onClick={() => setNotificationSettings({...notificationSettings, soundAlerts: !notificationSettings.soundAlerts})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              notificationSettings.soundAlerts ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              notificationSettings.soundAlerts ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Show Previews</h5>\n                            <p className=\"text-gray-400 text-sm\">Show message content in notifications</p>\n                          </div>\n                          <button\n                            onClick={() => setNotificationSettings({...notificationSettings, showPreviews: !notificationSettings.showPreviews})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              notificationSettings.showPreviews ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              notificationSettings.showPreviews ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Quiet Hours */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Quiet Hours</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Enable Quiet Hours</h5>\n                            <p className=\"text-gray-400 text-sm\">Mute notifications during specified hours</p>\n                          </div>\n                          <button\n                            onClick={() => setNotificationSettings({\n                              ...notificationSettings,\n                              quietHours: {...notificationSettings.quietHours, enabled: !notificationSettings.quietHours.enabled}\n                            })}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              notificationSettings.quietHours.enabled ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              notificationSettings.quietHours.enabled ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        {notificationSettings.quietHours.enabled && (\n                          <div className=\"grid grid-cols-2 gap-4\">\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-300 mb-2\">Start Time</label>\n                              <input\n                                type=\"time\"\n                                value={notificationSettings.quietHours.start}\n                                onChange={(e) => setNotificationSettings({\n                                  ...notificationSettings,\n                                  quietHours: {...notificationSettings.quietHours, start: e.target.value}\n                                })}\n                                className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                              />\n                            </div>\n                            <div>\n                              <label className=\"block text-sm font-medium text-gray-300 mb-2\">End Time</label>\n                              <input\n                                type=\"time\"\n                                value={notificationSettings.quietHours.end}\n                                onChange={(e) => setNotificationSettings({\n                                  ...notificationSettings,\n                                  quietHours: {...notificationSettings.quietHours, end: e.target.value}\n                                })}\n                                className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                              />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Privacy Settings */}\n                {activeTab === 'privacy' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Privacy</h3>\n                      <p className=\"text-gray-400\">Control who can see your information and activity</p>\n                    </div>\n\n                    {/* Visibility Settings */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Visibility</h4>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Profile Visibility</label>\n                          <select\n                            value={privacySettings.profileVisibility}\n                            onChange={(e) => setPrivacySettings({...privacySettings, profileVisibility: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"everyone\">Everyone</option>\n                            <option value=\"contacts\">My Contacts</option>\n                            <option value=\"nobody\">Nobody</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Last Seen</label>\n                          <select\n                            value={privacySettings.lastSeenVisibility}\n                            onChange={(e) => setPrivacySettings({...privacySettings, lastSeenVisibility: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"everyone\">Everyone</option>\n                            <option value=\"contacts\">My Contacts</option>\n                            <option value=\"nobody\">Nobody</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Status Visibility</label>\n                          <select\n                            value={privacySettings.statusVisibility}\n                            onChange={(e) => setPrivacySettings({...privacySettings, statusVisibility: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"everyone\">Everyone</option>\n                            <option value=\"contacts\">My Contacts</option>\n                            <option value=\"nobody\">Nobody</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Data & Analytics */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Data & Analytics</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Data Collection</h5>\n                            <p className=\"text-gray-400 text-sm\">Allow collection of usage data for improvements</p>\n                          </div>\n                          <button\n                            onClick={() => setPrivacySettings({...privacySettings, dataCollection: !privacySettings.dataCollection})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              privacySettings.dataCollection ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              privacySettings.dataCollection ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Analytics Sharing</h5>\n                            <p className=\"text-gray-400 text-sm\">Share anonymous analytics data</p>\n                          </div>\n                          <button\n                            onClick={() => setPrivacySettings({...privacySettings, analyticsSharing: !privacySettings.analyticsSharing})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              privacySettings.analyticsSharing ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              privacySettings.analyticsSharing ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Location Sharing</h5>\n                            <p className=\"text-gray-400 text-sm\">Allow location sharing in messages</p>\n                          </div>\n                          <button\n                            onClick={() => setPrivacySettings({...privacySettings, locationSharing: !privacySettings.locationSharing})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              privacySettings.locationSharing ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              privacySettings.locationSharing ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Appearance Settings */}\n                {activeTab === 'appearance' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Appearance</h3>\n                      <p className=\"text-gray-400\">Customize the look and feel of BoGuani</p>\n                    </div>\n\n                    {/* Theme Settings */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Theme</h4>\n                      <div className=\"grid grid-cols-3 gap-4\">\n                        <button\n                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'dark'})}\n                          className={`p-4 rounded-xl border-2 transition-all ${\n                            appearanceSettings.theme === 'dark'\n                              ? 'border-yellow-400 bg-yellow-400/10'\n                              : 'border-gray-600 hover:border-gray-500'\n                          }`}\n                        >\n                          <div className=\"w-full h-16 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg mb-2\"></div>\n                          <p className=\"text-white text-sm font-medium\">Dark</p>\n                        </button>\n\n                        <button\n                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'light'})}\n                          className={`p-4 rounded-xl border-2 transition-all ${\n                            appearanceSettings.theme === 'light'\n                              ? 'border-yellow-400 bg-yellow-400/10'\n                              : 'border-gray-600 hover:border-gray-500'\n                          }`}\n                        >\n                          <div className=\"w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-2\"></div>\n                          <p className=\"text-white text-sm font-medium\">Light</p>\n                        </button>\n\n                        <button\n                          onClick={() => setAppearanceSettings({...appearanceSettings, theme: 'auto'})}\n                          className={`p-4 rounded-xl border-2 transition-all ${\n                            appearanceSettings.theme === 'auto'\n                              ? 'border-yellow-400 bg-yellow-400/10'\n                              : 'border-gray-600 hover:border-gray-500'\n                          }`}\n                        >\n                          <div className=\"w-full h-16 bg-gradient-to-r from-gray-900 via-gray-500 to-gray-100 rounded-lg mb-2\"></div>\n                          <p className=\"text-white text-sm font-medium\">Auto</p>\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Customization */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Customization</h4>\n                      <div className=\"space-y-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Accent Color</label>\n                          <div className=\"grid grid-cols-6 gap-3\">\n                            {['gold', 'blue', 'green', 'purple', 'red', 'orange'].map((color) => (\n                              <button\n                                key={color}\n                                onClick={() => setAppearanceSettings({...appearanceSettings, accentColor: color})}\n                                className={`w-12 h-12 rounded-full border-2 transition-all ${\n                                  appearanceSettings.accentColor === color ? 'border-white scale-110' : 'border-gray-600'\n                                } ${\n                                  color === 'gold' ? 'bg-yellow-400' :\n                                  color === 'blue' ? 'bg-blue-500' :\n                                  color === 'green' ? 'bg-green-500' :\n                                  color === 'purple' ? 'bg-purple-500' :\n                                  color === 'red' ? 'bg-red-500' : 'bg-orange-500'\n                                }`}\n                              />\n                            ))}\n                          </div>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Font Size</label>\n                          <select\n                            value={appearanceSettings.fontSize}\n                            onChange={(e) => setAppearanceSettings({...appearanceSettings, fontSize: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"small\">Small</option>\n                            <option value=\"medium\">Medium</option>\n                            <option value=\"large\">Large</option>\n                            <option value=\"extra-large\">Extra Large</option>\n                          </select>\n                        </div>\n\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-300 mb-2\">Message Bubble Style</label>\n                          <select\n                            value={appearanceSettings.bubbleStyle}\n                            onChange={(e) => setAppearanceSettings({...appearanceSettings, bubbleStyle: e.target.value})}\n                            className=\"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none\"\n                          >\n                            <option value=\"transparent\">Transparent</option>\n                            <option value=\"solid\">Solid</option>\n                            <option value=\"gradient\">Gradient</option>\n                          </select>\n                        </div>\n\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h5 className=\"text-white font-medium\">Animations</h5>\n                            <p className=\"text-gray-400 text-sm\">Enable smooth animations and transitions</p>\n                          </div>\n                          <button\n                            onClick={() => setAppearanceSettings({...appearanceSettings, animationsEnabled: !appearanceSettings.animationsEnabled})}\n                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                              appearanceSettings.animationsEnabled ? 'bg-yellow-400' : 'bg-gray-600'\n                            }`}\n                          >\n                            <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                              appearanceSettings.animationsEnabled ? 'translate-x-6' : 'translate-x-1'\n                            }`} />\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Advanced Settings */}\n                {activeTab === 'advanced' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"space-y-8\"\n                  >\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-white mb-2\">Advanced</h3>\n                      <p className=\"text-gray-400\">Advanced settings and developer options</p>\n                    </div>\n\n                    {/* Data Management */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Data Management</h4>\n                      <div className=\"space-y-3\">\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-download text-blue-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Export Data</span>\n                              <span className=\"text-gray-400 text-sm\">Download your messages and data</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-upload text-green-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Import Data</span>\n                              <span className=\"text-gray-400 text-sm\">Import messages from other apps</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-broom text-yellow-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Clear Cache</span>\n                              <span className=\"text-gray-400 text-sm\">Free up storage space</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Developer Options */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Developer Options</h4>\n                      <div className=\"space-y-3\">\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 rounded-xl border border-purple-400/30 hover:border-purple-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-code text-purple-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">API Settings</span>\n                              <span className=\"text-gray-400 text-sm\">Configure API endpoints and keys</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-600/20 to-indigo-500/20 rounded-xl border border-indigo-400/30 hover:border-indigo-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-bug text-indigo-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Debug Logs</span>\n                              <span className=\"text-gray-400 text-sm\">View and export debug information</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-teal-600/20 to-teal-500/20 rounded-xl border border-teal-400/30 hover:border-teal-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-flask text-teal-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Beta Features</span>\n                              <span className=\"text-gray-400 text-sm\">Enable experimental features</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Account Actions */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">Account Actions</h4>\n                      <div className=\"space-y-3\">\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-orange-600/20 to-orange-500/20 rounded-xl border border-orange-400/30 hover:border-orange-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-user-times text-orange-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Deactivate Account</span>\n                              <span className=\"text-gray-400 text-sm\">Temporarily disable your account</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n\n                        <button className=\"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors\">\n                          <div className=\"flex items-center\">\n                            <i className=\"fas fa-trash text-red-400 mr-3\"></i>\n                            <div className=\"text-left\">\n                              <span className=\"text-white font-medium block\">Delete Account</span>\n                              <span className=\"text-gray-400 text-sm\">Permanently delete your account and data</span>\n                            </div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* App Information */}\n                    <div className=\"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30\">\n                      <h4 className=\"text-lg font-semibold text-white mb-6\">App Information</h4>\n                      <div className=\"space-y-4\">\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-400\">Version</span>\n                          <span className=\"text-white font-medium\">1.0.0</span>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-400\">Build</span>\n                          <span className=\"text-white font-medium\">2024.01.15</span>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-400\">Platform</span>\n                          <span className=\"text-white font-medium\">Web</span>\n                        </div>\n                        <div className=\"flex justify-between items-center\">\n                          <span className=\"text-gray-400\">Encryption</span>\n                          <span className=\"text-green-400 font-medium flex items-center\">\n                            <i className=\"fas fa-shield-alt mr-2\"></i>\n                            AES-256 Active\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAee,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAsB;IACpH,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM,aAAa,QAAQ;QAC3B,UAAU,aAAa,YAAY;QACnC,OAAO,aAAa,SAAS;QAC7B,OAAO;QACP,QAAQ,aAAa,UAAU;QAC/B,KAAK;QACL,QAAQ,aAAa,UAAU;IACjC;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,aAAa;QACb,cAAc;QACd,kBAAkB;QAClB,UAAU;QACV,mBAAmB;QACnB,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,cAAc;IAChB;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,kBAAkB;QAClB,sBAAsB;QACtB,mBAAmB;YACjB,OAAO;YACP,SAAS;QACX;QACA,UAAU;QACV,aAAa;QACb,uBAAuB;QACvB,eAAe;IACjB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/D,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,aAAa;QACb,WAAW;QACX,cAAc;QACd,YAAY;YACV,SAAS;YACT,OAAO;YACP,KAAK;QACP;QACA,kBAAkB,EAAE;IACtB;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,mBAAmB;QACnB,oBAAoB;QACpB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;IACnB;IAEA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,OAAO;QACP,aAAa;QACb,UAAU;QACV,eAAe;QACf,aAAa;QACb,mBAAmB;QACnB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAc;QACtD;YAAE,IAAI;YAAQ,MAAM;YAAQ,MAAM;QAAkB;QACpD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAoB;QAC5D;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAoB;QAC9D;YAAE,IAAI;YAAiB,MAAM;YAAiB,MAAM;QAAc;QAClE;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAc;QACtD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAiB;QAC/D;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAc;KACzD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,SAAS,CAAC,IAAM,EAAE,eAAe;0BAEjC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8C;;;;;;sDAC5D,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,IAAI,EAAE,GAChB,qGACA,uDACJ;;8DAEF,8OAAC;oDAAE,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC;;;;;;8DACnC,8OAAC;oDAAK,WAAU;8DAAe,IAAI,IAAI;;;;;;;2CATlC,IAAI,EAAE;;;;;;;;;;;;;;;;sCAgBnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCAEZ,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;0EAE7D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAO,WAAU;kFAAwK;;;;;;kFAG1L,8OAAC;wEAAO,WAAU;kFAAiE;;;;;;;;;;;;;;;;;;;;;;;;0DAQzF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,MAAK;wEACL,OAAO,YAAY,IAAI;wEACvB,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACrE,WAAU;;;;;;;;;;;;0EAGd,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,MAAK;wEACL,OAAO,YAAY,QAAQ;wEAC3B,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACzE,WAAU;;;;;;;;;;;;0EAGd,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,MAAK;wEACL,OAAO,YAAY,KAAK;wEACxB,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACtE,WAAU;;;;;;;;;;;;0EAGd,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,MAAK;wEACL,OAAO,YAAY,KAAK;wEACxB,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACtE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,MAAK;wEACL,OAAO,YAAY,MAAM;wEACzB,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACvE,aAAY;wEACZ,WAAU;;;;;;;;;;;;0EAGd,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,YAAY,GAAG;wEACtB,UAAU,CAAC,IAAM,eAAe;gFAAC,GAAG,WAAW;gFAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACpE,aAAY;wEACZ,MAAM;wEACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAOlB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;4DAAE,WAAU;;;;;;wDAAuB;;;;;;;;;;;;;;;;;;oCAQ3C,cAAc,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,aAAa,CAAC,aAAa,WAAW;4EAAA;wEACvF,WAAW,CAAC,0EAA0E,EACpF,aAAa,WAAW,GAAG,kBAAkB,eAC7C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,WAAW,GAAG,kBAAkB,iBAC7C;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,cAAc,CAAC,aAAa,YAAY;4EAAA;wEACzF,WAAW,CAAC,0EAA0E,EACpF,aAAa,YAAY,GAAG,kBAAkB,eAC9C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,YAAY,GAAG,kBAAkB,iBAC9C;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,kBAAkB,CAAC,aAAa,gBAAgB;4EAAA;wEACjG,WAAW,CAAC,0EAA0E,EACpF,aAAa,gBAAgB,GAAG,kBAAkB,eAClD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,gBAAgB,GAAG,kBAAkB,iBAClD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,UAAU,CAAC,aAAa,QAAQ;4EAAA;wEACjF,WAAW,CAAC,0EAA0E,EACpF,aAAa,QAAQ,GAAG,kBAAkB,eAC1C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,QAAQ,GAAG,kBAAkB,iBAC1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAyB;;;;;;sFACvC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,8OAAC;oEACC,SAAS,IAAM,gBAAgB;4EAAC,GAAG,YAAY;4EAAE,mBAAmB,CAAC,aAAa,iBAAiB;wEAAA;oEACnG,WAAW,CAAC,0EAA0E,EACpF,aAAa,iBAAiB,GAAG,kBAAkB,eACnD;8EAEF,cAAA,8OAAC;wEAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,iBAAiB,GAAG,kBAAkB,iBACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,cAAc,CAAC,aAAa,YAAY;4EAAA;wEACzF,WAAW,CAAC,0EAA0E,EACpF,aAAa,YAAY,GAAG,kBAAkB,eAC9C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,YAAY,GAAG,kBAAkB,iBAC9C;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,kBAAkB,CAAC,aAAa,gBAAgB;4EAAA;wEACjG,WAAW,CAAC,0EAA0E,EACpF,aAAa,gBAAgB,GAAG,kBAAkB,eAClD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,gBAAgB,GAAG,kBAAkB,iBAClD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,gBAAgB;gFAAC,GAAG,YAAY;gFAAE,gBAAgB,CAAC,aAAa,cAAc;4EAAA;wEAC7F,WAAW,CAAC,0EAA0E,EACpF,aAAa,cAAc,GAAG,kBAAkB,eAChD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,aAAa,cAAc,GAAG,kBAAkB,iBAChD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASb,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FACb,cAAA,8OAAC;oFAAE,WAAU;;;;;;;;;;;0FAEf,8OAAC;;kGACC,8OAAC;wFAAG,WAAU;kGAA0B;;;;;;kGACxC,8OAAC;wFAAE,WAAU;kGAAyB;;;;;;kGACtC,8OAAC;wFAAE,WAAU;;4FAAwB;4FAAW,YAAY,cAAc;;;;;;;;;;;;;;;;;;;kFAG9E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC;oFAAE,WAAU;;;;;;;;;;;0FAEf,8OAAC;gFAAO,WAAU;0FAChB,cAAA,8OAAC;oFAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKnB,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAE,WAAU;;;;;;oEAAuB;;;;;;;;;;;;;;;;;;;0DAO1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,kBAAkB,CAAC,gBAAgB,gBAAgB;4EAAA;wEAC1G,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,gBAAgB,GAAG,kBAAkB,eACrD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,gBAAgB,GAAG,kBAAkB,iBACrD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,sBAAsB,CAAC,gBAAgB,oBAAoB;4EAAA;wEAClH,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,oBAAoB,GAAG,kBAAkB,eACzD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,oBAAoB,GAAG,kBAAkB,iBACzD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,uBAAuB,CAAC,gBAAgB,qBAAqB;4EAAA;wEACpH,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,qBAAqB,GAAG,kBAAkB,eAC1D;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,qBAAqB,GAAG,kBAAkB,iBAC1D;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,eAAe,CAAC,gBAAgB,aAAa;4EAAA;wEACpG,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,aAAa,GAAG,kBAAkB,eAClD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,aAAa,GAAG,kBAAkB,iBAClD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAqE;;;;;;0FACrF,8OAAC;gFACC,MAAK;gFACL,OAAO,gBAAgB,iBAAiB,CAAC,KAAK;gFAC9C,UAAU,CAAC,IAAM,mBAAmB;wFAClC,GAAG,eAAe;wFAClB,mBAAmB;4FAAC,GAAG,gBAAgB,iBAAiB;4FAAE,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;wFAAC;oFAC3F;gFACA,WAAU;;;;;;;;;;;;;;;;;;0EAIhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAqE;;;;;;0FACrF,8OAAC;gFACC,MAAK;gFACL,OAAO,gBAAgB,iBAAiB,CAAC,OAAO;gFAChD,UAAU,CAAC,IAAM,mBAAmB;wFAClC,GAAG,eAAe;wFAClB,mBAAmB;4FAAC,GAAG,gBAAgB,iBAAiB;4FAAE,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;wFAAC;oFAC7F;gFACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAQpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,UAAU,CAAC,gBAAgB,QAAQ;4EAAA;wEAC1F,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,QAAQ,GAAG,kBAAkB,eAC7C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,QAAQ,GAAG,kBAAkB,iBAC7C;;;;;;;;;;;;;;;;;0EAIN,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAqE;;;;;;0FACrF,8OAAC;gFACC,MAAK;gFACL,OAAO,gBAAgB,WAAW;gFAClC,UAAU,CAAC,IAAM,mBAAmB;wFAAC,GAAG,eAAe;wFAAE,aAAa,SAAS,EAAE,MAAM,CAAC,KAAK;oFAAC;gFAC9F,WAAU;gFACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAUzB,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,eAAe,CAAC,iBAAiB,aAAa;4EAAA;wEACvG,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,aAAa,GAAG,iBAAiB,eAClD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,aAAa,GAAG,kBAAkB,iBACnD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,gBAAgB,CAAC,iBAAiB,cAAc;4EAAA;wEACzG,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,cAAc,GAAG,iBAAiB,eACnD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,cAAc,GAAG,kBAAkB,iBACpD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,oBAAoB,CAAC,iBAAiB,kBAAkB;4EAAA;wEACjH,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,kBAAkB,GAAG,iBAAiB,eACvD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,kBAAkB,GAAG,kBAAkB,iBACxD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,iBAAiB,cAAc;wEACtC,UAAU,CAAC,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;4EAAC;wEACnG,WAAU;;0FAEV,8OAAC;gFAAO,OAAO;0FAAI;;;;;;0FACnB,8OAAC;gFAAO,OAAO;0FAAI;;;;;;0FACnB,8OAAC;gFAAO,OAAO;0FAAI;;;;;;0FACnB,8OAAC;gFAAO,OAAO;0FAAK;;;;;;0FACpB,8OAAC;gFAAO,OAAO;0FAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAO1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,iBAAiB,eAAe;wEACvC,UAAU,CAAC,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;4EAAA;wEAC1F,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;;;;;;;kFAE3B,8OAAC;wEAAE,WAAU;;0FACX,8OAAC;gFAAE,WAAU;;;;;;4EAA6B;;;;;;;;;;;;;0EAK9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,gBAAgB,CAAC,iBAAiB,cAAc;4EAAA;wEACzG,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,cAAc,GAAG,iBAAiB,eACnD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,cAAc,GAAG,kBAAkB,iBACpD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,YAAY,CAAC,iBAAiB,UAAU;4EAAA;wEACjG,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,UAAU,GAAG,iBAAiB,eAC/C;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,UAAU,GAAG,kBAAkB,iBAChD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,oBAAoB;gFAAC,GAAG,gBAAgB;gFAAE,eAAe,CAAC,iBAAiB,aAAa;4EAAA;wEACvG,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,aAAa,GAAG,kBAAkB,eACnD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,iBAAiB,aAAa,GAAG,kBAAkB,iBACnD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;kFAE3C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;kFAE3C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;kFAE3C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAK,WAAU;0FAAyB;;;;;;;;;;;;kFAE3C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQtB,cAAc,iCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,wBAAwB;gFAAC,GAAG,oBAAoB;gFAAE,mBAAmB,CAAC,qBAAqB,iBAAiB;4EAAA;wEAC3H,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,iBAAiB,GAAG,kBAAkB,eAC3D;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,qBAAqB,iBAAiB,GAAG,kBAAkB,iBAC3D;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,wBAAwB;gFAAC,GAAG,oBAAoB;gFAAE,oBAAoB,CAAC,qBAAqB,kBAAkB;4EAAA;wEAC7H,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,kBAAkB,GAAG,kBAAkB,eAC5D;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,qBAAqB,kBAAkB,GAAG,kBAAkB,iBAC5D;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,wBAAwB;gFAAC,GAAG,oBAAoB;gFAAE,aAAa,CAAC,qBAAqB,WAAW;4EAAA;wEAC/G,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,WAAW,GAAG,kBAAkB,eACrD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,qBAAqB,WAAW,GAAG,kBAAkB,iBACrD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,wBAAwB;gFAAC,GAAG,oBAAoB;gFAAE,cAAc,CAAC,qBAAqB,YAAY;4EAAA;wEACjH,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,YAAY,GAAG,kBAAkB,eACtD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,qBAAqB,YAAY,GAAG,kBAAkB,iBACtD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,wBAAwB;gFACrC,GAAG,oBAAoB;gFACvB,YAAY;oFAAC,GAAG,qBAAqB,UAAU;oFAAE,SAAS,CAAC,qBAAqB,UAAU,CAAC,OAAO;gFAAA;4EACpG;wEACA,WAAW,CAAC,0EAA0E,EACpF,qBAAqB,UAAU,CAAC,OAAO,GAAG,kBAAkB,eAC5D;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,qBAAqB,UAAU,CAAC,OAAO,GAAG,kBAAkB,iBAC5D;;;;;;;;;;;;;;;;;4DAIL,qBAAqB,UAAU,CAAC,OAAO,kBACtC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAChE,8OAAC;gFACC,MAAK;gFACL,OAAO,qBAAqB,UAAU,CAAC,KAAK;gFAC5C,UAAU,CAAC,IAAM,wBAAwB;wFACvC,GAAG,oBAAoB;wFACvB,YAAY;4FAAC,GAAG,qBAAqB,UAAU;4FAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wFAAA;oFACxE;gFACA,WAAU;;;;;;;;;;;;kFAGd,8OAAC;;0FACC,8OAAC;gFAAM,WAAU;0FAA+C;;;;;;0FAChE,8OAAC;gFACC,MAAK;gFACL,OAAO,qBAAqB,UAAU,CAAC,GAAG;gFAC1C,UAAU,CAAC,IAAM,wBAAwB;wFACvC,GAAG,oBAAoB;wFACvB,YAAY;4FAAC,GAAG,qBAAqB,UAAU;4FAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wFAAA;oFACtE;gFACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAWzB,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,gBAAgB,iBAAiB;wEACxC,UAAU,CAAC,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;4EAAA;wEAC1F,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;;;;;;;;;;;;;0EAI3B,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,gBAAgB,kBAAkB;wEACzC,UAAU,CAAC,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;4EAAA;wEAC3F,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;;;;;;;;;;;;;0EAI3B,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,gBAAgB,gBAAgB;wEACvC,UAAU,CAAC,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACzF,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;0FACzB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAO/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,gBAAgB,CAAC,gBAAgB,cAAc;4EAAA;wEACtG,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,cAAc,GAAG,kBAAkB,eACnD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,cAAc,GAAG,kBAAkB,iBACnD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,kBAAkB,CAAC,gBAAgB,gBAAgB;4EAAA;wEAC1G,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,gBAAgB,GAAG,kBAAkB,eACrD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,gBAAgB,GAAG,kBAAkB,iBACrD;;;;;;;;;;;;;;;;;0EAIN,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,mBAAmB;gFAAC,GAAG,eAAe;gFAAE,iBAAiB,CAAC,gBAAgB,eAAe;4EAAA;wEACxG,WAAW,CAAC,0EAA0E,EACpF,gBAAgB,eAAe,GAAG,kBAAkB,eACpD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,gBAAgB,eAAe,GAAG,kBAAkB,iBACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASb,cAAc,8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,sBAAsB;wEAAC,GAAG,kBAAkB;wEAAE,OAAO;oEAAM;gEAC1E,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,KAAK,KAAK,SACzB,uCACA,yCACJ;;kFAEF,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAE,WAAU;kFAAiC;;;;;;;;;;;;0EAGhD,8OAAC;gEACC,SAAS,IAAM,sBAAsB;wEAAC,GAAG,kBAAkB;wEAAE,OAAO;oEAAO;gEAC3E,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,KAAK,KAAK,UACzB,uCACA,yCACJ;;kFAEF,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAE,WAAU;kFAAiC;;;;;;;;;;;;0EAGhD,8OAAC;gEACC,SAAS,IAAM,sBAAsB;wEAAC,GAAG,kBAAkB;wEAAE,OAAO;oEAAM;gEAC1E,WAAW,CAAC,uCAAuC,EACjD,mBAAmB,KAAK,KAAK,SACzB,uCACA,yCACJ;;kFAEF,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAE,WAAU;kFAAiC;;;;;;;;;;;;;;;;;;;;;;;;0DAMpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEAAI,WAAU;kFACZ;4EAAC;4EAAQ;4EAAQ;4EAAS;4EAAU;4EAAO;yEAAS,CAAC,GAAG,CAAC,CAAC,sBACzD,8OAAC;gFAEC,SAAS,IAAM,sBAAsB;wFAAC,GAAG,kBAAkB;wFAAE,aAAa;oFAAK;gFAC/E,WAAW,CAAC,+CAA+C,EACzD,mBAAmB,WAAW,KAAK,QAAQ,2BAA2B,kBACvE,CAAC,EACA,UAAU,SAAS,kBACnB,UAAU,SAAS,gBACnB,UAAU,UAAU,iBACpB,UAAU,WAAW,kBACrB,UAAU,QAAQ,eAAe,iBACjC;+EAVG;;;;;;;;;;;;;;;;0EAgBb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,mBAAmB,QAAQ;wEAClC,UAAU,CAAC,IAAM,sBAAsB;gFAAC,GAAG,kBAAkB;gFAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4EAAA;wEACvF,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAQ;;;;;;0FACtB,8OAAC;gFAAO,OAAM;0FAAS;;;;;;0FACvB,8OAAC;gFAAO,OAAM;0FAAQ;;;;;;0FACtB,8OAAC;gFAAO,OAAM;0FAAc;;;;;;;;;;;;;;;;;;0EAIhC,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC;wEACC,OAAO,mBAAmB,WAAW;wEACrC,UAAU,CAAC,IAAM,sBAAsB;gFAAC,GAAG,kBAAkB;gFAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4EAAA;wEAC1F,WAAU;;0FAEV,8OAAC;gFAAO,OAAM;0FAAc;;;;;;0FAC5B,8OAAC;gFAAO,OAAM;0FAAQ;;;;;;0FACtB,8OAAC;gFAAO,OAAM;0FAAW;;;;;;;;;;;;;;;;;;0EAI7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAyB;;;;;;0FACvC,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEACC,SAAS,IAAM,sBAAsB;gFAAC,GAAG,kBAAkB;gFAAE,mBAAmB,CAAC,mBAAmB,iBAAiB;4EAAA;wEACrH,WAAW,CAAC,0EAA0E,EACpF,mBAAmB,iBAAiB,GAAG,kBAAkB,eACzD;kFAEF,cAAA,8OAAC;4EAAK,WAAW,CAAC,0EAA0E,EAC1F,mBAAmB,iBAAiB,GAAG,kBAAkB,iBACzD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCASb,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAI/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAMnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAMnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;0EAGf,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;;;;;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGAA+B;;;;;;kGAC/C,8OAAC;wFAAK,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAG5C,8OAAC;wEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAMnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;0EAE3C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;0EAE3C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;0EAE3C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;wEAAK,WAAU;;0FACd,8OAAC;gFAAE,WAAU;;;;;;4EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAetE", "debugId": null}}, {"offset": {"line": 4516, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport SettingsModal from '../../components/SettingsModal';\n\ntype MessageType = 'text' | 'payment' | 'image' | 'voice' | 'video' | 'file' | 'location';\ntype MessageStatus = 'sent' | 'delivered' | 'read' | 'sending' | 'failed';\ntype CallType = 'voice' | 'video';\n\ninterface User {\n  id: string;\n  name: string;\n  username: string;\n  phone: string;\n  isOnline: boolean;\n  lastSeen?: string;\n  avatar?: string;\n  status?: string;\n  isTyping?: boolean;\n}\n\ninterface Message {\n  id: string;\n  senderId: string;\n  text: string;\n  timestamp: Date;\n  status: MessageStatus;\n  type: MessageType;\n  amount?: number;\n  currency?: string;\n  imageUrl?: string;\n  fileUrl?: string;\n  fileName?: string;\n  fileSize?: string;\n  duration?: string;\n  location?: { lat: number; lng: number; address: string };\n  replyTo?: string;\n  isForwarded?: boolean;\n  reactions?: { emoji: string; users: string[] }[];\n}\n\ninterface Chat {\n  id: string;\n  participant: User;\n  lastMessage: string;\n  timestamp: Date;\n  unreadCount: number;\n  messages: Message[];\n  isPinned?: boolean;\n  isMuted?: boolean;\n  isArchived?: boolean;\n}\n\ninterface CallState {\n  isActive: boolean;\n  type: CallType;\n  participant?: User;\n  duration?: string;\n  isMuted?: boolean;\n  isVideoOff?: boolean;\n}\n\n// Mock data for demonstration\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    name: 'John Doe',\n    username: 'johndoe',\n    phone: '+1234567890',\n    isOnline: true,\n    lastSeen: '2 min ago',\n    avatar: 'JD',\n    status: 'Available for chat'\n  },\n  {\n    id: '2',\n    name: 'Jane Smith',\n    username: 'janesmith',\n    phone: '+1234567891',\n    isOnline: false,\n    lastSeen: '1 hour ago',\n    avatar: 'JS',\n    status: 'Busy with work'\n  },\n  {\n    id: '3',\n    name: 'Mike Johnson',\n    username: 'mikej',\n    phone: '+1234567892',\n    isOnline: true,\n    lastSeen: 'now',\n    avatar: 'MJ',\n    status: 'Ready to receive payments'\n  },\n  {\n    id: '4',\n    name: 'Sarah Wilson',\n    username: 'sarahw',\n    phone: '+1234567893',\n    isOnline: false,\n    lastSeen: '30 min ago',\n    avatar: 'SW',\n    status: 'At the gym'\n  },\n];\n\nconst mockMessages: Message[] = [\n  {\n    id: '1',\n    senderId: '1',\n    text: 'Hey! How are you doing?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 30),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '2',\n    senderId: 'current',\n    text: 'I\\'m doing great! Just finished a big project.',\n    timestamp: new Date(Date.now() - 1000 * 60 * 25),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '3',\n    senderId: '1',\n    text: 'That\\'s awesome! Want to celebrate? I can send you some money for dinner 🍽️',\n    timestamp: new Date(Date.now() - 1000 * 60 * 20),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '4',\n    senderId: '1',\n    text: 'Here you go!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    status: 'read',\n    type: 'payment',\n    amount: 50,\n    currency: 'USD'\n  },\n  {\n    id: '5',\n    senderId: 'current',\n    text: 'Thank you so much! 🙏',\n    timestamp: new Date(Date.now() - 1000 * 60 * 10),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '6',\n    senderId: '1',\n    text: 'Check out this photo from my vacation!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 8),\n    status: 'read',\n    type: 'image',\n    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'\n  },\n  {\n    id: '7',\n    senderId: 'current',\n    text: 'Beautiful! Where is this?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 5),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '8',\n    senderId: '1',\n    text: 'Voice message',\n    timestamp: new Date(Date.now() - 1000 * 60 * 3),\n    status: 'read',\n    type: 'voice',\n    duration: '0:45'\n  },\n  {\n    id: '9',\n    senderId: 'current',\n    text: 'Let\\'s have a video call later!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 1),\n    status: 'delivered',\n    type: 'text'\n  }\n];\n\nconst mockChats: Chat[] = [\n  {\n    id: '1',\n    participant: mockUsers[0],\n    lastMessage: 'Can you send me the files?',\n    timestamp: new Date(),\n    unreadCount: 2,\n    messages: mockMessages,\n  },\n  {\n    id: '2',\n    participant: mockUsers[1],\n    lastMessage: 'Meeting at 3 PM',\n    timestamp: new Date(Date.now() - 86400000),\n    unreadCount: 0,\n    messages: [],\n  },\n];\n\nexport default function ChatPage() {\n  const router = useRouter();\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [currentChat, setCurrentChat] = useState<Chat | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [chats, setChats] = useState<Chat[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentAmount, setPaymentAmount] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);\n  const [callState, setCallState] = useState<CallState>({ isActive: false, type: 'voice' });\n  const [showUserProfile, setShowUserProfile] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);\n  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);\n\n  // Enhanced security and banking states\n  const [encryptionStatus, setEncryptionStatus] = useState<'connecting' | 'secured' | 'error'>('secured');\n  const [bankBalance, setBankBalance] = useState<number>(2847.32);\n  const [connectedBank, setConnectedBank] = useState<string>('Chase ****1234');\n  const [plaidToken, setPlaidToken] = useState<string | null>(null);\n  const [isVideoCallActive, setIsVideoCallActive] = useState(false);\n  const [localStream, setLocalStream] = useState<MediaStream | null>(null);\n  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);\n  const [peerConnection, setPeerConnection] = useState<RTCPeerConnection | null>(null);\n\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const localVideoRef = useRef<HTMLVideoElement>(null);\n  const remoteVideoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    // Simulate loading user data\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n        const user: User = {\n          id: 'current',\n          name: 'Current User',\n          username: 'currentuser',\n          phone: '+**********',\n          isOnline: true,\n          avatar: 'CU',\n          status: 'Available'\n        };\n        setCurrentUser(user);\n        setChats(mockChats);\n        setCurrentChat(mockChats[0]);\n        setMessages(mockMessages);\n      } catch (error) {\n        console.error('Error loading chat data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Auto-scroll to bottom of messages and mark messages as read\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n    \n    // Mark messages as read when chat is opened\n    if (currentChat) {\n      const updatedChats = chats.map(chat => {\n        if (chat.id === currentChat.id && chat.unreadCount > 0) {\n          return { ...chat, unreadCount: 0 };\n        }\n        return chat;\n      });\n      setChats(updatedChats);\n    }\n  }, [messages, currentChat]);\n\n  const handleSendMessage = (e?: React.FormEvent) => {\n    e?.preventDefault();\n    if (!newMessage.trim() || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: newMessage,\n      timestamp: new Date(),\n      status: 'sending',\n      type: 'text',\n    };\n\n    // Optimistic update\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setNewMessage('');\n\n    // Simulate message sending\n    setTimeout(() => {\n      setMessages(prevMessages => \n        prevMessages.map(msg => \n          msg.id === message.id \n            ? { ...msg, status: 'delivered' } \n            : msg\n        )\n      );\n    }, 1000);\n\n    // Update chat list\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: newMessage,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: [...updatedMessages],\n          }\n        : chat\n    );\n    \n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleSendPayment = () => {\n    if (!paymentAmount || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: `Payment of $${paymentAmount}`,\n      timestamp: new Date(),\n      status: 'sent',\n      type: 'payment',\n      amount: parseFloat(paymentAmount),\n      currency: 'USD',\n    };\n\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setShowPaymentModal(false);\n    setPaymentAmount('');\n\n    // Update last message in chats\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: `Payment of $${paymentAmount}`,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: updatedMessages,\n          }\n        : chat\n    );\n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleLogout = () => {\n    // Clear user session and redirect to auth page\n    router.push('/auth');\n  };\n\n  if (isLoading || !currentUser) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4\"></div>\n          <p className=\"text-white\">Loading chat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Format time for messages\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Format date for message grouping\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString([], { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    });\n  };\n\n  // Get message status icon\n  const getStatusIcon = (status: MessageStatus) => {\n    switch (status) {\n      case 'sending':\n        return <span className=\"text-gray-400\">🕒</span>;\n      case 'sent':\n        return <span className=\"text-gray-400\">✓</span>;\n      case 'delivered':\n        return <span className=\"text-gray-400\">✓✓</span>;\n      case 'read':\n        return <span className=\"text-blue-500\">✓✓</span>;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n\n        .message-bubble {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(212, 175, 55, 0.2);\n        }\n\n        .message-bubble:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n          border-color: rgba(212, 175, 55, 0.4);\n        }\n\n        .message-bubble-sent {\n          background: rgba(212, 175, 55, 0.15);\n          border-color: rgba(212, 175, 55, 0.3);\n        }\n\n        .message-bubble-received {\n          background: rgba(45, 27, 78, 0.4);\n          border-color: rgba(107, 114, 128, 0.3);\n        }\n\n        .chat-input {\n          background: rgba(17, 24, 39, 0.8);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(75, 85, 99, 0.5);\n          transition: all 0.3s ease;\n        }\n\n        .chat-input:focus {\n          border-color: #D4AF37;\n          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);\n          background: rgba(17, 24, 39, 0.9);\n        }\n\n        .sidebar-panel {\n          background: linear-gradient(180deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);\n          backdrop-filter: blur(20px);\n          border-right: 1px solid rgba(75, 85, 99, 0.3);\n        }\n\n        .chat-panel {\n          background: linear-gradient(180deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);\n          backdrop-filter: blur(20px);\n        }\n\n        .encryption-indicator {\n          animation: pulse 2s infinite;\n        }\n\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.7; }\n        }\n\n        .bank-balance {\n          background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%);\n          border: 1px solid rgba(34, 197, 94, 0.3);\n        }\n\n        .professional-shadow {\n          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);\n        }\n      `}</style>\n\n      <div className=\"flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern\">\n        {/* Enhanced Sidebar */}\n        <div className=\"w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow\">\n          {/* Enhanced Header with Encryption Status */}\n          <div className=\"p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md\">\n            <div className=\"flex items-center\">\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow\">\n                  {currentUser.name.split(' ').map((n: string) => n[0]).join('')}\n                </div>\n                <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator\" title=\"End-to-End Encrypted\"></div>\n              </div>\n              <div className=\"ml-3\">\n                <h2 className=\"font-semibold text-white\">{currentUser.name}</h2>\n                <div className=\"flex items-center space-x-2\">\n                  <p className=\"text-xs text-gray-400\">@{currentUser.username}</p>\n                  <div className=\"flex items-center text-xs text-green-400\">\n                    <i className=\"fas fa-shield-alt mr-1\"></i>\n                    <span>E2E Encrypted</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                title=\"Settings\"\n              >\n                <i className=\"fas fa-cog text-lg\"></i>\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                title=\"Logout\"\n              >\n                <i className=\"fas fa-sign-out-alt text-lg\"></i>\n              </button>\n            </div>\n          </div>\n\n          {/* Enhanced Search Bar */}\n          <div className=\"p-4 border-b border-gray-600/30\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search conversations...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\"></i>\n              {searchQuery && (\n                <button\n                  onClick={() => setSearchQuery('')}\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Chat List */}\n          <div className=\"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent\">\n            {chats.map((chat) => (\n              <motion.div\n                key={chat.id}\n                className={`p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ${\n                  currentChat?.id === chat.id ? 'bg-gray-800/70 border-l-4 border-l-yellow-400' : ''\n                }`}\n                onClick={() => {\n                  setCurrentChat(chat);\n                  setMessages(chat.messages);\n                }}\n                whileHover={{ x: 6 }}\n                transition={{ duration: 0.3, ease: \"easeOut\" }}\n              >\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow\">\n                      {chat.participant.avatar || chat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {chat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900\"></div>\n                    )}\n                    {chat.unreadCount > 0 && (\n                      <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold\">\n                        {chat.unreadCount}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"ml-3 flex-1\">\n                    <div className=\"flex justify-between items-start\">\n                      <h3 className=\"font-semibold text-white\">{chat.participant.name}</h3>\n                      <span className=\"text-xs text-gray-500\">{formatTime(chat.timestamp)}</span>\n                    </div>\n                    <p className=\"text-sm text-gray-400 truncate\">{chat.lastMessage}</p>\n                    {chat.participant.status && (\n                      <p className=\"text-xs text-yellow-400 italic mt-1\">{chat.participant.status}</p>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Bank Balance Section */}\n          <div className=\"p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md\">\n            <div className=\"bank-balance rounded-xl p-4 backdrop-blur-lg\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-university text-green-400 mr-2\"></i>\n                  <span className=\"text-sm font-medium text-gray-300\">{connectedBank}</span>\n                </div>\n                <button\n                  onClick={() => setShowSettings(true)}\n                  className=\"text-gray-500 hover:text-yellow-400 transition-colors\"\n                  title=\"Manage Bank Account\"\n                >\n                  <i className=\"fas fa-cog text-sm\"></i>\n                </button>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs text-gray-500\">Available Balance</p>\n                  <p className=\"text-xl font-bold text-green-400\">${bankBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>\n                </div>\n                <button\n                  onClick={() => setShowPaymentModal(true)}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow\"\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  Send\n                </button>\n              </div>\n              <div className=\"mt-2 flex items-center text-xs text-gray-500\">\n                <i className=\"fas fa-shield-alt mr-1 text-green-400\"></i>\n                <span>Secured by Plaid & 256-bit AES encryption</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Main Chat Area */}\n        <div className=\"flex-1 flex flex-col chat-panel\">\n          {currentChat ? (\n            <>\n              {/* Enhanced Chat Header */}\n              <div className=\"p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow\">\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow\">\n                      {currentChat.participant.avatar || currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {currentChat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900\"></div>\n                    )}\n                  </div>\n                  <div className=\"ml-3\">\n                    <h2 className=\"font-semibold text-white\">{currentChat.participant.name}</h2>\n                    <div className=\"flex items-center space-x-2\">\n                      <p className=\"text-xs text-gray-400\">\n                        {currentChat.participant.isOnline\n                          ? (currentChat.participant.isTyping ? 'Typing...' : 'Online')\n                          : `Last seen ${currentChat.participant.lastSeen}`}\n                      </p>\n                      <div className=\"flex items-center text-xs text-green-400\">\n                        <i className=\"fas fa-lock mr-1\"></i>\n                        <span>Encrypted</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Enhanced Chat Actions */}\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => setCallState({ isActive: true, type: 'voice', participant: currentChat.participant })}\n                    className=\"text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"Voice Call\"\n                  >\n                    <i className=\"fas fa-phone text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => {\n                      setCallState({ isActive: true, type: 'video', participant: currentChat.participant });\n                      setIsVideoCallActive(true);\n                    }}\n                    className=\"text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"Video Call\"\n                  >\n                    <i className=\"fas fa-video text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => setShowUserProfile(true)}\n                    className=\"text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"User Info\"\n                  >\n                    <i className=\"fas fa-info-circle text-lg\"></i>\n                  </button>\n                </div>\n              </div>\n\n              {/* Messages Area */}\n              <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                {messages.length > 0 && (\n                  <div className=\"text-center mb-4\">\n                    <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                      {formatDate(messages[0].timestamp)}\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  {messages.map((message, index) => {\n                    const isCurrentUser = message.senderId === currentUser.id;\n                    const showDate = index === 0 ||\n                      new Date(message.timestamp).toDateString() !==\n                      new Date(messages[index - 1].timestamp).toDateString();\n\n                    return (\n                      <motion.div\n                        key={message.id}\n                        className=\"space-y-1\"\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {showDate && index !== 0 && (\n                          <div className=\"text-center my-4\">\n                            <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                              {formatDate(message.timestamp)}\n                            </span>\n                          </div>\n                        )}\n\n                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>\n                          <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${\n                            isCurrentUser\n                              ? 'message-bubble-sent text-white rounded-tr-none'\n                              : 'message-bubble-received text-white rounded-tl-none'\n                          }`}>\n\n                            {/* Enhanced Payment Message */}\n                            {message.type === 'payment' && (\n                              <div className=\"flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg\">\n                                <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3\">\n                                  <i className=\"fas fa-dollar-sign text-white text-sm\"></i>\n                                </div>\n                                <div>\n                                  <span className=\"text-sm font-semibold text-green-400\">\n                                    Payment Sent: ${message.amount?.toFixed(2)} {message.currency}\n                                  </span>\n                                  <p className=\"text-xs text-gray-400 mt-1\">Secured by Plaid • Instant Transfer</p>\n                                </div>\n                              </div>\n                            )}\n\n                            {/* Image Message */}\n                            {message.type === 'image' && message.imageUrl && (\n                              <div className=\"mb-2\">\n                                <img\n                                  src={message.imageUrl}\n                                  alt=\"Shared image\"\n                                  className=\"rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity\"\n                                  onClick={() => window.open(message.imageUrl, '_blank')}\n                                />\n                              </div>\n                            )}\n\n                            {/* Enhanced Voice Message */}\n                            {message.type === 'voice' && (\n                              <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg\">\n                                <button className=\"w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow\">\n                                  <i className=\"fas fa-play text-sm\"></i>\n                                </button>\n                                <div className=\"flex-1 h-2 bg-gray-600/50 rounded-full\">\n                                  <div className=\"h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full\"></div>\n                                </div>\n                                <span className=\"text-xs text-gray-400 font-medium\">{message.duration}</span>\n                              </div>\n                            )}\n\n                            {/* Enhanced Text Message */}\n                            {(message.type === 'text' || message.type === 'payment') && (\n                              <p className={`${message.type === 'payment' ? 'text-sm' : ''} leading-relaxed`}>\n                                {message.text}\n                              </p>\n                            )}\n\n                            {/* Enhanced Message Footer */}\n                            <div className=\"flex items-center justify-end mt-2 space-x-2\">\n                              <span className={`text-xs ${isCurrentUser ? 'text-gray-400' : 'text-gray-500'} font-medium`}>\n                                {formatTime(message.timestamp)}\n                              </span>\n                              {isCurrentUser && (\n                                <span className=\"ml-1\">\n                                  {getStatusIcon(message.status)}\n                                </span>\n                              )}\n                              <div className=\"flex items-center text-xs text-green-400\">\n                                <i className=\"fas fa-lock text-xs\"></i>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    );\n                  })}\n                </div>\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Enhanced Message Input */}\n              <div className=\"p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow\">\n                {/* Reply Preview */}\n                {replyToMessage && (\n                  <div className=\"mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <p className=\"text-xs text-yellow-400 font-semibold\">Replying to {replyToMessage.senderId === currentUser.id ? 'yourself' : currentChat.participant.name}</p>\n                        <p className=\"text-sm text-gray-300 truncate\">{replyToMessage.text}</p>\n                      </div>\n                      <button\n                        onClick={() => setReplyToMessage(null)}\n                        className=\"text-gray-400 hover:text-white\"\n                      >\n                        <i className=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {/* Attachment Menu */}\n                <AnimatePresence>\n                  {showAttachmentMenu && (\n                    <motion.div\n                      className=\"mb-3 flex space-x-2\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 10 }}\n                    >\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-image text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Photo</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-file text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Document</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-map-marker-alt text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Location</span>\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <form onSubmit={handleSendMessage} className=\"flex items-end space-x-3\">\n                  {/* Enhanced Attachment Button */}\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}\n                    className=\"p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300\"\n                  >\n                    <i className=\"fas fa-plus text-lg\"></i>\n                  </button>\n\n                  {/* Message Input */}\n                  <div className=\"flex-1 relative\">\n                    <textarea\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyDown={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          handleSendMessage(e);\n                        }\n                      }}\n                      placeholder=\"Type a message...\"\n                      rows={1}\n                      className=\"w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none\"\n                      style={{ minHeight: '48px', maxHeight: '120px' }}\n                    />\n\n                    {/* Enhanced Input Actions */}\n                    <div className=\"absolute right-3 bottom-3 flex items-center space-x-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                        className=\"text-gray-500 hover:text-yellow-400 transition-all duration-300\"\n                      >\n                        <i className=\"fas fa-smile text-lg\"></i>\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"text-gray-500 hover:text-blue-400 transition-all duration-300\"\n                        title=\"Voice Message\"\n                      >\n                        <i className=\"fas fa-microphone text-lg\"></i>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Enhanced Send/Payment Buttons */}\n                  <div className=\"flex space-x-3\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPaymentModal(true)}\n                      className=\"p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow\"\n                      title=\"Send Money via Plaid\"\n                    >\n                      <i className=\"fas fa-dollar-sign text-lg\"></i>\n                    </button>\n\n                    <button\n                      type=\"submit\"\n                      disabled={!newMessage.trim()}\n                      className={`p-3 rounded-full transition-all duration-300 professional-shadow ${\n                        newMessage.trim()\n                          ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'\n                          : 'bg-gray-700 text-gray-500 cursor-not-allowed'\n                      }`}\n                    >\n                      <i className=\"fas fa-paper-plane text-lg\"></i>\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center p-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6\">\n                  <i className=\"fas fa-comment-dollar text-3xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-xl font-medium text-white mb-2\">Welcome to BoGuani</h3>\n                <p className=\"text-gray-300\">Select a conversation to start messaging</p>\n                <p className=\"text-yellow-400 text-sm mt-2 italic\">\"Speak Gold. Share Value.\"</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Enhanced Call Interface */}\n        <AnimatePresence>\n          {callState.isActive && (\n            <motion.div\n              className=\"fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n            >\n              <div className=\"text-center text-white\">\n                {/* Video Call Area */}\n                {callState.type === 'video' && isVideoCallActive && (\n                  <div className=\"mb-8\">\n                    <div className=\"relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow\">\n                      <video\n                        ref={remoteVideoRef}\n                        autoPlay\n                        playsInline\n                        className=\"w-full h-full object-cover\"\n                      />\n                      <div className=\"absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden\">\n                        <video\n                          ref={localVideoRef}\n                          autoPlay\n                          playsInline\n                          muted\n                          className=\"w-full h-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Call Info */}\n                <div className=\"w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow\">\n                  <span className=\"text-3xl font-bold\">\n                    {callState.participant?.avatar || callState.participant?.name.split(' ').map((n: string) => n[0]).join('')}\n                  </span>\n                </div>\n                <h2 className=\"text-3xl font-semibold mb-2\">{callState.participant?.name}</h2>\n                <div className=\"flex items-center justify-center mb-2\">\n                  <i className=\"fas fa-shield-alt text-green-400 mr-2\"></i>\n                  <p className=\"text-green-400 text-sm\">End-to-End Encrypted</p>\n                </div>\n                <p className=\"text-gray-400 mb-8 text-lg\">\n                  {callState.type === 'video' ? 'Video calling...' : 'Voice calling...'}\n                </p>\n\n                {/* Enhanced Call Controls */}\n                <div className=\"flex justify-center space-x-6\">\n                  <button\n                    onClick={() => setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }))}\n                    className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${\n                      callState.isMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'\n                    }`}\n                  >\n                    <i className={`fas ${callState.isMuted ? 'fa-microphone-slash' : 'fa-microphone'} text-xl`}></i>\n                  </button>\n\n                  {callState.type === 'video' && (\n                    <button\n                      onClick={() => setCallState(prev => ({ ...prev, isVideoOff: !prev.isVideoOff }))}\n                      className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${\n                        callState.isVideoOff ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'\n                      }`}\n                    >\n                      <i className={`fas ${callState.isVideoOff ? 'fa-video-slash' : 'fa-video'} text-xl`}></i>\n                    </button>\n                  )}\n\n                  <button\n                    onClick={() => {\n                      setCallState({ isActive: false, type: 'voice' });\n                      setIsVideoCallActive(false);\n                    }}\n                    className=\"w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow\"\n                  >\n                    <i className=\"fas fa-phone-slash text-xl\"></i>\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n\n      {/* Settings Modal */}\n      <SettingsModal\n        isOpen={showSettings}\n        onClose={() => setShowSettings(false)}\n        currentUser={currentUser}\n        bankBalance={bankBalance}\n        connectedBank={connectedBank}\n      />\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <motion.div\n          className=\"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n        >\n          <motion.div\n            className=\"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow\"\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.9, opacity: 0 }}\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <div>\n                  <h3 className=\"text-xl font-semibold text-white gold-gradient\">Send Payment</h3>\n                  <div className=\"flex items-center mt-1\">\n                    <i className=\"fas fa-shield-alt text-green-400 mr-2 text-sm\"></i>\n                    <span className=\"text-xs text-green-400\">Secured by Plaid</span>\n                  </div>\n                </div>\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n\n              {/* Bank Account Info */}\n              <div className=\"mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-university text-green-400 mr-2\"></i>\n                    <span className=\"text-sm text-gray-300\">{connectedBank}</span>\n                  </div>\n                  <span className=\"text-sm text-green-400 font-semibold\">${bankBalance.toLocaleString()}</span>\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Amount (USD)\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <span className=\"text-yellow-400 text-lg font-semibold\">$</span>\n                  </div>\n                  <input\n                    type=\"number\"\n                    name=\"amount\"\n                    id=\"amount\"\n                    value={paymentAmount}\n                    onChange={(e) => setPaymentAmount(e.target.value)}\n                    className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg\"\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                  />\n                </div>\n                <div className=\"flex justify-between items-center mt-2\">\n                  <p className=\"text-xs text-gray-500\">\n                    Sending to: {currentChat?.participant.name}\n                  </p>\n                  <p className=\"text-xs text-green-400\">\n                    <i className=\"fas fa-bolt mr-1\"></i>\n                    Instant Transfer\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSendPayment}\n                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}\n                  className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ${\n                    paymentAmount && parseFloat(paymentAmount) > 0\n                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'\n                      : 'bg-gray-700 text-gray-500 cursor-not-allowed'\n                  }`}\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  Send ${paymentAmount || '0.00'}\n                </button>\n              </div>\n\n              {/* Security Notice */}\n              <div className=\"mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20\">\n                <div className=\"flex items-center text-xs text-green-400\">\n                  <i className=\"fas fa-lock mr-2\"></i>\n                  <span>256-bit AES encryption • FDIC insured • Instant settlement</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAEA;AANA;;;;;;;AAiEA,8BAA8B;AAC9B,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI;QACf,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,aAAa;QACb,UAAU,EAAE;IACd;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAE,UAAU;QAAO,MAAM;IAAQ;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,uCAAuC;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAE/E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,WAAW;YACf,IAAI;gBACF,aAAa;gBACb,MAAM,OAAa;oBACjB,IAAI;oBACJ,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;gBACV;gBACA,eAAe;gBACf,SAAS;gBACT,eAAe,SAAS,CAAC,EAAE;gBAC3B,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC7D;QAEA,4CAA4C;QAC5C,IAAI,aAAa;YACf,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA;gBAC7B,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,WAAW,GAAG,GAAG;oBACtD,OAAO;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAE;gBACnC;gBACA,OAAO;YACT;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,oBAAoB,CAAC;QACzB,GAAG;QACH,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,eAAe,CAAC,aAAa;QAExD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM;YACN,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;QACR;QAEA,oBAAoB;QACpB,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,cAAc;QAEd,2BAA2B;QAC3B,WAAW;YACT,YAAY,CAAA,eACV,aAAa,GAAG,CAAC,CAAA,MACf,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAY,IAC9B;QAGV,GAAG;QAEH,mBAAmB;QACnB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa;gBACb,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;uBAAI;iBAAgB;YAChC,IACA;QAGN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa;QAEpD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM,CAAC,YAAY,EAAE,eAAe;YACpC,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;YACN,QAAQ,WAAW;YACnB,UAAU;QACZ;QAEA,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QAEjB,+BAA+B;QAC/B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa,CAAC,YAAY,EAAE,eAAe;gBAC3C,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;YACZ,IACA;QAEN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,eAAe;QACnB,+CAA+C;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,aAAa,CAAC,aAAa;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACjC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;;;;;0BA2FE,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAE7D,8OAAC;wDAA4H,OAAM;kGAApH;;;;;;;;;;;;0DAEjB,8OAAC;0FAAc;;kEACb,8OAAC;kGAAa;kEAA4B,YAAY,IAAI;;;;;;kEAC1D,8OAAC;kGAAc;;0EACb,8OAAC;0GAAY;;oEAAwB;oEAAE,YAAY,QAAQ;;;;;;;0EAC3D,8OAAC;0GAAc;;kFACb,8OAAC;kHAAY;;;;;;kFACb,8OAAC;;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKd,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAE/B,OAAM;0FADI;0DAGV,cAAA,8OAAC;8FAAY;;;;;;;;;;;0DAEf,8OAAC;gDACC,SAAS;gDAET,OAAM;0FADI;0DAGV,cAAA,8OAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;0CAMnB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;sFACpC;;;;;;sDAEZ,8OAAC;sFAAY;;;;;;wCACZ,6BACC,8OAAC;4CACC,SAAS,IAAM,eAAe;sFACpB;sDAEV,cAAA,8OAAC;0FAAY;;;;;;;;;;;;;;;;;;;;;;0CAOrB,8OAAC;0EAAc;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC,gGAAgG,EAC1G,aAAa,OAAO,KAAK,EAAE,GAAG,kDAAkD,IAChF;wCACF,SAAS;4CACP,eAAe;4CACf,YAAY,KAAK,QAAQ;wCAC3B;wCACA,YAAY;4CAAE,GAAG;wCAAE;wCACnB,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAU;kDAE7C,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEACZ,KAAK,WAAW,CAAC,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE5F,KAAK,WAAW,CAAC,QAAQ,kBACxB,8OAAC;sGAAc;;;;;;wDAEhB,KAAK,WAAW,GAAG,mBAClB,8OAAC;sGAAc;sEACZ,KAAK,WAAW;;;;;;;;;;;;8DAIvB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;;8EACb,8OAAC;8GAAa;8EAA4B,KAAK,WAAW,CAAC,IAAI;;;;;;8EAC/D,8OAAC;8GAAe;8EAAyB,WAAW,KAAK,SAAS;;;;;;;;;;;;sEAEpE,8OAAC;sGAAY;sEAAkC,KAAK,WAAW;;;;;;wDAC9D,KAAK,WAAW,CAAC,MAAM,kBACtB,8OAAC;sGAAY;sEAAuC,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;uCAhC5E,KAAK,EAAE;;;;;;;;;;0CAyClB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAY;;;;;;sEACb,8OAAC;sGAAe;sEAAqC;;;;;;;;;;;;8DAEvD,8OAAC;oDACC,SAAS,IAAM,gBAAgB;oDAE/B,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;;;;;;;sDAGjB,8OAAC;sFAAc;;8DACb,8OAAC;;;sEACC,8OAAC;sGAAY;sEAAwB;;;;;;sEACrC,8OAAC;sGAAY;;gEAAmC;gEAAE,YAAY,cAAc,CAAC,SAAS;oEAAE,uBAAuB;gEAAE;;;;;;;;;;;;;8DAEnH,8OAAC;oDACC,SAAS,IAAM,oBAAoB;8FACzB;;sEAEV,8OAAC;sGAAY;;;;;;wDAA8B;;;;;;;;;;;;;sDAI/C,8OAAC;sFAAc;;8DACb,8OAAC;8FAAY;;;;;;8DACb,8OAAC;;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;kEAAc;kCACZ,4BACC;;8CAEE,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEACZ,YAAY,WAAW,CAAC,MAAM,IAAI,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE1G,YAAY,WAAW,CAAC,QAAQ,kBAC/B,8OAAC;sGAAc;;;;;;;;;;;;8DAGnB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAa;sEAA4B,YAAY,WAAW,CAAC,IAAI;;;;;;sEACtE,8OAAC;sGAAc;;8EACb,8OAAC;8GAAY;8EACV,YAAY,WAAW,CAAC,QAAQ,GAC5B,YAAY,WAAW,CAAC,QAAQ,GAAG,cAAc,WAClD,CAAC,UAAU,EAAE,YAAY,WAAW,CAAC,QAAQ,EAAE;;;;;;8EAErD,8OAAC;8GAAc;;sFACb,8OAAC;sHAAY;;;;;;sFACb,8OAAC;;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOd,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,SAAS,IAAM,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;oDAElG,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDACC,SAAS;wDACP,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;wDACnF,qBAAqB;oDACvB;oDAEA,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAElC,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMnB,8OAAC;8EAAc;;wCACZ,SAAS,MAAM,GAAG,mBACjB,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAe;0DACb,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS;;;;;;;;;;;sDAKvC,8OAAC;sFAAc;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gDACtB,MAAM,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;gDACzD,MAAM,WAAW,UAAU,KACzB,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY,OACxC,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY;gDAEtD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;oDAAI;;wDAE3B,YAAY,UAAU,mBACrB,8OAAC;sGAAc;sEACb,cAAA,8OAAC;0GAAe;0EACb,WAAW,QAAQ,SAAS;;;;;;;;;;;sEAKnC,8OAAC;sGAAe,CAAC,KAAK,EAAE,gBAAgB,gBAAgB,iBAAiB;sEACvE,cAAA,8OAAC;0GAAe,CAAC,0DAA0D,EACzE,gBACI,mDACA,sDACJ;;oEAGC,QAAQ,IAAI,KAAK,2BAChB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FACb,cAAA,8OAAC;8HAAY;;;;;;;;;;;0FAEf,8OAAC;;;kGACC,8OAAC;kIAAe;;4FAAuC;4FACrC,QAAQ,MAAM,EAAE,QAAQ;4FAAG;4FAAE,QAAQ,QAAQ;;;;;;;kGAE/D,8OAAC;kIAAY;kGAA6B;;;;;;;;;;;;;;;;;;oEAM/C,QAAQ,IAAI,KAAK,WAAW,QAAQ,QAAQ,kBAC3C,8OAAC;kHAAc;kFACb,cAAA,8OAAC;4EACC,KAAK,QAAQ,QAAQ;4EACrB,KAAI;4EAEJ,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;sHADnC;;;;;;;;;;;oEAOf,QAAQ,IAAI,KAAK,yBAChB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAiB;0FAChB,cAAA,8OAAC;8HAAY;;;;;;;;;;;0FAEf,8OAAC;0HAAc;0FACb,cAAA,8OAAC;8HAAc;;;;;;;;;;;0FAEjB,8OAAC;0HAAe;0FAAqC,QAAQ,QAAQ;;;;;;;;;;;;oEAKxE,CAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,SAAS,mBACrD,8OAAC;kHAAa,GAAG,QAAQ,IAAI,KAAK,YAAY,YAAY,GAAG,gBAAgB,CAAC;kFAC3E,QAAQ,IAAI;;;;;;kFAKjB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAgB,CAAC,QAAQ,EAAE,gBAAgB,kBAAkB,gBAAgB,YAAY,CAAC;0FACxF,WAAW,QAAQ,SAAS;;;;;;4EAE9B,+BACC,8OAAC;0HAAe;0FACb,cAAc,QAAQ,MAAM;;;;;;0FAGjC,8OAAC;0HAAc;0FACb,cAAA,8OAAC;8HAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA/EhB,QAAQ,EAAE;;;;;4CAsFrB;;;;;;sDAEF,8OAAC;4CAAI,KAAK;;;;;;;;;;;;;8CAIZ,8OAAC;8EAAc;;wCAEZ,gCACC,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAY;;oEAAwC;oEAAa,eAAe,QAAQ,KAAK,YAAY,EAAE,GAAG,aAAa,YAAY,WAAW,CAAC,IAAI;;;;;;;0EACxJ,8OAAC;0GAAY;0EAAkC,eAAe,IAAI;;;;;;;;;;;;kEAEpE,8OAAC;wDACC,SAAS,IAAM,kBAAkB;kGACvB;kEAEV,cAAA,8OAAC;sGAAY;;;;;;;;;;;;;;;;;;;;;;sDAOrB,8OAAC,yLAAA,CAAA,kBAAe;sDACb,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;;kEAE1B,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAK,UAAU;sFAA6B;;8DAE3C,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,sBAAsB,CAAC;8FAC5B;8DAEV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAIf,8OAAC;8FAAc;;sEACb,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAW,CAAC;gEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oEACpC,EAAE,cAAc;oEAChB,kBAAkB;gEACpB;4DACF;4DACA,aAAY;4DACZ,MAAM;4DAEN,OAAO;gEAAE,WAAW;gEAAQ,WAAW;4DAAQ;sGADrC;;;;;;sEAKZ,8OAAC;sGAAc;;8EACb,8OAAC;oEACC,MAAK;oEACL,SAAS,IAAM,mBAAmB,CAAC;8GACzB;8EAEV,cAAA,8OAAC;kHAAY;;;;;;;;;;;8EAEf,8OAAC;oEACC,MAAK;oEAEL,OAAM;8GADI;8EAGV,cAAA,8OAAC;kHAAY;;;;;;;;;;;;;;;;;;;;;;;8DAMnB,8OAAC;8FAAc;;sEACb,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,oBAAoB;4DAEnC,OAAM;sGADI;sEAGV,cAAA,8OAAC;0GAAY;;;;;;;;;;;sEAGf,8OAAC;4DACC,MAAK;4DACL,UAAU,CAAC,WAAW,IAAI;sGACf,CAAC,iEAAiE,EAC3E,WAAW,IAAI,KACX,2GACA,gDACJ;sEAEF,cAAA,8OAAC;0GAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOvB,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAY;;;;;;;;;;;kDAEf,8OAAC;kFAAa;kDAAsC;;;;;;kDACpD,8OAAC;kFAAY;kDAAgB;;;;;;kDAC7B,8OAAC;kFAAY;kDAAsC;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC,yLAAA,CAAA,kBAAe;kCACb,UAAU,QAAQ,kBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;sCAEnB,cAAA,8OAAC;0EAAc;;oCAEZ,UAAU,IAAI,KAAK,WAAW,mCAC7B,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,KAAK;oDACL,QAAQ;oDACR,WAAW;8FACD;;;;;;8DAEZ,8OAAC;8FAAc;8DACb,cAAA,8OAAC;wDACC,KAAK;wDACL,QAAQ;wDACR,WAAW;wDACX,KAAK;kGACK;;;;;;;;;;;;;;;;;;;;;;kDAQpB,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAe;sDACb,UAAU,WAAW,EAAE,UAAU,UAAU,WAAW,EAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,KAAK;;;;;;;;;;;kDAG3G,8OAAC;kFAAa;kDAA+B,UAAU,WAAW,EAAE;;;;;;kDACpE,8OAAC;kFAAc;;0DACb,8OAAC;0FAAY;;;;;;0DACb,8OAAC;0FAAY;0DAAyB;;;;;;;;;;;;kDAExC,8OAAC;kFAAY;kDACV,UAAU,IAAI,KAAK,UAAU,qBAAqB;;;;;;kDAIrD,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,CAAC,KAAK,OAAO;wDAAC,CAAC;0FAC7D,CAAC,wGAAwG,EAClH,UAAU,OAAO,GAAG,gCAAgC,iCACpD;0DAEF,cAAA,8OAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,OAAO,GAAG,wBAAwB,gBAAgB,QAAQ,CAAC;;;;;;;;;;;4CAG3F,UAAU,IAAI,KAAK,yBAClB,8OAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,CAAC,KAAK,UAAU;wDAAC,CAAC;0FACnE,CAAC,wGAAwG,EAClH,UAAU,UAAU,GAAG,gCAAgC,iCACvD;0DAEF,cAAA,8OAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,UAAU,GAAG,mBAAmB,WAAW,QAAQ,CAAC;;;;;;;;;;;0DAIvF,8OAAC;gDACC,SAAS;oDACP,aAAa;wDAAE,UAAU;wDAAO,MAAM;oDAAQ;oDAC9C,qBAAqB;gDACvB;0FACU;0DAEV,cAAA,8OAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3B,8OAAC,mIAAA,CAAA,UAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,aAAa;gBACb,aAAa;gBACb,eAAe;;;;;;YAIhB,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;0BAEnB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAK,SAAS;oBAAE;8BAE/B,cAAA,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;;;0DACC,8OAAC;0FAAa;0DAAiD;;;;;;0DAC/D,8OAAC;0FAAc;;kEACb,8OAAC;kGAAY;;;;;;kEACb,8OAAC;kGAAe;kEAAyB;;;;;;;;;;;;;;;;;;kDAG7C,8OAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDAEV,cAAA,8OAAC;sFAAY;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAY;;;;;;8DACb,8OAAC;8FAAe;8DAAyB;;;;;;;;;;;;sDAE3C,8OAAC;sFAAe;;gDAAuC;gDAAE,YAAY,cAAc;;;;;;;;;;;;;;;;;;0CAIvF,8OAAC;0EAAc;;kDACb,8OAAC;wCAAM,SAAQ;kFAAmB;kDAA+C;;;;;;kDAGjF,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAe;8DAAwC;;;;;;;;;;;0DAE1D,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAEhD,aAAY;gDACZ,MAAK;gDACL,KAAI;0FAHM;;;;;;;;;;;;kDAMd,8OAAC;kFAAc;;0DACb,8OAAC;0FAAY;;oDAAwB;oDACtB,aAAa,YAAY;;;;;;;0DAExC,8OAAC;0FAAY;;kEACX,8OAAC;kGAAY;;;;;;oDAAuB;;;;;;;;;;;;;;;;;;;0CAM1C,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,iBAAiB,WAAW,kBAAkB;kFAC9C,CAAC,mFAAmF,EAC7F,iBAAiB,WAAW,iBAAiB,IACzC,2GACA,gDACJ;;0DAEF,8OAAC;0FAAY;;;;;;4CAA8B;4CACpC,iBAAiB;;;;;;;;;;;;;0CAK5B,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;sFAAY;;;;;;sDACb,8OAAC;;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}]}