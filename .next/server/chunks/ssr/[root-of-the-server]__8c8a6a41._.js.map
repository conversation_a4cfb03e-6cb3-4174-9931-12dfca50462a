{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <div className=\"text-white min-h-screen\" style={{\n      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',\n      fontFamily: 'Montserrat, sans-serif'\n    }}>\n      <div className=\"min-h-screen\" style={{\n        backgroundImage: `\n          radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),\n          radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),\n          radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%),\n          url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n      }}>\n        {/* Navigation */}\n        <nav className=\"fixed w-full z-10\" style={{\n          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 100%)',\n          backdropFilter: 'blur(20px)',\n          borderBottom: '1px solid rgba(212, 175, 55, 0.2)'\n        }}>\n          <div className=\"container mx-auto px-6 py-4 flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <div className=\"text-3xl mr-3\" style={{ color: '#D4AF37' }}>\n                <i className=\"fas fa-comment-dollar\"></i>\n              </div>\n              <span className=\"font-bold text-2xl\" style={{\n                background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                WebkitBackgroundClip: 'text',\n                backgroundClip: 'text',\n                color: 'transparent'\n              }}>\n                BoGuani\n              </span>\n            </div>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#features\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\" style={{ color: '#D4AF37' }}>Features</a>\n              <a href=\"#about\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\" style={{ color: '#D4AF37' }}>About</a>\n              <a href=\"#download\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\" style={{ color: '#D4AF37' }}>Download</a>\n              <Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\" style={{ color: '#D4AF37' }}>Privacy</Link>\n            </div>\n          </div>\n        </nav>\n\n        {/* Hero Section */}\n        <header className=\"pt-32 pb-24 px-6 min-h-screen flex items-center\" style={{\n          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)'\n        }}>\n          <div className=\"container mx-auto flex flex-col md:flex-row items-center relative\">\n            <div className=\"md:w-1/2 mb-12 md:mb-0 relative z-10\">\n              <div className=\"mb-6\">\n                <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n                  <span style={{\n                    background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                    WebkitBackgroundClip: 'text',\n                    backgroundClip: 'text',\n                    color: 'transparent',\n                    filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'\n                  }}>BoGuani</span>\n                </h1>\n                <h2 className=\"text-3xl md:text-4xl font-semibold mb-4 text-gray-100\">Messenger of Value</h2>\n                <p className=\"text-2xl mb-8 italic font-medium\" style={{ color: '#F2D675' }}>Where Words Carry Worth</p>\n              </div>\n\n              <div className=\"p-6 rounded-2xl mb-8\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(212, 175, 55, 0.2)'\n              }}>\n                <p className=\"mb-6 text-gray-200 text-lg leading-relaxed\">\n                  Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.\n                  Send messages that matter, share moments that count, and transfer value instantly -\n                  all protected by sacred-level encryption.\n                </p>\n                <div className=\"text-center\">\n                  <p className=\"text-2xl font-bold\" style={{\n                    background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                    WebkitBackgroundClip: 'text',\n                    backgroundClip: 'text',\n                    color: 'transparent'\n                  }}>\"Speak Gold. Share Value.\"</p>\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-4 justify-center md:justify-start\">\n                <Link href=\"/auth\" className=\"px-8 py-4 rounded-full font-bold text-lg transition-all flex items-center shadow-lg relative overflow-hidden\" style={{\n                  background: 'linear-gradient(to right, #D4AF37, #F2D675)',\n                  color: '#2D1B4E'\n                }}>\n                  <i className=\"fas fa-globe mr-3\"></i> Open BoGuani Web Version\n                </Link>\n                <a href=\"#features\" className=\"px-8 py-4 rounded-full font-bold text-lg transition-all flex items-center\" style={{\n                  border: '2px solid transparent',\n                  background: 'linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box, linear-gradient(90deg, #D4AF37, #F2D675) border-box',\n                  color: '#D4AF37'\n                }}>\n                  <i className=\"fas fa-play mr-3\"></i> <span>Watch Demo</span>\n                </a>\n              </div>\n            </div>\n\n            <div className=\"md:w-1/2 flex justify-center relative z-10\">\n              <div className=\"relative w-80 h-96\">\n                <div className=\"absolute inset-0 rounded-3xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-500\" style={{\n                  background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                  backdropFilter: 'blur(10px)',\n                  border: '2px solid transparent',\n                  backgroundImage: 'linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)), linear-gradient(90deg, #D4AF37, #F2D675)',\n                  backgroundOrigin: 'border-box',\n                  backgroundClip: 'content-box, border-box'\n                }}>\n                  <div className=\"p-4 border-b\" style={{\n                    background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)',\n                    borderColor: 'rgba(212, 175, 55, 0.4)'\n                  }}>\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"text-xl\" style={{ color: '#D4AF37' }}>\n                        <i className=\"fas fa-comment-dollar\"></i>\n                      </div>\n                      <p className=\"font-bold text-lg\" style={{\n                        background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                        WebkitBackgroundClip: 'text',\n                        backgroundClip: 'text',\n                        color: 'transparent'\n                      }}>BoGuani</p>\n                      <div style={{ color: '#D4AF37' }}>\n                        <i className=\"fas fa-ellipsis-v\"></i>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 h-full\" style={{\n                    background: 'linear-gradient(to bottom, transparent, rgba(45, 27, 78, 0.2))'\n                  }}>\n                    <div className=\"flex flex-col h-full space-y-3\">\n                      <div className=\"p-3 rounded-xl self-start max-w-[75%]\" style={{\n                        background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                        backdropFilter: 'blur(10px)',\n                        border: '1px solid rgba(78, 58, 112, 0.3)'\n                      }}>\n                        <p className=\"text-sm text-gray-200\">Hey! Can you send me 20 for dinner tonight?</p>\n                      </div>\n                      <div className=\"p-3 rounded-xl self-end max-w-[75%]\" style={{\n                        background: 'linear-gradient(to right, rgba(212, 175, 55, 0.2), rgba(242, 214, 117, 0.2))',\n                        border: '1px solid rgba(212, 175, 55, 0.4)'\n                      }}>\n                        <p className=\"text-sm text-gray-100\">Sure! Sending it now with a special message.</p>\n                      </div>\n                      <div className=\"p-3 rounded-xl self-end max-w-[75%]\" style={{\n                        background: 'linear-gradient(to right, rgba(212, 175, 55, 0.3), rgba(242, 214, 117, 0.3))',\n                        border: '1px solid rgba(212, 175, 55, 0.5)'\n                      }}>\n                        <div className=\"flex items-center\">\n                          <div className=\"mr-2\" style={{ color: '#D4AF37' }}>\n                            <i className=\"fas fa-coins\"></i>\n                          </div>\n                          <p className=\"text-sm font-semibold text-gray-100\">$20.00 sent - Enjoy dinner!</p>\n                        </div>\n                      </div>\n                      <div className=\"p-3 rounded-xl self-start max-w-[75%]\" style={{\n                        background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                        backdropFilter: 'blur(10px)',\n                        border: '1px solid rgba(78, 58, 112, 0.3)'\n                      }}>\n                        <p className=\"text-sm text-gray-200\">Thanks! Value received. 🙏</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n        {/* Features Section */}\n        <section id=\"features\" className=\"py-20 relative overflow-hidden\" style={{\n          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'\n        }}>\n          <div className=\"container mx-auto px-6 relative z-10\">\n            <div className=\"text-center mb-20\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6\" style={{\n                background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                WebkitBackgroundClip: 'text',\n                backgroundClip: 'text',\n                color: 'transparent'\n              }}>Key Features</h2>\n              <div className=\"w-24 h-1 mx-auto mb-4\" style={{\n                background: 'linear-gradient(to right, #D4AF37, #F2D675)'\n              }}></div>\n              <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">Experience the future of value-based communication</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"p-8 rounded-2xl relative overflow-hidden group transition-all duration-400\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',\n                backdropFilter: 'blur(15px)',\n                border: '1px solid rgba(212, 175, 55, 0.3)'\n              }}>\n                <div className=\"relative z-10\">\n                  <div className=\"text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}>\n                    <i className=\"fas fa-lock\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{ color: '#F2D675' }}>256-bit End-to-End Encryption</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>\n                </div>\n              </div>\n\n              <div className=\"p-8 rounded-2xl relative overflow-hidden group transition-all duration-400\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',\n                backdropFilter: 'blur(15px)',\n                border: '1px solid rgba(212, 175, 55, 0.3)'\n              }}>\n                <div className=\"relative z-10\">\n                  <div className=\"text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}>\n                    <i className=\"fas fa-bolt\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{ color: '#F2D675' }}>Instant Money Transfers</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>\n                </div>\n              </div>\n\n              <div className=\"p-8 rounded-2xl relative overflow-hidden group transition-all duration-400\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',\n                backdropFilter: 'blur(15px)',\n                border: '1px solid rgba(212, 175, 55, 0.3)'\n              }}>\n                <div className=\"relative z-10\">\n                  <div className=\"text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}>\n                    <i className=\"fas fa-headset\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4\" style={{ color: '#F2D675' }}>24/7 Global Support</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Download Section */}\n        <section id=\"download\" className=\"py-20 relative overflow-hidden\" style={{\n          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)'\n        }}>\n          <div className=\"container mx-auto px-6 relative z-10\">\n            <div className=\"text-center mb-20\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6\" style={{\n                background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',\n                WebkitBackgroundClip: 'text',\n                backgroundClip: 'text',\n                color: 'transparent'\n              }}>Get BoGuani Now</h2>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto mb-6\">Experience the revolution in value-based messaging. Available on all major platforms.</p>\n              <div className=\"w-24 h-1 mx-auto\" style={{\n                background: 'linear-gradient(to right, #D4AF37, #F2D675)'\n              }}></div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto\">\n              <Link href=\"/downloads\" className=\"px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(212, 175, 55, 0.2)'\n              }}>\n                <i className=\"fab fa-apple text-5xl mb-4 group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg\" style={{ color: '#F2D675' }}>iOS</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </Link>\n\n              <Link href=\"/downloads\" className=\"px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(212, 175, 55, 0.2)'\n              }}>\n                <i className=\"fab fa-android text-5xl mb-4 group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg\" style={{ color: '#F2D675' }}>Android</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </Link>\n\n              <Link href=\"/downloads\" className=\"px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden\" style={{\n                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',\n                backdropFilter: 'blur(10px)',\n                border: '1px solid rgba(212, 175, 55, 0.2)'\n              }}>\n                <i className=\"fas fa-desktop text-5xl mb-4 group-hover:scale-110 transition-transform duration-300\" style={{ color: '#D4AF37' }}></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg\" style={{ color: '#F2D675' }}>Desktop</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Windows, Mac, Linux</p>\n                </div>\n              </Link>\n\n              <Link href=\"/auth\" className=\"px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden\" style={{\n                background: 'linear-gradient(to right, #D4AF37, #F2D675)',\n                color: '#2D1B4E'\n              }}>\n                <i className=\"fas fa-globe text-5xl mb-4 group-hover:scale-110 transition-transform duration-300\"></i>\n                <div>\n                  <p className=\"text-sm mb-1 opacity-80\">Try it now</p>\n                  <p className=\"font-bold text-lg\">Web App</p>\n                  <p className=\"text-xs mt-2 opacity-80\">Available Now</p>\n                </div>\n              </Link>\n            </div>\n          </div>\n        </section>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,8OAAC;QAAI,WAAU;QAA0B,OAAO;YAC9C,YAAY;YACZ,YAAY;QACd;kBACE,cAAA,8OAAC;YAAI,WAAU;YAAe,OAAO;gBACnC,iBAAiB,CAAC;;;;4XAIkW,CAAC;YACvX;;8BAEE,8OAAC;oBAAI,WAAU;oBAAoB,OAAO;wBACxC,YAAY;wBACZ,gBAAgB;wBAChB,cAAc;oBAChB;8BACE,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAgB,OAAO;4CAAE,OAAO;wCAAU;kDACvD,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;wCAAK,WAAU;wCAAqB,OAAO;4CAC1C,YAAY;4CACZ,sBAAsB;4CACtB,gBAAgB;4CAChB,OAAO;wCACT;kDAAG;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAY,WAAU;wCAAgE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDAC3H,8OAAC;wCAAE,MAAK;wCAAS,WAAU;wCAAgE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDACxH,8OAAC;wCAAE,MAAK;wCAAY,WAAU;wCAAgE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDAC3H,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;wCAAgE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;;;;;;8BAMnI,8OAAC;oBAAO,WAAU;oBAAkD,OAAO;wBACzE,YAAY;oBACd;8BACE,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACZ,cAAA,8OAAC;oDAAK,OAAO;wDACX,YAAY;wDACZ,sBAAsB;wDACtB,gBAAgB;wDAChB,OAAO;wDACP,QAAQ;oDACV;8DAAG;;;;;;;;;;;0DAEL,8OAAC;gDAAG,WAAU;0DAAwD;;;;;;0DACtE,8OAAC;gDAAE,WAAU;gDAAmC,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAG/E,8OAAC;wCAAI,WAAU;wCAAuB,OAAO;4CAC3C,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;;0DACE,8OAAC;gDAAE,WAAU;0DAA6C;;;;;;0DAK1D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;oDAAqB,OAAO;wDACvC,YAAY;wDACZ,sBAAsB;wDACtB,gBAAgB;wDAChB,OAAO;oDACT;8DAAG;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;gDAA+G,OAAO;oDACjJ,YAAY;oDACZ,OAAO;gDACT;;kEACE,8OAAC;wDAAE,WAAU;;;;;;oDAAwB;;;;;;;0DAEvC,8OAAC;gDAAE,MAAK;gDAAY,WAAU;gDAA4E,OAAO;oDAC/G,QAAQ;oDACR,YAAY;oDACZ,OAAO;gDACT;;kEACE,8OAAC;wDAAE,WAAU;;;;;;oDAAuB;kEAAC,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;0CAKjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAgH,OAAO;4CACpI,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;4CACR,iBAAiB;4CACjB,kBAAkB;4CAClB,gBAAgB;wCAClB;;0DACE,8OAAC;gDAAI,WAAU;gDAAe,OAAO;oDACnC,YAAY;oDACZ,aAAa;gDACf;0DACE,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAU,OAAO;gEAAE,OAAO;4DAAU;sEACjD,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;sEAEf,8OAAC;4DAAE,WAAU;4DAAoB,OAAO;gEACtC,YAAY;gEACZ,sBAAsB;gEACtB,gBAAgB;gEAChB,OAAO;4DACT;sEAAG;;;;;;sEACH,8OAAC;4DAAI,OAAO;gEAAE,OAAO;4DAAU;sEAC7B,cAAA,8OAAC;gEAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAInB,8OAAC;gDAAI,WAAU;gDAAa,OAAO;oDACjC,YAAY;gDACd;0DACE,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAwC,OAAO;gEAC5D,YAAY;gEACZ,gBAAgB;gEAChB,QAAQ;4DACV;sEACE,cAAA,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;4DAAsC,OAAO;gEAC1D,YAAY;gEACZ,QAAQ;4DACV;sEACE,cAAA,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;4DAAsC,OAAO;gEAC1D,YAAY;gEACZ,QAAQ;4DACV;sEACE,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAO,OAAO;4EAAE,OAAO;wEAAU;kFAC9C,cAAA,8OAAC;4EAAE,WAAU;;;;;;;;;;;kFAEf,8OAAC;wEAAE,WAAU;kFAAsC;;;;;;;;;;;;;;;;;sEAGvD,8OAAC;4DAAI,WAAU;4DAAwC,OAAO;gEAC5D,YAAY;gEACZ,gBAAgB;gEAChB,QAAQ;4DACV;sEACE,cAAA,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWrD,8OAAC;oBAAQ,IAAG;oBAAW,WAAU;oBAAiC,OAAO;wBACvE,YAAY;oBACd;8BACE,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;wCAAsC,OAAO;4CACzD,YAAY;4CACZ,sBAAsB;4CACtB,gBAAgB;4CAChB,OAAO;wCACT;kDAAG;;;;;;kDACH,8OAAC;wCAAI,WAAU;wCAAwB,OAAO;4CAC5C,YAAY;wCACd;;;;;;kDACA,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAGzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA6E,OAAO;4CACjG,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAkF,OAAO;wDAAE,OAAO;oDAAU;8DACzH,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;oDAA0B,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;wCAA6E,OAAO;4CACjG,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAkF,OAAO;wDAAE,OAAO;oDAAU;8DACzH,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;oDAA0B,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;kDAIjD,8OAAC;wCAAI,WAAU;wCAA6E,OAAO;4CACjG,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAkF,OAAO;wDAAE,OAAO;oDAAU;8DACzH,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAG,WAAU;oDAA0B,OAAO;wDAAE,OAAO;oDAAU;8DAAG;;;;;;8DACrE,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvD,8OAAC;oBAAQ,IAAG;oBAAW,WAAU;oBAAiC,OAAO;wBACvE,YAAY;oBACd;8BACE,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;wCAAsC,OAAO;4CACzD,YAAY;4CACZ,sBAAsB;4CACtB,gBAAgB;4CAChB,OAAO;wCACT;kDAAG;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;wCAAmB,OAAO;4CACvC,YAAY;wCACd;;;;;;;;;;;;0CAGF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;wCAA0H,OAAO;4CACjK,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;;0DACE,8OAAC;gDAAE,WAAU;gDAAqF,OAAO;oDAAE,OAAO;gDAAU;;;;;;0DAC5H,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;wDAAoB,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAI9C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;wCAA0H,OAAO;4CACjK,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;;0DACE,8OAAC;gDAAE,WAAU;gDAAuF,OAAO;oDAAE,OAAO;gDAAU;;;;;;0DAC9H,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;wDAAoB,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAI9C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;wCAA0H,OAAO;4CACjK,YAAY;4CACZ,gBAAgB;4CAChB,QAAQ;wCACV;;0DACE,8OAAC;gDAAE,WAAU;gDAAuF,OAAO;oDAAE,OAAO;gDAAU;;;;;;0DAC9H,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;wDAAoB,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;kDAI9C,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;wCAA0H,OAAO;4CAC5J,YAAY;4CACZ,OAAO;wCACT;;0DACE,8OAAC;gDAAE,WAAU;;;;;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA0B;;;;;;kEACvC,8OAAC;wDAAE,WAAU;kEAAoB;;;;;;kEACjC,8OAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}]}