{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState, useEffect } from 'react';\nimport Head from 'next/head';\n\n// Helper component for feature cards\nconst FeatureCard = ({ icon, title, description }: { icon: string; title: string; description: string }) => (\n  <div className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]\">\n    <div className=\"w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-xl flex items-center justify-center mb-6 text-gray-900\">\n      <i className={`fas ${icon} text-2xl`}></i>\n    </div>\n    <h3 className=\"text-xl font-bold mb-3 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">{title}</h3>\n    <p className=\"text-gray-300\">{description}</p>\n  </div>\n);\n\n// Main Home component\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const handleScroll = (sectionId: string) => {\n    const element = document.getElementById(sectionId);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      setIsMenuOpen(false);\n    }\n  };\n\n  // Add smooth scrolling effect\n  useEffect(() => {\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLElement;\n      if (target.matches('a[href^=\"#\"]')) {\n        e.preventDefault();\n        const targetId = target.getAttribute('href');\n        if (targetId) {\n          const element = document.querySelector(targetId);\n          if (element) {\n            element.scrollIntoView({ behavior: 'smooth' });\n          }\n        }\n      }\n    };\n\n    document.addEventListener('click', handleSmoothScroll);\n    return () => document.removeEventListener('click', handleSmoothScroll);\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white\">\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n      `}</style>\n      <Head>\n        <title>BoGuani - Messenger of Value</title>\n        <meta name=\"description\" content=\"Where Words Carry Worth - Experience secure messaging with value transfer\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </Head>\n      \n      {/* Navigation */}\n      <nav className=\"fixed w-full z-50 bg-gradient-to-r from-purple-900/90 to-black/90 backdrop-blur-lg shadow-lg border-b border-yellow-500/20\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"text-yellow-400 text-3xl mr-2\">\n                  <i className=\"fas fa-comment-dollar\"></i>\n                </div>\n                <span className=\"font-bold text-2xl gold-gradient\">BoGuani</span>\n              </Link>\n            </div>\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              <a \n                href=\"#features\" \n                className=\"text-gray-300 hover:text-yellow-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n              >\n                Features\n              </a>\n              <a \n                href=\"#about\" \n                className=\"text-gray-300 hover:text-yellow-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n              >\n                About\n              </a>\n              <a \n                href=\"#download\" \n                className=\"text-gray-300 hover:text-yellow-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n              >\n                Download\n              </a>\n              <Link \n                href=\"/support\"\n                className=\"text-gray-300 hover:text-yellow-400 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\n              >\n                Support\n              </Link>\n              <Link \n                href=\"/auth\"\n                className=\"ml-4 bg-yellow-400 text-gray-900 hover:bg-yellow-300 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 flex items-center\"\n              >\n                <i className=\"fas fa-sign-in-alt mr-2\"></i> Sign In\n              </Link>\n            </div>\n            {/* Mobile menu button */}\n            <div className=\"md:hidden flex items-center\">\n              <button \n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-yellow-400\"\n                aria-expanded=\"false\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <i className=\"fas fa-times text-xl\"></i>\n                ) : (\n                  <i className=\"fas fa-bars text-xl\"></i>\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-gray-700\">\n            <div className=\"container mx-auto px-6 py-3 flex flex-col space-y-4\">\n              <button \n                onClick={() => handleScroll('features')}\n                className=\"hover:text-yellow-400 transition-colors\"\n              >\n                Features\n              </button>\n              <button \n                onClick={() => handleScroll('about')}\n                className=\"hover:text-yellow-400 transition-colors\"\n              >\n                About\n              </button>\n              <button \n                onClick={() => handleScroll('download')}\n                className=\"hover:text-yellow-400 transition-colors\"\n              >\n                Download\n              </button>\n              <Link \n                href=\"/support\"\n                className=\"hover:text-yellow-400 transition-colors\"\n              >\n                Support\n              </Link>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Hero Section */}\n      <main className=\"pt-28 pb-20 px-4 sm:px-6 lg:px-8 hero-pattern\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"lg:grid lg:grid-cols-12 lg:gap-8 items-center\">\n            <div className=\"px-4 sm:px-6 sm:text-center md:mx-auto md:max-w-2xl lg:col-span-6 lg:text-left lg:flex lg:items-center\">\n              <div>\n                <h1 className=\"text-4xl font-extrabold tracking-tight text-white sm:text-5xl md:text-6xl\">\n                  <span className=\"block\">Messenger of</span>\n                  <span className=\"block gold-gradient\">Value</span>\n                </h1>\n                <p className=\"mt-3 text-base text-gray-300 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0\">\n                  Where Words Carry Worth. Experience secure messaging with built-in value transfer, powered by blockchain technology.\n                </p>\n                <div className=\"mt-10 sm:flex sm:justify-center lg:justify-start space-y-4 sm:space-y-0 sm:space-x-4\">\n                  <div className=\"rounded-md shadow\">\n                    <Link \n                      href=\"/auth\" \n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-yellow-400 hover:bg-yellow-300 md:py-4 md:text-lg md:px-10\"\n                    >\n                      Get Started\n                    </Link>\n                  </div>\n                  <div className=\"rounded-md shadow\">\n                    <Link \n                      href=\"#features\" \n                      className=\"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gray-700 hover:bg-gray-600 md:py-4 md:text-lg md:px-10\"\n                    >\n                      Learn More\n                    </Link>\n                  </div>\n                </div>\n              </div>\n              <div className=\"mt-8 lg:mt-0 lg:col-span-6\">\n                <div className=\"bg-gray-800 rounded-2xl p-6 shadow-2xl\">\n                  <div className=\"flex items-center mb-6\">\n                    <div className=\"bg-yellow-400 p-2 rounded-full\">\n                      <i className=\"fas fa-comment-dollar text-gray-900\"></i>\n                    </div>\n                    <div className=\"ml-3\">\n                      <h3 className=\"font-bold\">BoGuani</h3>\n                      <p className=\"text-sm text-gray-400\">Messenger of Value</p>\n                    </div>\n                  </div>\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gray-700 p-4 rounded-2xl max-w-xs\">\n                      <p>Hey! Can you send me 10 BGN for lunch? 🍔</p>\n                      <p className=\"text-xs text-gray-400 mt-1\">2:30 PM</p>\n                    </div>\n                    <div className=\"bg-yellow-400 p-4 rounded-2xl max-w-xs ml-auto text-gray-900\">\n                      <p>Sure! Sending now 💸</p>\n                      <p className=\"text-xs text-yellow-800 mt-1\">2:31 PM</p>\n                    </div>\n                    <div className=\"bg-gray-700 p-3 rounded-2xl max-w-xs text-center\">\n                      <p className=\"text-xs text-yellow-400\">You sent 10.00 BGN</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-20 bg-gradient-to-br from-purple-900/40 to-black/40\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl font-bold mb-4\">Key Features</h2>\n            <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <FeatureCard \n              icon=\"fa-lock\"\n              title=\"End-to-End Encryption\"\n              description=\"Your messages and transactions are protected with military-grade encryption.\"\n            />\n            <FeatureCard \n              icon=\"fa-bolt\"\n              title=\"Instant Transfers\"\n              description=\"Send and receive money instantly with just a few taps.\"\n            />\n            <FeatureCard \n              icon=\"fa-shield-alt\"\n              title=\"Secure Storage\"\n              description=\"Your funds are stored securely using multi-signature technology.\"\n            />\n          </div>\n        </div>\n      </section>\n\n      {/* Download Section */}\n      <section id=\"download\" className=\"py-20 bg-gradient-to-br from-purple-900/30 to-black/30\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl font-bold mb-4\">Get BoGuani Now</h2>\n            <p className=\"text-gray-300 max-w-2xl mx-auto\">Experience the revolution in value-based messaging. Available on all major platforms.</p>\n            <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Link \n              href=\"/downloads\"\n              className=\"bg-gray-800 hover:bg-gray-700 transition-colors px-8 py-4 rounded-xl flex items-center gold-border\"\n            >\n              <i className=\"fab fa-apple text-3xl mr-4 text-yellow-400\"></i>\n              <div>\n                <p className=\"text-xs\">Download for</p>\n                <p className=\"font-semibold\">iOS</p>\n              </div>\n            </Link>\n            <Link \n              href=\"/downloads\"\n              className=\"bg-gray-800 hover:bg-gray-700 transition-colors px-8 py-4 rounded-xl flex items-center gold-border\"\n            >\n              <i className=\"fab fa-android text-3xl mr-4 text-yellow-400\"></i>\n              <div>\n                <p className=\"text-xs\">Download for</p>\n                <p className=\"font-semibold\">Android</p>\n              </div>\n            </Link>\n            <Link \n              href=\"/downloads\"\n              className=\"bg-gray-800 hover:bg-gray-700 transition-colors px-8 py-4 rounded-xl flex items-center gold-border\"\n            >\n              <i className=\"fas fa-desktop text-3xl mr-4 text-yellow-400\"></i>\n              <div>\n                <p className=\"text-xs\">Download</p>\n                <p className=\"font-semibold\">Desktop App</p>\n              </div>\n            </Link>\n            <Link \n              href=\"/auth\"\n              className=\"bg-gray-800 hover:bg-gray-700 transition-colors px-8 py-4 rounded-xl flex items-center gold-border\"\n            >\n              <i className=\"fas fa-globe text-3xl mr-4 text-yellow-400\"></i>\n              <div>\n                <p className=\"text-xs\">Open</p>\n                <p className=\"font-semibold\">Web Version</p>\n              </div>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* About Section */}\n      <section id=\"about\" className=\"py-20 bg-gradient-to-br from-purple-900/40 to-black/40\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"flex flex-col md:flex-row items-center\">\n            <div className=\"md:w-1/2 mb-10 md:mb-0\">\n              {/* Add your illustration or image here */}\n            </div>\n            <div className=\"md:w-1/2 md:pl-10\">\n              <h2 className=\"text-3xl font-bold mb-6\">Our Story</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mb-6\"></div>\n              <p className=\"mb-4 text-gray-300\">\n                BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. \n                The name \"BoGuani\" combines elements from their language representing both \"message\" and \"worth.\"\n              </p>\n              <p className=\"mb-6 text-gray-300\">\n                We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, \n                but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.\n              </p>\n              <div className=\"flex items-center\">\n                <div className=\"h-px bg-yellow-400 flex-grow\"></div>\n                <p className=\"px-4 text-yellow-400 italic\">\"Speak Gold. Share Value.\"</p>\n                <div className=\"h-px bg-yellow-400 flex-grow\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-gradient-to-r from-purple-900 to-black py-12\">\n        <div className=\"container mx-auto px-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"flex items-center mb-4 md:mb-0\">\n              <div className=\"text-yellow-400 text-2xl mr-2\">\n                <i className=\"fas fa-comment-dollar\"></i>\n              </div>\n              <span className=\"font-bold text-xl\">BoGuani</span>\n            </div>\n            <div className=\"flex space-x-6\">\n              <a \n                href=\"https://twitter.com/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"Twitter\"\n              >\n                <i className=\"fab fa-twitter text-yellow-400\"></i>\n              </a>\n              <a \n                href=\"https://facebook.com/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"Facebook\"\n              >\n                <i className=\"fab fa-facebook-f text-yellow-400\"></i>\n              </a>\n              <a \n                href=\"https://instagram.com/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"Instagram\"\n              >\n                <i className=\"fab fa-instagram text-yellow-400\"></i>\n              </a>\n              <a \n                href=\"https://linkedin.com/company/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"LinkedIn\"\n              >\n                <i className=\"fab fa-linkedin-in text-yellow-400\"></i>\n              </a>\n              <a \n                href=\"https://t.me/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"Telegram\"\n              >\n                <i className=\"fab fa-telegram text-yellow-400\"></i>\n              </a>\n              <a \n                href=\"https://github.com/boguani\" \n                target=\"_blank\" \n                rel=\"noopener noreferrer\"\n                className=\"w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700 transition-colors\"\n                aria-label=\"GitHub\"\n              >\n                <i className=\"fab fa-github text-yellow-400\"></i>\n              </a>\n            </div>\n          </div>\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center md:text-left\">\n            <p className=\"text-gray-400 text-sm\">&copy; {new Date().getFullYear()} BoGuani. All rights reserved.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;;AAMA,qCAAqC;AACrC,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAwD,iBACrG,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAW,CAAC,IAAI,EAAE,KAAK,SAAS,CAAC;;;;;;;;;;;0BAEtC,8OAAC;gBAAG,WAAU;0BAAuG;;;;;;0BACrH,8OAAC;gBAAE,WAAU;0BAAiB;;;;;;;;;;;;AAKnB,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,cAAc;QAChB;IACF;IAEA,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,CAAC,iBAAiB;gBAClC,EAAE,cAAc;gBAChB,MAAM,WAAW,OAAO,YAAY,CAAC;gBACrC,IAAI,UAAU;oBACZ,MAAM,UAAU,SAAS,aAAa,CAAC;oBACvC,IAAI,SAAS;wBACX,QAAQ,cAAc,CAAC;4BAAE,UAAU;wBAAS;oBAC9C;gBACF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;IACrD,GAAG,EAAE;IAEL,qBACE,8OAAC;kDAAc;;;;;;0BAsBb,8OAAC,oKAAA,CAAA,UAAI;;kCACH,8OAAC;;kCAAM;;;;;;kCACP,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;;kCACjC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;;;;;;;;0BAIxB,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAY;;;;;;;;;;;0DAEf,8OAAC;0FAAe;0DAAmC;;;;;;;;;;;;;;;;;8CAIvD,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,MAAK;sFACK;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;sFACK;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;sFACK;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC;8FAAY;;;;;;gDAA8B;;;;;;;;;;;;;8CAI/C,8OAAC;8EAAc;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAE9B,iBAAc;kFADJ;;0DAGV,8OAAC;0FAAe;0DAAU;;;;;;4CACzB,2BACC,8OAAC;0FAAY;;;;;qEAEb,8OAAC;0FAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQtB,4BACC,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa;8EAClB;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;8EAClB;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,aAAa;8EAClB;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;0DAAe;0BACd,cAAA,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;;;sDACC,8OAAC;sFAAa;;8DACZ,8OAAC;8FAAe;8DAAQ;;;;;;8DACxB,8OAAC;8FAAe;8DAAsB;;;;;;;;;;;;sDAExC,8OAAC;sFAAY;sDAAoG;;;;;;sDAGjH,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;8DAIH,8OAAC;8FAAc;8DACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;8CAMP,8OAAC;8EAAc;8CACb,cAAA,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAY;;;;;;;;;;;kEAEf,8OAAC;kGAAc;;0EACb,8OAAC;0GAAa;0EAAY;;;;;;0EAC1B,8OAAC;0GAAY;0EAAwB;;;;;;;;;;;;;;;;;;0DAGzC,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;;0EACb,8OAAC;;0EAAE;;;;;;0EACH,8OAAC;0GAAY;0EAA6B;;;;;;;;;;;;kEAE5C,8OAAC;kGAAc;;0EACb,8OAAC;;0EAAE;;;;;;0EACH,8OAAC;0GAAY;0EAA+B;;;;;;;;;;;;kEAE9C,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAY;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvD,8OAAC;gBAAQ,IAAG;0DAAqB;0BAC/B,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAA0B;;;;;;8CACxC,8OAAC;8EAAc;;;;;;;;;;;;sCAEjB,8OAAC;sEAAc;;8CACb,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;;;;;;8CAEd,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;;;;;;8CAEd,8OAAC;oCACC,MAAK;oCACL,OAAM;oCACN,aAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,8OAAC;gBAAQ,IAAG;0DAAqB;0BAC/B,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAA0B;;;;;;8CACxC,8OAAC;8EAAY;8CAAkC;;;;;;8CAC/C,8OAAC;8EAAc;;;;;;;;;;;;sCAEjB,8OAAC;sEAAc;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sFAAY;;;;;;sDACb,8OAAC;;;8DACC,8OAAC;8FAAY;8DAAU;;;;;;8DACvB,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sFAAY;;;;;;sDACb,8OAAC;;;8DACC,8OAAC;8FAAY;8DAAU;;;;;;8DACvB,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sFAAY;;;;;;sDACb,8OAAC;;;8DACC,8OAAC;8FAAY;8DAAU;;;;;;8DACvB,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sFAAY;;;;;;sDACb,8OAAC;;;8DACC,8OAAC;8FAAY;8DAAU;;;;;;8DACvB,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC;gBAAQ,IAAG;0DAAkB;0BAC5B,cAAA,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;;;;;0CAGf,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAA0B;;;;;;kDACxC,8OAAC;kFAAc;;;;;;kDACf,8OAAC;kFAAY;kDAAqB;;;;;;kDAIlC,8OAAC;kFAAY;kDAAqB;;;;;;kDAIlC,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;;;;;0DACf,8OAAC;0FAAY;0DAA8B;;;;;;0DAC3C,8OAAC;0FAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;0DAAiB;0BAChB,cAAA,8OAAC;8DAAc;;sCACb,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;sFAAe;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CAEJ,cAAW;sFADD;sDAGV,cAAA,8OAAC;0FAAY;;;;;;;;;;;;;;;;;;;;;;;sCAInB,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAY;;oCAAwB;oCAAQ,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlF", "debugId": null}}]}