{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport Image from 'next/image';\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n        \n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n        \n        .gold-gradient {\n          background: linear-gradient(90deg, #fbbf24 0%, #fde047 50%, #fbbf24 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n        \n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #fbbf24, #fde047) border-box;\n        }\n        \n        .feature-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n        \n        .feature-card:hover {\n          transform: translateY(-5px);\n          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);\n        }\n        \n        .btn-hover:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(251, 191, 36, 0.3);\n        }\n        \n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23fbbf24' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen hero-pattern\">\n        {/* Navigation */}\n        <nav className=\"bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10\">\n          <div className=\"container mx-auto px-6 py-3 flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <div className=\"text-yellow-400 text-3xl mr-2\">\n                <i className=\"fas fa-comment-dollar\"></i>\n              </div>\n              <span className=\"font-bold text-2xl gold-gradient\">BoGuani</span>\n            </div>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a>\n              <a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\">About</a>\n              <a href=\"#download\" className=\"hover:text-yellow-400 transition-colors\">Download</a>\n              <a href=\"#support\" className=\"hover:text-yellow-400 transition-colors\">Support</a>\n            </div>\n            <div className=\"md:hidden\">\n              <button \n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-white focus:outline-none\"\n              >\n                <i className=\"fas fa-bars\"></i>\n              </button>\n            </div>\n          </div>\n          {/* Mobile menu */}\n          {isMenuOpen && (\n            <div className=\"md:hidden bg-gray-700\">\n              <div className=\"container mx-auto px-6 py-3 flex flex-col space-y-4\">\n                <a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a>\n                <a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\">About</a>\n                <a href=\"#download\" className=\"hover:text-yellow-400 transition-colors\">Download</a>\n                <a href=\"#support\" className=\"hover:text-yellow-400 transition-colors\">Support</a>\n              </div>\n            </div>\n          )}\n        </nav>\n\n        {/* Hero Section */}\n        <section className=\"pt-24 pb-16\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"flex flex-col lg:flex-row items-center min-h-screen\">\n              <div className=\"lg:w-1/2 lg:pr-12\">\n                <h1 className=\"text-5xl lg:text-7xl font-bold mb-6 leading-tight\">\n                  <span className=\"gold-gradient\">BoGuani</span>\n                </h1>\n                <h2 className=\"text-2xl lg:text-3xl font-semibold mb-4\">Messenger of Value</h2>\n                <h3 className=\"text-xl lg:text-2xl text-gray-300 mb-6\">Where Words Carry Worth</h3>\n                <p className=\"text-lg text-gray-400 mb-8 max-w-lg leading-relaxed\">\n                  Inspired by ancient Taíno wisdom, BoGuani transforms communication into something more meaningful. Send messages that matter, share moments that count, and transfer value instantly - all protected by sacred-level encryption.\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n                  <Link href=\"/auth\" className=\"bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover text-center inline-flex items-center justify-center\">\n                    <i className=\"fas fa-globe mr-2\"></i> Open Web Version\n                  </Link>\n                  <a href=\"#download\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover text-center inline-flex items-center justify-center\">\n                    <i className=\"fas fa-play mr-2 text-yellow-400\"></i> <span className=\"text-yellow-400\">Watch Demo</span>\n                  </a>\n                </div>\n                <p className=\"text-yellow-400 italic font-medium text-lg\">\"Speak Gold. Share Value.\"</p>\n              </div>\n              \n              <div className=\"lg:w-1/2 flex justify-center lg:justify-end mt-12 lg:mt-0\">\n                <div className=\"relative\">\n                  <div className=\"w-80 h-[600px] bg-gradient-to-b from-purple-800 to-purple-900 rounded-3xl shadow-2xl border border-yellow-400 border-opacity-30 overflow-hidden\">\n                    {/* Phone mockup content */}\n                    <div className=\"p-6 h-full flex flex-col\">\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 text-sm font-bold\">J</div>\n                          <span className=\"ml-3 font-semibold\">Joel</span>\n                        </div>\n                        <div className=\"text-yellow-400 text-xl\">\n                          <i className=\"fas fa-video\"></i>\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex-1 space-y-4 overflow-y-auto\">\n                        <div className=\"bg-purple-700 bg-opacity-60 p-4 rounded-2xl max-w-xs\">\n                          <p className=\"text-sm\">Hey! Can you send me $50 for lunch today?</p>\n                        </div>\n                        <div className=\"bg-yellow-400 text-gray-900 p-4 rounded-2xl max-w-xs ml-auto\">\n                          <p className=\"text-sm font-medium\">Sure! Sending now 💰</p>\n                          <div className=\"mt-3 p-3 bg-yellow-300 rounded-xl text-xs font-bold text-center\">\n                            💸 $50.00 sent\n                          </div>\n                        </div>\n                        <div className=\"bg-purple-700 bg-opacity-60 p-4 rounded-2xl max-w-xs\">\n                          <p className=\"text-sm\">Thanks! Value received ✨</p>\n                        </div>\n                      </div>\n                      \n                      <div className=\"mt-4 p-3 bg-purple-700 bg-opacity-40 rounded-xl text-center\">\n                        <p className=\"text-xs text-yellow-400 font-medium\">End-to-End Encrypted</p>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {/* Floating elements */}\n                  <div className=\"absolute -top-6 -right-6 w-20 h-20 bg-yellow-400 rounded-full flex items-center justify-center text-gray-900 text-3xl shadow-lg animate-pulse\">\n                    <i className=\"fas fa-dollar-sign\"></i>\n                  </div>\n                  <div className=\"absolute -bottom-6 -left-6 w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center text-yellow-400 text-xl shadow-lg animate-bounce\">\n                    <i className=\"fas fa-shield-alt\"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Key Features */}\n        <section id=\"features\" className=\"py-20 bg-purple-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold mb-4\">Key Features</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"bg-purple-800 p-8 rounded-xl feature-card text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-shield-alt\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">256-bit End-to-End Encryption</h3>\n                <p className=\"text-gray-300\">Your messages and transactions are protected with sacred-level security. No one can access your communications.</p>\n              </div>\n              \n              <div className=\"bg-purple-800 p-8 rounded-xl feature-card text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-bolt\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">Instant Money Transfers</h3>\n                <p className=\"text-gray-300\">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>\n              </div>\n              \n              <div className=\"bg-purple-800 p-8 rounded-xl feature-card text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-headset\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">24/7 Global Support</h3>\n                <p className=\"text-gray-300\">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Download Section */}\n        <section id=\"download\" className=\"py-20 bg-gray-900\">\n          <div className=\"container mx-auto px-6 text-center\">\n            <h2 className=\"text-3xl font-bold mb-8\">Get BoGuani Now</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n              <div className=\"bg-purple-800 rounded-xl p-6\">\n                <h3 className=\"text-xl font-semibold mb-4\">Android</h3>\n                <p className=\"text-gray-300 mb-6\">Download BoGuani from Google Play Store</p>\n                <a href=\"#\" className=\"btn-primary inline-flex items-center\">\n                  <i className=\"fas fa-android mr-2\"></i>\n                  <span>Get it on Google Play</span>\n                </a>\n              </div>\n              <div className=\"bg-purple-800 rounded-xl p-6\">\n                <h3 className=\"text-xl font-semibold mb-4\">iOS</h3>\n                <p className=\"text-gray-300 mb-6\">Download BoGuani from App Store</p>\n                <a href=\"#\" className=\"btn-primary inline-flex items-center\">\n                  <i className=\"fab fa-apple mr-2\"></i>\n                  <span>Download on the App Store</span>\n                </a>\n              </div>\n            </div>\n            <h2 className=\"text-3xl font-bold mb-4\">Get BoGuani Now</h2>\n            <p className=\"text-gray-300 mb-12 max-w-2xl mx-auto\">\n              Experience the revolution in value-based messaging. Available on all major platforms.\n            </p>\n\n            <div className=\"flex flex-wrap justify-center gap-6\">\n              <a href=\"#\" className=\"bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fab fa-apple mr-2\"></i> Download for iOS\n              </a>\n              <a href=\"#\" className=\"bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fab fa-android mr-2\"></i> Download for Android\n              </a>\n              <a href=\"#\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fas fa-desktop mr-2 text-yellow-400\"></i> <span className=\"text-yellow-400\">Desktop App</span>\n              </a>\n              <Link href=\"/auth\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fas fa-globe mr-2 text-yellow-400\"></i> <span className=\"text-yellow-400\">Web Version</span>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* About Section */}\n        <section id=\"about\" className=\"py-20 bg-purple-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"flex flex-col lg:flex-row items-center\">\n              <div className=\"lg:w-1/2 mb-12 lg:mb-0\">\n                <div className=\"relative\">\n                  <svg className=\"w-full max-w-md mx-auto\" viewBox=\"0 0 200 200\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path fill=\"#fbbf24\" fillOpacity=\"0.2\" d=\"M45.7,-51.9C59.9,-41.5,72.3,-27.7,76.3,-11.5C80.3,4.7,75.9,23.4,65.1,36.6C54.3,49.8,37.1,57.5,19.3,63.3C1.6,69.1,-16.8,73,-32.5,67.5C-48.2,62,-61.3,47.1,-68.3,29.8C-75.3,12.5,-76.2,-7.2,-69.8,-23.6C-63.3,-40,-49.5,-53.1,-34.7,-63C-19.9,-72.9,-3.9,-79.7,9.9,-76.8C23.8,-73.9,31.5,-62.3,45.7,-51.9Z\" transform=\"translate(100 100)\" />\n                    <text x=\"50%\" y=\"50%\" dominantBaseline=\"middle\" textAnchor=\"middle\" fill=\"#fbbf24\" fontSize=\"20\" fontWeight=\"bold\">Taíno Wisdom</text>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"lg:w-1/2 lg:pl-12\">\n                <h2 className=\"text-3xl font-bold mb-6\">Our Story</h2>\n                <div className=\"w-20 h-1 bg-yellow-400 mb-6\"></div>\n                <p className=\"mb-4 text-gray-300 leading-relaxed\">\n                  BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. The name \"BoGuani\" combines elements from their language representing both \"message\" and \"worth.\"\n                </p>\n                <p className=\"mb-6 text-gray-300 leading-relaxed\">\n                  We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.\n                </p>\n                <div className=\"flex items-center\">\n                  <div className=\"h-px bg-yellow-400 flex-grow\"></div>\n                  <p className=\"px-4 text-yellow-400 italic font-medium\">\"Speak Gold. Share Value.\"</p>\n                  <div className=\"h-px bg-yellow-400 flex-grow\"></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Testimonials */}\n        <section className=\"py-20 bg-gray-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold mb-4\">What Our Users Say</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"bg-purple-800 p-6 rounded-xl relative\">\n                <div className=\"text-yellow-400 text-4xl absolute -top-5 -left-2\">\n                  <i className=\"fas fa-quote-left\"></i>\n                </div>\n                <p className=\"mt-4 mb-6 text-gray-300 leading-relaxed\">BoGuani has completely changed how I think about messaging. Being able to send value along with my words makes every conversation more meaningful.</p>\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold\">M</div>\n                  <div className=\"ml-3\">\n                    <p className=\"font-semibold\">Maria L.</p>\n                    <p className=\"text-xs text-gray-400\">Entrepreneur</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-purple-800 p-6 rounded-xl relative\">\n                <div className=\"text-yellow-400 text-4xl absolute -top-5 -left-2\">\n                  <i className=\"fas fa-quote-left\"></i>\n                </div>\n                <p className=\"mt-4 mb-6 text-gray-300 leading-relaxed\">The security features are impressive. I feel confident sending money through BoGuani knowing that my transactions are protected by top-tier encryption.</p>\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold\">J</div>\n                  <div className=\"ml-3\">\n                    <p className=\"font-semibold\">James T.</p>\n                    <p className=\"text-xs text-gray-400\">Security Analyst</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-purple-800 p-6 rounded-xl relative\">\n                <div className=\"text-yellow-400 text-4xl absolute -top-5 -left-2\">\n                  <i className=\"fas fa-quote-left\"></i>\n                </div>\n                <p className=\"mt-4 mb-6 text-gray-300 leading-relaxed\">I use BoGuani daily for both personal and business communications. The ability to instantly transfer value has streamlined so many of my interactions.</p>\n                <div className=\"flex items-center\">\n                  <div className=\"w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold\">S</div>\n                  <div className=\"ml-3\">\n                    <p className=\"font-semibold\">Sarah K.</p>\n                    <p className=\"text-xs text-gray-400\">Digital Nomad</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-gradient-to-b from-purple-900 to-gray-900\">\n          <div className=\"container mx-auto px-6 text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">Ready to Transform Your Communications?</h2>\n            <p className=\"text-xl text-gray-300 mb-10 max-w-2xl mx-auto\">Join thousands of users who are already experiencing the power of value-based messaging with BoGuani.</p>\n\n            <div className=\"flex flex-wrap justify-center gap-6\">\n              <Link href=\"/auth\" className=\"bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fas fa-globe mr-2\"></i> Open Web Version\n              </Link>\n              <a href=\"#download\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center\">\n                <i className=\"fas fa-download mr-2 text-yellow-400\"></i> <span className=\"text-yellow-400\">Download App</span>\n              </a>\n            </div>\n          </div>\n        </section>\n\n        {/* Footer */}\n        <footer className=\"bg-gray-900 py-12\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center mb-8\">\n              <div className=\"mb-6 md:mb-0\">\n                <div className=\"flex items-center\">\n                  <div className=\"text-yellow-400 text-2xl mr-2\">\n                    <i className=\"fas fa-comment-dollar\"></i>\n                  </div>\n                  <span className=\"font-bold text-xl gold-gradient\">BoGuani</span>\n                </div>\n                <p className=\"text-gray-400 mt-2\">Messenger of Value</p>\n              </div>\n\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-6 md:mb-0\">\n                <div>\n                  <h3 className=\"font-semibold mb-3 text-yellow-400\">Product</h3>\n                  <ul className=\"space-y-2 text-gray-400\">\n                    <li><a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a></li>\n                    <li><a href=\"/security\" className=\"hover:text-yellow-400 transition-colors\">Security</a></li>\n                    <li><a href=\"#\" className=\"hover:text-yellow-400 transition-colors\">Pricing</a></li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-3 text-yellow-400\">Company</h3>\n                  <ul className=\"space-y-2 text-gray-400\">\n                    <li><a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\">About</a></li>\n                    <li><a href=\"#\" className=\"hover:text-yellow-400 transition-colors\">Careers</a></li>\n                    <li><a href=\"#\" className=\"hover:text-yellow-400 transition-colors\">Blog</a></li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-3 text-yellow-400\">Resources</h3>\n                  <ul className=\"space-y-2 text-gray-400\">\n                    <li><a href=\"/support\" className=\"hover:text-yellow-400 transition-colors\">Help Center</a></li>\n                    <li><a href=\"/guides\" className=\"hover:text-yellow-400 transition-colors\">Guides</a></li>\n                    <li><a href=\"#\" className=\"hover:text-yellow-400 transition-colors\">API Docs</a></li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold mb-3 text-yellow-400\">Legal</h3>\n                  <ul className=\"space-y-2 text-gray-400\">\n                    <li><a href=\"/privacy\" className=\"hover:text-yellow-400 transition-colors\">Privacy</a></li>\n                    <li><a href=\"/terms\" className=\"hover:text-yellow-400 transition-colors\">Terms</a></li>\n                    <li><a href=\"/security\" className=\"hover:text-yellow-400 transition-colors\">Security</a></li>\n                  </ul>\n                </div>\n              </div>\n\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors\">\n                  <i className=\"fab fa-twitter text-yellow-400\"></i>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors\">\n                  <i className=\"fab fa-facebook-f text-yellow-400\"></i>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors\">\n                  <i className=\"fab fa-instagram text-yellow-400\"></i>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors\">\n                  <i className=\"fab fa-linkedin-in text-yellow-400\"></i>\n                </a>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-700 pt-6 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm mb-4 md:mb-0\">© 2024 BoGuani. All rights reserved.</p>\n              <div className=\"flex space-x-6 text-gray-400 text-sm\">\n                <a href=\"/privacy\" className=\"hover:text-yellow-400 transition-colors\">Privacy Policy</a>\n                <a href=\"/terms\" className=\"hover:text-yellow-400 transition-colors\">Terms of Service</a>\n                <a href=\"#\" className=\"hover:text-yellow-400 transition-colors\">Cookie Policy</a>\n              </div>\n            </div>\n          </div>\n        </footer>\n        {/* Footer */}\n        <footer className=\"bg-gray-800 py-12\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n              <div>\n                <h3 className=\"text-xl font-semibold mb-4\">BoGuani</h3>\n                <p className=\"text-gray-400\">Messenger of Value</p>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold mb-4\">Company</h3>\n                <ul className=\"space-y-2\">\n                  <li><a href=\"#about\" className=\"text-gray-400 hover:text-yellow-400\">About Us</a></li>\n                  <li><a href=\"#features\" className=\"text-gray-400 hover:text-yellow-400\">Features</a></li>\n                  <li><a href=\"#download\" className=\"text-gray-400 hover:text-yellow-400\">Download</a></li>\n                </ul>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold mb-4\">Support</h3>\n                <ul className=\"space-y-2\">\n                  <li><a href=\"#support\" className=\"text-gray-400 hover:text-yellow-400\">Help Center</a></li>\n                  <li><a href=\"#\" className=\"text-gray-400 hover:text-yellow-400\">Contact Us</a></li>\n                  <li><a href=\"#\" className=\"text-gray-400 hover:text-yellow-400\">Terms of Service</a></li>\n                </ul>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-semibold mb-4\">Social</h3>\n                <div className=\"flex space-x-4\">\n                  <a href=\"#\" className=\"text-gray-400 hover:text-yellow-400\">\n                    <i className=\"fab fa-twitter\"></i>\n                  </a>\n                  <a href=\"#\" className=\"text-gray-400 hover:text-yellow-400\">\n                    <i className=\"fab fa-facebook\"></i>\n                  </a>\n                  <a href=\"#\" className=\"text-gray-400 hover:text-yellow-400\">\n                    <i className=\"fab fa-instagram\"></i>\n                  </a>\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400\">\n              <p>&copy; {new Date().getFullYear()} BoGuani. All rights reserved.</p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE;;;;;;0BAyCE,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAY;;;;;;;;;;;0DAEf,8OAAC;0FAAe;0DAAmC;;;;;;;;;;;;kDAErD,8OAAC;kFAAc;;0DACb,8OAAC;gDAAE,MAAK;0FAAsB;0DAA0C;;;;;;0DACxE,8OAAC;gDAAE,MAAK;0FAAmB;0DAA0C;;;;;;0DACrE,8OAAC;gDAAE,MAAK;0FAAsB;0DAA0C;;;;;;0DACxE,8OAAC;gDAAE,MAAK;0FAAqB;0DAA0C;;;;;;;;;;;;kDAEzE,8OAAC;kFAAc;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,cAAc,CAAC;sFACpB;sDAEV,cAAA,8OAAC;0FAAY;;;;;;;;;;;;;;;;;;;;;;4BAKlB,4BACC,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;4CAAE,MAAK;sFAAsB;sDAA0C;;;;;;sDACxE,8OAAC;4CAAE,MAAK;sFAAmB;sDAA0C;;;;;;sDACrE,8OAAC;4CAAE,MAAK;sFAAsB;sDAA0C;;;;;;sDACxE,8OAAC;4CAAE,MAAK;sFAAqB;sDAA0C;;;;;;;;;;;;;;;;;;;;;;;kCAO/E,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAa;0DACZ,cAAA,8OAAC;8FAAe;8DAAgB;;;;;;;;;;;0DAElC,8OAAC;0FAAa;0DAA0C;;;;;;0DACxD,8OAAC;0FAAa;0DAAyC;;;;;;0DACvD,8OAAC;0FAAY;0DAAsD;;;;;;0DAGnE,8OAAC;0FAAc;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;;0EAC3B,8OAAC;0GAAY;;;;;;4DAAwB;;;;;;;kEAEvC,8OAAC;wDAAE,MAAK;kGAAsB;;0EAC5B,8OAAC;0GAAY;;;;;;4DAAuC;0EAAC,8OAAC;0GAAe;0EAAkB;;;;;;;;;;;;;;;;;;0DAG3F,8OAAC;0FAAY;0DAA6C;;;;;;;;;;;;kDAG5D,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DAEb,cAAA,8OAAC;kGAAc;;0EACb,8OAAC;0GAAc;;kFACb,8OAAC;kHAAc;;0FACb,8OAAC;0HAAc;0FAAwG;;;;;;0FACvH,8OAAC;0HAAe;0FAAqB;;;;;;;;;;;;kFAEvC,8OAAC;kHAAc;kFACb,cAAA,8OAAC;sHAAY;;;;;;;;;;;;;;;;;0EAIjB,8OAAC;0GAAc;;kFACb,8OAAC;kHAAc;kFACb,cAAA,8OAAC;sHAAY;sFAAU;;;;;;;;;;;kFAEzB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAY;0FAAsB;;;;;;0FACnC,8OAAC;0HAAc;0FAAkE;;;;;;;;;;;;kFAInF,8OAAC;kHAAc;kFACb,cAAA,8OAAC;sHAAY;sFAAU;;;;;;;;;;;;;;;;;0EAI3B,8OAAC;0GAAc;0EACb,cAAA,8OAAC;8GAAY;8EAAsC;;;;;;;;;;;;;;;;;;;;;;8DAMzD,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,8OAAC;wBAAQ,IAAG;kEAAqB;kCAC/B,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAA0B;;;;;;sDACxC,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;sDAG/B,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;sDAG/B,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAQ,IAAG;kEAAqB;kCAC/B,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAA0B;;;;;;8CACxC,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAqB;;;;;;8DAClC,8OAAC;oDAAE,MAAK;8FAAc;;sEACpB,8OAAC;sGAAY;;;;;;sEACb,8OAAC;;sEAAK;;;;;;;;;;;;;;;;;;sDAGV,8OAAC;sFAAc;;8DACb,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAqB;;;;;;8DAClC,8OAAC;oDAAE,MAAK;8FAAc;;sEACpB,8OAAC;sGAAY;;;;;;sEACb,8OAAC;;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAIZ,8OAAC;8EAAa;8CAA0B;;;;;;8CACxC,8OAAC;8EAAY;8CAAwC;;;;;;8CAIrD,8OAAC;8EAAc;;sDACb,8OAAC;4CAAE,MAAK;sFAAc;;8DACpB,8OAAC;8FAAY;;;;;;gDAAwB;;;;;;;sDAEvC,8OAAC;4CAAE,MAAK;sFAAc;;8DACpB,8OAAC;8FAAY;;;;;;gDAA0B;;;;;;;sDAEzC,8OAAC;4CAAE,MAAK;sFAAc;;8DACpB,8OAAC;8FAAY;;;;;;gDAA0C;8DAAC,8OAAC;8FAAe;8DAAkB;;;;;;;;;;;;sDAE5F,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,8OAAC;8FAAY;;;;;;gDAAwC;8DAAC,8OAAC;8FAAe;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhG,8OAAC;wBAAQ,IAAG;kEAAkB;kCAC5B,cAAA,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAc;sDACb,cAAA,8OAAC;gDAAwC,SAAQ;gDAAc,OAAM;0FAAtD;;kEACb,8OAAC;wDAAK,MAAK;wDAAU,aAAY;wDAAM,GAAE;wDAA2S,WAAU;;;;;;;kEAC9V,8OAAC;wDAAK,GAAE;wDAAM,GAAE;wDAAM,kBAAiB;wDAAS,YAAW;wDAAS,MAAK;wDAAU,UAAS;wDAAK,YAAW;;kEAAO;;;;;;;;;;;;;;;;;;;;;;kDAIzH,8OAAC;kFAAc;;0DACb,8OAAC;0FAAa;0DAA0B;;;;;;0DACxC,8OAAC;0FAAc;;;;;;0DACf,8OAAC;0FAAY;0DAAqC;;;;;;0DAGlD,8OAAC;0FAAY;0DAAqC;;;;;;0DAGlD,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;;;;;;kEACf,8OAAC;kGAAY;kEAA0C;;;;;;kEACvD,8OAAC;kGAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzB,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAA0B;;;;;;sDACxC,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAY;8DAA0C;;;;;;8DACvD,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEAAgG;;;;;;sEAC/G,8OAAC;sGAAc;;8EACb,8OAAC;8GAAY;8EAAgB;;;;;;8EAC7B,8OAAC;8GAAY;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAY;8DAA0C;;;;;;8DACvD,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEAAgG;;;;;;sEAC/G,8OAAC;sGAAc;;8EACb,8OAAC;8GAAY;8EAAgB;;;;;;8EAC7B,8OAAC;8GAAY;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAY;8DAA0C;;;;;;8DACvD,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEAAgG;;;;;;sEAC/G,8OAAC;sGAAc;;8EACb,8OAAC;8GAAY;8EAAgB;;;;;;8EAC7B,8OAAC;8GAAY;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAAsC;;;;;;8CACpD,8OAAC;8EAAY;8CAAgD;;;;;;8CAE7D,8OAAC;8EAAc;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,8OAAC;8FAAY;;;;;;gDAAwB;;;;;;;sDAEvC,8OAAC;4CAAE,MAAK;sFAAsB;;8DAC5B,8OAAC;8FAAY;;;;;;gDAA2C;8DAAC,8OAAC;8FAAe;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOnG,8OAAC;kEAAiB;kCAChB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEACb,cAAA,8OAAC;0GAAY;;;;;;;;;;;sEAEf,8OAAC;sGAAe;sEAAkC;;;;;;;;;;;;8DAEpD,8OAAC;8FAAY;8DAAqB;;;;;;;;;;;;sDAGpC,8OAAC;sFAAc;;8DACb,8OAAC;;;sEACC,8OAAC;sGAAa;sEAAqC;;;;;;sEACnD,8OAAC;sGAAa;;8EACZ,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAsB;kFAA0C;;;;;;;;;;;8EAC5E,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAsB;kFAA0C;;;;;;;;;;;8EAC5E,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAc;kFAA0C;;;;;;;;;;;;;;;;;;;;;;;8DAIxE,8OAAC;;;sEACC,8OAAC;sGAAa;sEAAqC;;;;;;sEACnD,8OAAC;sGAAa;;8EACZ,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAmB;kFAA0C;;;;;;;;;;;8EACzE,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAc;kFAA0C;;;;;;;;;;;8EACpE,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAc;kFAA0C;;;;;;;;;;;;;;;;;;;;;;;8DAIxE,8OAAC;;;sEACC,8OAAC;sGAAa;sEAAqC;;;;;;sEACnD,8OAAC;sGAAa;;8EACZ,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAqB;kFAA0C;;;;;;;;;;;8EAC3E,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAoB;kFAA0C;;;;;;;;;;;8EAC1E,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAc;kFAA0C;;;;;;;;;;;;;;;;;;;;;;;8DAIxE,8OAAC;;;sEACC,8OAAC;sGAAa;sEAAqC;;;;;;sEACnD,8OAAC;sGAAa;;8EACZ,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAqB;kFAA0C;;;;;;;;;;;8EAC3E,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAmB;kFAA0C;;;;;;;;;;;8EACzE,8OAAC;;8EAAG,cAAA,8OAAC;wEAAE,MAAK;kHAAsB;kFAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKlF,8OAAC;sFAAc;;8DACb,8OAAC;oDAAE,MAAK;8FAAc;8DACpB,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;8FAAc;8DACpB,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;8FAAc;8DACpB,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDAAE,MAAK;8FAAc;8DACpB,cAAA,8OAAC;kGAAY;;;;;;;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAY;sDAAqC;;;;;;sDAClD,8OAAC;sFAAc;;8DACb,8OAAC;oDAAE,MAAK;8FAAqB;8DAA0C;;;;;;8DACvE,8OAAC;oDAAE,MAAK;8FAAmB;8DAA0C;;;;;;8DACrE,8OAAC;oDAAE,MAAK;8FAAc;8DAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMxE,8OAAC;kEAAiB;kCAChB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;;;8DACC,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;;;8DACC,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAa;;sEACZ,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAmB;0EAAsC;;;;;;;;;;;sEACrE,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAsB;0EAAsC;;;;;;;;;;;sEACxE,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAsB;0EAAsC;;;;;;;;;;;;;;;;;;;;;;;sDAG5E,8OAAC;;;8DACC,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAa;;sEACZ,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAqB;0EAAsC;;;;;;;;;;;sEACvE,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAc;0EAAsC;;;;;;;;;;;sEAChE,8OAAC;;sEAAG,cAAA,8OAAC;gEAAE,MAAK;0GAAc;0EAAsC;;;;;;;;;;;;;;;;;;;;;;;sDAGpE,8OAAC;;;8DACC,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAc;;sEACb,8OAAC;4DAAE,MAAK;sGAAc;sEACpB,cAAA,8OAAC;0GAAY;;;;;;;;;;;sEAEf,8OAAC;4DAAE,MAAK;sGAAc;sEACpB,cAAA,8OAAC;0GAAY;;;;;;;;;;;sEAEf,8OAAC;4DAAE,MAAK;sGAAc;sEACpB,cAAA,8OAAC;0GAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKrB,8OAAC;8EAAc;8CACb,cAAA,8OAAC;;;4CAAE;4CAAQ,IAAI,OAAO,WAAW;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}]}