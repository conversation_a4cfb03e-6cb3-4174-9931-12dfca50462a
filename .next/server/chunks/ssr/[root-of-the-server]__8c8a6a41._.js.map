{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function HomePage() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);\n          font-family: 'Montserrat', sans-serif;\n          min-height: 100vh;\n        }\n\n        .hero-gradient {\n          background: linear-gradient(135deg,\n            rgba(30, 30, 36, 0.95) 0%,\n            rgba(45, 27, 78, 0.9) 25%,\n            rgba(61, 42, 95, 0.85) 50%,\n            rgba(78, 58, 112, 0.8) 75%,\n            rgba(45, 27, 78, 0.9) 100%);\n        }\n\n        .section-gradient {\n          background: linear-gradient(135deg,\n            rgba(30, 30, 36, 0.8) 0%,\n            rgba(45, 27, 78, 0.7) 50%,\n            rgba(61, 42, 95, 0.6) 100%);\n        }\n\n        .card-gradient {\n          background: linear-gradient(135deg,\n            rgba(61, 42, 95, 0.4) 0%,\n            rgba(78, 58, 112, 0.3) 50%,\n            rgba(45, 27, 78, 0.4) 100%);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(212, 175, 55, 0.2);\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .feature-card {\n          transition: all 0.4s ease;\n          background: linear-gradient(135deg,\n            rgba(61, 42, 95, 0.6) 0%,\n            rgba(78, 58, 112, 0.4) 50%,\n            rgba(45, 27, 78, 0.6) 100%);\n          backdrop-filter: blur(15px);\n          border: 1px solid rgba(212, 175, 55, 0.3);\n        }\n\n        .feature-card:hover {\n          transform: translateY(-8px) scale(1.02);\n          box-shadow: 0 20px 40px -10px rgba(212, 175, 55, 0.3);\n          background: linear-gradient(135deg,\n            rgba(61, 42, 95, 0.8) 0%,\n            rgba(78, 58, 112, 0.6) 50%,\n            rgba(45, 27, 78, 0.8) 100%);\n        }\n\n        .btn-hover {\n          transition: all 0.3s ease;\n          position: relative;\n          overflow: hidden;\n        }\n\n        .btn-hover:hover {\n          transform: translateY(-3px);\n          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);\n        }\n\n        .btn-hover::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n          transition: left 0.5s;\n        }\n\n        .btn-hover:hover::before {\n          left: 100%;\n        }\n\n        .hero-pattern {\n          background-image:\n            radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),\n            radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%),\n            url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n\n        .nav-gradient {\n          background: linear-gradient(135deg,\n            rgba(30, 30, 36, 0.95) 0%,\n            rgba(45, 27, 78, 0.9) 100%);\n          backdrop-filter: blur(20px);\n          border-bottom: 1px solid rgba(212, 175, 55, 0.2);\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen\">\n        <div className=\"hero-pattern min-h-screen\">\n          {/* Navigation */}\n          <nav className=\"nav-gradient fixed w-full z-10\">\n            <div className=\"container mx-auto px-6 py-4 flex justify-between items-center\">\n              <div className=\"flex items-center\">\n                <div className=\"text-yellow-400 text-3xl mr-3\">\n                  <i className=\"fas fa-comment-dollar\"></i>\n                </div>\n                <span className=\"font-bold text-2xl gold-gradient\">BoGuani</span>\n              </div>\n              <div className=\"hidden md:flex space-x-8\">\n                <a href=\"#features\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Features</a>\n                <a href=\"#about\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">About</a>\n                <a href=\"#download\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Download</a>\n                <a href=\"/privacy\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Privacy</a>\n              </div>\n              <div className=\"md:hidden\">\n                <button\n                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n                  className=\"text-white focus:outline-none p-2\"\n                >\n                  <i className=\"fas fa-bars text-xl\"></i>\n                </button>\n              </div>\n            </div>\n            {/* Mobile menu */}\n            {mobileMenuOpen && (\n              <div className=\"md:hidden section-gradient border-t border-yellow-400 border-opacity-20\">\n                <div className=\"container mx-auto px-6 py-4 flex flex-col space-y-4\">\n                  <a href=\"#features\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Features</a>\n                  <a href=\"#about\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">About</a>\n                  <a href=\"#download\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Download</a>\n                  <a href=\"/privacy\" className=\"hover:text-yellow-400 transition-all duration-300 font-medium\">Privacy</a>\n                </div>\n              </div>\n            )}\n          </nav>\n\n          {/* Hero Section */}\n          <header className=\"hero-gradient pt-32 pb-24 px-6 min-h-screen flex items-center\">\n            <div className=\"container mx-auto flex flex-col md:flex-row items-center relative\">\n              {/* Floating background elements */}\n              <div className=\"absolute top-10 left-10 w-32 h-32 bg-yellow-400 opacity-5 rounded-full blur-3xl animate-pulse\"></div>\n              <div className=\"absolute bottom-20 right-20 w-40 h-40 bg-yellow-400 opacity-3 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '2s'}}></div>\n\n              <div className=\"md:w-1/2 mb-12 md:mb-0 relative z-10\">\n                <div className=\"mb-6\">\n                  <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n                    <span className=\"gold-gradient drop-shadow-lg\">BoGuani</span>\n                  </h1>\n                  <h2 className=\"text-3xl md:text-4xl font-semibold mb-4 text-gray-100\">Messenger of Value</h2>\n                  <p className=\"text-2xl mb-8 text-yellow-200 italic font-medium\">Where Words Carry Worth</p>\n                </div>\n\n                <div className=\"card-gradient p-6 rounded-2xl mb-8\">\n                  <p className=\"mb-6 text-gray-200 text-lg leading-relaxed\">\n                    Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.\n                    Send messages that matter, share moments that count, and transfer value instantly -\n                    all protected by sacred-level encryption.\n                  </p>\n                  <div className=\"text-center\">\n                    <p className=\"text-2xl font-bold gold-gradient\">\"Speak Gold. Share Value.\"</p>\n                  </div>\n                </div>\n\n                <div className=\"flex flex-wrap gap-4 justify-center md:justify-start\">\n                  <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-800 px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center shadow-lg\">\n                    <i className=\"fas fa-globe mr-3\"></i> Open BoGuani Web Version\n                  </Link>\n                  <a href=\"#features\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center\">\n                    <i className=\"fas fa-play mr-3 text-yellow-400\"></i> <span className=\"text-yellow-400\">Watch Demo</span>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"md:w-1/2 flex justify-center relative z-10\">\n                <div className=\"relative w-80 h-96\">\n                  {/* App mockup */}\n                  <div className=\"absolute inset-0 card-gradient rounded-3xl gold-border shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-500\">\n                    <div className=\"section-gradient p-4 border-b border-yellow-400 border-opacity-40\">\n                      <div className=\"flex justify-between items-center\">\n                        <div className=\"text-yellow-400 text-xl\">\n                          <i className=\"fas fa-comment-dollar\"></i>\n                        </div>\n                        <p className=\"font-bold text-lg gold-gradient\">BoGuani</p>\n                        <div className=\"text-yellow-400\">\n                          <i className=\"fas fa-ellipsis-v\"></i>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"p-4 h-full bg-gradient-to-b from-transparent to-gray-800 to-opacity-20\">\n                      <div className=\"flex flex-col h-full space-y-3\">\n                        <div className=\"card-gradient p-3 rounded-xl self-start max-w-[75%] border border-purple-400 border-opacity-30\">\n                          <p className=\"text-sm text-gray-200\">Hey! Can you send me 20 for dinner tonight?</p>\n                        </div>\n                        <div className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-opacity-20 p-3 rounded-xl self-end max-w-[75%] border border-yellow-400 border-opacity-40\">\n                          <p className=\"text-sm text-gray-100\">Sure! Sending it now with a special message.</p>\n                        </div>\n                        <div className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-opacity-30 p-3 rounded-xl self-end max-w-[75%] border border-yellow-400 border-opacity-50\">\n                          <div className=\"flex items-center\">\n                            <div className=\"text-yellow-400 mr-2\">\n                              <i className=\"fas fa-coins\"></i>\n                            </div>\n                            <p className=\"text-sm font-semibold text-gray-100\">$20.00 sent - Enjoy dinner!</p>\n                          </div>\n                        </div>\n                        <div className=\"card-gradient p-3 rounded-xl self-start max-w-[75%] border border-purple-400 border-opacity-30\">\n                          <p className=\"text-sm text-gray-200\">Thanks! Value received. 🙏</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Enhanced decorative elements */}\n                  <div className=\"absolute -bottom-8 -right-8 w-48 h-48 bg-gradient-to-r from-yellow-400 to-yellow-200 opacity-10 rounded-full blur-3xl animate-pulse\"></div>\n                  <div className=\"absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-r from-yellow-400 to-yellow-200 opacity-15 rounded-full blur-2xl animate-pulse\" style={{animationDelay: '1s'}}></div>\n                  <div className=\"absolute top-1/2 -right-4 w-16 h-16 bg-yellow-400 opacity-20 rounded-full blur-xl animate-bounce\" style={{animationDelay: '3s'}}></div>\n                </div>\n              </div>\n            </div>\n          </header>\n        </div>\n\n        {/* Features Section */}\n        <section id=\"features\" className=\"py-20 section-gradient relative overflow-hidden\">\n          {/* Background decorative elements */}\n          <div className=\"absolute top-0 left-1/4 w-64 h-64 bg-yellow-400 opacity-5 rounded-full blur-3xl\"></div>\n          <div className=\"absolute bottom-0 right-1/4 w-48 h-48 bg-yellow-400 opacity-3 rounded-full blur-3xl\"></div>\n\n          <div className=\"container mx-auto px-6 relative z-10\">\n            <div className=\"text-center mb-20\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">Key Features</h2>\n              <div className=\"w-24 h-1 bg-gradient-to-r from-yellow-400 to-yellow-200 mx-auto mb-4\"></div>\n              <p className=\"text-xl text-gray-300 max-w-2xl mx-auto\">Experience the future of value-based communication</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"feature-card p-8 rounded-2xl relative overflow-hidden group\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\">\n                    <i className=\"fas fa-lock\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">256-bit End-to-End Encryption</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>\n                </div>\n              </div>\n\n              <div className=\"feature-card p-8 rounded-2xl relative overflow-hidden group\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\">\n                    <i className=\"fas fa-bolt\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">Instant Money Transfers</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>\n                </div>\n              </div>\n\n              <div className=\"feature-card p-8 rounded-2xl relative overflow-hidden group\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500\"></div>\n                <div className=\"relative z-10\">\n                  <div className=\"text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300\">\n                    <i className=\"fas fa-headset\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">24/7 Global Support</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Download Section */}\n        <section id=\"download\" className=\"py-20 hero-gradient relative overflow-hidden\">\n          {/* Background decorative elements */}\n          <div className=\"absolute top-1/2 left-0 w-72 h-72 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-y-1/2\"></div>\n          <div className=\"absolute top-1/2 right-0 w-72 h-72 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-y-1/2\"></div>\n\n          <div className=\"container mx-auto px-6 relative z-10\">\n            <div className=\"text-center mb-20\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">Get BoGuani Now</h2>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto mb-6\">Experience the revolution in value-based messaging. Available on all major platforms.</p>\n              <div className=\"w-24 h-1 bg-gradient-to-r from-yellow-400 to-yellow-200 mx-auto\"></div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto\">\n              <a href=\"/downloads/ios\" className=\"card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group\">\n                <i className=\"fab fa-apple text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300\"></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg text-yellow-200\">iOS</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </a>\n\n              <a href=\"/downloads/android\" className=\"card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group\">\n                <i className=\"fab fa-android text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300\"></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg text-yellow-200\">Android</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </a>\n\n              <a href=\"/downloads/windows\" className=\"card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group\">\n                <i className=\"fas fa-desktop text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300\"></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg text-yellow-200\">Windows</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </a>\n\n              <a href=\"/downloads/mac\" className=\"card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group\">\n                <i className=\"fas fa-laptop text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300\"></i>\n                <div>\n                  <p className=\"text-sm text-gray-400 mb-1\">Download for</p>\n                  <p className=\"font-bold text-lg text-yellow-200\">macOS</p>\n                  <p className=\"text-xs text-gray-500 mt-2\">Coming Soon</p>\n                </div>\n              </a>\n            </div>\n          </div>\n        </section>\n\n        {/* Footer */}\n        <footer className=\"section-gradient py-12 border-t border-yellow-400 border-opacity-20\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-4\">\n                <div className=\"text-yellow-400 text-2xl mr-3\">\n                  <i className=\"fas fa-comment-dollar\"></i>\n                </div>\n                <span className=\"font-bold text-xl gold-gradient\">BoGuani</span>\n              </div>\n              <p className=\"text-gray-400 mb-4\">Messenger of Value - Where Words Carry Worth</p>\n              <p className=\"text-gray-500 text-sm\">© 2024 BoGuani. All rights reserved.</p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE;;;;;;0BA+GE,8OAAC;yDAAc;;kCACb,8OAAC;iEAAc;;0CAEb,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAc;;kEACb,8OAAC;iGAAc;kEACb,cAAA,8OAAC;qGAAY;;;;;;;;;;;kEAEf,8OAAC;iGAAe;kEAAmC;;;;;;;;;;;;0DAErD,8OAAC;yFAAc;;kEACb,8OAAC;wDAAE,MAAK;iGAAsB;kEAAgE;;;;;;kEAC9F,8OAAC;wDAAE,MAAK;iGAAmB;kEAAgE;;;;;;kEAC3F,8OAAC;wDAAE,MAAK;iGAAsB;kEAAgE;;;;;;kEAC9F,8OAAC;wDAAE,MAAK;iGAAqB;kEAAgE;;;;;;;;;;;;0DAE/F,8OAAC;yFAAc;0DACb,cAAA,8OAAC;oDACC,SAAS,IAAM,kBAAkB,CAAC;6FACxB;8DAEV,cAAA,8OAAC;iGAAY;;;;;;;;;;;;;;;;;;;;;;oCAKlB,gCACC,8OAAC;iFAAc;kDACb,cAAA,8OAAC;qFAAc;;8DACb,8OAAC;oDAAE,MAAK;6FAAsB;8DAAgE;;;;;;8DAC9F,8OAAC;oDAAE,MAAK;6FAAmB;8DAAgE;;;;;;8DAC3F,8OAAC;oDAAE,MAAK;6FAAsB;8DAAgE;;;;;;8DAC9F,8OAAC;oDAAE,MAAK;6FAAqB;8DAAgE;;;;;;;;;;;;;;;;;;;;;;;0CAOrG,8OAAC;yEAAiB;0CAChB,cAAA,8OAAC;6EAAc;;sDAEb,8OAAC;qFAAc;;;;;;sDACf,8OAAC;4CAAkH,OAAO;gDAAC,gBAAgB;4CAAI;qFAAhI;;;;;;sDAEf,8OAAC;qFAAc;;8DACb,8OAAC;6FAAc;;sEACb,8OAAC;qGAAa;sEACZ,cAAA,8OAAC;yGAAe;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;qGAAa;sEAAwD;;;;;;sEACtE,8OAAC;qGAAY;sEAAmD;;;;;;;;;;;;8DAGlE,8OAAC;6FAAc;;sEACb,8OAAC;qGAAY;sEAA6C;;;;;;sEAK1D,8OAAC;qGAAc;sEACb,cAAA,8OAAC;yGAAY;0EAAmC;;;;;;;;;;;;;;;;;8DAIpD,8OAAC;6FAAc;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;;8EAC3B,8OAAC;6GAAY;;;;;;gEAAwB;;;;;;;sEAEvC,8OAAC;4DAAE,MAAK;qGAAsB;;8EAC5B,8OAAC;6GAAY;;;;;;gEAAuC;8EAAC,8OAAC;6GAAe;8EAAkB;;;;;;;;;;;;;;;;;;;;;;;;sDAK7F,8OAAC;qFAAc;sDACb,cAAA,8OAAC;yFAAc;;kEAEb,8OAAC;iGAAc;;0EACb,8OAAC;yGAAc;0EACb,cAAA,8OAAC;6GAAc;;sFACb,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAY;;;;;;;;;;;sFAEf,8OAAC;qHAAY;sFAAkC;;;;;;sFAC/C,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAY;;;;;;;;;;;;;;;;;;;;;;0EAInB,8OAAC;yGAAc;0EACb,cAAA,8OAAC;6GAAc;;sFACb,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAY;0FAAwB;;;;;;;;;;;sFAEvC,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAY;0FAAwB;;;;;;;;;;;sFAEvC,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAc;;kGACb,8OAAC;iIAAc;kGACb,cAAA,8OAAC;qIAAY;;;;;;;;;;;kGAEf,8OAAC;iIAAY;kGAAsC;;;;;;;;;;;;;;;;;sFAGvD,8OAAC;qHAAc;sFACb,cAAA,8OAAC;yHAAY;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAM7C,8OAAC;iGAAc;;;;;;kEACf,8OAAC;wDAAgJ,OAAO;4DAAC,gBAAgB;wDAAI;iGAA9J;;;;;;kEACf,8OAAC;wDAAiH,OAAO;4DAAC,gBAAgB;wDAAI;iGAA/H;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzB,8OAAC;wBAAQ,IAAG;iEAAqB;;0CAE/B,8OAAC;yEAAc;;;;;;0CACf,8OAAC;yEAAc;;;;;;0CAEf,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAa;0DAAoD;;;;;;0DAClE,8OAAC;yFAAc;;;;;;0DACf,8OAAC;yFAAY;0DAA0C;;;;;;;;;;;;kDAGzD,8OAAC;iFAAc;;0DACb,8OAAC;yFAAc;;kEACb,8OAAC;iGAAc;;;;;;kEACf,8OAAC;iGAAc;;0EACb,8OAAC;yGAAc;0EACb,cAAA,8OAAC;6GAAY;;;;;;;;;;;0EAEf,8OAAC;yGAAa;0EAA0C;;;;;;0EACxD,8OAAC;yGAAY;0EAAgC;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;yFAAc;;kEACb,8OAAC;iGAAc;;;;;;kEACf,8OAAC;iGAAc;;0EACb,8OAAC;yGAAc;0EACb,cAAA,8OAAC;6GAAY;;;;;;;;;;;0EAEf,8OAAC;yGAAa;0EAA0C;;;;;;0EACxD,8OAAC;yGAAY;0EAAgC;;;;;;;;;;;;;;;;;;0DAIjD,8OAAC;yFAAc;;kEACb,8OAAC;iGAAc;;;;;;kEACf,8OAAC;iGAAc;;0EACb,8OAAC;yGAAc;0EACb,cAAA,8OAAC;6GAAY;;;;;;;;;;;0EAEf,8OAAC;yGAAa;0EAA0C;;;;;;0EACxD,8OAAC;yGAAY;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,8OAAC;wBAAQ,IAAG;iEAAqB;;0CAE/B,8OAAC;yEAAc;;;;;;0CACf,8OAAC;yEAAc;;;;;;0CAEf,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAa;0DAAoD;;;;;;0DAClE,8OAAC;yFAAY;0DAA+C;;;;;;0DAC5D,8OAAC;yFAAc;;;;;;;;;;;;kDAGjB,8OAAC;iFAAc;;0DACb,8OAAC;gDAAE,MAAK;yFAA2B;;kEACjC,8OAAC;iGAAY;;;;;;kEACb,8OAAC;;;0EACC,8OAAC;yGAAY;0EAA6B;;;;;;0EAC1C,8OAAC;yGAAY;0EAAoC;;;;;;0EACjD,8OAAC;yGAAY;0EAA6B;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAE,MAAK;yFAA+B;;kEACrC,8OAAC;iGAAY;;;;;;kEACb,8OAAC;;;0EACC,8OAAC;yGAAY;0EAA6B;;;;;;0EAC1C,8OAAC;yGAAY;0EAAoC;;;;;;0EACjD,8OAAC;yGAAY;0EAA6B;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAE,MAAK;yFAA+B;;kEACrC,8OAAC;iGAAY;;;;;;kEACb,8OAAC;;;0EACC,8OAAC;yGAAY;0EAA6B;;;;;;0EAC1C,8OAAC;yGAAY;0EAAoC;;;;;;0EACjD,8OAAC;yGAAY;0EAA6B;;;;;;;;;;;;;;;;;;0DAI9C,8OAAC;gDAAE,MAAK;yFAA2B;;kEACjC,8OAAC;iGAAY;;;;;;kEACb,8OAAC;;;0EACC,8OAAC;yGAAY;0EAA6B;;;;;;0EAC1C,8OAAC;yGAAY;0EAAoC;;;;;;0EACjD,8OAAC;yGAAY;0EAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;iEAAiB;kCAChB,cAAA,8OAAC;qEAAc;sCACb,cAAA,8OAAC;yEAAc;;kDACb,8OAAC;iFAAc;;0DACb,8OAAC;yFAAc;0DACb,cAAA,8OAAC;6FAAY;;;;;;;;;;;0DAEf,8OAAC;yFAAe;0DAAkC;;;;;;;;;;;;kDAEpD,8OAAC;iFAAY;kDAAqB;;;;;;kDAClC,8OAAC;iFAAY;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}