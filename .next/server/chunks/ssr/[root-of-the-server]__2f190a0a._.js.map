{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/downloads/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function DownloadsPage() {\n  return (\n    <div className=\"min-h-screen text-white\" style={{\n      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',\n      fontFamily: 'Montserrat, sans-serif'\n    }}>\n      <div className=\"relative min-h-screen\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',\n        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'\n      }}>\n        {/* Navigation */}\n        <nav className=\"fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20\">\n          <div className=\"container mx-auto px-6 py-4 flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <Link href=\"/\" className=\"flex items-center\">\n                <div className=\"text-yellow-500 text-3xl mr-3\">\n                  <i className=\"fas fa-comment-dollar\"></i>\n                </div>\n                <span className=\"font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                  BoGuani\n                </span>\n              </Link>\n            </div>\n            <div className=\"flex space-x-6\">\n              <Link href=\"/\" className=\"hover:text-yellow-400 transition-colors\">Home</Link>\n              <Link href=\"/support\" className=\"hover:text-yellow-400 transition-colors\">Support</Link>\n              <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all\">\n                Open Web App\n              </Link>\n            </div>\n          </div>\n        </nav>\n\n        {/* Main Content */}\n        <div className=\"pt-24 pb-16 px-6\">\n          <div className=\"container mx-auto max-w-6xl\">\n            {/* Header */}\n            <div className=\"text-center mb-16\">\n              <div className=\"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                <i className=\"fas fa-download text-3xl text-purple-900\"></i>\n              </div>\n              <h1 className=\"text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                Download BoGuani\n              </h1>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Get BoGuani on all your devices and start experiencing the future of value-based communication.\n              </p>\n            </div>\n\n            {/* Download Options */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n              {/* Mobile Apps */}\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300\"\n                initial={{ opacity: 0, y: 50 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8 }}\n              >\n                <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                  <i className=\"fab fa-apple text-2xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">iOS App</h3>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  Download BoGuani for iPhone and iPad. Optimized for iOS with native performance and seamless integration.\n                </p>\n                <Link href=\"/downloads/ios\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block\">\n                  <i className=\"fab fa-app-store mr-2\"></i>\n                  App Store\n                </Link>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300\"\n                initial={{ opacity: 0, y: 50 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.1 }}\n              >\n                <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                  <i className=\"fab fa-android text-2xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">Android App</h3>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  Get BoGuani for Android devices. Full feature support with Material Design and Android-specific optimizations.\n                </p>\n                <Link href=\"/downloads/android\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block\">\n                  <i className=\"fab fa-google-play mr-2\"></i>\n                  Google Play\n                </Link>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300\"\n                initial={{ opacity: 0, y: 50 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n              >\n                <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                  <i className=\"fas fa-desktop text-2xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">Desktop App</h3>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  Download BoGuani for Windows, macOS, and Linux. Full desktop experience with keyboard shortcuts and multi-window support.\n                </p>\n                <Link href=\"/downloads/desktop\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block\">\n                  <i className=\"fas fa-download mr-2\"></i>\n                  Download\n                </Link>\n              </motion.div>\n            </div>\n\n            {/* Web App */}\n            <div className=\"text-center mb-16\">\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto\"\n                initial={{ opacity: 0, y: 50 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n              >\n                <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                  <i className=\"fas fa-globe text-2xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 text-yellow-200\">Web App</h3>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  Use BoGuani directly in your browser. No download required - access all features instantly from any device with an internet connection.\n                </p>\n                <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block\">\n                  <i className=\"fas fa-rocket mr-2\"></i>\n                  Launch Web App\n                </Link>\n              </motion.div>\n            </div>\n\n            {/* Features */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n              {[\n                {\n                  icon: 'fas fa-sync',\n                  title: 'Cross-Platform Sync',\n                  description: 'Your messages and data sync seamlessly across all devices'\n                },\n                {\n                  icon: 'fas fa-shield-alt',\n                  title: 'End-to-End Encryption',\n                  description: 'Military-grade security on every platform'\n                },\n                {\n                  icon: 'fas fa-bolt',\n                  title: 'Lightning Fast',\n                  description: 'Optimized performance on all devices'\n                },\n                {\n                  icon: 'fas fa-mobile-alt',\n                  title: 'Mobile First',\n                  description: 'Designed for mobile with desktop power'\n                }\n              ].map((feature, index) => (\n                <motion.div\n                  key={index}\n                  className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center\"\n                  initial={{ opacity: 0, y: 50 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.8, delay: 0.4 + index * 0.1 }}\n                >\n                  <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className={`${feature.icon} text-purple-900`}></i>\n                  </div>\n                  <h3 className=\"font-bold mb-2 text-yellow-200\">{feature.title}</h3>\n                  <p className=\"text-gray-300 text-sm\">{feature.description}</p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* System Requirements */}\n            <div className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20\">\n              <h2 className=\"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent text-center\">\n                System Requirements\n              </h2>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                <div>\n                  <h3 className=\"text-xl font-semibold mb-4 text-yellow-200\">\n                    <i className=\"fab fa-apple mr-2\"></i>\n                    iOS\n                  </h3>\n                  <ul className=\"space-y-2 text-gray-300\">\n                    <li>• iOS 14.0 or later</li>\n                    <li>• iPhone 6s or newer</li>\n                    <li>• iPad (5th generation) or newer</li>\n                    <li>• 100 MB free storage</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"text-xl font-semibold mb-4 text-yellow-200\">\n                    <i className=\"fab fa-android mr-2\"></i>\n                    Android\n                  </h3>\n                  <ul className=\"space-y-2 text-gray-300\">\n                    <li>• Android 7.0 (API level 24)</li>\n                    <li>• 2 GB RAM minimum</li>\n                    <li>• ARMv7 or ARM64 processor</li>\n                    <li>• 150 MB free storage</li>\n                  </ul>\n                </div>\n\n                <div>\n                  <h3 className=\"text-xl font-semibold mb-4 text-yellow-200\">\n                    <i className=\"fas fa-desktop mr-2\"></i>\n                    Desktop\n                  </h3>\n                  <ul className=\"space-y-2 text-gray-300\">\n                    <li>• Windows 10, macOS 10.14, or Linux</li>\n                    <li>• 4 GB RAM minimum</li>\n                    <li>• 500 MB free storage</li>\n                    <li>• Internet connection required</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n\n            {/* Call to Action */}\n            <div className=\"text-center mt-16\">\n              <h2 className=\"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                Ready to Get Started?\n              </h2>\n              <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n                Choose your platform and start experiencing the future of value-based messaging today.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all\">\n                  <i className=\"fas fa-rocket mr-2\"></i>\n                  Try Web App Now\n                </Link>\n                <Link href=\"/support\" className=\"border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all\">\n                  <i className=\"fas fa-question-circle mr-2\"></i>\n                  Need Help?\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;QAA0B,OAAO;YAC9C,YAAY;YACZ,YAAY;QACd;kBACE,cAAA,8OAAC;YAAI,WAAU;YAAwB,OAAO;gBAC5C,YAAY;gBACZ,iBAAiB;YACnB;;8BAEE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAK,WAAU;sDAAkG;;;;;;;;;;;;;;;;;0CAKtH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAA0C;;;;;;kDACnE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA0C;;;;;;kDAC1E,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAA+J;;;;;;;;;;;;;;;;;;;;;;;8BAQlM,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,8OAAC;wCAAG,WAAU;kDAAmH;;;;;;kDAGjI,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;;0CAMzD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;;kEACpC,8OAAC;wDAAE,WAAU;;;;;;oDAA4B;;;;;;;;;;;;;kDAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;kEACxC,8OAAC;wDAAE,WAAU;;;;;;oDAA8B;;;;;;;;;;;;;kDAK/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;;kEACxC,8OAAC;wDAAE,WAAU;;;;;;oDAA2B;;;;;;;;;;;;;;;;;;;0CAO9C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;sDAEf,8OAAC;4CAAG,WAAU;sDAA0C;;;;;;sDACxD,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAGlD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,8OAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;;;;;;;;;;;;0CAO5C,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;oCACA;wCACE,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;iCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;;0DAEtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAW,GAAG,QAAQ,IAAI,CAAC,gBAAgB,CAAC;;;;;;;;;;;0DAEjD,8OAAC;gDAAG,WAAU;0DAAkC,QAAQ,KAAK;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;0DAAyB,QAAQ,WAAW;;;;;;;uCAVpD;;;;;;;;;;0CAgBX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmH;;;;;;kDAIjI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAE,WAAU;;;;;;4DAAwB;;;;;;;kEAGvC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAE,WAAU;;;;;;4DAA0B;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAE,WAAU;;;;;;4DAA0B;;;;;;;kEAGzC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;;kEAC3B,8OAAC;wDAAE,WAAU;;;;;;oDAAyB;;;;;;;0DAGxC,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;;kEAC9B,8OAAC;wDAAE,WAAU;;;;;;oDAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE", "debugId": null}}]}