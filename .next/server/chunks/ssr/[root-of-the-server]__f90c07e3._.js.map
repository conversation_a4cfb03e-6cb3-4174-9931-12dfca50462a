{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/careers/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\n\nexport default function CareersPage() {\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n        \n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n        \n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n        \n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n        \n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen hero-pattern\">\n        {/* Navigation */}\n        <nav className=\"bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10\">\n          <div className=\"container mx-auto px-6 py-3 flex justify-between items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-yellow-400 text-3xl mr-2\">\n                <i className=\"fas fa-comment-dollar\"></i>\n              </div>\n              <span className=\"font-bold text-2xl gold-gradient\">BoGuani</span>\n            </Link>\n            <div className=\"hidden md:flex space-x-8\">\n              <Link href=\"/#features\" className=\"hover:text-yellow-400 transition-colors\">Features</Link>\n              <Link href=\"/#about\" className=\"hover:text-yellow-400 transition-colors\">About</Link>\n              <Link href=\"/#download\" className=\"hover:text-yellow-400 transition-colors\">Download</Link>\n              <Link href=\"/support\" className=\"hover:text-yellow-400 transition-colors\">Support</Link>\n            </div>\n          </div>\n        </nav>\n\n        {/* Hero Section */}\n        <section className=\"pt-24 pb-16\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h1 className=\"text-5xl lg:text-6xl font-bold mb-6 gold-gradient\">Join Our Team</h1>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Help us build the future of value-based communication. Join a team that's revolutionizing how people connect and share value.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Company Values */}\n        <section className=\"py-20 bg-purple-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold mb-4\">Our Values</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"bg-purple-800 p-8 rounded-xl text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-heart\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">Sacred Trust</h3>\n                <p className=\"text-gray-300\">We protect our users' privacy and security with the same reverence ancient cultures held for sacred communication.</p>\n              </div>\n              \n              <div className=\"bg-purple-800 p-8 rounded-xl text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-lightbulb\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">Innovation</h3>\n                <p className=\"text-gray-300\">We constantly push boundaries to create meaningful connections between communication and value exchange.</p>\n              </div>\n              \n              <div className=\"bg-purple-800 p-8 rounded-xl text-center\">\n                <div className=\"text-yellow-400 text-5xl mb-6\">\n                  <i className=\"fas fa-users\"></i>\n                </div>\n                <h3 className=\"text-xl font-semibold mb-4\">Community</h3>\n                <p className=\"text-gray-300\">We believe in building inclusive teams that reflect the diverse communities we serve worldwide.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Open Positions */}\n        <section className=\"py-20 bg-gray-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold mb-4\">Open Positions</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n            \n            <div className=\"max-w-4xl mx-auto space-y-6\">\n              <div className=\"bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h3 className=\"text-xl font-semibold mb-2 text-yellow-400\">Senior Full-Stack Developer</h3>\n                    <p className=\"text-gray-300 mb-4\">Help build our core messaging and payment infrastructure using React, Node.js, and blockchain technologies.</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">React</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Node.js</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">TypeScript</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">WebRTC</span>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-gray-400 text-sm\">Remote</p>\n                    <p className=\"text-gray-400 text-sm\">Full-time</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h3 className=\"text-xl font-semibold mb-2 text-yellow-400\">Security Engineer</h3>\n                    <p className=\"text-gray-300 mb-4\">Lead our security initiatives, implement end-to-end encryption, and ensure the highest standards of user privacy.</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Cryptography</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Security</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Penetration Testing</span>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-gray-400 text-sm\">San Francisco</p>\n                    <p className=\"text-gray-400 text-sm\">Full-time</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h3 className=\"text-xl font-semibold mb-2 text-yellow-400\">Product Designer</h3>\n                    <p className=\"text-gray-300 mb-4\">Design intuitive user experiences that make complex financial communications feel natural and beautiful.</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">UI/UX</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Figma</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Mobile Design</span>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-gray-400 text-sm\">Remote</p>\n                    <p className=\"text-gray-400 text-sm\">Full-time</p>\n                  </div>\n                </div>\n              </div>\n              \n              <div className=\"bg-purple-800 rounded-xl p-6 hover:bg-purple-700 transition-colors\">\n                <div className=\"flex justify-between items-start\">\n                  <div>\n                    <h3 className=\"text-xl font-semibold mb-2 text-yellow-400\">DevOps Engineer</h3>\n                    <p className=\"text-gray-300 mb-4\">Scale our infrastructure to handle millions of secure messages and transactions worldwide.</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">AWS</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Kubernetes</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Docker</span>\n                      <span className=\"bg-yellow-400/20 text-yellow-400 px-3 py-1 rounded-full text-sm\">Terraform</span>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"text-gray-400 text-sm\">Remote</p>\n                    <p className=\"text-gray-400 text-sm\">Full-time</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Benefits */}\n        <section className=\"py-20 bg-purple-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-3xl font-bold mb-4\">Why Work With Us</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto\"></div>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-yellow-400 text-4xl mb-4\">\n                  <i className=\"fas fa-home\"></i>\n                </div>\n                <h3 className=\"font-semibold mb-2\">Remote First</h3>\n                <p className=\"text-gray-400 text-sm\">Work from anywhere in the world</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-yellow-400 text-4xl mb-4\">\n                  <i className=\"fas fa-heart\"></i>\n                </div>\n                <h3 className=\"font-semibold mb-2\">Health & Wellness</h3>\n                <p className=\"text-gray-400 text-sm\">Comprehensive health coverage</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-yellow-400 text-4xl mb-4\">\n                  <i className=\"fas fa-graduation-cap\"></i>\n                </div>\n                <h3 className=\"font-semibold mb-2\">Learning Budget</h3>\n                <p className=\"text-gray-400 text-sm\">$2,000 annual learning allowance</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"text-yellow-400 text-4xl mb-4\">\n                  <i className=\"fas fa-chart-line\"></i>\n                </div>\n                <h3 className=\"font-semibold mb-2\">Equity Package</h3>\n                <p className=\"text-gray-400 text-sm\">Share in our success</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* CTA Section */}\n        <section className=\"py-20 bg-gradient-to-b from-purple-900 to-gray-900\">\n          <div className=\"container mx-auto px-6 text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">Ready to Join the Revolution?</h2>\n            <p className=\"text-xl text-gray-300 mb-10 max-w-2xl mx-auto\">Don't see a position that fits? We're always looking for exceptional talent.</p>\n            \n            <div className=\"flex flex-wrap justify-center gap-6\">\n              <Link href=\"/contact\" className=\"bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all hover:scale-105 flex items-center\">\n                <i className=\"fas fa-envelope mr-2\"></i> Send Us Your Resume\n              </Link>\n              <Link href=\"/support\" className=\"gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all hover:scale-105 flex items-center\">\n                <i className=\"fas fa-question-circle mr-2 text-yellow-400\"></i> <span className=\"text-yellow-400\">Have Questions?</span>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Footer */}\n        <footer className=\"bg-gray-900 py-12\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"flex flex-col md:flex-row justify-between items-center\">\n              <div className=\"mb-6 md:mb-0\">\n                <Link href=\"/\" className=\"flex items-center\">\n                  <div className=\"text-yellow-400 text-2xl mr-2\">\n                    <i className=\"fas fa-comment-dollar\"></i>\n                  </div>\n                  <span className=\"font-bold text-xl gold-gradient\">BoGuani</span>\n                </Link>\n                <p className=\"text-gray-400 mt-2\">Messenger of Value</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <p className=\"text-gray-400 text-sm\">© 2024 BoGuani. All rights reserved.</p>\n                <div className=\"flex space-x-6 text-gray-400 text-sm mt-2\">\n                  <Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-colors\">Privacy Policy</Link>\n                  <Link href=\"/terms\" className=\"hover:text-yellow-400 transition-colors\">Terms of Service</Link>\n                  <Link href=\"/security\" className=\"hover:text-yellow-400 transition-colors\">Security</Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAIe,SAAS;IACtB,qBACE;;;;;;0BA2BE,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;kCACb,cAAA,8OAAC;sEAAc;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;sFAAe;sDAAmC;;;;;;;;;;;;8CAErD,8OAAC;8EAAc;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAA0C;;;;;;sDAC5E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAA0C;;;;;;sDACzE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;sDAA0C;;;;;;sDAC5E,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA0C;;;;;;;;;;;;;;;;;;;;;;;kCAMhF,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAoD;;;;;;kDAClE,8OAAC;kFAAY;kDAA0C;;;;;;;;;;;;;;;;;;;;;;kCAQ7D,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAA0B;;;;;;sDACxC,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;sDAG/B,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;sDAG/B,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAA6B;;;;;;8DAC3C,8OAAC;8FAAY;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAA0B;;;;;;sDACxC,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAa;0EAA6C;;;;;;0EAC3D,8OAAC;0GAAY;0EAAqB;;;;;;0EAClC,8OAAC;0GAAc;;kFACb,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;;;;;;;;;;;;;kEAGtF,8OAAC;kGAAc;;0EACb,8OAAC;0GAAY;0EAAwB;;;;;;0EACrC,8OAAC;0GAAY;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAa;0EAA6C;;;;;;0EAC3D,8OAAC;0GAAY;0EAAqB;;;;;;0EAClC,8OAAC;0GAAc;;kFACb,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;;;;;;;;;;;;;kEAGtF,8OAAC;kGAAc;;0EACb,8OAAC;0GAAY;0EAAwB;;;;;;0EACrC,8OAAC;0GAAY;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAa;0EAA6C;;;;;;0EAC3D,8OAAC;0GAAY;0EAAqB;;;;;;0EAClC,8OAAC;0GAAc;;kFACb,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;;;;;;;;;;;;;kEAGtF,8OAAC;kGAAc;;0EACb,8OAAC;0GAAY;0EAAwB;;;;;;0EACrC,8OAAC;0GAAY;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAa;0EAA6C;;;;;;0EAC3D,8OAAC;0GAAY;0EAAqB;;;;;;0EAClC,8OAAC;0GAAc;;kFACb,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;kFAClF,8OAAC;kHAAe;kFAAkE;;;;;;;;;;;;;;;;;;kEAGtF,8OAAC;kGAAc;;0EACb,8OAAC;0GAAY;0EAAwB;;;;;;0EACrC,8OAAC;0GAAY;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjD,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;;sDACb,8OAAC;sFAAa;sDAA0B;;;;;;sDACxC,8OAAC;sFAAc;;;;;;;;;;;;8CAGjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAAqB;;;;;;8DACnC,8OAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAGvC,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAAqB;;;;;;8DACnC,8OAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAGvC,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAAqB;;;;;;8DACnC,8OAAC;8FAAY;8DAAwB;;;;;;;;;;;;sDAGvC,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;8DACb,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;8FAAa;8DAAqB;;;;;;8DACnC,8OAAC;8FAAY;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7C,8OAAC;kEAAkB;kCACjB,cAAA,8OAAC;sEAAc;;8CACb,8OAAC;8EAAa;8CAAsC;;;;;;8CACpD,8OAAC;8EAAY;8CAAgD;;;;;;8CAE7D,8OAAC;8EAAc;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,8OAAC;8FAAY;;;;;;gDAA2B;;;;;;;sDAE1C,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,8OAAC;8FAAY;;;;;;gDAAkD;8DAAC,8OAAC;8FAAe;8DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1G,8OAAC;kEAAiB;kCAChB,cAAA,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;;kEACvB,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAY;;;;;;;;;;;kEAEf,8OAAC;kGAAe;kEAAkC;;;;;;;;;;;;0DAEpD,8OAAC;0FAAY;0DAAqB;;;;;;;;;;;;kDAGpC,8OAAC;kFAAc;;0DACb,8OAAC;0FAAY;0DAAwB;;;;;;0DACrC,8OAAC;0FAAc;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA0C;;;;;;kEAC1E,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAA0C;;;;;;kEACxE,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAY,WAAU;kEAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7F", "debugId": null}}]}