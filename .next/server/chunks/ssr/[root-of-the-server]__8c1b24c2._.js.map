{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\n\ntype MessageType = 'text' | 'payment' | 'image' | 'voice' | 'video' | 'file' | 'location';\ntype MessageStatus = 'sent' | 'delivered' | 'read' | 'sending' | 'failed';\ntype CallType = 'voice' | 'video';\n\ninterface User {\n  id: string;\n  name: string;\n  username: string;\n  phone: string;\n  isOnline: boolean;\n  lastSeen?: string;\n  avatar?: string;\n  status?: string;\n  isTyping?: boolean;\n}\n\ninterface Message {\n  id: string;\n  senderId: string;\n  text: string;\n  timestamp: Date;\n  status: MessageStatus;\n  type: MessageType;\n  amount?: number;\n  currency?: string;\n  imageUrl?: string;\n  fileUrl?: string;\n  fileName?: string;\n  fileSize?: string;\n  duration?: string;\n  location?: { lat: number; lng: number; address: string };\n  replyTo?: string;\n  isForwarded?: boolean;\n  reactions?: { emoji: string; users: string[] }[];\n}\n\ninterface Chat {\n  id: string;\n  participant: User;\n  lastMessage: string;\n  timestamp: Date;\n  unreadCount: number;\n  messages: Message[];\n  isPinned?: boolean;\n  isMuted?: boolean;\n  isArchived?: boolean;\n}\n\ninterface CallState {\n  isActive: boolean;\n  type: CallType;\n  participant?: User;\n  duration?: string;\n  isMuted?: boolean;\n  isVideoOff?: boolean;\n}\n\n// Mock data for demonstration\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    name: 'John Doe',\n    username: 'johndoe',\n    phone: '+1234567890',\n    isOnline: true,\n    lastSeen: '2 min ago',\n    avatar: 'JD',\n    status: 'Available for chat'\n  },\n  {\n    id: '2',\n    name: 'Jane Smith',\n    username: 'janesmith',\n    phone: '+1234567891',\n    isOnline: false,\n    lastSeen: '1 hour ago',\n    avatar: 'JS',\n    status: 'Busy with work'\n  },\n  {\n    id: '3',\n    name: 'Mike Johnson',\n    username: 'mikej',\n    phone: '+1234567892',\n    isOnline: true,\n    lastSeen: 'now',\n    avatar: 'MJ',\n    status: 'Ready to receive payments'\n  },\n  {\n    id: '4',\n    name: 'Sarah Wilson',\n    username: 'sarahw',\n    phone: '+1234567893',\n    isOnline: false,\n    lastSeen: '30 min ago',\n    avatar: 'SW',\n    status: 'At the gym'\n  },\n];\n\nconst mockMessages: Message[] = [\n  {\n    id: '1',\n    senderId: '1',\n    text: 'Hey! How are you doing?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 30),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '2',\n    senderId: 'current',\n    text: 'I\\'m doing great! Just finished a big project.',\n    timestamp: new Date(Date.now() - 1000 * 60 * 25),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '3',\n    senderId: '1',\n    text: 'That\\'s awesome! Want to celebrate? I can send you some money for dinner 🍽️',\n    timestamp: new Date(Date.now() - 1000 * 60 * 20),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '4',\n    senderId: '1',\n    text: 'Here you go!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    status: 'read',\n    type: 'payment',\n    amount: 50,\n    currency: 'USD'\n  },\n  {\n    id: '5',\n    senderId: 'current',\n    text: 'Thank you so much! 🙏',\n    timestamp: new Date(Date.now() - 1000 * 60 * 10),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '6',\n    senderId: '1',\n    text: 'Check out this photo from my vacation!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 8),\n    status: 'read',\n    type: 'image',\n    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'\n  },\n  {\n    id: '7',\n    senderId: 'current',\n    text: 'Beautiful! Where is this?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 5),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '8',\n    senderId: '1',\n    text: 'Voice message',\n    timestamp: new Date(Date.now() - 1000 * 60 * 3),\n    status: 'read',\n    type: 'voice',\n    duration: '0:45'\n  },\n  {\n    id: '9',\n    senderId: 'current',\n    text: 'Let\\'s have a video call later!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 1),\n    status: 'delivered',\n    type: 'text'\n  }\n];\n\nconst mockChats: Chat[] = [\n  {\n    id: '1',\n    participant: mockUsers[0],\n    lastMessage: 'Can you send me the files?',\n    timestamp: new Date(),\n    unreadCount: 2,\n    messages: mockMessages,\n  },\n  {\n    id: '2',\n    participant: mockUsers[1],\n    lastMessage: 'Meeting at 3 PM',\n    timestamp: new Date(Date.now() - 86400000),\n    unreadCount: 0,\n    messages: [],\n  },\n];\n\nexport default function ChatPage() {\n  const router = useRouter();\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [currentChat, setCurrentChat] = useState<Chat | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [chats, setChats] = useState<Chat[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentAmount, setPaymentAmount] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);\n  const [callState, setCallState] = useState<CallState>({ isActive: false, type: 'voice' });\n  const [showUserProfile, setShowUserProfile] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);\n  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    // Simulate loading user data\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n        const user: User = {\n          id: 'current',\n          name: 'Current User',\n          username: 'currentuser',\n          phone: '+1234567899',\n          isOnline: true,\n          avatar: 'CU',\n          status: 'Available'\n        };\n        setCurrentUser(user);\n        setChats(mockChats);\n        setCurrentChat(mockChats[0]);\n        setMessages(mockMessages);\n      } catch (error) {\n        console.error('Error loading chat data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Auto-scroll to bottom of messages and mark messages as read\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n    \n    // Mark messages as read when chat is opened\n    if (currentChat) {\n      const updatedChats = chats.map(chat => {\n        if (chat.id === currentChat.id && chat.unreadCount > 0) {\n          return { ...chat, unreadCount: 0 };\n        }\n        return chat;\n      });\n      setChats(updatedChats);\n    }\n  }, [messages, currentChat]);\n\n  const handleSendMessage = (e?: React.FormEvent) => {\n    e?.preventDefault();\n    if (!newMessage.trim() || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: newMessage,\n      timestamp: new Date(),\n      status: 'sending',\n      type: 'text',\n    };\n\n    // Optimistic update\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setNewMessage('');\n\n    // Simulate message sending\n    setTimeout(() => {\n      setMessages(prevMessages => \n        prevMessages.map(msg => \n          msg.id === message.id \n            ? { ...msg, status: 'delivered' } \n            : msg\n        )\n      );\n    }, 1000);\n\n    // Update chat list\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: newMessage,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: [...updatedMessages],\n          }\n        : chat\n    );\n    \n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleSendPayment = () => {\n    if (!paymentAmount || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: `Payment of $${paymentAmount}`,\n      timestamp: new Date(),\n      status: 'sent',\n      type: 'payment',\n      amount: parseFloat(paymentAmount),\n      currency: 'USD',\n    };\n\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setShowPaymentModal(false);\n    setPaymentAmount('');\n\n    // Update last message in chats\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: `Payment of $${paymentAmount}`,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: updatedMessages,\n          }\n        : chat\n    );\n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleLogout = () => {\n    // Clear user session and redirect to auth page\n    router.push('/auth');\n  };\n\n  if (isLoading || !currentUser) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4\"></div>\n          <p className=\"text-white\">Loading chat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Format time for messages\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Format date for message grouping\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString([], { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    });\n  };\n\n  // Get message status icon\n  const getStatusIcon = (status: MessageStatus) => {\n    switch (status) {\n      case 'sending':\n        return <span className=\"text-gray-400\">🕒</span>;\n      case 'sent':\n        return <span className=\"text-gray-400\">✓</span>;\n      case 'delivered':\n        return <span className=\"text-gray-400\">✓✓</span>;\n      case 'read':\n        return <span className=\"text-blue-500\">✓✓</span>;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n\n        .message-bubble {\n          transition: all 0.3s ease;\n        }\n\n        .message-bubble:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .chat-input {\n          background: rgba(45, 27, 78, 0.3);\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(212, 175, 55, 0.3);\n        }\n\n        .chat-input:focus {\n          border-color: #D4AF37;\n          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);\n        }\n      `}</style>\n\n      <div className=\"flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern\">\n        {/* Sidebar */}\n        <div className=\"w-full md:w-1/3 bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg border-r border-yellow-400/20 flex flex-col\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-yellow-400/20 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-md\">\n            <div className=\"flex items-center\">\n              <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg\">\n                {currentUser.name.split(' ').map((n: string) => n[0]).join('')}\n              </div>\n              <div className=\"ml-3\">\n                <h2 className=\"font-semibold text-white\">{currentUser.name}</h2>\n                <p className=\"text-xs text-gray-300\">@{currentUser.username}</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"text-yellow-400 hover:text-yellow-300 p-2 rounded-full hover:bg-purple-800/50 transition-colors\"\n                title=\"Settings\"\n              >\n                <i className=\"fas fa-cog text-lg\"></i>\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"text-yellow-400 hover:text-yellow-300 p-2 rounded-full hover:bg-purple-800/50 transition-colors\"\n                title=\"Logout\"\n              >\n                <i className=\"fas fa-sign-out-alt text-lg\"></i>\n              </button>\n            </div>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"p-4 border-b border-yellow-400/20\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search conversations...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-3 pl-10 chat-input rounded-full text-white placeholder-gray-400 focus:outline-none\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"></i>\n            </div>\n          </div>\n\n          {/* Chat List */}\n          <div className=\"flex-1 overflow-y-auto\">\n            {chats.map((chat) => (\n              <motion.div\n                key={chat.id}\n                className={`p-4 border-b border-yellow-400/10 cursor-pointer transition-all hover:bg-purple-800/30 ${\n                  currentChat?.id === chat.id ? 'bg-purple-800/50 border-l-4 border-l-yellow-400' : ''\n                }`}\n                onClick={() => {\n                  setCurrentChat(chat);\n                  setMessages(chat.messages);\n                }}\n                whileHover={{ x: 4 }}\n                transition={{ duration: 0.2 }}\n              >\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center text-white font-semibold\">\n                      {chat.participant.avatar || chat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {chat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-purple-900\"></div>\n                    )}\n                    {chat.unreadCount > 0 && (\n                      <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-purple-900 rounded-full flex items-center justify-center text-xs font-bold\">\n                        {chat.unreadCount}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"ml-3 flex-1\">\n                    <div className=\"flex justify-between items-start\">\n                      <h3 className=\"font-semibold text-white\">{chat.participant.name}</h3>\n                      <span className=\"text-xs text-gray-400\">{formatTime(chat.timestamp)}</span>\n                    </div>\n                    <p className=\"text-sm text-gray-300 truncate\">{chat.lastMessage}</p>\n                    {chat.participant.status && (\n                      <p className=\"text-xs text-yellow-400 italic\">{chat.participant.status}</p>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Main Chat Area */}\n        <div className=\"flex-1 flex flex-col bg-gradient-to-br from-purple-900/20 to-black/20 backdrop-blur-lg\">\n          {currentChat ? (\n            <>\n              {/* Chat Header */}\n              <div className=\"p-4 border-b border-yellow-400/20 flex items-center justify-between bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-md sticky top-0 z-10\">\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center text-white font-semibold\">\n                      {currentChat.participant.avatar || currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {currentChat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-purple-900\"></div>\n                    )}\n                  </div>\n                  <div className=\"ml-3\">\n                    <h2 className=\"font-semibold text-white\">{currentChat.participant.name}</h2>\n                    <p className=\"text-xs text-gray-300\">\n                      {currentChat.participant.isOnline\n                        ? (currentChat.participant.isTyping ? 'Typing...' : 'Online')\n                        : `Last seen ${currentChat.participant.lastSeen}`}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Chat Actions */}\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => setCallState({ isActive: true, type: 'voice', participant: currentChat.participant })}\n                    className=\"text-yellow-400 hover:text-yellow-300 p-2 rounded-full hover:bg-purple-800/50 transition-colors\"\n                    title=\"Voice Call\"\n                  >\n                    <i className=\"fas fa-phone text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => setCallState({ isActive: true, type: 'video', participant: currentChat.participant })}\n                    className=\"text-yellow-400 hover:text-yellow-300 p-2 rounded-full hover:bg-purple-800/50 transition-colors\"\n                    title=\"Video Call\"\n                  >\n                    <i className=\"fas fa-video text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => setShowUserProfile(true)}\n                    className=\"text-yellow-400 hover:text-yellow-300 p-2 rounded-full hover:bg-purple-800/50 transition-colors\"\n                    title=\"User Info\"\n                  >\n                    <i className=\"fas fa-info-circle text-lg\"></i>\n                  </button>\n                </div>\n              </div>\n\n              {/* Messages Area */}\n              <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                {messages.length > 0 && (\n                  <div className=\"text-center mb-4\">\n                    <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                      {formatDate(messages[0].timestamp)}\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  {messages.map((message, index) => {\n                    const isCurrentUser = message.senderId === currentUser.id;\n                    const showDate = index === 0 ||\n                      new Date(message.timestamp).toDateString() !==\n                      new Date(messages[index - 1].timestamp).toDateString();\n\n                    return (\n                      <motion.div\n                        key={message.id}\n                        className=\"space-y-1\"\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {showDate && index !== 0 && (\n                          <div className=\"text-center my-4\">\n                            <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                              {formatDate(message.timestamp)}\n                            </span>\n                          </div>\n                        )}\n\n                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>\n                          <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${\n                            isCurrentUser\n                              ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 rounded-tr-none'\n                              : 'bg-gradient-to-br from-purple-800/60 to-purple-900/60 backdrop-blur-lg border border-yellow-400/20 text-white rounded-tl-none'\n                          }`}>\n\n                            {/* Payment Message */}\n                            {message.type === 'payment' && (\n                              <div className=\"flex items-center mb-2 p-2 bg-green-500/20 rounded-lg\">\n                                <i className=\"fas fa-dollar-sign text-green-400 mr-2\"></i>\n                                <span className=\"text-sm font-medium text-green-400\">\n                                  Payment: ${message.amount?.toFixed(2)} {message.currency}\n                                </span>\n                              </div>\n                            )}\n\n                            {/* Image Message */}\n                            {message.type === 'image' && message.imageUrl && (\n                              <div className=\"mb-2\">\n                                <img\n                                  src={message.imageUrl}\n                                  alt=\"Shared image\"\n                                  className=\"rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity\"\n                                  onClick={() => window.open(message.imageUrl, '_blank')}\n                                />\n                              </div>\n                            )}\n\n                            {/* Voice Message */}\n                            {message.type === 'voice' && (\n                              <div className=\"flex items-center space-x-3 p-2 bg-purple-700/30 rounded-lg\">\n                                <button className=\"w-8 h-8 bg-yellow-400 text-purple-900 rounded-full flex items-center justify-center hover:bg-yellow-300 transition-colors\">\n                                  <i className=\"fas fa-play text-xs\"></i>\n                                </button>\n                                <div className=\"flex-1 h-2 bg-purple-600/50 rounded-full\">\n                                  <div className=\"h-full w-1/3 bg-yellow-400 rounded-full\"></div>\n                                </div>\n                                <span className=\"text-xs text-gray-300\">{message.duration}</span>\n                              </div>\n                            )}\n\n                            {/* Text Message */}\n                            {(message.type === 'text' || message.type === 'payment') && (\n                              <p className={`${message.type === 'payment' ? 'text-sm' : ''} leading-relaxed`}>\n                                {message.text}\n                              </p>\n                            )}\n\n                            {/* Message Footer */}\n                            <div className=\"flex items-center justify-end mt-2 space-x-1\">\n                              <span className={`text-xs ${isCurrentUser ? 'text-purple-700' : 'text-gray-400'}`}>\n                                {formatTime(message.timestamp)}\n                              </span>\n                              {isCurrentUser && (\n                                <span className=\"ml-1\">\n                                  {getStatusIcon(message.status)}\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    );\n                  })}\n                </div>\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Message Input */}\n              <div className=\"p-4 border-t border-yellow-400/20 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-md\">\n                {/* Reply Preview */}\n                {replyToMessage && (\n                  <div className=\"mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <p className=\"text-xs text-yellow-400 font-semibold\">Replying to {replyToMessage.senderId === currentUser.id ? 'yourself' : currentChat.participant.name}</p>\n                        <p className=\"text-sm text-gray-300 truncate\">{replyToMessage.text}</p>\n                      </div>\n                      <button\n                        onClick={() => setReplyToMessage(null)}\n                        className=\"text-gray-400 hover:text-white\"\n                      >\n                        <i className=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {/* Attachment Menu */}\n                <AnimatePresence>\n                  {showAttachmentMenu && (\n                    <motion.div\n                      className=\"mb-3 flex space-x-2\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 10 }}\n                    >\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-image text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Photo</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-file text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Document</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-map-marker-alt text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Location</span>\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <form onSubmit={handleSendMessage} className=\"flex items-end space-x-3\">\n                  {/* Attachment Button */}\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}\n                    className=\"p-3 text-yellow-400 hover:text-yellow-300 hover:bg-purple-800/50 rounded-full transition-colors\"\n                  >\n                    <i className=\"fas fa-plus text-lg\"></i>\n                  </button>\n\n                  {/* Message Input */}\n                  <div className=\"flex-1 relative\">\n                    <textarea\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyPress={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          handleSendMessage(e);\n                        }\n                      }}\n                      placeholder=\"Type a message...\"\n                      rows={1}\n                      className=\"w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-400 focus:outline-none resize-none\"\n                      style={{ minHeight: '48px', maxHeight: '120px' }}\n                    />\n\n                    {/* Input Actions */}\n                    <div className=\"absolute right-3 bottom-3 flex items-center space-x-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                        className=\"text-yellow-400 hover:text-yellow-300 transition-colors\"\n                      >\n                        <i className=\"fas fa-smile text-lg\"></i>\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"text-yellow-400 hover:text-yellow-300 transition-colors\"\n                      >\n                        <i className=\"fas fa-microphone text-lg\"></i>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Send/Payment Buttons */}\n                  <div className=\"flex space-x-2\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPaymentModal(true)}\n                      className=\"p-3 bg-green-600 hover:bg-green-500 text-white rounded-full transition-colors\"\n                      title=\"Send Money\"\n                    >\n                      <i className=\"fas fa-dollar-sign text-lg\"></i>\n                    </button>\n\n                    <button\n                      type=\"submit\"\n                      disabled={!newMessage.trim()}\n                      className={`p-3 rounded-full transition-colors ${\n                        newMessage.trim()\n                          ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 hover:from-yellow-300 hover:to-yellow-100'\n                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                      }`}\n                    >\n                      <i className=\"fas fa-paper-plane text-lg\"></i>\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center p-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6\">\n                  <i className=\"fas fa-comment-dollar text-3xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-xl font-medium text-white mb-2\">Welcome to BoGuani</h3>\n                <p className=\"text-gray-300\">Select a conversation to start messaging</p>\n                <p className=\"text-yellow-400 text-sm mt-2 italic\">\"Speak Gold. Share Value.\"</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Call Interface */}\n        <AnimatePresence>\n          {callState.isActive && (\n            <motion.div\n              className=\"fixed inset-0 bg-gradient-to-br from-purple-900 to-black z-50 flex items-center justify-center\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n            >\n              <div className=\"text-center text-white\">\n                <div className=\"w-32 h-32 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full flex items-center justify-center mx-auto mb-6\">\n                  {callState.participant?.avatar || callState.participant?.name.split(' ').map((n: string) => n[0]).join('')}\n                </div>\n                <h2 className=\"text-2xl font-semibold mb-2\">{callState.participant?.name}</h2>\n                <p className=\"text-gray-300 mb-8\">\n                  {callState.type === 'video' ? 'Video calling...' : 'Voice calling...'}\n                </p>\n\n                <div className=\"flex justify-center space-x-6\">\n                  <button\n                    onClick={() => setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }))}\n                    className={`w-14 h-14 rounded-full flex items-center justify-center transition-colors ${\n                      callState.isMuted ? 'bg-red-600' : 'bg-gray-700'\n                    }`}\n                  >\n                    <i className={`fas ${callState.isMuted ? 'fa-microphone-slash' : 'fa-microphone'} text-xl`}></i>\n                  </button>\n\n                  {callState.type === 'video' && (\n                    <button\n                      onClick={() => setCallState(prev => ({ ...prev, isVideoOff: !prev.isVideoOff }))}\n                      className={`w-14 h-14 rounded-full flex items-center justify-center transition-colors ${\n                        callState.isVideoOff ? 'bg-red-600' : 'bg-gray-700'\n                      }`}\n                    >\n                      <i className={`fas ${callState.isVideoOff ? 'fa-video-slash' : 'fa-video'} text-xl`}></i>\n                    </button>\n                  )}\n\n                  <button\n                    onClick={() => setCallState({ isActive: false, type: 'voice' })}\n                    className=\"w-14 h-14 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors\"\n                  >\n                    <i className=\"fas fa-phone-slash text-xl\"></i>\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <motion.div\n          className=\"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n        >\n          <motion.div\n            className=\"bg-gradient-to-br from-purple-900/90 to-black/90 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md gold-border\"\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.9, opacity: 0 }}\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <h3 className=\"text-xl font-semibold text-white gold-gradient\">Send Payment</h3>\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Amount (USD)\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <span className=\"text-yellow-400 text-lg font-semibold\">$</span>\n                  </div>\n                  <input\n                    type=\"number\"\n                    name=\"amount\"\n                    id=\"amount\"\n                    value={paymentAmount}\n                    onChange={(e) => setPaymentAmount(e.target.value)}\n                    className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-400 focus:outline-none text-lg\"\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                  />\n                </div>\n                <p className=\"text-xs text-gray-400 mt-2\">\n                  Sending to: {currentChat?.participant.name}\n                </p>\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-colors\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSendPayment}\n                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}\n                  className={`px-6 py-3 rounded-xl font-semibold transition-colors ${\n                    paymentAmount && parseFloat(paymentAmount) > 0\n                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 hover:from-yellow-300 hover:to-yellow-100'\n                      : 'bg-gray-600 text-gray-400 cursor-not-allowed'\n                  }`}\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  Send ${paymentAmount || '0.00'}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;;AAgEA,8BAA8B;AAC9B,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI;QACf,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,aAAa;QACb,UAAU,EAAE;IACd;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAAE,UAAU;QAAO,MAAM;IAAQ;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,MAAM,WAAW;YACf,IAAI;gBACF,aAAa;gBACb,MAAM,OAAa;oBACjB,IAAI;oBACJ,MAAM;oBACN,UAAU;oBACV,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,QAAQ;gBACV;gBACA,eAAe;gBACf,SAAS;gBACT,eAAe,SAAS,CAAC,EAAE;gBAC3B,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC7D;QAEA,4CAA4C;QAC5C,IAAI,aAAa;YACf,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA;gBAC7B,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,WAAW,GAAG,GAAG;oBACtD,OAAO;wBAAE,GAAG,IAAI;wBAAE,aAAa;oBAAE;gBACnC;gBACA,OAAO;YACT;YACA,SAAS;QACX;IACF,GAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,oBAAoB,CAAC;QACzB,GAAG;QACH,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,eAAe,CAAC,aAAa;QAExD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM;YACN,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;QACR;QAEA,oBAAoB;QACpB,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,cAAc;QAEd,2BAA2B;QAC3B,WAAW;YACT,YAAY,CAAA,eACV,aAAa,GAAG,CAAC,CAAA,MACf,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAY,IAC9B;QAGV,GAAG;QAEH,mBAAmB;QACnB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa;gBACb,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;uBAAI;iBAAgB;YAChC,IACA;QAGN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa;QAEpD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM,CAAC,YAAY,EAAE,eAAe;YACpC,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;YACN,QAAQ,WAAW;YACnB,UAAU;QACZ;QAEA,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QAEjB,+BAA+B;QAC/B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa,CAAC,YAAY,EAAE,eAAe;gBAC3C,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;YACZ,IACA;QAEN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,eAAe;QACnB,+CAA+C;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,aAAa,CAAC,aAAa;QAC7B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACjC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;;;;;0BA+CE,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;0DAE7D,8OAAC;0FAAc;;kEACb,8OAAC;kGAAa;kEAA4B,YAAY,IAAI;;;;;;kEAC1D,8OAAC;kGAAY;;4DAAwB;4DAAE,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;kDAG/D,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAE/B,OAAM;0FADI;0DAGV,cAAA,8OAAC;8FAAY;;;;;;;;;;;0DAEf,8OAAC;gDACC,SAAS;gDAET,OAAM;0FADI;0DAGV,cAAA,8OAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;0CAMnB,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAc;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;sFACpC;;;;;;sDAEZ,8OAAC;sFAAY;;;;;;;;;;;;;;;;;0CAKjB,8OAAC;0EAAc;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC,uFAAuF,EACjG,aAAa,OAAO,KAAK,EAAE,GAAG,oDAAoD,IAClF;wCACF,SAAS;4CACP,eAAe;4CACf,YAAY,KAAK,QAAQ;wCAC3B;wCACA,YAAY;4CAAE,GAAG;wCAAE;wCACnB,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEACZ,KAAK,WAAW,CAAC,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE5F,KAAK,WAAW,CAAC,QAAQ,kBACxB,8OAAC;sGAAc;;;;;;wDAEhB,KAAK,WAAW,GAAG,mBAClB,8OAAC;sGAAc;sEACZ,KAAK,WAAW;;;;;;;;;;;;8DAIvB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;;8EACb,8OAAC;8GAAa;8EAA4B,KAAK,WAAW,CAAC,IAAI;;;;;;8EAC/D,8OAAC;8GAAe;8EAAyB,WAAW,KAAK,SAAS;;;;;;;;;;;;sEAEpE,8OAAC;sGAAY;sEAAkC,KAAK,WAAW;;;;;;wDAC9D,KAAK,WAAW,CAAC,MAAM,kBACtB,8OAAC;sGAAY;sEAAkC,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;uCAhCvE,KAAK,EAAE;;;;;;;;;;;;;;;;kCA0CpB,8OAAC;kEAAc;kCACZ,4BACC;;8CAEE,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc;sEACZ,YAAY,WAAW,CAAC,MAAM,IAAI,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE1G,YAAY,WAAW,CAAC,QAAQ,kBAC/B,8OAAC;sGAAc;;;;;;;;;;;;8DAGnB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAa;sEAA4B,YAAY,WAAW,CAAC,IAAI;;;;;;sEACtE,8OAAC;sGAAY;sEACV,YAAY,WAAW,CAAC,QAAQ,GAC5B,YAAY,WAAW,CAAC,QAAQ,GAAG,cAAc,WAClD,CAAC,UAAU,EAAE,YAAY,WAAW,CAAC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;sDAMzD,8OAAC;sFAAc;;8DACb,8OAAC;oDACC,SAAS,IAAM,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;oDAElG,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDACC,SAAS,IAAM,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;oDAElG,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAEf,8OAAC;oDACC,SAAS,IAAM,mBAAmB;oDAElC,OAAM;8FADI;8DAGV,cAAA,8OAAC;kGAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMnB,8OAAC;8EAAc;;wCACZ,SAAS,MAAM,GAAG,mBACjB,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAe;0DACb,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS;;;;;;;;;;;sDAKvC,8OAAC;sFAAc;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gDACtB,MAAM,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;gDACzD,MAAM,WAAW,UAAU,KACzB,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY,OACxC,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY;gDAEtD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;oDAAI;;wDAE3B,YAAY,UAAU,mBACrB,8OAAC;sGAAc;sEACb,cAAA,8OAAC;0GAAe;0EACb,WAAW,QAAQ,SAAS;;;;;;;;;;;sEAKnC,8OAAC;sGAAe,CAAC,KAAK,EAAE,gBAAgB,gBAAgB,iBAAiB;sEACvE,cAAA,8OAAC;0GAAe,CAAC,0DAA0D,EACzE,gBACI,mFACA,iIACJ;;oEAGC,QAAQ,IAAI,KAAK,2BAChB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAY;;;;;;0FACb,8OAAC;0HAAe;;oFAAqC;oFACxC,QAAQ,MAAM,EAAE,QAAQ;oFAAG;oFAAE,QAAQ,QAAQ;;;;;;;;;;;;;oEAM7D,QAAQ,IAAI,KAAK,WAAW,QAAQ,QAAQ,kBAC3C,8OAAC;kHAAc;kFACb,cAAA,8OAAC;4EACC,KAAK,QAAQ,QAAQ;4EACrB,KAAI;4EAEJ,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;sHADnC;;;;;;;;;;;oEAOf,QAAQ,IAAI,KAAK,yBAChB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAiB;0FAChB,cAAA,8OAAC;8HAAY;;;;;;;;;;;0FAEf,8OAAC;0HAAc;0FACb,cAAA,8OAAC;8HAAc;;;;;;;;;;;0FAEjB,8OAAC;0HAAe;0FAAyB,QAAQ,QAAQ;;;;;;;;;;;;oEAK5D,CAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,SAAS,mBACrD,8OAAC;kHAAa,GAAG,QAAQ,IAAI,KAAK,YAAY,YAAY,GAAG,gBAAgB,CAAC;kFAC3E,QAAQ,IAAI;;;;;;kFAKjB,8OAAC;kHAAc;;0FACb,8OAAC;0HAAgB,CAAC,QAAQ,EAAE,gBAAgB,oBAAoB,iBAAiB;0FAC9E,WAAW,QAAQ,SAAS;;;;;;4EAE9B,+BACC,8OAAC;0HAAe;0FACb,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAtElC,QAAQ,EAAE;;;;;4CA8ErB;;;;;;sDAEF,8OAAC;4CAAI,KAAK;;;;;;;;;;;;;8CAIZ,8OAAC;8EAAc;;wCAEZ,gCACC,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;;;0EACC,8OAAC;0GAAY;;oEAAwC;oEAAa,eAAe,QAAQ,KAAK,YAAY,EAAE,GAAG,aAAa,YAAY,WAAW,CAAC,IAAI;;;;;;;0EACxJ,8OAAC;0GAAY;0EAAkC,eAAe,IAAI;;;;;;;;;;;;kEAEpE,8OAAC;wDACC,SAAS,IAAM,kBAAkB;kGACvB;kEAEV,cAAA,8OAAC;sGAAY;;;;;;;;;;;;;;;;;;;;;;sDAOrB,8OAAC,yLAAA,CAAA,kBAAe;sDACb,oCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;;kEAE1B,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,8OAAC;kGAAiB;;0EAChB,8OAAC;0GAAY;;;;;;0EACb,8OAAC;0GAAe;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAK,UAAU;sFAA6B;;8DAE3C,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,sBAAsB,CAAC;8FAC5B;8DAEV,cAAA,8OAAC;kGAAY;;;;;;;;;;;8DAIf,8OAAC;8FAAc;;sEACb,8OAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,YAAY,CAAC;gEACX,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oEACpC,EAAE,cAAc;oEAChB,kBAAkB;gEACpB;4DACF;4DACA,aAAY;4DACZ,MAAM;4DAEN,OAAO;gEAAE,WAAW;gEAAQ,WAAW;4DAAQ;sGADrC;;;;;;sEAKZ,8OAAC;sGAAc;;8EACb,8OAAC;oEACC,MAAK;oEACL,SAAS,IAAM,mBAAmB,CAAC;8GACzB;8EAEV,cAAA,8OAAC;kHAAY;;;;;;;;;;;8EAEf,8OAAC;oEACC,MAAK;8GACK;8EAEV,cAAA,8OAAC;kHAAY;;;;;;;;;;;;;;;;;;;;;;;8DAMnB,8OAAC;8FAAc;;sEACb,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,oBAAoB;4DAEnC,OAAM;sGADI;sEAGV,cAAA,8OAAC;0GAAY;;;;;;;;;;;sEAGf,8OAAC;4DACC,MAAK;4DACL,UAAU,CAAC,WAAW,IAAI;sGACf,CAAC,mCAAmC,EAC7C,WAAW,IAAI,KACX,6GACA,gDACJ;sEAEF,cAAA,8OAAC;0GAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOvB,8OAAC;sEAAc;sCACb,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDACb,cAAA,8OAAC;sFAAY;;;;;;;;;;;kDAEf,8OAAC;kFAAa;kDAAsC;;;;;;kDACpD,8OAAC;kFAAY;kDAAgB;;;;;;kDAC7B,8OAAC;kFAAY;kDAAsC;;;;;;;;;;;;;;;;;;;;;;kCAO3D,8OAAC,yLAAA,CAAA,kBAAe;kCACb,UAAU,QAAQ,kBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;sCAEnB,cAAA,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc;kDACZ,UAAU,WAAW,EAAE,UAAU,UAAU,WAAW,EAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,KAAK;;;;;;kDAEzG,8OAAC;kFAAa;kDAA+B,UAAU,WAAW,EAAE;;;;;;kDACpE,8OAAC;kFAAY;kDACV,UAAU,IAAI,KAAK,UAAU,qBAAqB;;;;;;kDAGrD,8OAAC;kFAAc;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,CAAC,KAAK,OAAO;wDAAC,CAAC;0FAC7D,CAAC,0EAA0E,EACpF,UAAU,OAAO,GAAG,eAAe,eACnC;0DAEF,cAAA,8OAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,OAAO,GAAG,wBAAwB,gBAAgB,QAAQ,CAAC;;;;;;;;;;;4CAG3F,UAAU,IAAI,KAAK,yBAClB,8OAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,CAAC,KAAK,UAAU;wDAAC,CAAC;0FACnE,CAAC,0EAA0E,EACpF,UAAU,UAAU,GAAG,eAAe,eACtC;0DAEF,cAAA,8OAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,UAAU,GAAG,mBAAmB,WAAW,QAAQ,CAAC;;;;;;;;;;;0DAIvF,8OAAC;gDACC,SAAS,IAAM,aAAa;wDAAE,UAAU;wDAAO,MAAM;oDAAQ;0FACnD;0DAEV,cAAA,8OAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1B,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;0BAEnB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAK,SAAS;oBAAE;8BAE/B,cAAA,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAiD;;;;;;kDAC/D,8OAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDAEV,cAAA,8OAAC;sFAAY;;;;;;;;;;;;;;;;;0CAIjB,8OAAC;0EAAc;;kDACb,8OAAC;wCAAM,SAAQ;kFAAmB;kDAA+C;;;;;;kDAGjF,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAe;8DAAwC;;;;;;;;;;;0DAE1D,8OAAC;gDACC,MAAK;gDACL,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAEhD,aAAY;gDACZ,MAAK;gDACL,KAAI;0FAHM;;;;;;;;;;;;kDAMd,8OAAC;kFAAY;;4CAA6B;4CAC3B,aAAa,YAAY;;;;;;;;;;;;;0CAI1C,8OAAC;0EAAc;;kDACb,8OAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,iBAAiB,WAAW,kBAAkB;kFAC9C,CAAC,qDAAqD,EAC/D,iBAAiB,WAAW,iBAAiB,IACzC,6GACA,gDACJ;;0DAEF,8OAAC;0FAAY;;;;;;4CAA8B;4CACpC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C", "debugId": null}}]}