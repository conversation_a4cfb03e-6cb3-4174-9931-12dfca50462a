{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\ninterface CountryCode {\n  code: string;\n  country: string;\n  flag: string;\n}\n\nexport default function AuthPage() {\n  const router = useRouter();\n  const [step, setStep] = useState<'phone' | 'otp' | 'profile'>('phone');\n  const [countryCode, setCountryCode] = useState('+1');\n  const [phoneNumber, setPhoneNumber] = useState('');\n  const [otpCode, setOtpCode] = useState('');\n  const [name, setName] = useState('');\n  const [username, setUsername] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [success, setSuccess] = useState(false);\n\n  const countryCodes: CountryCode[] = [\n    { code: '+1', country: 'United States', flag: '🇺🇸' },\n    { code: '+1', country: 'Canada', flag: '🇨🇦' },\n    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },\n    { code: '+33', country: 'France', flag: '🇫🇷' },\n    { code: '+49', country: 'Germany', flag: '🇩🇪' },\n    { code: '+81', country: 'Japan', flag: '🇯🇵' },\n    { code: '+86', country: 'China', flag: '🇨🇳' },\n    { code: '+91', country: 'India', flag: '🇮🇳' },\n    { code: '+55', country: 'Brazil', flag: '🇧🇷' },\n    { code: '+61', country: 'Australia', flag: '🇦🇺' }\n  ];\n\n  const sendOTP = async () => {\n    if (!phoneNumber.trim()) {\n      setError('Please enter your phone number');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      setStep('otp');\n      setLoading(false);\n    } catch (err) {\n      setError('Failed to send verification code. Please try again.');\n      setLoading(false);\n    }\n  };\n\n  const verifyOTP = async () => {\n    if (!otpCode.trim() || otpCode.length !== 6) {\n      setError('Please enter a valid 6-digit code');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      setStep('profile');\n      setLoading(false);\n    } catch (err) {\n      setError('Invalid verification code. Please try again.');\n      setLoading(false);\n    }\n  };\n\n  const completeProfile = async () => {\n    if (!name.trim() || !username.trim()) {\n      setError('Please fill in all fields');\n      return;\n    }\n\n    setLoading(true);\n    setError('');\n\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      if (typeof window !== 'undefined') {\n        localStorage.setItem('boguani_user', JSON.stringify({\n          id: 'user1',\n          uid: 'user-' + Date.now(),\n          phoneNumber: countryCode + phoneNumber,\n          name: name,\n          username: username,\n          displayName: name\n        }));\n      }\n      \n      setLoading(false);\n      setSuccess(true);\n      \n      setTimeout(() => {\n        router.push('/chat');\n      }, 1500);\n    } catch (err) {\n      setError('Failed to create profile. Please try again.');\n      setLoading(false);\n    }\n  };\n\n  const goBack = () => {\n    if (step === 'otp') {\n      setStep('phone');\n    } else if (step === 'profile') {\n      setStep('otp');\n    }\n    setError('');\n  };\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const userData = localStorage.getItem('boguani_user');\n      if (userData) {\n        router.push('/chat');\n      }\n    }\n  }, [router]);\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .feature-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .feature-card:hover {\n          transform: translateY(-5px);\n          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);\n        }\n\n        .btn-hover:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen hero-pattern\">\n        <div className=\"min-h-screen flex items-center justify-center px-6\">\n          <div className=\"w-full max-w-md\">\n            {/* Header */}\n            <div className=\"text-center mb-12\">\n              <Link href=\"/\" className=\"inline-block\">\n                <div className=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300\">\n                  <i className=\"fas fa-comment-dollar text-3xl text-purple-900\"></i>\n                </div>\n              </Link>\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-4 gold-gradient\">\n                Welcome to BoGuani\n              </h1>\n              <p className=\"text-xl text-gray-300 mb-2\">Messenger of Value</p>\n              <p className=\"text-gray-400 italic\">&quot;Speak Gold. Share Value.&quot;</p>\n            </div>\n\n            {/* Auth Card */}\n            <div className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl gold-border shadow-2xl\">\n              \n              {success ? (\n                /* Success State */\n                <div className=\"text-center py-8\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-check text-2xl text-purple-900\"></i>\n                  </div>\n                  <h2 className=\"text-2xl font-bold mb-4 gold-gradient\">Welcome to BoGuani!</h2>\n                  <p className=\"text-gray-300 mb-6\">Your account has been created successfully.</p>\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto\"></div>\n                  <p className=\"text-sm text-gray-400 mt-4\">Redirecting to chat...</p>\n                </div>\n              ) : (\n                <>\n                  {/* Step Indicator */}\n                  <div className=\"flex justify-center mb-8\">\n                    <div className=\"flex space-x-4\">\n                      <div className={`w-3 h-3 rounded-full ${step === 'phone' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>\n                      <div className={`w-3 h-3 rounded-full ${step === 'otp' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>\n                      <div className={`w-3 h-3 rounded-full ${step === 'profile' ? 'bg-yellow-400' : 'bg-gray-600'}`}></div>\n                    </div>\n                  </div>\n\n                  {/* Error Message */}\n                  {error && (\n                    <div className=\"bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6\">\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-exclamation-triangle text-red-400 mr-3\"></i>\n                        <p className=\"text-red-200 text-sm\">{error}</p>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Phone Step */}\n                  {step === 'phone' && (\n                    <>\n                      <div className=\"mb-6\">\n                        <h2 className=\"text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">Enter Your Phone</h2>\n                        <p className=\"text-gray-400 text-sm\">We&apos;ll send you a verification code</p>\n                      </div>\n\n                      <form onSubmit={(e) => { e.preventDefault(); sendOTP(); }} className=\"space-y-6\">\n                        <div>\n                          <label htmlFor=\"countryCode\" className=\"block text-gray-300 text-sm font-medium mb-2\">Country</label>\n                          <select\n                            id=\"countryCode\"\n                            value={countryCode}\n                            onChange={(e) => setCountryCode(e.target.value)}\n                            className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                          >\n                            {countryCodes.map((country, index) => (\n                              <option key={index} value={country.code}>\n                                {country.flag} {country.code} ({country.country})\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n\n                        <div>\n                          <label htmlFor=\"phone\" className=\"block text-gray-300 text-sm font-medium mb-2\">Phone Number</label>\n                          <div className=\"flex space-x-2\">\n                            <div className=\"w-20 px-3 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-center text-gray-300\">\n                              {countryCode}\n                            </div>\n                            <input\n                              type=\"tel\"\n                              id=\"phone\"\n                              value={phoneNumber}\n                              onChange={(e) => setPhoneNumber(e.target.value)}\n                              placeholder=\"1234567890\"\n                              required\n                              className=\"flex-1 px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                            />\n                          </div>\n                        </div>\n\n                        <button\n                          type=\"submit\"\n                          disabled={loading}\n                          className=\"w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                              Sending Code...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"fas fa-paper-plane mr-2\"></i>\n                              Send Verification Code\n                            </>\n                          )}\n                        </button>\n                      </form>\n                    </>\n                  )}\n\n                  {/* OTP Step */}\n                  {step === 'otp' && (\n                    <>\n                      <div className=\"mb-6\">\n                        <button onClick={goBack} className=\"text-yellow-400 hover:text-yellow-200 transition-colors mb-4\">\n                          <i className=\"fas fa-arrow-left mr-2\"></i>\n                          Back\n                        </button>\n                        <h2 className=\"text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">Enter Verification Code</h2>\n                        <p className=\"text-gray-400 text-sm\">We sent a 6-digit code to {countryCode}{phoneNumber}</p>\n                      </div>\n\n                      <form onSubmit={(e) => { e.preventDefault(); verifyOTP(); }} className=\"space-y-6\">\n                        <div>\n                          <label htmlFor=\"otp\" className=\"block text-gray-300 text-sm font-medium mb-2\">Verification Code</label>\n                          <input\n                            type=\"text\"\n                            id=\"otp\"\n                            value={otpCode}\n                            onChange={(e) => setOtpCode(e.target.value)}\n                            placeholder=\"123456\"\n                            maxLength={6}\n                            required\n                            className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                          />\n                        </div>\n\n                        <button\n                          type=\"submit\"\n                          disabled={loading}\n                          className=\"w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                              Verifying...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"fas fa-check mr-2\"></i>\n                              Verify Code\n                            </>\n                          )}\n                        </button>\n                      </form>\n                    </>\n                  )}\n\n                  {/* Profile Step */}\n                  {step === 'profile' && (\n                    <>\n                      <div className=\"mb-6\">\n                        <button onClick={goBack} className=\"text-yellow-400 hover:text-yellow-200 transition-colors mb-4\">\n                          <i className=\"fas fa-arrow-left mr-2\"></i>\n                          Back\n                        </button>\n                        <h2 className=\"text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">Create Your Profile</h2>\n                        <p className=\"text-gray-400 text-sm\">Tell us a bit about yourself</p>\n                      </div>\n\n                      <form onSubmit={(e) => { e.preventDefault(); completeProfile(); }} className=\"space-y-6\">\n                        <div>\n                          <label htmlFor=\"name\" className=\"block text-gray-300 text-sm font-medium mb-2\">Full Name</label>\n                          <input\n                            type=\"text\"\n                            id=\"name\"\n                            value={name}\n                            onChange={(e) => setName(e.target.value)}\n                            placeholder=\"John Doe\"\n                            required\n                            className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                          />\n                        </div>\n\n                        <div>\n                          <label htmlFor=\"username\" className=\"block text-gray-300 text-sm font-medium mb-2\">Username</label>\n                          <input\n                            type=\"text\"\n                            id=\"username\"\n                            value={username}\n                            onChange={(e) => setUsername(e.target.value)}\n                            placeholder=\"johndoe\"\n                            required\n                            className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                          />\n                        </div>\n\n                        <button\n                          type=\"submit\"\n                          disabled={loading}\n                          className=\"w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\"\n                        >\n                          {loading ? (\n                            <>\n                              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                              Creating Profile...\n                            </>\n                          ) : (\n                            <>\n                              <i className=\"fas fa-user-plus mr-2\"></i>\n                              Complete Setup\n                            </>\n                          )}\n                        </button>\n                      </form>\n                    </>\n                  )}\n                </>\n              )}\n            </div>\n\n            {/* Footer */}\n            <div className=\"text-center mt-8\">\n              <p className=\"text-gray-400 text-sm\">\n                By continuing, you agree to our{' '}\n                <Link href=\"/terms\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors\">Terms</Link> and{' '}\n                <Link href=\"/privacy\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors\">Privacy Policy</Link>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;;AAae,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAA8B;QAClC;YAAE,MAAM;YAAM,SAAS;YAAiB,MAAM;QAAO;QACrD;YAAE,MAAM;YAAM,SAAS;YAAU,MAAM;QAAO;QAC9C;YAAE,MAAM;YAAO,SAAS;YAAkB,MAAM;QAAO;QACvD;YAAE,MAAM;YAAO,SAAS;YAAU,MAAM;QAAO;QAC/C;YAAE,MAAM;YAAO,SAAS;YAAW,MAAM;QAAO;QAChD;YAAE,MAAM;YAAO,SAAS;YAAS,MAAM;QAAO;QAC9C;YAAE,MAAM;YAAO,SAAS;YAAS,MAAM;QAAO;QAC9C;YAAE,MAAM;YAAO,SAAS;YAAS,MAAM;QAAO;QAC9C;YAAE,MAAM;YAAO,SAAS;YAAU,MAAM;QAAO;QAC/C;YAAE,MAAM;YAAO,SAAS;YAAa,MAAM;QAAO;KACnD;IAED,MAAM,UAAU;QACd,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,QAAQ;YACR,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,QAAQ,IAAI,MAAM,QAAQ,MAAM,KAAK,GAAG;YAC3C,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,QAAQ;YACR,WAAW;QACb,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI;YACpC,SAAS;YACT;QACF;QAEA,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uCAAmC;;YASnC;YAEA,WAAW;YACX,WAAW;YAEX,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,IAAI,SAAS,OAAO;YAClB,QAAQ;QACV,OAAO,IAAI,SAAS,WAAW;YAC7B,QAAQ;QACV;QACA,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAmC;;QAKnC;IACF,GAAG;QAAC;KAAO;IAEX,qBACE;;;;;;0BAyCE,8OAAC;0DAAc;0BACb,cAAA,8OAAC;8DAAc;8BACb,cAAA,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAc;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAY;;;;;;;;;;;;;;;;kDAGjB,8OAAC;kFAAa;kDAAoD;;;;;;kDAGlE,8OAAC;kFAAY;kDAA6B;;;;;;kDAC1C,8OAAC;kFAAY;kDAAuB;;;;;;;;;;;;0CAItC,8OAAC;0EAAc;0CAEZ,UACC,iBAAiB,iBACjB,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAY;;;;;;;;;;;sDAEf,8OAAC;sFAAa;sDAAwC;;;;;;sDACtD,8OAAC;sFAAY;sDAAqB;;;;;;sDAClC,8OAAC;sFAAc;;;;;;sDACf,8OAAC;sFAAY;sDAA6B;;;;;;;;;;;yDAG5C;;sDAEE,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;kGAAe,CAAC,qBAAqB,EAAE,SAAS,UAAU,kBAAkB,eAAe;;;;;;kEAC5F,8OAAC;kGAAe,CAAC,qBAAqB,EAAE,SAAS,QAAQ,kBAAkB,eAAe;;;;;;kEAC1F,8OAAC;kGAAe,CAAC,qBAAqB,EAAE,SAAS,YAAY,kBAAkB,eAAe;;;;;;;;;;;;;;;;;wCAKjG,uBACC,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;;kEACb,8OAAC;kGAAY;;;;;;kEACb,8OAAC;kGAAY;kEAAwB;;;;;;;;;;;;;;;;;wCAM1C,SAAS,yBACR;;8DACE,8OAAC;8FAAc;;sEACb,8OAAC;sGAAa;sEAAuG;;;;;;sEACrH,8OAAC;sGAAY;sEAAwB;;;;;;;;;;;;8DAGvC,8OAAC;oDAAK,UAAU,CAAC;wDAAQ,EAAE,cAAc;wDAAI;oDAAW;8FAAa;;sEACnE,8OAAC;;;8EACC,8OAAC;oEAAM,SAAQ;8GAAwB;8EAA+C;;;;;;8EACtF,8OAAC;oEACC,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;8GACpC;8EAET,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,8OAAC;4EAAmB,OAAO,QAAQ,IAAI;;;gFACpC,QAAQ,IAAI;gFAAC;gFAAE,QAAQ,IAAI;gFAAC;gFAAG,QAAQ,OAAO;gFAAC;;2EADrC;;;;;;;;;;;;;;;;sEAOnB,8OAAC;;;8EACC,8OAAC;oEAAM,SAAQ;8GAAkB;8EAA+C;;;;;;8EAChF,8OAAC;8GAAc;;sFACb,8OAAC;sHAAc;sFACZ;;;;;;sFAEH,8OAAC;4EACC,MAAK;4EACL,IAAG;4EACH,OAAO;4EACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4EAC9C,aAAY;4EACZ,QAAQ;sHACE;;;;;;;;;;;;;;;;;;sEAKhB,8OAAC;4DACC,MAAK;4DACL,UAAU;sGACA;sEAET,wBACC;;kFACE,8OAAC;kHAAY;;;;;;oEAAkC;;6FAIjD;;kFACE,8OAAC;kHAAY;;;;;;oEAA8B;;;;;;;;;;;;;;;;wCAUtD,SAAS,uBACR;;8DACE,8OAAC;8FAAc;;sEACb,8OAAC;4DAAO,SAAS;sGAAkB;;8EACjC,8OAAC;8GAAY;;;;;;gEAA6B;;;;;;;sEAG5C,8OAAC;sGAAa;sEAAuG;;;;;;sEACrH,8OAAC;sGAAY;;gEAAwB;gEAA2B;gEAAa;;;;;;;;;;;;;8DAG/E,8OAAC;oDAAK,UAAU,CAAC;wDAAQ,EAAE,cAAc;wDAAI;oDAAa;8FAAa;;sEACrE,8OAAC;;;8EACC,8OAAC;oEAAM,SAAQ;8GAAgB;8EAA+C;;;;;;8EAC9E,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oEAC1C,aAAY;oEACZ,WAAW;oEACX,QAAQ;8GACE;;;;;;;;;;;;sEAId,8OAAC;4DACC,MAAK;4DACL,UAAU;sGACA;sEAET,wBACC;;kFACE,8OAAC;kHAAY;;;;;;oEAAkC;;6FAIjD;;kFACE,8OAAC;kHAAY;;;;;;oEAAwB;;;;;;;;;;;;;;;;wCAUhD,SAAS,2BACR;;8DACE,8OAAC;8FAAc;;sEACb,8OAAC;4DAAO,SAAS;sGAAkB;;8EACjC,8OAAC;8GAAY;;;;;;gEAA6B;;;;;;;sEAG5C,8OAAC;sGAAa;sEAAuG;;;;;;sEACrH,8OAAC;sGAAY;sEAAwB;;;;;;;;;;;;8DAGvC,8OAAC;oDAAK,UAAU,CAAC;wDAAQ,EAAE,cAAc;wDAAI;oDAAmB;8FAAa;;sEAC3E,8OAAC;;;8EACC,8OAAC;oEAAM,SAAQ;8GAAiB;8EAA+C;;;;;;8EAC/E,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oEACvC,aAAY;oEACZ,QAAQ;8GACE;;;;;;;;;;;;sEAId,8OAAC;;;8EACC,8OAAC;oEAAM,SAAQ;8GAAqB;8EAA+C;;;;;;8EACnF,8OAAC;oEACC,MAAK;oEACL,IAAG;oEACH,OAAO;oEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC3C,aAAY;oEACZ,QAAQ;8GACE;;;;;;;;;;;;sEAId,8OAAC;4DACC,MAAK;4DACL,UAAU;sGACA;sEAET,wBACC;;kFACE,8OAAC;kHAAY;;;;;;oEAAkC;;6FAIjD;;kFACE,8OAAC;kHAAY;;;;;;oEAA4B;;;;;;;;;;;;;;;;;;;;;;;0CAa3D,8OAAC;0EAAc;0CACb,cAAA,8OAAC;8EAAY;;wCAAwB;wCACH;sDAChC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA0D;;;;;;wCAAY;wCAAK;sDACzG,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1G", "debugId": null}}]}