(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[919],{760:(e,t,n)=>{"use strict";n.d(t,{N:()=>y});var r=n(5155),i=n(2115),s=n(869),o=n(2885),u=n(7494),l=n(845),c=n(7351),h=n(1508);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function a(e){let{children:t,isPresent:n,anchorX:s,root:o}=e,u=(0,i.useId)(),l=(0,i.useRef)(null),c=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=(0,i.useContext)(h.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:h}=c.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=u;let d=document.createElement("style");a&&(d.nonce=a);let f=null!=o?o:document.head;return f.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(u,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(i):"right: ".concat(h),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{f.removeChild(d),f.contains(d)&&f.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:l,sizeRef:c,children:i.cloneElement(t,{ref:l})})}let f=e=>{let{children:t,initial:n,isPresent:s,onExitComplete:u,custom:c,presenceAffectsLayout:h,mode:d,anchorX:f,root:m}=e,_=(0,o.M)(p),v=(0,i.useId)(),y=!0,S=(0,i.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:s,custom:c,onExitComplete:e=>{for(let t of(_.set(e,!0),_.values()))if(!t)return;u&&u()},register:e=>(_.set(e,!1),()=>_.delete(e))}),[s,_,u]);return h&&y&&(S={...S}),(0,i.useMemo)(()=>{_.forEach((e,t)=>_.set(t,!1))},[s]),i.useEffect(()=>{s||_.size||!u||u()},[s]),"popLayout"===d&&(t=(0,r.jsx)(a,{isPresent:s,anchorX:f,root:m,children:t})),(0,r.jsx)(l.t.Provider,{value:S,children:t})};function p(){return new Map}var m=n(2082);let _=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:c,presenceAffectsLayout:h=!0,mode:d="sync",propagate:a=!1,anchorX:p="left",root:y}=e,[S,g]=(0,m.xQ)(a),R=(0,i.useMemo)(()=>v(t),[t]),w=a&&!S?[]:R.map(_),C=(0,i.useRef)(!0),F=(0,i.useRef)(R),x=(0,o.M)(()=>new Map),[z,b]=(0,i.useState)(R),[j,E]=(0,i.useState)(R);(0,u.E)(()=>{C.current=!1,F.current=R;for(let e=0;e<j.length;e++){let t=_(j[e]);w.includes(t)?x.delete(t):!0!==x.get(t)&&x.set(t,!1)}},[j,w.length,w.join("-")]);let k=[];if(R!==z){let e=[...R];for(let t=0;t<j.length;t++){let n=j[t],r=_(n);w.includes(r)||(e.splice(t,0,n),k.push(n))}return"wait"===d&&k.length&&(e=k),E(v(e)),b(R),null}let{forceRender:A}=(0,i.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:j.map(e=>{let t=_(e),i=(!a||!!S)&&(R===j||w.includes(t));return(0,r.jsx)(f,{isPresent:i,initial:(!C.current||!!l)&&void 0,custom:n,presenceAffectsLayout:h,mode:d,root:y,onExitComplete:i?void 0:()=>{if(!x.has(t))return;x.set(t,!0);let e=!0;x.forEach(t=>{t||(e=!1)}),e&&(null==A||A(),E(F.current),a&&(null==g||g()),c&&c())},anchorX:p,children:e},t)})})}},2269:(e,t,n)=>{"use strict";var r=n(1890);n(8375);var i=n(2115),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==r&&r.env&&!0,u=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,s=void 0===i?o:i;c(u(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){if(c(u(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},n.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},n.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},n.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},n.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},n.makeStyleTag=function(e,t,n){t&&c(u(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var h=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function a(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+h(e+"-"+n)),d[r]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var p=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),r&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=s,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=a(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:a(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var _=s.default.useInsertionEffect||s.default.useLayoutEffect,v="undefined"!=typeof window?new p:void 0;function y(e){var t=v||i.useContext(m);return t&&("undefined"==typeof window?t.add(e):_(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return a(e[0],e[1])}).join(" ")},t.style=y},5695:(e,t,n)=>{"use strict";var r=n(8999);n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},8375:()=>{},9137:(e,t,n)=>{"use strict";e.exports=n(2269).style}}]);