{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\n\ntype MessageType = 'text' | 'payment' | 'image' | 'voice' | 'video' | 'file' | 'location';\ntype MessageStatus = 'sent' | 'delivered' | 'read' | 'sending' | 'failed';\ntype CallType = 'voice' | 'video';\n\ninterface User {\n  id: string;\n  name: string;\n  username: string;\n  phone: string;\n  isOnline: boolean;\n  lastSeen?: string;\n  avatar?: string;\n  status?: string;\n  isTyping?: boolean;\n}\n\ninterface Message {\n  id: string;\n  senderId: string;\n  text: string;\n  timestamp: Date;\n  status: MessageStatus;\n  type: MessageType;\n  amount?: number;\n  currency?: string;\n  imageUrl?: string;\n  fileUrl?: string;\n  fileName?: string;\n  fileSize?: string;\n  duration?: string;\n  location?: { lat: number; lng: number; address: string };\n  replyTo?: string;\n  isForwarded?: boolean;\n  reactions?: { emoji: string; users: string[] }[];\n}\n\ninterface Chat {\n  id: string;\n  participant: User;\n  lastMessage: string;\n  timestamp: Date;\n  unreadCount: number;\n  messages: Message[];\n  isPinned?: boolean;\n  isMuted?: boolean;\n  isArchived?: boolean;\n}\n\ninterface CallState {\n  isActive: boolean;\n  type: CallType;\n  participant?: User;\n  duration?: string;\n  isMuted?: boolean;\n  isVideoOff?: boolean;\n}\n\n// Mock data for demonstration\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    name: 'John Doe',\n    username: 'johndoe',\n    phone: '+1234567890',\n    isOnline: true,\n    lastSeen: '2 min ago',\n    avatar: 'JD',\n    status: 'Available for chat'\n  },\n  {\n    id: '2',\n    name: 'Jane Smith',\n    username: 'janesmith',\n    phone: '+1234567891',\n    isOnline: false,\n    lastSeen: '1 hour ago',\n    avatar: 'JS',\n    status: 'Busy with work'\n  },\n  {\n    id: '3',\n    name: 'Mike Johnson',\n    username: 'mikej',\n    phone: '+1234567892',\n    isOnline: true,\n    lastSeen: 'now',\n    avatar: 'MJ',\n    status: 'Ready to receive payments'\n  },\n  {\n    id: '4',\n    name: 'Sarah Wilson',\n    username: 'sarahw',\n    phone: '+1234567893',\n    isOnline: false,\n    lastSeen: '30 min ago',\n    avatar: 'SW',\n    status: 'At the gym'\n  },\n];\n\nconst mockMessages: Message[] = [\n  {\n    id: '1',\n    senderId: '1',\n    text: 'Hey! How are you doing?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 30),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '2',\n    senderId: 'current',\n    text: 'I\\'m doing great! Just finished a big project.',\n    timestamp: new Date(Date.now() - 1000 * 60 * 25),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '3',\n    senderId: '1',\n    text: 'That\\'s awesome! Want to celebrate? I can send you some money for dinner 🍽️',\n    timestamp: new Date(Date.now() - 1000 * 60 * 20),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '4',\n    senderId: '1',\n    text: 'Here you go!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 15),\n    status: 'read',\n    type: 'payment',\n    amount: 50,\n    currency: 'USD'\n  },\n  {\n    id: '5',\n    senderId: 'current',\n    text: 'Thank you so much! 🙏',\n    timestamp: new Date(Date.now() - 1000 * 60 * 10),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '6',\n    senderId: '1',\n    text: 'Check out this photo from my vacation!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 8),\n    status: 'read',\n    type: 'image',\n    imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'\n  },\n  {\n    id: '7',\n    senderId: 'current',\n    text: 'Beautiful! Where is this?',\n    timestamp: new Date(Date.now() - 1000 * 60 * 5),\n    status: 'read',\n    type: 'text'\n  },\n  {\n    id: '8',\n    senderId: '1',\n    text: 'Voice message',\n    timestamp: new Date(Date.now() - 1000 * 60 * 3),\n    status: 'read',\n    type: 'voice',\n    duration: '0:45'\n  },\n  {\n    id: '9',\n    senderId: 'current',\n    text: 'Let\\'s have a video call later!',\n    timestamp: new Date(Date.now() - 1000 * 60 * 1),\n    status: 'delivered',\n    type: 'text'\n  }\n];\n\nconst mockChats: Chat[] = [\n  {\n    id: '1',\n    participant: mockUsers[0],\n    lastMessage: 'Can you send me the files?',\n    timestamp: new Date(),\n    unreadCount: 2,\n    messages: mockMessages,\n  },\n  {\n    id: '2',\n    participant: mockUsers[1],\n    lastMessage: 'Meeting at 3 PM',\n    timestamp: new Date(Date.now() - 86400000),\n    unreadCount: 0,\n    messages: [],\n  },\n];\n\nexport default function ChatPage() {\n  const router = useRouter();\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [currentChat, setCurrentChat] = useState<Chat | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [chats, setChats] = useState<Chat[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentAmount, setPaymentAmount] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);\n  const [callState, setCallState] = useState<CallState>({ isActive: false, type: 'voice' });\n  const [showUserProfile, setShowUserProfile] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);\n  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);\n\n  // Enhanced security and banking states\n  const [encryptionStatus, setEncryptionStatus] = useState<'connecting' | 'secured' | 'error'>('secured');\n  const [bankBalance, setBankBalance] = useState<number>(2847.32);\n  const [connectedBank, setConnectedBank] = useState<string>('Chase ****1234');\n  const [plaidToken, setPlaidToken] = useState<string | null>(null);\n  const [isVideoCallActive, setIsVideoCallActive] = useState(false);\n  const [localStream, setLocalStream] = useState<MediaStream | null>(null);\n  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);\n  const [peerConnection, setPeerConnection] = useState<RTCPeerConnection | null>(null);\n\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const localVideoRef = useRef<HTMLVideoElement>(null);\n  const remoteVideoRef = useRef<HTMLVideoElement>(null);\n\n  useEffect(() => {\n    // Simulate loading user data\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n        const user: User = {\n          id: 'current',\n          name: 'Current User',\n          username: 'currentuser',\n          phone: '+**********',\n          isOnline: true,\n          avatar: 'CU',\n          status: 'Available'\n        };\n        setCurrentUser(user);\n        setChats(mockChats);\n        setCurrentChat(mockChats[0]);\n        setMessages(mockMessages);\n      } catch (error) {\n        console.error('Error loading chat data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Auto-scroll to bottom of messages and mark messages as read\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n    \n    // Mark messages as read when chat is opened\n    if (currentChat) {\n      const updatedChats = chats.map(chat => {\n        if (chat.id === currentChat.id && chat.unreadCount > 0) {\n          return { ...chat, unreadCount: 0 };\n        }\n        return chat;\n      });\n      setChats(updatedChats);\n    }\n  }, [messages, currentChat]);\n\n  const handleSendMessage = (e?: React.FormEvent) => {\n    e?.preventDefault();\n    if (!newMessage.trim() || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: newMessage,\n      timestamp: new Date(),\n      status: 'sending',\n      type: 'text',\n    };\n\n    // Optimistic update\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setNewMessage('');\n\n    // Simulate message sending\n    setTimeout(() => {\n      setMessages(prevMessages => \n        prevMessages.map(msg => \n          msg.id === message.id \n            ? { ...msg, status: 'delivered' } \n            : msg\n        )\n      );\n    }, 1000);\n\n    // Update chat list\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: newMessage,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: [...updatedMessages],\n          }\n        : chat\n    );\n    \n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleSendPayment = () => {\n    if (!paymentAmount || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: `Payment of $${paymentAmount}`,\n      timestamp: new Date(),\n      status: 'sent',\n      type: 'payment',\n      amount: parseFloat(paymentAmount),\n      currency: 'USD',\n    };\n\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setShowPaymentModal(false);\n    setPaymentAmount('');\n\n    // Update last message in chats\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: `Payment of $${paymentAmount}`,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: updatedMessages,\n          }\n        : chat\n    );\n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleLogout = () => {\n    // Clear user session and redirect to auth page\n    router.push('/auth');\n  };\n\n  if (isLoading || !currentUser) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4\"></div>\n          <p className=\"text-white\">Loading chat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Format time for messages\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Format date for message grouping\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString([], { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    });\n  };\n\n  // Get message status icon\n  const getStatusIcon = (status: MessageStatus) => {\n    switch (status) {\n      case 'sending':\n        return <span className=\"text-gray-400\">🕒</span>;\n      case 'sent':\n        return <span className=\"text-gray-400\">✓</span>;\n      case 'delivered':\n        return <span className=\"text-gray-400\">✓✓</span>;\n      case 'read':\n        return <span className=\"text-blue-500\">✓✓</span>;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n\n        .message-bubble {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(212, 175, 55, 0.2);\n        }\n\n        .message-bubble:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n          border-color: rgba(212, 175, 55, 0.4);\n        }\n\n        .message-bubble-sent {\n          background: rgba(212, 175, 55, 0.15);\n          border-color: rgba(212, 175, 55, 0.3);\n        }\n\n        .message-bubble-received {\n          background: rgba(45, 27, 78, 0.4);\n          border-color: rgba(107, 114, 128, 0.3);\n        }\n\n        .chat-input {\n          background: rgba(17, 24, 39, 0.8);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(75, 85, 99, 0.5);\n          transition: all 0.3s ease;\n        }\n\n        .chat-input:focus {\n          border-color: #D4AF37;\n          box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);\n          background: rgba(17, 24, 39, 0.9);\n        }\n\n        .sidebar-panel {\n          background: linear-gradient(180deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);\n          backdrop-filter: blur(20px);\n          border-right: 1px solid rgba(75, 85, 99, 0.3);\n        }\n\n        .chat-panel {\n          background: linear-gradient(180deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);\n          backdrop-filter: blur(20px);\n        }\n\n        .encryption-indicator {\n          animation: pulse 2s infinite;\n        }\n\n        @keyframes pulse {\n          0%, 100% { opacity: 1; }\n          50% { opacity: 0.7; }\n        }\n\n        .bank-balance {\n          background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%);\n          border: 1px solid rgba(34, 197, 94, 0.3);\n        }\n\n        .professional-shadow {\n          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);\n        }\n      `}</style>\n\n      <div className=\"flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern\">\n        {/* Enhanced Sidebar */}\n        <div className=\"w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow\">\n          {/* Enhanced Header with Encryption Status */}\n          <div className=\"p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md\">\n            <div className=\"flex items-center\">\n              <div className=\"relative\">\n                <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow\">\n                  {currentUser.name.split(' ').map((n: string) => n[0]).join('')}\n                </div>\n                <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator\" title=\"End-to-End Encrypted\"></div>\n              </div>\n              <div className=\"ml-3\">\n                <h2 className=\"font-semibold text-white\">{currentUser.name}</h2>\n                <div className=\"flex items-center space-x-2\">\n                  <p className=\"text-xs text-gray-400\">@{currentUser.username}</p>\n                  <div className=\"flex items-center text-xs text-green-400\">\n                    <i className=\"fas fa-shield-alt mr-1\"></i>\n                    <span>E2E Encrypted</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                title=\"Settings\"\n              >\n                <i className=\"fas fa-cog text-lg\"></i>\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                title=\"Logout\"\n              >\n                <i className=\"fas fa-sign-out-alt text-lg\"></i>\n              </button>\n            </div>\n          </div>\n\n          {/* Enhanced Search Bar */}\n          <div className=\"p-4 border-b border-gray-600/30\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Search conversations...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\"></i>\n              {searchQuery && (\n                <button\n                  onClick={() => setSearchQuery('')}\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              )}\n            </div>\n          </div>\n\n          {/* Chat List */}\n          <div className=\"flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent\">\n            {chats.map((chat) => (\n              <motion.div\n                key={chat.id}\n                className={`p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ${\n                  currentChat?.id === chat.id ? 'bg-gray-800/70 border-l-4 border-l-yellow-400' : ''\n                }`}\n                onClick={() => {\n                  setCurrentChat(chat);\n                  setMessages(chat.messages);\n                }}\n                whileHover={{ x: 6 }}\n                transition={{ duration: 0.3, ease: \"easeOut\" }}\n              >\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow\">\n                      {chat.participant.avatar || chat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {chat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900\"></div>\n                    )}\n                    {chat.unreadCount > 0 && (\n                      <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold\">\n                        {chat.unreadCount}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"ml-3 flex-1\">\n                    <div className=\"flex justify-between items-start\">\n                      <h3 className=\"font-semibold text-white\">{chat.participant.name}</h3>\n                      <span className=\"text-xs text-gray-500\">{formatTime(chat.timestamp)}</span>\n                    </div>\n                    <p className=\"text-sm text-gray-400 truncate\">{chat.lastMessage}</p>\n                    {chat.participant.status && (\n                      <p className=\"text-xs text-yellow-400 italic mt-1\">{chat.participant.status}</p>\n                    )}\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Bank Balance Section */}\n          <div className=\"p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md\">\n            <div className=\"bank-balance rounded-xl p-4 backdrop-blur-lg\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-university text-green-400 mr-2\"></i>\n                  <span className=\"text-sm font-medium text-gray-300\">{connectedBank}</span>\n                </div>\n                <button\n                  onClick={() => setShowSettings(true)}\n                  className=\"text-gray-500 hover:text-yellow-400 transition-colors\"\n                  title=\"Manage Bank Account\"\n                >\n                  <i className=\"fas fa-cog text-sm\"></i>\n                </button>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-xs text-gray-500\">Available Balance</p>\n                  <p className=\"text-xl font-bold text-green-400\">${bankBalance.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>\n                </div>\n                <button\n                  onClick={() => setShowPaymentModal(true)}\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow\"\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  Send\n                </button>\n              </div>\n              <div className=\"mt-2 flex items-center text-xs text-gray-500\">\n                <i className=\"fas fa-shield-alt mr-1 text-green-400\"></i>\n                <span>Secured by Plaid & 256-bit AES encryption</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Enhanced Main Chat Area */}\n        <div className=\"flex-1 flex flex-col chat-panel\">\n          {currentChat ? (\n            <>\n              {/* Enhanced Chat Header */}\n              <div className=\"p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow\">\n                <div className=\"flex items-center\">\n                  <div className=\"relative\">\n                    <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow\">\n                      {currentChat.participant.avatar || currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                    </div>\n                    {currentChat.participant.isOnline && (\n                      <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900\"></div>\n                    )}\n                  </div>\n                  <div className=\"ml-3\">\n                    <h2 className=\"font-semibold text-white\">{currentChat.participant.name}</h2>\n                    <div className=\"flex items-center space-x-2\">\n                      <p className=\"text-xs text-gray-400\">\n                        {currentChat.participant.isOnline\n                          ? (currentChat.participant.isTyping ? 'Typing...' : 'Online')\n                          : `Last seen ${currentChat.participant.lastSeen}`}\n                      </p>\n                      <div className=\"flex items-center text-xs text-green-400\">\n                        <i className=\"fas fa-lock mr-1\"></i>\n                        <span>Encrypted</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Enhanced Chat Actions */}\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={() => setCallState({ isActive: true, type: 'voice', participant: currentChat.participant })}\n                    className=\"text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"Voice Call\"\n                  >\n                    <i className=\"fas fa-phone text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => {\n                      setCallState({ isActive: true, type: 'video', participant: currentChat.participant });\n                      setIsVideoCallActive(true);\n                    }}\n                    className=\"text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"Video Call\"\n                  >\n                    <i className=\"fas fa-video text-lg\"></i>\n                  </button>\n                  <button\n                    onClick={() => setShowUserProfile(true)}\n                    className=\"text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300\"\n                    title=\"User Info\"\n                  >\n                    <i className=\"fas fa-info-circle text-lg\"></i>\n                  </button>\n                </div>\n              </div>\n\n              {/* Messages Area */}\n              <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                {messages.length > 0 && (\n                  <div className=\"text-center mb-4\">\n                    <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                      {formatDate(messages[0].timestamp)}\n                    </span>\n                  </div>\n                )}\n\n                <div className=\"space-y-4\">\n                  {messages.map((message, index) => {\n                    const isCurrentUser = message.senderId === currentUser.id;\n                    const showDate = index === 0 ||\n                      new Date(message.timestamp).toDateString() !==\n                      new Date(messages[index - 1].timestamp).toDateString();\n\n                    return (\n                      <motion.div\n                        key={message.id}\n                        className=\"space-y-1\"\n                        initial={{ opacity: 0, y: 20 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        {showDate && index !== 0 && (\n                          <div className=\"text-center my-4\">\n                            <span className=\"text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full\">\n                              {formatDate(message.timestamp)}\n                            </span>\n                          </div>\n                        )}\n\n                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>\n                          <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${\n                            isCurrentUser\n                              ? 'message-bubble-sent text-white rounded-tr-none'\n                              : 'message-bubble-received text-white rounded-tl-none'\n                          }`}>\n\n                            {/* Enhanced Payment Message */}\n                            {message.type === 'payment' && (\n                              <div className=\"flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg\">\n                                <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3\">\n                                  <i className=\"fas fa-dollar-sign text-white text-sm\"></i>\n                                </div>\n                                <div>\n                                  <span className=\"text-sm font-semibold text-green-400\">\n                                    Payment Sent: ${message.amount?.toFixed(2)} {message.currency}\n                                  </span>\n                                  <p className=\"text-xs text-gray-400 mt-1\">Secured by Plaid • Instant Transfer</p>\n                                </div>\n                              </div>\n                            )}\n\n                            {/* Image Message */}\n                            {message.type === 'image' && message.imageUrl && (\n                              <div className=\"mb-2\">\n                                <img\n                                  src={message.imageUrl}\n                                  alt=\"Shared image\"\n                                  className=\"rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity\"\n                                  onClick={() => window.open(message.imageUrl, '_blank')}\n                                />\n                              </div>\n                            )}\n\n                            {/* Enhanced Voice Message */}\n                            {message.type === 'voice' && (\n                              <div className=\"flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg\">\n                                <button className=\"w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow\">\n                                  <i className=\"fas fa-play text-sm\"></i>\n                                </button>\n                                <div className=\"flex-1 h-2 bg-gray-600/50 rounded-full\">\n                                  <div className=\"h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full\"></div>\n                                </div>\n                                <span className=\"text-xs text-gray-400 font-medium\">{message.duration}</span>\n                              </div>\n                            )}\n\n                            {/* Enhanced Text Message */}\n                            {(message.type === 'text' || message.type === 'payment') && (\n                              <p className={`${message.type === 'payment' ? 'text-sm' : ''} leading-relaxed`}>\n                                {message.text}\n                              </p>\n                            )}\n\n                            {/* Enhanced Message Footer */}\n                            <div className=\"flex items-center justify-end mt-2 space-x-2\">\n                              <span className={`text-xs ${isCurrentUser ? 'text-gray-400' : 'text-gray-500'} font-medium`}>\n                                {formatTime(message.timestamp)}\n                              </span>\n                              {isCurrentUser && (\n                                <span className=\"ml-1\">\n                                  {getStatusIcon(message.status)}\n                                </span>\n                              )}\n                              <div className=\"flex items-center text-xs text-green-400\">\n                                <i className=\"fas fa-lock text-xs\"></i>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </motion.div>\n                    );\n                  })}\n                </div>\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Enhanced Message Input */}\n              <div className=\"p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow\">\n                {/* Reply Preview */}\n                {replyToMessage && (\n                  <div className=\"mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400\">\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <p className=\"text-xs text-yellow-400 font-semibold\">Replying to {replyToMessage.senderId === currentUser.id ? 'yourself' : currentChat.participant.name}</p>\n                        <p className=\"text-sm text-gray-300 truncate\">{replyToMessage.text}</p>\n                      </div>\n                      <button\n                        onClick={() => setReplyToMessage(null)}\n                        className=\"text-gray-400 hover:text-white\"\n                      >\n                        <i className=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                )}\n\n                {/* Attachment Menu */}\n                <AnimatePresence>\n                  {showAttachmentMenu && (\n                    <motion.div\n                      className=\"mb-3 flex space-x-2\"\n                      initial={{ opacity: 0, y: 10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: 10 }}\n                    >\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-image text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Photo</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-file text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Document</span>\n                      </button>\n                      <button className=\"flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors\">\n                        <i className=\"fas fa-map-marker-alt text-yellow-400\"></i>\n                        <span className=\"text-sm text-white\">Location</span>\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <form onSubmit={handleSendMessage} className=\"flex items-end space-x-3\">\n                  {/* Enhanced Attachment Button */}\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowAttachmentMenu(!showAttachmentMenu)}\n                    className=\"p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300\"\n                  >\n                    <i className=\"fas fa-plus text-lg\"></i>\n                  </button>\n\n                  {/* Message Input */}\n                  <div className=\"flex-1 relative\">\n                    <textarea\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      onKeyDown={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault();\n                          handleSendMessage(e);\n                        }\n                      }}\n                      placeholder=\"Type a message...\"\n                      rows={1}\n                      className=\"w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none\"\n                      style={{ minHeight: '48px', maxHeight: '120px' }}\n                    />\n\n                    {/* Enhanced Input Actions */}\n                    <div className=\"absolute right-3 bottom-3 flex items-center space-x-2\">\n                      <button\n                        type=\"button\"\n                        onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                        className=\"text-gray-500 hover:text-yellow-400 transition-all duration-300\"\n                      >\n                        <i className=\"fas fa-smile text-lg\"></i>\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"text-gray-500 hover:text-blue-400 transition-all duration-300\"\n                        title=\"Voice Message\"\n                      >\n                        <i className=\"fas fa-microphone text-lg\"></i>\n                      </button>\n                    </div>\n                  </div>\n\n                  {/* Enhanced Send/Payment Buttons */}\n                  <div className=\"flex space-x-3\">\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPaymentModal(true)}\n                      className=\"p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow\"\n                      title=\"Send Money via Plaid\"\n                    >\n                      <i className=\"fas fa-dollar-sign text-lg\"></i>\n                    </button>\n\n                    <button\n                      type=\"submit\"\n                      disabled={!newMessage.trim()}\n                      className={`p-3 rounded-full transition-all duration-300 professional-shadow ${\n                        newMessage.trim()\n                          ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'\n                          : 'bg-gray-700 text-gray-500 cursor-not-allowed'\n                      }`}\n                    >\n                      <i className=\"fas fa-paper-plane text-lg\"></i>\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <div className=\"text-center p-8\">\n                <div className=\"w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6\">\n                  <i className=\"fas fa-comment-dollar text-3xl text-purple-900\"></i>\n                </div>\n                <h3 className=\"text-xl font-medium text-white mb-2\">Welcome to BoGuani</h3>\n                <p className=\"text-gray-300\">Select a conversation to start messaging</p>\n                <p className=\"text-yellow-400 text-sm mt-2 italic\">\"Speak Gold. Share Value.\"</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Enhanced Call Interface */}\n        <AnimatePresence>\n          {callState.isActive && (\n            <motion.div\n              className=\"fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n            >\n              <div className=\"text-center text-white\">\n                {/* Video Call Area */}\n                {callState.type === 'video' && isVideoCallActive && (\n                  <div className=\"mb-8\">\n                    <div className=\"relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow\">\n                      <video\n                        ref={remoteVideoRef}\n                        autoPlay\n                        playsInline\n                        className=\"w-full h-full object-cover\"\n                      />\n                      <div className=\"absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden\">\n                        <video\n                          ref={localVideoRef}\n                          autoPlay\n                          playsInline\n                          muted\n                          className=\"w-full h-full object-cover\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Call Info */}\n                <div className=\"w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow\">\n                  <span className=\"text-3xl font-bold\">\n                    {callState.participant?.avatar || callState.participant?.name.split(' ').map((n: string) => n[0]).join('')}\n                  </span>\n                </div>\n                <h2 className=\"text-3xl font-semibold mb-2\">{callState.participant?.name}</h2>\n                <div className=\"flex items-center justify-center mb-2\">\n                  <i className=\"fas fa-shield-alt text-green-400 mr-2\"></i>\n                  <p className=\"text-green-400 text-sm\">End-to-End Encrypted</p>\n                </div>\n                <p className=\"text-gray-400 mb-8 text-lg\">\n                  {callState.type === 'video' ? 'Video calling...' : 'Voice calling...'}\n                </p>\n\n                {/* Enhanced Call Controls */}\n                <div className=\"flex justify-center space-x-6\">\n                  <button\n                    onClick={() => setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }))}\n                    className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${\n                      callState.isMuted ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'\n                    }`}\n                  >\n                    <i className={`fas ${callState.isMuted ? 'fa-microphone-slash' : 'fa-microphone'} text-xl`}></i>\n                  </button>\n\n                  {callState.type === 'video' && (\n                    <button\n                      onClick={() => setCallState(prev => ({ ...prev, isVideoOff: !prev.isVideoOff }))}\n                      className={`w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${\n                        callState.isVideoOff ? 'bg-red-600 hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-600'\n                      }`}\n                    >\n                      <i className={`fas ${callState.isVideoOff ? 'fa-video-slash' : 'fa-video'} text-xl`}></i>\n                    </button>\n                  )}\n\n                  <button\n                    onClick={() => {\n                      setCallState({ isActive: false, type: 'voice' });\n                      setIsVideoCallActive(false);\n                    }}\n                    className=\"w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow\"\n                  >\n                    <i className=\"fas fa-phone-slash text-xl\"></i>\n                  </button>\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <motion.div\n          className=\"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n        >\n          <motion.div\n            className=\"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow\"\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.9, opacity: 0 }}\n          >\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-6\">\n                <div>\n                  <h3 className=\"text-xl font-semibold text-white gold-gradient\">Send Payment</h3>\n                  <div className=\"flex items-center mt-1\">\n                    <i className=\"fas fa-shield-alt text-green-400 mr-2 text-sm\"></i>\n                    <span className=\"text-xs text-green-400\">Secured by Plaid</span>\n                  </div>\n                </div>\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n\n              {/* Bank Account Info */}\n              <div className=\"mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-university text-green-400 mr-2\"></i>\n                    <span className=\"text-sm text-gray-300\">{connectedBank}</span>\n                  </div>\n                  <span className=\"text-sm text-green-400 font-semibold\">${bankBalance.toLocaleString()}</span>\n                </div>\n              </div>\n\n              <div className=\"mb-6\">\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-300 mb-2\">\n                  Amount (USD)\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                    <span className=\"text-yellow-400 text-lg font-semibold\">$</span>\n                  </div>\n                  <input\n                    type=\"number\"\n                    name=\"amount\"\n                    id=\"amount\"\n                    value={paymentAmount}\n                    onChange={(e) => setPaymentAmount(e.target.value)}\n                    className=\"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg\"\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                  />\n                </div>\n                <div className=\"flex justify-between items-center mt-2\">\n                  <p className=\"text-xs text-gray-500\">\n                    Sending to: {currentChat?.participant.name}\n                  </p>\n                  <p className=\"text-xs text-green-400\">\n                    <i className=\"fas fa-bolt mr-1\"></i>\n                    Instant Transfer\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleSendPayment}\n                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}\n                  className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ${\n                    paymentAmount && parseFloat(paymentAmount) > 0\n                      ? 'bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100'\n                      : 'bg-gray-700 text-gray-500 cursor-not-allowed'\n                  }`}\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  Send ${paymentAmount || '0.00'}\n                </button>\n              </div>\n\n              {/* Security Notice */}\n              <div className=\"mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20\">\n                <div className=\"flex items-center text-xs text-green-400\">\n                  <i className=\"fas fa-lock mr-2\"></i>\n                  <span>256-bit AES encryption • FDIC insured • Instant settlement</span>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;;AAgEA,8BAA8B;AAC9B,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,QAAQ;IACV;CACD;AAED,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;QAC7C,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI;QACf,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,aAAa;QACb,UAAU,EAAE;IACd;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAAE,UAAU;QAAO,MAAM;IAAQ;IACvF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,uCAAuC;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IAC7F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAE/E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,6BAA6B;YAC7B,MAAM;+CAAW;oBACf,IAAI;wBACF,aAAa;wBACb,MAAM,OAAa;4BACjB,IAAI;4BACJ,MAAM;4BACN,UAAU;4BACV,OAAO;4BACP,UAAU;4BACV,QAAQ;4BACR,QAAQ;wBACV;wBACA,eAAe;wBACf,SAAS;wBACT,eAAe,SAAS,CAAC,EAAE;wBAC3B,YAAY;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC7D;YAEA,4CAA4C;YAC5C,IAAI,aAAa;gBACf,MAAM,eAAe,MAAM,GAAG;uDAAC,CAAA;wBAC7B,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,WAAW,GAAG,GAAG;4BACtD,OAAO;gCAAE,GAAG,IAAI;gCAAE,aAAa;4BAAE;wBACnC;wBACA,OAAO;oBACT;;gBACA,SAAS;YACX;QACF;6BAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,oBAAoB,CAAC;QACzB,GAAG;QACH,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,eAAe,CAAC,aAAa;QAExD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM;YACN,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;QACR;QAEA,oBAAoB;QACpB,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,cAAc;QAEd,2BAA2B;QAC3B,WAAW;YACT,YAAY,CAAA,eACV,aAAa,GAAG,CAAC,CAAA,MACf,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAY,IAC9B;QAGV,GAAG;QAEH,mBAAmB;QACnB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa;gBACb,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;uBAAI;iBAAgB;YAChC,IACA;QAGN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa;QAEpD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM,CAAC,YAAY,EAAE,eAAe;YACpC,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;YACN,QAAQ,WAAW;YACnB,UAAU;QACZ;QAEA,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QAEjB,+BAA+B;QAC/B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa,CAAC,YAAY,EAAE,eAAe;gBAC3C,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;YACZ,IACA;QAEN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,eAAe;QACnB,+CAA+C;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,aAAa,CAAC,aAAa;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACjC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC;gBACE,OAAO;QACX;IACF;IAEA,qBACE;;;;;;0BA2FE,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAc;;0CAEb,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAE7D,6LAAC;wDAA4H,OAAM;kGAApH;;;;;;;;;;;;0DAEjB,6LAAC;0FAAc;;kEACb,6LAAC;kGAAa;kEAA4B,YAAY,IAAI;;;;;;kEAC1D,6LAAC;kGAAc;;0EACb,6LAAC;0GAAY;;oEAAwB;oEAAE,YAAY,QAAQ;;;;;;;0EAC3D,6LAAC;0GAAc;;kFACb,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKd,6LAAC;kFAAc;;0DACb,6LAAC;gDACC,SAAS,IAAM,gBAAgB;gDAE/B,OAAM;0FADI;0DAGV,cAAA,6LAAC;8FAAY;;;;;;;;;;;0DAEf,6LAAC;gDACC,SAAS;gDAET,OAAM;0FADI;0DAGV,cAAA,6LAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;0CAMnB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;sFACpC;;;;;;sDAEZ,6LAAC;sFAAY;;;;;;wCACZ,6BACC,6LAAC;4CACC,SAAS,IAAM,eAAe;sFACpB;sDAEV,cAAA,6LAAC;0FAAY;;;;;;;;;;;;;;;;;;;;;;0CAOrB,6LAAC;0EAAc;0CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC,gGAAgG,EAC1G,aAAa,OAAO,KAAK,EAAE,GAAG,kDAAkD,IAChF;wCACF,SAAS;4CACP,eAAe;4CACf,YAAY,KAAK,QAAQ;wCAC3B;wCACA,YAAY;4CAAE,GAAG;wCAAE;wCACnB,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAU;kDAE7C,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACZ,KAAK,WAAW,CAAC,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE5F,KAAK,WAAW,CAAC,QAAQ,kBACxB,6LAAC;sGAAc;;;;;;wDAEhB,KAAK,WAAW,GAAG,mBAClB,6LAAC;sGAAc;sEACZ,KAAK,WAAW;;;;;;;;;;;;8DAIvB,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;;8EACb,6LAAC;8GAAa;8EAA4B,KAAK,WAAW,CAAC,IAAI;;;;;;8EAC/D,6LAAC;8GAAe;8EAAyB,WAAW,KAAK,SAAS;;;;;;;;;;;;sEAEpE,6LAAC;sGAAY;sEAAkC,KAAK,WAAW;;;;;;wDAC9D,KAAK,WAAW,CAAC,MAAM,kBACtB,6LAAC;sGAAY;sEAAuC,KAAK,WAAW,CAAC,MAAM;;;;;;;;;;;;;;;;;;uCAhC5E,KAAK,EAAE;;;;;;;;;;0CAyClB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAY;;;;;;sEACb,6LAAC;sGAAe;sEAAqC;;;;;;;;;;;;8DAEvD,6LAAC;oDACC,SAAS,IAAM,gBAAgB;oDAE/B,OAAM;8FADI;8DAGV,cAAA,6LAAC;kGAAY;;;;;;;;;;;;;;;;;sDAGjB,6LAAC;sFAAc;;8DACb,6LAAC;;;sEACC,6LAAC;sGAAY;sEAAwB;;;;;;sEACrC,6LAAC;sGAAY;;gEAAmC;gEAAE,YAAY,cAAc,CAAC,SAAS;oEAAE,uBAAuB;gEAAE;;;;;;;;;;;;;8DAEnH,6LAAC;oDACC,SAAS,IAAM,oBAAoB;8FACzB;;sEAEV,6LAAC;sGAAY;;;;;;wDAA8B;;;;;;;;;;;;;sDAI/C,6LAAC;sFAAc;;8DACb,6LAAC;8FAAY;;;;;;8DACb,6LAAC;;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;kEAAc;kCACZ,4BACC;;8CAEE,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACZ,YAAY,WAAW,CAAC,MAAM,IAAI,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;wDAE1G,YAAY,WAAW,CAAC,QAAQ,kBAC/B,6LAAC;sGAAc;;;;;;;;;;;;8DAGnB,6LAAC;8FAAc;;sEACb,6LAAC;sGAAa;sEAA4B,YAAY,WAAW,CAAC,IAAI;;;;;;sEACtE,6LAAC;sGAAc;;8EACb,6LAAC;8GAAY;8EACV,YAAY,WAAW,CAAC,QAAQ,GAC5B,YAAY,WAAW,CAAC,QAAQ,GAAG,cAAc,WAClD,CAAC,UAAU,EAAE,YAAY,WAAW,CAAC,QAAQ,EAAE;;;;;;8EAErD,6LAAC;8GAAc;;sFACb,6LAAC;sHAAY;;;;;;sFACb,6LAAC;;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOd,6LAAC;sFAAc;;8DACb,6LAAC;oDACC,SAAS,IAAM,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;oDAElG,OAAM;8FADI;8DAGV,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;oDACC,SAAS;wDACP,aAAa;4DAAE,UAAU;4DAAM,MAAM;4DAAS,aAAa,YAAY,WAAW;wDAAC;wDACnF,qBAAqB;oDACvB;oDAEA,OAAM;8FADI;8DAGV,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAElC,OAAM;8FADI;8DAGV,cAAA,6LAAC;kGAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;8EAAc;;wCACZ,SAAS,MAAM,GAAG,mBACjB,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAe;0DACb,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS;;;;;;;;;;;sDAKvC,6LAAC;sFAAc;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gDACtB,MAAM,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;gDACzD,MAAM,WAAW,UAAU,KACzB,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY,OACxC,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY;gDAEtD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;oDAAI;;wDAE3B,YAAY,UAAU,mBACrB,6LAAC;sGAAc;sEACb,cAAA,6LAAC;0GAAe;0EACb,WAAW,QAAQ,SAAS;;;;;;;;;;;sEAKnC,6LAAC;sGAAe,CAAC,KAAK,EAAE,gBAAgB,gBAAgB,iBAAiB;sEACvE,cAAA,6LAAC;0GAAe,CAAC,0DAA0D,EACzE,gBACI,mDACA,sDACJ;;oEAGC,QAAQ,IAAI,KAAK,2BAChB,6LAAC;kHAAc;;0FACb,6LAAC;0HAAc;0FACb,cAAA,6LAAC;8HAAY;;;;;;;;;;;0FAEf,6LAAC;;;kGACC,6LAAC;kIAAe;;4FAAuC;4FACrC,QAAQ,MAAM,EAAE,QAAQ;4FAAG;4FAAE,QAAQ,QAAQ;;;;;;;kGAE/D,6LAAC;kIAAY;kGAA6B;;;;;;;;;;;;;;;;;;oEAM/C,QAAQ,IAAI,KAAK,WAAW,QAAQ,QAAQ,kBAC3C,6LAAC;kHAAc;kFACb,cAAA,6LAAC;4EACC,KAAK,QAAQ,QAAQ;4EACrB,KAAI;4EAEJ,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,QAAQ,EAAE;sHADnC;;;;;;;;;;;oEAOf,QAAQ,IAAI,KAAK,yBAChB,6LAAC;kHAAc;;0FACb,6LAAC;0HAAiB;0FAChB,cAAA,6LAAC;8HAAY;;;;;;;;;;;0FAEf,6LAAC;0HAAc;0FACb,cAAA,6LAAC;8HAAc;;;;;;;;;;;0FAEjB,6LAAC;0HAAe;0FAAqC,QAAQ,QAAQ;;;;;;;;;;;;oEAKxE,CAAC,QAAQ,IAAI,KAAK,UAAU,QAAQ,IAAI,KAAK,SAAS,mBACrD,6LAAC;kHAAa,GAAG,QAAQ,IAAI,KAAK,YAAY,YAAY,GAAG,gBAAgB,CAAC;kFAC3E,QAAQ,IAAI;;;;;;kFAKjB,6LAAC;kHAAc;;0FACb,6LAAC;0HAAgB,CAAC,QAAQ,EAAE,gBAAgB,kBAAkB,gBAAgB,YAAY,CAAC;0FACxF,WAAW,QAAQ,SAAS;;;;;;4EAE9B,+BACC,6LAAC;0HAAe;0FACb,cAAc,QAAQ,MAAM;;;;;;0FAGjC,6LAAC;0HAAc;0FACb,cAAA,6LAAC;8HAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDA/EhB,QAAQ,EAAE;;;;;4CAsFrB;;;;;;sDAEF,6LAAC;4CAAI,KAAK;;;;;;;;;;;;;8CAIZ,6LAAC;8EAAc;;wCAEZ,gCACC,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;;;0EACC,6LAAC;0GAAY;;oEAAwC;oEAAa,eAAe,QAAQ,KAAK,YAAY,EAAE,GAAG,aAAa,YAAY,WAAW,CAAC,IAAI;;;;;;;0EACxJ,6LAAC;0GAAY;0EAAkC,eAAe,IAAI;;;;;;;;;;;;kEAEpE,6LAAC;wDACC,SAAS,IAAM,kBAAkB;kGACvB;kEAEV,cAAA,6LAAC;sGAAY;;;;;;;;;;;;;;;;;;;;;;sDAOrB,6LAAC,4LAAA,CAAA,kBAAe;sDACb,oCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;;kEAE1B,6LAAC;kGAAiB;;0EAChB,6LAAC;0GAAY;;;;;;0EACb,6LAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,6LAAC;kGAAiB;;0EAChB,6LAAC;0GAAY;;;;;;0EACb,6LAAC;0GAAe;0EAAqB;;;;;;;;;;;;kEAEvC,6LAAC;kGAAiB;;0EAChB,6LAAC;0GAAY;;;;;;0EACb,6LAAC;0GAAe;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;sDAM7C,6LAAC;4CAAK,UAAU;sFAA6B;;8DAE3C,6LAAC;oDACC,MAAK;oDACL,SAAS,IAAM,sBAAsB,CAAC;8FAC5B;8DAEV,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAIf,6LAAC;8FAAc;;sEACb,6LAAC;4DACC,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAW,CAAC;gEACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oEACpC,EAAE,cAAc;oEAChB,kBAAkB;gEACpB;4DACF;4DACA,aAAY;4DACZ,MAAM;4DAEN,OAAO;gEAAE,WAAW;gEAAQ,WAAW;4DAAQ;sGADrC;;;;;;sEAKZ,6LAAC;sGAAc;;8EACb,6LAAC;oEACC,MAAK;oEACL,SAAS,IAAM,mBAAmB,CAAC;8GACzB;8EAEV,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;oEACC,MAAK;oEAEL,OAAM;8GADI;8EAGV,cAAA,6LAAC;kHAAY;;;;;;;;;;;;;;;;;;;;;;;8DAMnB,6LAAC;8FAAc;;sEACb,6LAAC;4DACC,MAAK;4DACL,SAAS,IAAM,oBAAoB;4DAEnC,OAAM;sGADI;sEAGV,cAAA,6LAAC;0GAAY;;;;;;;;;;;sEAGf,6LAAC;4DACC,MAAK;4DACL,UAAU,CAAC,WAAW,IAAI;sGACf,CAAC,iEAAiE,EAC3E,WAAW,IAAI,KACX,2GACA,gDACJ;sEAEF,cAAA,6LAAC;0GAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOvB,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAY;;;;;;;;;;;kDAEf,6LAAC;kFAAa;kDAAsC;;;;;;kDACpD,6LAAC;kFAAY;kDAAgB;;;;;;kDAC7B,6LAAC;kFAAY;kDAAsC;;;;;;;;;;;;;;;;;;;;;;kCAO3D,6LAAC,4LAAA,CAAA,kBAAe;kCACb,UAAU,QAAQ,kBACjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;sCAEnB,cAAA,6LAAC;0EAAc;;oCAEZ,UAAU,IAAI,KAAK,WAAW,mCAC7B,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;oDACC,KAAK;oDACL,QAAQ;oDACR,WAAW;8FACD;;;;;;8DAEZ,6LAAC;8FAAc;8DACb,cAAA,6LAAC;wDACC,KAAK;wDACL,QAAQ;wDACR,WAAW;wDACX,KAAK;kGACK;;;;;;;;;;;;;;;;;;;;;;kDAQpB,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAe;sDACb,UAAU,WAAW,EAAE,UAAU,UAAU,WAAW,EAAE,KAAK,MAAM,KAAK,IAAI,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,KAAK;;;;;;;;;;;kDAG3G,6LAAC;kFAAa;kDAA+B,UAAU,WAAW,EAAE;;;;;;kDACpE,6LAAC;kFAAc;;0DACb,6LAAC;0FAAY;;;;;;0DACb,6LAAC;0FAAY;0DAAyB;;;;;;;;;;;;kDAExC,6LAAC;kFAAY;kDACV,UAAU,IAAI,KAAK,UAAU,qBAAqB;;;;;;kDAIrD,6LAAC;kFAAc;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,CAAC,KAAK,OAAO;wDAAC,CAAC;0FAC7D,CAAC,wGAAwG,EAClH,UAAU,OAAO,GAAG,gCAAgC,iCACpD;0DAEF,cAAA,6LAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,OAAO,GAAG,wBAAwB,gBAAgB,QAAQ,CAAC;;;;;;;;;;;4CAG3F,UAAU,IAAI,KAAK,yBAClB,6LAAC;gDACC,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,CAAC,KAAK,UAAU;wDAAC,CAAC;0FACnE,CAAC,wGAAwG,EAClH,UAAU,UAAU,GAAG,gCAAgC,iCACvD;0DAEF,cAAA,6LAAC;8FAAa,CAAC,IAAI,EAAE,UAAU,UAAU,GAAG,mBAAmB,WAAW,QAAQ,CAAC;;;;;;;;;;;0DAIvF,6LAAC;gDACC,SAAS;oDACP,aAAa;wDAAE,UAAU;wDAAO,MAAM;oDAAQ;oDAC9C,qBAAqB;gDACvB;0FACU;0DAEV,cAAA,6LAAC;8FAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU1B,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;0BAEnB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAK,SAAS;oBAAE;8BAE/B,cAAA,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC;;;0DACC,6LAAC;0FAAa;0DAAiD;;;;;;0DAC/D,6LAAC;0FAAc;;kEACb,6LAAC;kGAAY;;;;;;kEACb,6LAAC;kGAAe;kEAAyB;;;;;;;;;;;;;;;;;;kDAG7C,6LAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDAEV,cAAA,6LAAC;sFAAY;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;8FAAY;;;;;;8DACb,6LAAC;8FAAe;8DAAyB;;;;;;;;;;;;sDAE3C,6LAAC;sFAAe;;gDAAuC;gDAAE,YAAY,cAAc;;;;;;;;;;;;;;;;;;0CAIvF,6LAAC;0EAAc;;kDACb,6LAAC;wCAAM,SAAQ;kFAAmB;kDAA+C;;;;;;kDAGjF,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAe;8DAAwC;;;;;;;;;;;0DAE1D,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAEhD,aAAY;gDACZ,MAAK;gDACL,KAAI;0FAHM;;;;;;;;;;;;kDAMd,6LAAC;kFAAc;;0DACb,6LAAC;0FAAY;;oDAAwB;oDACtB,aAAa,YAAY;;;;;;;0DAExC,6LAAC;0FAAY;;kEACX,6LAAC;kGAAY;;;;;;oDAAuB;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;0EAAc;;kDACb,6LAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;kFACU;kDACX;;;;;;kDAGD,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,iBAAiB,WAAW,kBAAkB;kFAC9C,CAAC,mFAAmF,EAC7F,iBAAiB,WAAW,iBAAiB,IACzC,2GACA,gDACJ;;0DAEF,6LAAC;0FAAY;;;;;;4CAA8B;4CACpC,iBAAiB;;;;;;;;;;;;;0CAK5B,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAY;;;;;;sDACb,6LAAC;;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAj7BwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}