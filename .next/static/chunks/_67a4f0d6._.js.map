{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\n\ntype MessageType = 'text' | 'payment' | 'image';\ntype MessageStatus = 'sent' | 'delivered' | 'read' | 'sending';\n\ninterface User {\n  id: string;\n  name: string;\n  username: string;\n  isOnline: boolean;\n  lastSeen?: string;\n  avatar?: string;\n}\n\ninterface Message {\n  id: string;\n  senderId: string;\n  text: string;\n  timestamp: Date;\n  status: MessageStatus;\n  type: MessageType;\n  amount?: number;\n  currency?: string;\n  imageUrl?: string;\n}\n\ninterface Chat {\n  id: string;\n  participant: User;\n  lastMessage: string;\n  timestamp: Date;\n  unreadCount: number;\n  messages: Message[];\n}\n\n// Mock data for demonstration\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    name: '<PERSON>',\n    username: 'johndo<PERSON>',\n    isOnline: true,\n    lastSeen: '2 min ago',\n    avatar: 'JD'\n  },\n  {\n    id: '2',\n    name: '<PERSON>',\n    username: 'j<PERSON><PERSON>',\n    isOnline: false,\n    lastSeen: '1 hour ago',\n    avatar: 'JS'\n  },\n];\n\nconst mockMessages: Message[] = [\n  {\n    id: '1',\n    senderId: '1',\n    text: 'Hey there!',\n    timestamp: new Date(Date.now() - 3600000),\n    status: 'read',\n    type: 'text',\n  },\n  {\n    id: '2',\n    senderId: 'current',\n    text: 'Hi! How are you?',\n    timestamp: new Date(Date.now() - 1800000),\n    status: 'read',\n    type: 'text',\n  },\n  {\n    id: '3',\n    senderId: '1',\n    text: 'Can you send me the files?',\n    timestamp: new Date(),\n    status: 'read',\n    type: 'text',\n  },\n];\n\nconst mockChats: Chat[] = [\n  {\n    id: '1',\n    participant: mockUsers[0],\n    lastMessage: 'Can you send me the files?',\n    timestamp: new Date(),\n    unreadCount: 2,\n    messages: mockMessages,\n  },\n  {\n    id: '2',\n    participant: mockUsers[1],\n    lastMessage: 'Meeting at 3 PM',\n    timestamp: new Date(Date.now() - 86400000),\n    unreadCount: 0,\n    messages: [],\n  },\n];\n\nexport default function ChatPage() {\n  const router = useRouter();\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [currentChat, setCurrentChat] = useState<Chat | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [chats, setChats] = useState<Chat[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [paymentAmount, setPaymentAmount] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    // Simulate loading user data\n    const loadData = async () => {\n      try {\n        setIsLoading(true);\n        const user: User = {\n          id: 'current',\n          name: 'Current User',\n          username: 'currentuser',\n          isOnline: true,\n          avatar: 'CU'\n        };\n        setCurrentUser(user);\n        setChats(mockChats);\n        setCurrentChat(mockChats[0]);\n        setMessages(mockMessages);\n      } catch (error) {\n        console.error('Error loading chat data:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadData();\n  }, []);\n\n  // Auto-scroll to bottom of messages and mark messages as read\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n    \n    // Mark messages as read when chat is opened\n    if (currentChat) {\n      const updatedChats = chats.map(chat => {\n        if (chat.id === currentChat.id && chat.unreadCount > 0) {\n          return { ...chat, unreadCount: 0 };\n        }\n        return chat;\n      });\n      setChats(updatedChats);\n    }\n  }, [messages, currentChat]);\n\n  const handleSendMessage = (e?: React.FormEvent) => {\n    e?.preventDefault();\n    if (!newMessage.trim() || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: newMessage,\n      timestamp: new Date(),\n      status: 'sending',\n      type: 'text',\n    };\n\n    // Optimistic update\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setNewMessage('');\n\n    // Simulate message sending\n    setTimeout(() => {\n      setMessages(prevMessages => \n        prevMessages.map(msg => \n          msg.id === message.id \n            ? { ...msg, status: 'delivered' } \n            : msg\n        )\n      );\n    }, 1000);\n\n    // Update chat list\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: newMessage,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: [...updatedMessages],\n          }\n        : chat\n    );\n    \n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleSendPayment = () => {\n    if (!paymentAmount || !currentChat || !currentUser) return;\n\n    const message: Message = {\n      id: Date.now().toString(),\n      senderId: currentUser.id,\n      text: `Payment of $${paymentAmount}`,\n      timestamp: new Date(),\n      status: 'sent',\n      type: 'payment',\n      amount: parseFloat(paymentAmount),\n      currency: 'USD',\n    };\n\n    const updatedMessages = [...messages, message];\n    setMessages(updatedMessages);\n    setShowPaymentModal(false);\n    setPaymentAmount('');\n\n    // Update last message in chats\n    const updatedChats = chats.map((chat) =>\n      chat.id === currentChat.id\n        ? {\n            ...chat,\n            lastMessage: `Payment of $${paymentAmount}`,\n            timestamp: new Date(),\n            unreadCount: 0,\n            messages: updatedMessages,\n          }\n        : chat\n    );\n    setChats(updatedChats);\n    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);\n  };\n\n  const handleLogout = () => {\n    // Clear user session and redirect to auth page\n    router.push('/auth');\n  };\n\n  if (isLoading || !currentUser) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4\"></div>\n          <p className=\"text-white\">Loading chat...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Format time for messages\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  // Format date for message grouping\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString([], { \n      weekday: 'long', \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric' \n    });\n  };\n\n  // Get message status icon\n  const getStatusIcon = (status: MessageStatus) => {\n    switch (status) {\n      case 'sending':\n        return <span className=\"text-gray-400\">🕒</span>;\n      case 'sent':\n        return <span className=\"text-gray-400\">✓</span>;\n      case 'delivered':\n        return <span className=\"text-gray-400\">✓✓</span>;\n      case 'read':\n        return <span className=\"text-blue-500\">✓✓</span>;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar */}\n      <div className=\"w-full md:w-1/3 border-r border-gray-200 bg-white flex flex-col\">\n        {/* Header */}\n        <div className=\"p-4 border-b border-gray-200 flex justify-between items-center bg-white sticky top-0 z-10\">\n          <div className=\"flex items-center\">\n            <div className=\"w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-white font-bold\">\n              {currentUser.name.split(' ').map((n: string) => n[0]).join('')}\n            </div>\n            <div className=\"ml-3\">\n              <h2 className=\"font-semibold\">{currentUser.name}</h2>\n              <p className=\"text-xs text-gray-500\">@{currentUser.username}</p>\n            </div>\n          </div>\n          <button \n            onClick={handleLogout}\n            className=\"text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100\"\n            title=\"Logout\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Chat Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {currentChat ? (\n            <>\n              {/* Chat Header */}\n              <div className=\"p-4 border-b border-gray-200 flex items-center bg-white sticky top-0 z-10\">\n                <div className=\"relative\">\n                  <div className=\"w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold\">\n                    {currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}\n                  </div>\n                  {currentChat.participant.isOnline && (\n                    <div className=\"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n                  )}\n                </div>\n                <div className=\"ml-3\">\n                  <h2 className=\"font-semibold\">{currentChat.participant.name}</h2>\n                  <p className=\"text-xs text-gray-500\">\n                    {currentChat.participant.isOnline \n                      ? 'Online' \n                      : `Last seen ${currentChat.participant.lastSeen}`}\n                  </p>\n                </div>\n              </div>\n\n              {/* Messages */}\n              <div className=\"flex-1 overflow-y-auto p-4 bg-gray-50\">\n                {messages.length > 0 && (\n                  <div className=\"text-center mb-4\">\n                    <span className=\"text-xs text-gray-500 bg-white px-3 py-1 rounded-full\">\n                      {formatDate(messages[0].timestamp)}\n                    </span>\n                  </div>\n                )}\n                \n                <div className=\"space-y-4\">\n                  {messages.map((message, index) => {\n                    const isCurrentUser = message.senderId === currentUser.id;\n                    const showDate = index === 0 || \n                      new Date(message.timestamp).toDateString() !== \n                      new Date(messages[index - 1].timestamp).toDateString();\n                    \n                    return (\n                      <div key={message.id} className=\"space-y-1\">\n                        {showDate && index !== 0 && (\n                          <div className=\"text-center my-4\">\n                            <span className=\"text-xs text-gray-500 bg-white px-3 py-1 rounded-full\">\n                              {formatDate(message.timestamp)}\n                            </span>\n                          </div>\n                        )}\n                        \n                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>\n                          <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${\n                            isCurrentUser\n                              ? 'bg-yellow-400 rounded-tr-none'\n                              : 'bg-white border border-gray-200 rounded-tl-none shadow-sm'\n                          }`}>\n                            {message.type === 'payment' && (\n                              <div className=\"flex items-center mb-1\">\n                                <svg className=\"w-4 h-4 text-green-600 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                                  <path fillRule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z\" clipRule=\"evenodd\" />\n                                </svg>\n                                <span className=\"text-sm font-medium text-green-700\">\n                                  Payment: ${message.amount?.toFixed(2)}\n                                </span>\n                              </div>\n                            )}\n                            <p className={`text-gray-800 ${message.type === 'payment' ? 'text-sm' : ''}`}>\n                              {message.text}\n                            </p>\n                            <div className=\"flex items-center justify-end mt-1 space-x-1\">\n                              <span className=\"text-xs text-gray-500\">\n                                {formatTime(message.timestamp)}\n                              </span>\n                              {isCurrentUser && (\n                                <span className=\"ml-1\">\n                                  {getStatusIcon(message.status)}\n                                </span>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Message Input */}\n              <div className=\"p-4 border-t border-gray-200 bg-white\">\n                <form onSubmit={handleSendMessage} className=\"flex items-center\">\n                  <div className=\"relative flex-1\">\n                    <input\n                      type=\"text\"\n                      value={newMessage}\n                      onChange={(e) => setNewMessage(e.target.value)}\n                      placeholder=\"Type a message...\"\n                      className=\"w-full py-3 px-4 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent pr-12\"\n                    />\n                    <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1\">\n                      <button\n                        type=\"button\"\n                        className=\"text-gray-400 hover:text-gray-600 focus:outline-none\"\n                      >\n                        <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"text-gray-400 hover:text-gray-600 focus:outline-none\"\n                      >\n                        <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13\" />\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n                  <button\n                    type=\"submit\"\n                    disabled={!newMessage.trim()}\n                    className={`ml-2 p-2 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${\n                      newMessage.trim()\n                        ? 'bg-yellow-400 hover:bg-yellow-500 focus:ring-yellow-400'\n                        : 'bg-gray-300 cursor-not-allowed'\n                    }`}\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                      />\n                    </svg>\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPaymentModal(true)}\n                    className=\"ml-2 p-2 rounded-full bg-green-500 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2\"\n                    title=\"Send Payment\"\n                  >\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      className=\"h-6 w-6\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke=\"currentColor\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                      />\n                    </svg>\n                  </button>\n                </form>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center bg-gray-50\">\n              <div className=\"text-center p-8\">\n                <div className=\"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-yellow-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-1\">Select a chat</h3>\n                <p className=\"text-gray-500\">Choose a conversation to start messaging</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\n            <div className=\"p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Send Payment</h3>\n                <button\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"text-gray-400 hover:text-gray-500\"\n                >\n                  <span className=\"sr-only\">Close</span>\n                  <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <div className=\"mb-6\">\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Amount (USD)\n                </label>\n                <div className=\"mt-1 relative rounded-md shadow-sm\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <span className=\"text-gray-500 sm:text-sm\">$</span>\n                  </div>\n                  <input\n                    type=\"number\"\n                    name=\"amount\"\n                    id=\"amount\"\n                    value={paymentAmount}\n                    onChange={(e) => setPaymentAmount(e.target.value)}\n                    className=\"focus:ring-yellow-400 focus:border-yellow-400 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md py-3\"\n                    placeholder=\"0.00\"\n                    step=\"0.01\"\n                    min=\"0.01\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setShowPaymentModal(false);\n                    setPaymentAmount('');\n                  }}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={handleSendPayment}\n                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}\n                  className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400 ${\n                    !paymentAmount || parseFloat(paymentAmount) <= 0\n                      ? 'bg-yellow-300 cursor-not-allowed'\n                      : 'bg-yellow-500 hover:bg-yellow-600'\n                  }`}\n                >\n                  Send Payment\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAuCA,8BAA8B;AAC9B,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,QAAQ;QACR,MAAM;IACR;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,WAAW,IAAI;QACf,QAAQ;QACR,MAAM;IACR;CACD;AAED,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI;QACf,aAAa;QACb,UAAU;IACZ;IACA;QACE,IAAI;QACJ,aAAa,SAAS,CAAC,EAAE;QACzB,aAAa;QACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACjC,aAAa;QACb,UAAU,EAAE;IACd;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,6BAA6B;YAC7B,MAAM;+CAAW;oBACf,IAAI;wBACF,aAAa;wBACb,MAAM,OAAa;4BACjB,IAAI;4BACJ,MAAM;4BACN,UAAU;4BACV,UAAU;4BACV,QAAQ;wBACV;wBACA,eAAe;wBACf,SAAS;wBACT,eAAe,SAAS,CAAC,EAAE;wBAC3B,YAAY;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC7D;YAEA,4CAA4C;YAC5C,IAAI,aAAa;gBACf,MAAM,eAAe,MAAM,GAAG;uDAAC,CAAA;wBAC7B,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE,IAAI,KAAK,WAAW,GAAG,GAAG;4BACtD,OAAO;gCAAE,GAAG,IAAI;gCAAE,aAAa;4BAAE;wBACnC;wBACA,OAAO;oBACT;;gBACA,SAAS;YACX;QACF;6BAAG;QAAC;QAAU;KAAY;IAE1B,MAAM,oBAAoB,CAAC;QACzB,GAAG;QACH,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,eAAe,CAAC,aAAa;QAExD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM;YACN,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;QACR;QAEA,oBAAoB;QACpB,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,cAAc;QAEd,2BAA2B;QAC3B,WAAW;YACT,YAAY,CAAA,eACV,aAAa,GAAG,CAAC,CAAA,MACf,IAAI,EAAE,KAAK,QAAQ,EAAE,GACjB;wBAAE,GAAG,GAAG;wBAAE,QAAQ;oBAAY,IAC9B;QAGV,GAAG;QAEH,mBAAmB;QACnB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa;gBACb,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;uBAAI;iBAAgB;YAChC,IACA;QAGN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,aAAa;QAEpD,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,UAAU,YAAY,EAAE;YACxB,MAAM,CAAC,YAAY,EAAE,eAAe;YACpC,WAAW,IAAI;YACf,QAAQ;YACR,MAAM;YACN,QAAQ,WAAW;YACnB,UAAU;QACZ;QAEA,MAAM,kBAAkB;eAAI;YAAU;SAAQ;QAC9C,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QAEjB,+BAA+B;QAC/B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,OAC9B,KAAK,EAAE,KAAK,YAAY,EAAE,GACtB;gBACE,GAAG,IAAI;gBACP,aAAa,CAAC,YAAY,EAAE,eAAe;gBAC3C,WAAW,IAAI;gBACf,aAAa;gBACb,UAAU;YACZ,IACA;QAEN,SAAS;QACT,eAAe,aAAa,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK;IAC1E;IAEA,MAAM,eAAe;QACnB,+CAA+C;QAC/C,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,aAAa,CAAC,aAAa;QAC7B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;IAIlC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YACjC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC,KAAK;gBACH,qBAAO,6LAAC;oBAAK,WAAU;8BAAgB;;;;;;YACzC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiB,YAAY,IAAI;;;;;;0DAC/C,6LAAC;gDAAE,WAAU;;oDAAwB;oDAAE,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;0CAG/D,6LAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACjG,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACZ,4BACC;;8CAEE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,IAAc,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;gDAExE,YAAY,WAAW,CAAC,QAAQ,kBAC/B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiB,YAAY,WAAW,CAAC,IAAI;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DACV,YAAY,WAAW,CAAC,QAAQ,GAC7B,WACA,CAAC,UAAU,EAAE,YAAY,WAAW,CAAC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;8CAMzD,6LAAC;oCAAI,WAAU;;wCACZ,SAAS,MAAM,GAAG,mBACjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DACb,WAAW,QAAQ,CAAC,EAAE,CAAC,SAAS;;;;;;;;;;;sDAKvC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gDACtB,MAAM,gBAAgB,QAAQ,QAAQ,KAAK,YAAY,EAAE;gDACzD,MAAM,WAAW,UAAU,KACzB,IAAI,KAAK,QAAQ,SAAS,EAAE,YAAY,OACxC,IAAI,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY;gDAEtD,qBACE,6LAAC;oDAAqB,WAAU;;wDAC7B,YAAY,UAAU,mBACrB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,WAAW,QAAQ,SAAS;;;;;;;;;;;sEAKnC,6LAAC;4DAAI,WAAW,CAAC,KAAK,EAAE,gBAAgB,gBAAgB,iBAAiB;sEACvE,cAAA,6LAAC;gEAAI,WAAW,CAAC,2CAA2C,EAC1D,gBACI,kCACA,6DACJ;;oEACC,QAAQ,IAAI,KAAK,2BAChB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;gFAA8B,MAAK;gFAAe,SAAQ;0FACvE,cAAA,6LAAC;oFAAK,UAAS;oFAAU,GAAE;oFAAoJ,UAAS;;;;;;;;;;;0FAE1L,6LAAC;gFAAK,WAAU;;oFAAqC;oFACxC,QAAQ,MAAM,EAAE,QAAQ;;;;;;;;;;;;;kFAIzC,6LAAC;wEAAE,WAAW,CAAC,cAAc,EAAE,QAAQ,IAAI,KAAK,YAAY,YAAY,IAAI;kFACzE,QAAQ,IAAI;;;;;;kFAEf,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FACb,WAAW,QAAQ,SAAS;;;;;;4EAE9B,+BACC,6LAAC;gFAAK,WAAU;0FACb,cAAc,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;mDAlC/B,QAAQ,EAAE;;;;;4CA0CxB;;;;;;sDAEF,6LAAC;4CAAI,KAAK;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,UAAU;wCAAmB,WAAU;;0DAC3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAK;gEACL,WAAU;0EAEV,cAAA,6LAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC9D,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEACC,MAAK;gEACL,WAAU;0EAEV,cAAA,6LAAC;oEAAI,WAAU;oEAAU,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC9D,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK7E,6LAAC;gDACC,MAAK;gDACL,UAAU,CAAC,WAAW,IAAI;gDAC1B,WAAW,CAAC,qFAAqF,EAC/F,WAAW,IAAI,KACX,4DACA,kCACJ;0DAEF,cAAA,6LAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;0DAIR,6LAAC;gDACC,MAAK;gDACL,SAAS,IAAM,oBAAoB;gDACnC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,6LAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDAQd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA0B,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjF,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtC,kCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC;wCACC,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;wCACA,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC9D,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAS,WAAU;kDAA+C;;;;;;kDAGjF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAA2B;;;;;;;;;;;0DAE7C,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;gDACV,aAAY;gDACZ,MAAK;gDACL,KAAI;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,SAAS;4CACP,oBAAoB;4CACpB,iBAAiB;wCACnB;wCACA,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCACC,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,iBAAiB,WAAW,kBAAkB;wCACzD,WAAW,CAAC,kKAAkK,EAC5K,CAAC,iBAAiB,WAAW,kBAAkB,IAC3C,qCACA,qCACJ;kDACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAldwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1266, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}