{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\n\ninterface User {\n  id: string;\n  uid: string;\n  phoneNumber: string;\n  name: string;\n  username: string;\n  displayName: string;\n}\n\ninterface Chat {\n  id: string;\n  participants: string[];\n  lastMessage: string;\n  timestamp: Date;\n}\n\ninterface Message {\n  id: string;\n  chatId: string;\n  senderId: string;\n  text: string;\n  timestamp: Date;\n  type: 'text' | 'payment';\n  amount?: number;\n}\n\nexport default function ChatPage() {\n  const router = useRouter();\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [showSettings, setShowSettings] = useState(false);\n\n  // Mock data\n  const mockChats: Chat[] = [\n    {\n      id: 'chat1',\n      participants: ['user1', 'user2'],\n      lastMessage: 'Hey! Can you send me $50 for dinner?',\n      timestamp: new Date()\n    },\n    {\n      id: 'chat2',\n      participants: ['user1', 'user3'],\n      lastMessage: 'Thanks for the payment!',\n      timestamp: new Date(Date.now() - 3600000)\n    }\n  ];\n\n  const mockUsers = {\n    user2: { id: 'user2', name: 'Alex Rivera', isOnline: true },\n    user3: { id: 'user3', name: 'Sarah Chen', isOnline: false }\n  };\n\n  const mockMessages: Message[] = [\n    {\n      id: 'msg1',\n      chatId: 'chat1',\n      senderId: 'user2',\n      text: 'Hey! Can you send me $50 for dinner? 🍕',\n      timestamp: new Date(Date.now() - 1800000),\n      type: 'text'\n    },\n    {\n      id: 'msg2',\n      chatId: 'chat1',\n      senderId: 'user1',\n      text: 'Sure! Sending now 💰',\n      timestamp: new Date(Date.now() - 1200000),\n      type: 'text'\n    },\n    {\n      id: 'msg3',\n      chatId: 'chat1',\n      senderId: 'user1',\n      text: 'Payment sent',\n      timestamp: new Date(Date.now() - 1200000),\n      type: 'payment',\n      amount: 50\n    }\n  ];\n\n  const logout = () => {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('boguani_user');\n      router.push('/auth');\n    }\n  };\n\n  const sendMessage = () => {\n    if (!newMessage.trim() || !selectedChat) return;\n\n    const message: Message = {\n      id: 'msg' + Date.now(),\n      chatId: selectedChat.id,\n      senderId: currentUser?.id || 'user1',\n      text: newMessage,\n      timestamp: new Date(),\n      type: 'text'\n    };\n\n    setMessages([...messages, message]);\n    setNewMessage('');\n  };\n\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      const userData = localStorage.getItem('boguani_user');\n      if (userData) {\n        setCurrentUser(JSON.parse(userData));\n        setSelectedChat(mockChats[0]);\n        setMessages(mockMessages);\n      } else {\n        router.push('/auth');\n      }\n    }\n  }, [router]);\n\n  if (!currentUser) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\" style={{\n        background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)'\n      }}>\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex h-screen text-white\" style={{\n      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',\n      fontFamily: 'Montserrat, sans-serif'\n    }}>\n      <div className=\"relative h-screen w-full\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',\n        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'\n      }}>\n        <div className=\"flex h-full\">\n          {/* Sidebar */}\n          <div className=\"w-1/4 bg-gradient-to-b from-purple-900/40 to-black/40 backdrop-blur-lg flex flex-col h-full border-r border-yellow-500/20\">\n            \n            {/* App Header */}\n            <div className=\"p-6 bg-gradient-to-r from-purple-900/80 to-black/80 backdrop-blur-lg text-white flex justify-between items-center border-b border-yellow-500/30\">\n              <h1 className=\"text-2xl font-bold flex items-center\">\n                <div className=\"w-8 h-8 mr-3 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                  <i className=\"fas fa-comment-dollar text-sm text-purple-900\"></i>\n                </div>\n                <span className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">BoGuani</span>\n              </h1>\n              <div className=\"flex space-x-3\">\n                <button className=\"w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                  <i className=\"fas fa-plus text-yellow-400\"></i>\n                </button>\n                <button \n                  className=\"w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\"\n                  onClick={() => setShowSettings(!showSettings)}\n                >\n                  <i className=\"fas fa-cog text-yellow-400\"></i>\n                </button>\n              </div>\n            </div>\n            \n            {/* User Profile */}\n            <div className=\"p-6 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-lg border-b border-yellow-500/20\">\n              <div className=\"flex items-center\">\n                <div className=\"w-14 h-14 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-xl mr-4 shadow-lg\">\n                  {currentUser.name.charAt(0).toUpperCase()}\n                </div>\n                <div className=\"flex-1\">\n                  <h3 className=\"font-bold text-lg text-yellow-200\">{currentUser.name}</h3>\n                  <p className=\"text-sm text-gray-300\">@{currentUser.username}</p>\n                  <p className=\"text-xs text-yellow-400 italic\">&quot;Speak Gold. Share Value.&quot;</p>\n                </div>\n                <button \n                  className=\"w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center hover:bg-red-500/30 transition-all duration-300\"\n                  onClick={logout}\n                >\n                  <i className=\"fas fa-sign-out-alt text-red-400\"></i>\n                </button>\n              </div>\n            </div>\n\n            {/* Chat List */}\n            <div className=\"flex-1 overflow-y-auto\">\n              {mockChats.map((chat) => {\n                const otherUserId = chat.participants.find(p => p !== currentUser.id);\n                const otherUser = mockUsers[otherUserId as keyof typeof mockUsers];\n                \n                return (\n                  <div\n                    key={chat.id}\n                    className={`p-4 border-b border-yellow-500/10 cursor-pointer hover:bg-yellow-400/10 transition-all ${\n                      selectedChat?.id === chat.id ? 'bg-yellow-400/20' : ''\n                    }`}\n                    onClick={() => setSelectedChat(chat)}\n                  >\n                    <div className=\"flex items-center\">\n                      <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold mr-3\">\n                        {otherUser.name.charAt(0).toUpperCase()}\n                      </div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"font-semibold text-white\">{otherUser.name}</h3>\n                        <p className=\"text-sm text-gray-400 truncate\">{chat.lastMessage}</p>\n                      </div>\n                      <div className=\"text-right\">\n                        <span className={`w-2 h-2 ${otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full inline-block`}></span>\n                      </div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Chat Area */}\n          <div className=\"w-3/4 flex flex-col h-full bg-gradient-to-b from-purple-900/40 to-black/40 backdrop-blur-lg\">\n            {selectedChat ? (\n              <>\n                {/* Chat Header */}\n                <div className=\"p-6 bg-gradient-to-r from-purple-900/80 to-black/80 backdrop-blur-lg border-b border-yellow-500/30 flex justify-between items-center\">\n                  <div className=\"flex items-center\">\n                    {(() => {\n                      const otherUserId = selectedChat.participants.find(p => p !== currentUser.id);\n                      const otherUser = mockUsers[otherUserId as keyof typeof mockUsers];\n                      return (\n                        <>\n                          <div className=\"w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg mr-4 shadow-lg\">\n                            {otherUser.name.charAt(0).toUpperCase()}\n                          </div>\n                          <div>\n                            <h2 className=\"font-bold text-xl text-yellow-200\">{otherUser.name}</h2>\n                            <p className=\"text-sm text-gray-300 flex items-center\">\n                              <span className={`w-2 h-2 ${otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full mr-2`}></span>\n                              {otherUser.isOnline ? 'Online' : 'Offline'}\n                            </p>\n                          </div>\n                        </>\n                      );\n                    })()}\n                  </div>\n                  <div className=\"flex space-x-3\">\n                    <button className=\"w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                      <i className=\"fas fa-phone text-yellow-400\"></i>\n                    </button>\n                    <button className=\"w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                      <i className=\"fas fa-video text-yellow-400\"></i>\n                    </button>\n                    <button className=\"w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                      <i className=\"fas fa-ellipsis-v text-yellow-400\"></i>\n                    </button>\n                  </div>\n                </div>\n\n                {/* Messages */}\n                <div className=\"flex-1 overflow-y-auto p-6 space-y-4\">\n                  {messages\n                    .filter(msg => msg.chatId === selectedChat.id)\n                    .map((message) => (\n                      <div\n                        key={message.id}\n                        className={`flex ${message.senderId === currentUser.id ? 'justify-end' : 'justify-start'}`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${\n                            message.senderId === currentUser.id\n                              ? 'bg-gradient-to-r from-yellow-400/30 to-yellow-200/30 text-white rounded-br-sm'\n                              : 'bg-gradient-to-r from-purple-600/30 to-purple-800/30 text-white rounded-bl-sm'\n                          }`}\n                        >\n                          {message.type === 'payment' ? (\n                            <div className=\"text-center\">\n                              <div className=\"bg-yellow-400/20 p-3 rounded-lg\">\n                                <i className=\"fas fa-dollar-sign text-yellow-400 text-lg mb-2\"></i>\n                                <p className=\"text-yellow-200 font-semibold\">${message.amount?.toFixed(2)} sent securely</p>\n                                <p className=\"text-xs text-gray-400 mt-1\">End-to-end encrypted</p>\n                              </div>\n                            </div>\n                          ) : (\n                            <p className=\"text-sm\">{message.text}</p>\n                          )}\n                          <p className=\"text-xs text-gray-400 mt-1\">\n                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                </div>\n\n                {/* Message Input */}\n                <div className=\"p-6 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-lg border-t border-yellow-500/20\">\n                  <div className=\"flex space-x-4\">\n                    <button className=\"w-12 h-12 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                      <i className=\"fas fa-plus text-yellow-400\"></i>\n                    </button>\n                    <div className=\"flex-1 flex space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={newMessage}\n                        onChange={(e) => setNewMessage(e.target.value)}\n                        placeholder=\"Type a message...\"\n                        className=\"flex-1 px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}\n                      />\n                      <button className=\"w-12 h-12 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300\">\n                        <i className=\"fas fa-dollar-sign text-yellow-400\"></i>\n                      </button>\n                    </div>\n                    <button \n                      onClick={sendMessage}\n                      className=\"w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-200 flex items-center justify-center hover:from-yellow-200 hover:to-yellow-400 transition-all duration-300\"\n                    >\n                      <i className=\"fas fa-paper-plane text-purple-900\"></i>\n                    </button>\n                  </div>\n                </div>\n              </>\n            ) : (\n              <div className=\"flex-1 flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <div className=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-comment-dollar text-4xl text-purple-900\"></i>\n                  </div>\n                  <h2 className=\"text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                    Welcome to BoGuani\n                  </h2>\n                  <p className=\"text-gray-400\">Select a chat to start messaging</p>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgCe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,YAAY;IACZ,MAAM,YAAoB;QACxB;YACE,IAAI;YACJ,cAAc;gBAAC;gBAAS;aAAQ;YAChC,aAAa;YACb,WAAW,IAAI;QACjB;QACA;YACE,IAAI;YACJ,cAAc;gBAAC;gBAAS;aAAQ;YAChC,aAAa;YACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;QACnC;KACD;IAED,MAAM,YAAY;QAChB,OAAO;YAAE,IAAI;YAAS,MAAM;YAAe,UAAU;QAAK;QAC1D,OAAO;YAAE,IAAI;YAAS,MAAM;YAAc,UAAU;QAAM;IAC5D;IAEA,MAAM,eAA0B;QAC9B;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,MAAM;QACR;QACA;YACE,IAAI;YACJ,QAAQ;YACR,UAAU;YACV,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK;YACjC,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,SAAS;QACb,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,cAAc;QAEzC,MAAM,UAAmB;YACvB,IAAI,QAAQ,KAAK,GAAG;YACpB,QAAQ,aAAa,EAAE;YACvB,UAAU,aAAa,MAAM;YAC7B,MAAM;YACN,WAAW,IAAI;YACf,MAAM;QACR;QAEA,YAAY;eAAI;YAAU;SAAQ;QAClC,cAAc;IAChB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,wCAAmC;gBACjC,MAAM,WAAW,aAAa,OAAO,CAAC;gBACtC,IAAI,UAAU;oBACZ,eAAe,KAAK,KAAK,CAAC;oBAC1B,gBAAgB,SAAS,CAAC,EAAE;oBAC5B,YAAY;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;6BAAG;QAAC;KAAO;IAEX,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;YAAgD,OAAO;gBACpE,YAAY;YACd;sBACE,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA2B,OAAO;YAC/C,YAAY;YACZ,YAAY;QACd;kBACE,cAAA,6LAAC;YAAI,WAAU;YAA2B,OAAO;gBAC/C,YAAY;gBACZ,iBAAiB;YACnB;sBACE,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAK,WAAU;0DAA+E;;;;;;;;;;;;kDAEjG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,gBAAgB,CAAC;0DAEhC,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAMnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqC,YAAY,IAAI;;;;;;8DACnE,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAE,YAAY,QAAQ;;;;;;;8DAC3D,6LAAC;oDAAE,WAAU;8DAAiC;;;;;;;;;;;;sDAEhD,6LAAC;4CACC,WAAU;4CACV,SAAS;sDAET,cAAA,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAMnB,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC;oCACd,MAAM,cAAc,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,MAAM,YAAY,EAAE;oCACpE,MAAM,YAAY,SAAS,CAAC,YAAsC;oCAElE,qBACE,6LAAC;wCAEC,WAAW,CAAC,uFAAuF,EACjG,cAAc,OAAO,KAAK,EAAE,GAAG,qBAAqB,IACpD;wCACF,SAAS,IAAM,gBAAgB;kDAE/B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA4B,UAAU,IAAI;;;;;;sEACxD,6LAAC;4DAAE,WAAU;sEAAkC,KAAK,WAAW;;;;;;;;;;;;8DAEjE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,UAAU,QAAQ,GAAG,iBAAiB,cAAc,0BAA0B,CAAC;;;;;;;;;;;;;;;;;uCAf1G,KAAK,EAAE;;;;;gCAoBlB;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;kCACZ,6BACC;;8CAEE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,cAAc,aAAa,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,MAAM,YAAY,EAAE;gDAC5E,MAAM,YAAY,SAAS,CAAC,YAAsC;gDAClE,qBACE;;sEACE,6LAAC;4DAAI,WAAU;sEACZ,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;sEAEvC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqC,UAAU,IAAI;;;;;;8EACjE,6LAAC;oEAAE,WAAU;;sFACX,6LAAC;4EAAK,WAAW,CAAC,QAAQ,EAAE,UAAU,QAAQ,GAAG,iBAAiB,cAAc,kBAAkB,CAAC;;;;;;wEAClG,UAAU,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;4CAK3C,CAAC;;;;;;sDAEH,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;oCAAI,WAAU;8CACZ,SACE,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,aAAa,EAAE,EAC5C,GAAG,CAAC,CAAC,wBACJ,6LAAC;4CAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,QAAQ,KAAK,YAAY,EAAE,GAAG,gBAAgB,iBAAiB;sDAE1F,cAAA,6LAAC;gDACC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,QAAQ,KAAK,YAAY,EAAE,GAC/B,kFACA,iFACJ;;oDAED,QAAQ,IAAI,KAAK,0BAChB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;;;;;;8EACb,6LAAC;oEAAE,WAAU;;wEAAgC;wEAAE,QAAQ,MAAM,EAAE,QAAQ;wEAAG;;;;;;;8EAC1E,6LAAC;oEAAE,WAAU;8EAA6B;;;;;;;;;;;;;;;;6EAI9C,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,IAAI;;;;;;kEAEtC,6LAAC;wDAAE,WAAU;kEACV,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;4DAAE,MAAM;4DAAW,QAAQ;wDAAU;;;;;;;;;;;;2CAtB9E,QAAQ,EAAE;;;;;;;;;;8CA8BvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAO,WAAU;0DAChB,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;0DAEf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,aAAY;wDACZ,WAAU;wDACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kEAE1C,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;;;;;;;0DAGjB,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC;oDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;yDAMrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GArTwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}