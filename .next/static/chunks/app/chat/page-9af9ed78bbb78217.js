(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(5155),r=s(9137),n=s.n(r),i=s(2115),l=s(5695),c=s(6408),o=s(760);function d(e){let{isOpen:t,onClose:s,currentUser:r,bankBalance:n,connectedBank:l}=e,[d,x]=(0,i.useState)("profile"),[m,h]=(0,i.useState)({name:(null==r?void 0:r.name)||"",username:(null==r?void 0:r.username)||"",phone:(null==r?void 0:r.phone)||"",email:"<EMAIL>",status:(null==r?void 0:r.status)||"",bio:"Messenger of Value",avatar:(null==r?void 0:r.avatar)||""}),[b,u]=(0,i.useState)({enterToSend:!0,readReceipts:!0,typingIndicators:!0,lastSeen:!0,mediaAutoDownload:!0,soundEnabled:!0,vibrationEnabled:!0,messagePreview:!0,groupNotifications:!0,archiveChats:!1}),[g,f]=(0,i.useState)({instantTransfers:!0,paymentNotifications:!0,transactionLimits:{daily:5e3,monthly:25e3},autoSave:!1,savingsGoal:1e3,requirePinForPayments:!0,biometricAuth:!0}),[j,p]=(0,i.useState)({twoFactorAuth:!0,biometricLogin:!0,sessionTimeout:30,deviceVerification:!0,encryptionLevel:"military",backupMessages:!0,screenLock:!0,incognitoMode:!1}),[y,N]=(0,i.useState)({pushNotifications:!0,emailNotifications:!1,smsNotifications:!1,soundAlerts:!0,vibration:!0,showPreviews:!0,quietHours:{enabled:!1,start:"22:00",end:"07:00"},priorityContacts:[]}),[v,w]=(0,i.useState)({profileVisibility:"contacts",lastSeenVisibility:"contacts",statusVisibility:"everyone",readReceiptSharing:!0,blockUnknownNumbers:!1,dataCollection:!1,analyticsSharing:!1,locationSharing:!1}),[k,S]=(0,i.useState)({theme:"dark",accentColor:"gold",fontSize:"medium",chatWallpaper:"default",bubbleStyle:"transparent",animationsEnabled:!0,compactMode:!1,highContrast:!1});return t?(0,a.jsx)(o.N,{children:(0,a.jsx)(c.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:s,children:(0,a.jsx)(c.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] border border-gray-600/50 professional-shadow overflow-hidden",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"w-64 bg-gradient-to-b from-gray-900/90 to-gray-800/90 border-r border-gray-600/30 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white gold-gradient",children:"Settings"}),(0,a.jsx)("button",{onClick:s,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,a.jsx)("nav",{className:"space-y-2",children:[{id:"profile",name:"Profile",icon:"fas fa-user"},{id:"chat",name:"Chat",icon:"fas fa-comments"},{id:"banking",name:"Banking",icon:"fas fa-university"},{id:"security",name:"Security",icon:"fas fa-shield-alt"},{id:"notifications",name:"Notifications",icon:"fas fa-bell"},{id:"privacy",name:"Privacy",icon:"fas fa-lock"},{id:"appearance",name:"Appearance",icon:"fas fa-palette"},{id:"advanced",name:"Advanced",icon:"fas fa-cogs"}].map(e=>(0,a.jsxs)("button",{onClick:()=>x(e.id),className:"w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ".concat(d===e.id?"bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 text-yellow-400":"text-gray-400 hover:text-white hover:bg-gray-700/50"),children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-lg")}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]},e.id))})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-8",children:["profile"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your personal information and profile appearance"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Profile Picture"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-gray-900 font-bold text-2xl professional-shadow",children:m.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-6 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300",children:"Upload Photo"}),(0,a.jsx)("button",{className:"block text-gray-400 hover:text-white transition-colors text-sm",children:"Remove Photo"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Personal Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:m.name,onChange:e=>h({...m,name:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),(0,a.jsx)("input",{type:"text",value:m.username,onChange:e=>h({...m,username:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:m.phone,onChange:e=>h({...m,phone:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:m.email,onChange:e=>h({...m,email:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Status & Bio"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Message"}),(0,a.jsx)("input",{type:"text",value:m.status,onChange:e=>h({...m,status:e.target.value}),placeholder:"Available for chat",className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),(0,a.jsx)("textarea",{value:m.bio,onChange:e=>h({...m,bio:e.target.value}),placeholder:"Tell others about yourself...",rows:3,className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none resize-none"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-8 py-3 rounded-xl font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})})]}),"chat"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Chat Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize your messaging experience and preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Message Behavior"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enter to Send"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Press Enter to send messages (Shift+Enter for new line)"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,enterToSend:!b.enterToSend}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.enterToSend?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.enterToSend?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Read Receipts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you've read messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,readReceipts:!b.readReceipts}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.readReceipts?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.readReceipts?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Typing Indicators"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you're typing to others"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,typingIndicators:!b.typingIndicators}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.typingIndicators?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.typingIndicators?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Last Seen"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show your last seen status to contacts"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,lastSeen:!b.lastSeen}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.lastSeen?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.lastSeen?"translate-x-6":"translate-x-1")})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Media & Files"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Download Media"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically download photos and videos"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,mediaAutoDownload:!b.mediaAutoDownload}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.mediaAutoDownload?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.mediaAutoDownload?"translate-x-6":"translate-x-1")})})]})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Chat Notifications"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for new messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,soundEnabled:!b.soundEnabled}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.soundEnabled?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.soundEnabled?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Vibration"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Vibrate for new messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,vibrationEnabled:!b.vibrationEnabled}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.vibrationEnabled?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.vibrationEnabled?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Message Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>u({...b,messagePreview:!b.messagePreview}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(b.messagePreview?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(b.messagePreview?"translate-x-6":"translate-x-1")})})]})]})]})]}),"banking"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Banking & Payments"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your financial settings and payment preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Connected Bank Accounts"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/20",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-university text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:l}),(0,a.jsx)("p",{className:"text-green-400 text-sm",children:"Primary Account • Verified"}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Balance: $",n.toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-edit"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-400 transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,a.jsxs)("button",{className:"w-full p-4 border-2 border-dashed border-gray-600 rounded-xl text-gray-400 hover:text-white hover:border-yellow-400 transition-all duration-300",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Add New Bank Account"]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Payment Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Instant Transfers"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable instant money transfers (small fee may apply)"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,instantTransfers:!g.instantTransfers}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(g.instantTransfers?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(g.instantTransfers?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Payment Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Get notified for all payment activities"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,paymentNotifications:!g.paymentNotifications}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(g.paymentNotifications?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(g.paymentNotifications?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Require PIN for Payments"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Require PIN verification for all payments"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,requirePinForPayments:!g.requirePinForPayments}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(g.requirePinForPayments?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(g.requirePinForPayments?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint/face ID for payments"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,biometricAuth:!g.biometricAuth}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(g.biometricAuth?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(g.biometricAuth?"translate-x-6":"translate-x-1")})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Transaction Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Daily Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:g.transactionLimits.daily,onChange:e=>f({...g,transactionLimits:{...g.transactionLimits,daily:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Monthly Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:g.transactionLimits.monthly,onChange:e=>f({...g,transactionLimits:{...g.transactionLimits,monthly:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Savings & Goals"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Save"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically save spare change from transactions"})]}),(0,a.jsx)("button",{onClick:()=>f({...g,autoSave:!g.autoSave}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(g.autoSave?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(g.autoSave?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Savings Goal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:g.savingsGoal,onChange:e=>f({...g,savingsGoal:parseInt(e.target.value)}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none",placeholder:"1000"})]})]})]})]})]}),"security"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Security & Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Protect your account with advanced security features"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Authentication"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,twoFactorAuth:!j.twoFactorAuth}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.twoFactorAuth?"bg-green-500":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.twoFactorAuth?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Login"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint or face ID to log in"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,biometricLogin:!j.biometricLogin}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.biometricLogin?"bg-green-500":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.biometricLogin?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Device Verification"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Verify new devices before allowing access"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,deviceVerification:!j.deviceVerification}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.deviceVerification?"bg-green-500":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.deviceVerification?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsxs)("select",{value:j.sessionTimeout,onChange:e=>p({...j,sessionTimeout:parseInt(e.target.value)}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:15,children:"15 minutes"}),(0,a.jsx)("option",{value:30,children:"30 minutes"}),(0,a.jsx)("option",{value:60,children:"1 hour"}),(0,a.jsx)("option",{value:120,children:"2 hours"}),(0,a.jsx)("option",{value:0,children:"Never"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Encryption & Data"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Encryption Level"}),(0,a.jsxs)("select",{value:j.encryptionLevel,onChange:e=>p({...j,encryptionLevel:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"standard",children:"Standard (128-bit)"}),(0,a.jsx)("option",{value:"enhanced",children:"Enhanced (256-bit)"}),(0,a.jsx)("option",{value:"military",children:"Military Grade (AES-256)"})]}),(0,a.jsxs)("p",{className:"text-green-400 text-sm mt-2 flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"Current: Military Grade AES-256 Encryption"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Backup Messages"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Securely backup your messages to the cloud"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,backupMessages:!j.backupMessages}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.backupMessages?"bg-green-500":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.backupMessages?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Screen Lock"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Lock app when switching to other apps"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,screenLock:!j.screenLock}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.screenLock?"bg-green-500":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.screenLock?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Incognito Mode"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Hide message previews and disable screenshots"})]}),(0,a.jsx)("button",{onClick:()=>p({...j,incognitoMode:!j.incognitoMode}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(j.incognitoMode?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(j.incognitoMode?"translate-x-6":"translate-x-1")})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Security Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-key text-blue-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Change Password"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-green-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Manage Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-history text-yellow-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Login History"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt text-red-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Sign Out All Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]})]}),"notifications"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Notifications"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control how and when you receive notifications"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"General"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive notifications on this device"})]}),(0,a.jsx)("button",{onClick:()=>N({...y,pushNotifications:!y.pushNotifications}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(y.pushNotifications?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(y.pushNotifications?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive important updates via email"})]}),(0,a.jsx)("button",{onClick:()=>N({...y,emailNotifications:!y.emailNotifications}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(y.emailNotifications?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(y.emailNotifications?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Alerts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for notifications"})]}),(0,a.jsx)("button",{onClick:()=>N({...y,soundAlerts:!y.soundAlerts}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(y.soundAlerts?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(y.soundAlerts?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Show Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>N({...y,showPreviews:!y.showPreviews}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(y.showPreviews?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(y.showPreviews?"translate-x-6":"translate-x-1")})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Quiet Hours"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enable Quiet Hours"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Mute notifications during specified hours"})]}),(0,a.jsx)("button",{onClick:()=>N({...y,quietHours:{...y.quietHours,enabled:!y.quietHours.enabled}}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(y.quietHours.enabled?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(y.quietHours.enabled?"translate-x-6":"translate-x-1")})})]}),y.quietHours.enabled&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Start Time"}),(0,a.jsx)("input",{type:"time",value:y.quietHours.start,onChange:e=>N({...y,quietHours:{...y.quietHours,start:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"End Time"}),(0,a.jsx)("input",{type:"time",value:y.quietHours.end,onChange:e=>N({...y,quietHours:{...y.quietHours,end:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]})]})]})]})]}),"privacy"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control who can see your information and activity"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Visibility"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Profile Visibility"}),(0,a.jsxs)("select",{value:v.profileVisibility,onChange:e=>w({...v,profileVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Last Seen"}),(0,a.jsxs)("select",{value:v.lastSeenVisibility,onChange:e=>w({...v,lastSeenVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Visibility"}),(0,a.jsxs)("select",{value:v.statusVisibility,onChange:e=>w({...v,statusVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data & Analytics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Data Collection"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow collection of usage data for improvements"})]}),(0,a.jsx)("button",{onClick:()=>w({...v,dataCollection:!v.dataCollection}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(v.dataCollection?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(v.dataCollection?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Analytics Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Share anonymous analytics data"})]}),(0,a.jsx)("button",{onClick:()=>w({...v,analyticsSharing:!v.analyticsSharing}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(v.analyticsSharing?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(v.analyticsSharing?"translate-x-6":"translate-x-1")})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Location Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow location sharing in messages"})]}),(0,a.jsx)("button",{onClick:()=>w({...v,locationSharing:!v.locationSharing}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(v.locationSharing?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(v.locationSharing?"translate-x-6":"translate-x-1")})})]})]})]})]}),"appearance"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Appearance"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize the look and feel of BoGuani"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>S({...k,theme:"dark"}),className:"p-4 rounded-xl border-2 transition-all ".concat("dark"===k.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"),children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Dark"})]}),(0,a.jsxs)("button",{onClick:()=>S({...k,theme:"light"}),className:"p-4 rounded-xl border-2 transition-all ".concat("light"===k.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"),children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Light"})]}),(0,a.jsxs)("button",{onClick:()=>S({...k,theme:"auto"}),className:"p-4 rounded-xl border-2 transition-all ".concat("auto"===k.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"),children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-r from-gray-900 via-gray-500 to-gray-100 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Auto"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Customization"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Accent Color"}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-3",children:["gold","blue","green","purple","red","orange"].map(e=>(0,a.jsx)("button",{onClick:()=>S({...k,accentColor:e}),className:"w-12 h-12 rounded-full border-2 transition-all ".concat(k.accentColor===e?"border-white scale-110":"border-gray-600"," ").concat("gold"===e?"bg-yellow-400":"blue"===e?"bg-blue-500":"green"===e?"bg-green-500":"purple"===e?"bg-purple-500":"red"===e?"bg-red-500":"bg-orange-500")},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Font Size"}),(0,a.jsxs)("select",{value:k.fontSize,onChange:e=>S({...k,fontSize:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"}),(0,a.jsx)("option",{value:"extra-large",children:"Extra Large"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Message Bubble Style"}),(0,a.jsxs)("select",{value:k.bubbleStyle,onChange:e=>S({...k,bubbleStyle:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"transparent",children:"Transparent"}),(0,a.jsx)("option",{value:"solid",children:"Solid"}),(0,a.jsx)("option",{value:"gradient",children:"Gradient"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Animations"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable smooth animations and transitions"})]}),(0,a.jsx)("button",{onClick:()=>S({...k,animationsEnabled:!k.animationsEnabled}),className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors ".concat(k.animationsEnabled?"bg-yellow-400":"bg-gray-600"),children:(0,a.jsx)("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform ".concat(k.animationsEnabled?"translate-x-6":"translate-x-1")})})]})]})]})]}),"advanced"===d&&(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Advanced"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Advanced settings and developer options"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data Management"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-download text-blue-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Export Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Download your messages and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-upload text-green-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Import Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Import messages from other apps"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-broom text-yellow-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Clear Cache"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Free up storage space"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Developer Options"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 rounded-xl border border-purple-400/30 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-code text-purple-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"API Settings"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Configure API endpoints and keys"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-600/20 to-indigo-500/20 rounded-xl border border-indigo-400/30 hover:border-indigo-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-bug text-indigo-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Debug Logs"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"View and export debug information"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-teal-600/20 to-teal-500/20 rounded-xl border border-teal-400/30 hover:border-teal-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-flask text-teal-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Beta Features"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Enable experimental features"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Account Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-orange-600/20 to-orange-500/20 rounded-xl border border-orange-400/30 hover:border-orange-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-user-times text-orange-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Deactivate Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Temporarily disable your account"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-trash text-red-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Delete Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Permanently delete your account and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"App Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Version"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"1.0.0"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Build"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"2024.01.15"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Platform"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Web"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Encryption"}),(0,a.jsxs)("span",{className:"text-green-400 font-medium flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"AES-256 Active"]})]})]})]})]})]})})]})})})}):null}let x=[{id:"1",name:"John Doe",username:"johndoe",phone:"+1234567890",isOnline:!0,lastSeen:"2 min ago",avatar:"JD",status:"Available for chat"},{id:"2",name:"Jane Smith",username:"janesmith",phone:"+1234567891",isOnline:!1,lastSeen:"1 hour ago",avatar:"JS",status:"Busy with work"},{id:"3",name:"Mike Johnson",username:"mikej",phone:"+1234567892",isOnline:!0,lastSeen:"now",avatar:"MJ",status:"Ready to receive payments"},{id:"4",name:"Sarah Wilson",username:"sarahw",phone:"+1234567893",isOnline:!1,lastSeen:"30 min ago",avatar:"SW",status:"At the gym"}],m=[{id:"1",senderId:"1",text:"Hey! How are you doing?",timestamp:new Date(Date.now()-18e5),status:"read",type:"text"},{id:"2",senderId:"current",text:"I&apos;m doing great! Just finished a big project.",timestamp:new Date(Date.now()-15e5),status:"read",type:"text"},{id:"3",senderId:"1",text:"That&apos;s awesome! Want to celebrate? I can send you some money for dinner \uD83C\uDF7D️",timestamp:new Date(Date.now()-12e5),status:"read",type:"text"},{id:"4",senderId:"1",text:"Here you go!",timestamp:new Date(Date.now()-9e5),status:"read",type:"payment",amount:50,currency:"USD"},{id:"5",senderId:"current",text:"Thank you so much! \uD83D\uDE4F",timestamp:new Date(Date.now()-6e5),status:"read",type:"text"},{id:"6",senderId:"1",text:"Check out this photo from my vacation!",timestamp:new Date(Date.now()-48e4),status:"read",type:"image",imageUrl:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400"},{id:"7",senderId:"current",text:"Beautiful! Where is this?",timestamp:new Date(Date.now()-3e5),status:"read",type:"text"},{id:"8",senderId:"1",text:"Voice message",timestamp:new Date(Date.now()-18e4),status:"read",type:"voice",duration:"0:45"},{id:"9",senderId:"current",text:"Let&apos;s have a video call later!",timestamp:new Date(Date.now()-6e4),status:"delivered",type:"text"}],h=[{id:"1",participant:x[0],lastMessage:"Can you send me the files?",timestamp:new Date,unreadCount:2,messages:m},{id:"2",participant:x[1],lastMessage:"Meeting at 3 PM",timestamp:new Date(Date.now()-864e5),unreadCount:0,messages:[]}];function b(){var e,t,s;let r=(0,l.useRouter)(),[x,b]=(0,i.useState)(null),[u,g]=(0,i.useState)(null),[f,j]=(0,i.useState)([]),[p,y]=(0,i.useState)([]),[N,v]=(0,i.useState)(""),[w,k]=(0,i.useState)(!1),[S,C]=(0,i.useState)(""),[D,A]=(0,i.useState)(!0),[E,P]=(0,i.useState)(""),[M,F]=(0,i.useState)(!1),[I,L]=(0,i.useState)(!1),[V,z]=(0,i.useState)({isActive:!1,type:"voice"}),[B,T]=(0,i.useState)(!1),[H,q]=(0,i.useState)(null),[O]=(0,i.useState)(2847.32),[R]=(0,i.useState)("Chase ****1234"),[U,G]=(0,i.useState)(!1),$=(0,i.useRef)(null),_=(0,i.useRef)(null),J=(0,i.useRef)(null);(0,i.useEffect)(()=>{(async()=>{try{A(!0),b({id:"current",name:"Current User",username:"currentuser",phone:"+1234567899",isOnline:!0,avatar:"CU",status:"Available"}),y(h),g(h[0]),j(m)}catch(e){console.error("Error loading chat data:",e)}finally{A(!1)}})()},[]),(0,i.useEffect)(()=>{$.current&&$.current.scrollIntoView({behavior:"smooth"}),u&&y(p.map(e=>e.id===u.id&&e.unreadCount>0?{...e,unreadCount:0}:e))},[f,u,p]);let W=e=>{if(null==e||e.preventDefault(),!N.trim()||!u||!x)return;let t={id:Date.now().toString(),senderId:x.id,text:N,timestamp:new Date,status:"sending",type:"text"},s=[...f,t];j(s),v(""),setTimeout(()=>{j(e=>e.map(e=>e.id===t.id?{...e,status:"delivered"}:e))},1e3);let a=p.map(e=>e.id===u.id?{...e,lastMessage:N,timestamp:new Date,unreadCount:0,messages:[...s]}:e);y(a),g(a.find(e=>e.id===u.id)||null)};if(D||!x)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading chat..."})]})});let K=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),Q=e=>e.toLocaleDateString([],{weekday:"long",year:"numeric",month:"long",day:"numeric"}),X=e=>{switch(e){case"sending":return(0,a.jsx)("span",{className:"text-gray-400",children:"\uD83D\uDD52"});case"sent":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓"});case"delivered":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓✓"});case"read":return(0,a.jsx)("span",{className:"text-blue-500",children:"✓✓"});default:return null}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{id:"7110b046d83e55cc",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-moz-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-o-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:linear-gradient(135deg,#1E1E24 0%,#2D1B4E 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}.message-bubble{-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(212,175,55,.2)}.message-bubble:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 8px 25px rgba(0,0,0,.3);-moz-box-shadow:0 8px 25px rgba(0,0,0,.3);box-shadow:0 8px 25px rgba(0,0,0,.3);border-color:rgba(212,175,55,.4)}.message-bubble-sent{background:rgba(212,175,55,.15);border-color:rgba(212,175,55,.3)}.message-bubble-received{background:rgba(45,27,78,.4);border-color:rgba(107,114,128,.3)}.chat-input{background:rgba(17,24,39,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.5);-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.chat-input:focus{border-color:#D4AF37;-webkit-box-shadow:0 0 0 3px rgba(212,175,55,.1);-moz-box-shadow:0 0 0 3px rgba(212,175,55,.1);box-shadow:0 0 0 3px rgba(212,175,55,.1);background:rgba(17,24,39,.9)}.sidebar-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-o-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:linear-gradient(180deg,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-right:1px solid rgba(75,85,99,.3)}.chat-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-o-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:linear-gradient(180deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}.encryption-indicator{-webkit-animation:pulse 2s infinite;-moz-animation:pulse 2s infinite;-o-animation:pulse 2s infinite;animation:pulse 2s infinite}@-webkit-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-moz-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-o-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}.bank-balance{background:-webkit-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-moz-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-o-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:linear-gradient(135deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);border:1px solid rgba(34,197,94,.3)}.professional-shadow{-webkit-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);-moz-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2)}"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow",children:x.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsx)("div",{title:"End-to-End Encrypted",className:"jsx-7110b046d83e55cc absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:x.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:["@",x.username]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"E2E Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>T(!0),title:"Settings",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-lg"})}),(0,a.jsx)("button",{onClick:()=>{b(null),y([]),j([]),g(null),localStorage.removeItem("boguani_user"),localStorage.removeItem("boguani_session"),r.push("/auth")},title:"Logout",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-sign-out-alt text-lg"})})]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search conversations...",value:E,onChange:e=>P(e.target.value),className:"jsx-7110b046d83e55cc w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"}),(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"}),E&&(0,a.jsx)("button",{onClick:()=>P(""),className:"jsx-7110b046d83e55cc absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",children:p.map(e=>(0,a.jsx)(c.P.div,{className:"p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ".concat((null==u?void 0:u.id)===e.id?"bg-gray-800/70 border-l-4 border-l-yellow-400":""),onClick:()=>{g(e),j(e.messages)},whileHover:{x:6},transition:{duration:.3,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:e.participant.avatar||e.participant.name.split(" ").map(e=>e[0]).join("")}),e.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"}),e.unreadCount>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold",children:e.unreadCount})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3 flex-1",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:e.participant.name}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:K(e.timestamp)})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-400 truncate",children:e.lastMessage}),e.participant.status&&(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 italic mt-1",children:e.participant.status})]})]})},e.id))}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc bank-balance rounded-xl p-4 backdrop-blur-lg",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm font-medium text-gray-300",children:R})]}),(0,a.jsx)("button",{onClick:()=>T(!0),title:"Manage Bank Account",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-sm"})})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:"Available Balance"}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xl font-bold text-green-400",children:["$",O.toLocaleString("en-US",{minimumFractionDigits:2})]})]}),(0,a.jsxs)("button",{onClick:()=>k(!0),className:"jsx-7110b046d83e55cc bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send"]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mt-2 flex items-center text-xs text-gray-500",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1 text-green-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Secured by Plaid & 256-bit AES encryption"})]})]})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex flex-col chat-panel",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:u.participant.avatar||u.participant.name.split(" ").map(e=>e[0]).join("")}),u.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:u.participant.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:u.participant.isOnline?u.participant.isTyping?"Typing...":"Online":"Last seen ".concat(u.participant.lastSeen)}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>z({isActive:!0,type:"voice",participant:u.participant}),title:"Voice Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone text-lg"})}),(0,a.jsx)("button",{onClick:()=>{z({isActive:!0,type:"video",participant:u.participant}),G(!0)},title:"Video Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-video text-lg"})}),(0,a.jsx)("button",{onClick:()=>T(!0),title:"User Info",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-info-circle text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto p-4 space-y-4",children:[f.length>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center mb-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:Q(f[0].timestamp)})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc space-y-4",children:f.map((e,t)=>{var s;let r=e.senderId===x.id,n=0===t||new Date(e.timestamp).toDateString()!==new Date(f[t-1].timestamp).toDateString();return(0,a.jsxs)(c.P.div,{className:"space-y-1",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[n&&0!==t&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center my-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:Q(e.timestamp)})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc "+"flex ".concat(r?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc "+"max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ".concat(r?"message-bubble-sent text-white rounded-tr-none":"message-bubble-received text-white rounded-tl-none"),children:["payment"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-white text-sm"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm font-semibold text-green-400",children:["Payment Sent: $",null==(s=e.amount)?void 0:s.toFixed(2)," ",e.currency]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 mt-1",children:"Secured by Plaid • Instant Transfer"})]})]}),"image"===e.type&&e.imageUrl&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-2",children:(0,a.jsx)("img",{src:e.imageUrl,alt:"Shared image",onClick:()=>window.open(e.imageUrl,"_blank"),className:"jsx-7110b046d83e55cc rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"})}),"voice"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg",children:[(0,a.jsx)("button",{className:"jsx-7110b046d83e55cc w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-play text-sm"})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 h-2 bg-gray-600/50 rounded-full",children:(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full"})}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 font-medium",children:e.duration})]}),("text"===e.type||"payment"===e.type)&&(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc "+"".concat("payment"===e.type?"text-sm":""," leading-relaxed"),children:e.text}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-end mt-2 space-x-2",children:[(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc "+"text-xs ".concat(r?"text-gray-400":"text-gray-500"," font-medium"),children:K(e.timestamp)}),r&&(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc ml-1",children:X(e.status)}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock text-xs"})})]})]})})]},e.id)})}),(0,a.jsx)("div",{ref:$,className:"jsx-7110b046d83e55cc"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow",children:[H&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 font-semibold",children:["Replying to ",H.senderId===x.id?"yourself":u.participant.name]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-300 truncate",children:H.text})]}),(0,a.jsx)("button",{onClick:()=>q(null),className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)(o.N,{children:I&&(0,a.jsxs)(c.P.div,{className:"mb-3 flex space-x-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},children:[(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-image text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Photo"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-file text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Document"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-map-marker-alt text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Location"})]})]})}),(0,a.jsxs)("form",{onSubmit:W,className:"jsx-7110b046d83e55cc flex items-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>L(!I),className:"jsx-7110b046d83e55cc p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-plus text-lg"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 relative",children:[(0,a.jsx)("textarea",{value:N,onChange:e=>v(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),W(e))},placeholder:"Type a message...",rows:1,style:{minHeight:"48px",maxHeight:"120px"},className:"jsx-7110b046d83e55cc w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc absolute right-3 bottom-3 flex items-center space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>F(!M),className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-smile text-lg"})}),(0,a.jsx)("button",{type:"button",title:"Voice Message",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-blue-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-microphone text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>k(!0),title:"Send Money via Plaid",className:"jsx-7110b046d83e55cc p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-lg"})}),(0,a.jsx)("button",{type:"submit",disabled:!N.trim(),className:"jsx-7110b046d83e55cc "+"p-3 rounded-full transition-all duration-300 professional-shadow ".concat(N.trim()?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"),children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane text-lg"})})]})]})]})]}):(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center p-8",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-comment-dollar text-3xl text-purple-900"})}),(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-medium text-white mb-2",children:"Welcome to BoGuani"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-300",children:"Select a conversation to start messaging"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-yellow-400 text-sm mt-2 italic",children:'"Speak Gold. Share Value."'})]})})}),(0,a.jsx)(o.N,{children:V.isActive&&(0,a.jsx)(c.P.div,{className:"fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center text-white",children:["video"===V.type&&U&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-8",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow",children:[(0,a.jsx)("video",{ref:J,autoPlay:!0,playsInline:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden",children:(0,a.jsx)("video",{ref:_,autoPlay:!0,playsInline:!0,muted:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-3xl font-bold",children:(null==(e=V.participant)?void 0:e.avatar)||(null==(t=V.participant)?void 0:t.name.split(" ").map(e=>e[0]).join(""))})}),(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc text-3xl font-semibold mb-2",children:null==(s=V.participant)?void 0:s.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-green-400 text-sm",children:"End-to-End Encrypted"})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-400 mb-8 text-lg",children:"video"===V.type?"Video calling...":"Voice calling..."}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-center space-x-6",children:[(0,a.jsx)("button",{onClick:()=>z(e=>({...e,isMuted:!e.isMuted})),className:"jsx-7110b046d83e55cc "+"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ".concat(V.isMuted?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"),children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc "+"fas ".concat(V.isMuted?"fa-microphone-slash":"fa-microphone"," text-xl")})}),"video"===V.type&&(0,a.jsx)("button",{onClick:()=>z(e=>({...e,isVideoOff:!e.isVideoOff})),className:"jsx-7110b046d83e55cc "+"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ".concat(V.isVideoOff?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"),children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc "+"fas ".concat(V.isVideoOff?"fa-video-slash":"fa-video"," text-xl")})}),(0,a.jsx)("button",{onClick:()=>{z({isActive:!1,type:"voice"}),G(!1)},className:"jsx-7110b046d83e55cc w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone-slash text-xl"})})]})]})})})]}),(0,a.jsx)(d,{isOpen:B,onClose:()=>T(!1),currentUser:x,bankBalance:O,connectedBank:R}),w&&(0,a.jsx)(c.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsx)(c.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-semibold text-white gold-gradient",children:"Send Payment"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mt-1",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2 text-sm"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:"Secured by Plaid"})]})]}),(0,a.jsx)("button",{onClick:()=>{k(!1),C("")},className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times text-xl"})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-gray-300",children:R})]}),(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm text-green-400 font-semibold",children:["$",O.toLocaleString()]})]})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mb-6",children:[(0,a.jsx)("label",{htmlFor:"amount",className:"jsx-7110b046d83e55cc block text-sm font-medium text-gray-300 mb-2",children:"Amount (USD)"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-yellow-400 text-lg font-semibold",children:"$"})}),(0,a.jsx)("input",{type:"number",name:"amount",id:"amount",value:S,onChange:e=>C(e.target.value),placeholder:"0.00",step:"0.01",min:"0.01",className:"jsx-7110b046d83e55cc w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mt-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:["Sending to: ",null==u?void 0:u.participant.name]}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-bolt mr-1"}),"Instant Transfer"]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{k(!1),C("")},className:"jsx-7110b046d83e55cc px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{if(!S||!u||!x)return;let e=[...f,{id:Date.now().toString(),senderId:x.id,text:"Payment of $".concat(S),timestamp:new Date,status:"sent",type:"payment",amount:parseFloat(S),currency:"USD"}];j(e),k(!1),C("");let t=p.map(t=>t.id===u.id?{...t,lastMessage:"Payment of $".concat(S),timestamp:new Date,unreadCount:0,messages:e}:t);y(t),g(t.find(e=>e.id===u.id)||null)},disabled:!S||0>=parseFloat(S),className:"jsx-7110b046d83e55cc "+"px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ".concat(S&&parseFloat(S)>0?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"),children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send $",S||"0.00"]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"256-bit AES encryption • FDIC insured • Instant settlement"})]})})]})})})]})}},7498:(e,t,s)=>{Promise.resolve().then(s.bind(s,824))}},e=>{var t=t=>e(e.s=t);e.O(0,[408,919,441,684,358],()=>t(7498)),_N_E=e.O()}]);