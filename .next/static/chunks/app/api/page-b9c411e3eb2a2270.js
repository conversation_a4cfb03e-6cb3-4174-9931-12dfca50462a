(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[877],{3184:(e,l,s)=>{Promise.resolve().then(s.bind(s,5139))},5139:(e,l,s)=>{"use strict";s.r(l),s.d(l,{default:()=>i});var a=s(5155),t=s(6874),r=s.n(t),n=s(6408);function i(){return(0,a.jsx)("div",{className:"min-h-screen text-white",style:{background:"linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)",fontFamily:"Montserrat, sans-serif"},children:(0,a.jsxs)("div",{className:"relative min-h-screen",style:{background:"linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)",backgroundImage:"radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)"},children:[(0,a.jsx)("nav",{className:"fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20",children:(0,a.jsxs)("div",{className:"container mx-auto px-6 py-4 flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(r(),{href:"/",className:"flex items-center",children:[(0,a.jsx)("div",{className:"text-yellow-500 text-3xl mr-3",children:(0,a.jsx)("i",{className:"fas fa-comment-dollar"})}),(0,a.jsx)("span",{className:"font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"BoGuani"})]})}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsx)(r(),{href:"/",className:"hover:text-yellow-400 transition-colors",children:"Home"}),(0,a.jsx)(r(),{href:"/guides",className:"hover:text-yellow-400 transition-colors",children:"Guides"}),(0,a.jsx)(r(),{href:"/support",className:"hover:text-yellow-400 transition-colors",children:"Support"}),(0,a.jsx)(r(),{href:"/auth",className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all",children:"Open Web App"})]})]})}),(0,a.jsx)("div",{className:"pt-24 pb-16 px-6",children:(0,a.jsxs)("div",{className:"container mx-auto max-w-6xl",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-code text-3xl text-purple-900"})}),(0,a.jsx)("h1",{className:"text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"API Documentation"}),(0,a.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Integrate BoGuani's secure messaging and payment features into your applications with our powerful API."})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Quick Start"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-900 font-bold",children:"1"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Get API Key"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Register your application and get your API credentials"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-900 font-bold",children:"2"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Authenticate"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Use OAuth 2.0 or API key authentication"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-900 font-bold",children:"3"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Start Building"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm",children:"Send messages and process payments programmatically"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:[(0,a.jsxs)(n.P.div,{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("i",{className:"fas fa-comments text-yellow-400 text-2xl mr-3"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-yellow-200",children:"Messaging API"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Send and receive encrypted messages programmatically"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-green-400",children:"POST /api/v1/messages"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Send a message"})]}),(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-blue-400",children:"GET /api/v1/messages"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Retrieve messages"})]}),(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-yellow-400",children:"PUT /api/v1/messages/:id"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Update message status"})]})]})]}),(0,a.jsxs)(n.P.div,{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("i",{className:"fas fa-credit-card text-yellow-400 text-2xl mr-3"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-yellow-200",children:"Payments API"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"Process secure payments and transfers"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-green-400",children:"POST /api/v1/payments"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Create payment"})]}),(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-blue-400",children:"GET /api/v1/payments/:id"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Get payment status"})]}),(0,a.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,a.jsx)("code",{className:"text-purple-400",children:"POST /api/v1/payments/:id/refund"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Refund payment"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Code Examples"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-yellow-200",children:"JavaScript"}),(0,a.jsx)("div",{className:"bg-black p-4 rounded-lg overflow-x-auto",children:(0,a.jsx)("pre",{className:"text-sm",children:(0,a.jsx)("code",{className:"text-gray-300",children:"const boguani = require('boguani-sdk');\n\nconst client = new boguani.Client({\n  apiKey: 'your-api-key'\n});\n\n// Send a message\nconst message = await client.messages.send({\n  to: '+1234567890',\n  text: 'Hello from BoGuani!'\n});"})})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4 text-yellow-200",children:"Python"}),(0,a.jsx)("div",{className:"bg-black p-4 rounded-lg overflow-x-auto",children:(0,a.jsx)("pre",{className:"text-sm",children:(0,a.jsx)("code",{className:"text-gray-300",children:"import boguani\n\nclient = boguani.Client(\n    api_key='your-api-key'\n)\n\n# Send a payment\npayment = client.payments.create({\n    'amount': 100.00,\n    'currency': 'USD',\n    'recipient': '+1234567890'\n})"})})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"SDKs & Libraries"}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[{name:"JavaScript",icon:"fab fa-js-square",install:"npm install boguani-sdk"},{name:"Python",icon:"fab fa-python",install:"pip install boguani"},{name:"PHP",icon:"fab fa-php",install:"composer require boguani/sdk"},{name:"Java",icon:"fab fa-java",install:"Maven & Gradle"}].map((e,l)=>(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("i",{className:"".concat(e.icon," text-4xl text-yellow-400 mb-3")}),(0,a.jsx)("h3",{className:"font-semibold text-yellow-200",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:e.install})]},l))})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Ready to Build?"}),(0,a.jsx)("p",{className:"text-gray-300 mb-6",children:"Get your API credentials and start integrating BoGuani today."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(r(),{href:"/auth",className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all",children:[(0,a.jsx)("i",{className:"fas fa-key mr-2"}),"Get API Key"]}),(0,a.jsxs)(r(),{href:"/guides",className:"border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all",children:[(0,a.jsx)("i",{className:"fas fa-book mr-2"}),"View Guides"]})]})]})})]})})]})})}}},e=>{var l=l=>e(e.s=l);e.O(0,[874,408,441,684,358],()=>l(3184)),_N_E=e.O()}]);