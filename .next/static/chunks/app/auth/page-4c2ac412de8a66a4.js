(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{10:(e,t,s)=>{Promise.resolve().then(s.bind(s,1579))},1579:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(5155),n=s(9137),a=s.n(n),o=s(2115),i=s(5695),l=s(6874),c=s.n(l);function d(){let e=(0,i.useRouter)(),[t,s]=(0,o.useState)("phone"),[n,l]=(0,o.useState)("+1"),[d,u]=(0,o.useState)(""),[h,f]=(0,o.useState)(""),[x,m]=(0,o.useState)(""),[p,g]=(0,o.useState)(""),[b,y]=(0,o.useState)(""),[j,v]=(0,o.useState)(!1),[w,_]=(0,o.useState)(!1),S=async()=>{if(!d.trim())return void y("Please enter your phone number");v(!0),y("");try{await new Promise(e=>setTimeout(e,2e3)),s("otp"),v(!1)}catch(e){y("Failed to send verification code. Please try again."),v(!1)}},N=async()=>{if(!h.trim()||6!==h.length)return void y("Please enter a valid 6-digit code");v(!0),y("");try{await new Promise(e=>setTimeout(e,1500)),s("profile"),v(!1)}catch(e){y("Invalid verification code. Please try again."),v(!1)}},F=async()=>{if(!x.trim()||!p.trim())return void y("Please fill in all fields");v(!0),y("");try{await new Promise(e=>setTimeout(e,2e3)),localStorage.setItem("boguani_user",JSON.stringify({id:"user1",uid:"user-"+Date.now(),phoneNumber:n+d,name:x,username:p,displayName:x})),v(!1),_(!0),setTimeout(()=>{e.push("/chat")},1500)}catch(e){y("Failed to create profile. Please try again."),v(!1)}},k=()=>{"otp"===t?s("phone"):"profile"===t&&s("otp"),y("")};return(0,o.useEffect)(()=>{localStorage.getItem("boguani_user")&&e.push("/chat")},[e]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a(),{id:"c8e67e6c32d144f",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:-moz-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:-o-linear-gradient(315deg,#111827 0%,#1F2937 50%,#374151 100%);background:linear-gradient(135deg,#111827 0%,#1F2937 50%,#374151 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.feature-card{-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.feature-card:hover{-webkit-transform:translatey(-5px);-moz-transform:translatey(-5px);-ms-transform:translatey(-5px);-o-transform:translatey(-5px);transform:translatey(-5px);-webkit-box-shadow:0 10px 25px -5px rgba(0,0,0,.3);-moz-box-shadow:0 10px 25px -5px rgba(0,0,0,.3);box-shadow:0 10px 25px -5px rgba(0,0,0,.3)}.btn-hover:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 5px 15px rgba(212,175,55,.3);-moz-box-shadow:0 5px 15px rgba(212,175,55,.3);box-shadow:0 5px 15px rgba(212,175,55,.3)}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}.auth-card{background:-webkit-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-moz-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-o-linear-gradient(315deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:linear-gradient(135deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.3)}.auth-input{background:rgba(17,24,39,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.5);-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.auth-input:focus{border-color:#D4AF37;-webkit-box-shadow:0 0 0 3px rgba(212,175,55,.1);-moz-box-shadow:0 0 0 3px rgba(212,175,55,.1);box-shadow:0 0 0 3px rgba(212,175,55,.1);background:rgba(17,24,39,.9)}.professional-shadow{-webkit-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);-moz-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2)}"}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900",children:(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f min-h-screen flex items-center justify-center px-6",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f w-full max-w-md",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f text-center mb-12",children:[(0,r.jsx)(c(),{href:"/",className:"inline-block",children:(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300",children:(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-comment-dollar text-3xl text-purple-900"})})}),(0,r.jsx)("h1",{className:"jsx-c8e67e6c32d144f text-4xl md:text-5xl font-bold mb-4 gold-gradient",children:"Welcome to BoGuani"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-xl text-gray-300 mb-2",children:"Messenger of Value"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 italic",children:'"Speak Gold. Share Value."'})]}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f auth-card p-8 rounded-2xl professional-shadow",children:w?(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f text-center py-8",children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-check text-2xl text-purple-900"})}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-4 gold-gradient",children:"Welcome to BoGuani!"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-300 mb-6",children:"Your account has been created successfully."}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-sm text-gray-400 mt-4",children:"Redirecting to chat..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f flex justify-center mb-8",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex space-x-4",children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f "+"w-3 h-3 rounded-full ".concat("phone"===t?"bg-yellow-400":"bg-gray-600")}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f "+"w-3 h-3 rounded-full ".concat("otp"===t?"bg-yellow-400":"bg-gray-600")}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f "+"w-3 h-3 rounded-full ".concat("profile"===t?"bg-yellow-400":"bg-gray-600")})]})}),b&&(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6",children:(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex items-center",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-exclamation-triangle text-red-400 mr-3"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-red-200 text-sm",children:b})]})}),"phone"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Enter Your Phone"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:"We'll send you a verification code"})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),S()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"countryCode",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Country"}),(0,r.jsx)("select",{id:"countryCode",value:n,onChange:e=>l(e.target.value),className:"jsx-c8e67e6c32d144f w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400",children:[{code:"+1",country:"United States",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"+1",country:"Canada",flag:"\uD83C\uDDE8\uD83C\uDDE6"},{code:"+44",country:"United Kingdom",flag:"\uD83C\uDDEC\uD83C\uDDE7"},{code:"+33",country:"France",flag:"\uD83C\uDDEB\uD83C\uDDF7"},{code:"+49",country:"Germany",flag:"\uD83C\uDDE9\uD83C\uDDEA"},{code:"+81",country:"Japan",flag:"\uD83C\uDDEF\uD83C\uDDF5"},{code:"+86",country:"China",flag:"\uD83C\uDDE8\uD83C\uDDF3"},{code:"+91",country:"India",flag:"\uD83C\uDDEE\uD83C\uDDF3"},{code:"+55",country:"Brazil",flag:"\uD83C\uDDE7\uD83C\uDDF7"},{code:"+61",country:"Australia",flag:"\uD83C\uDDE6\uD83C\uDDFA"}].map((e,t)=>(0,r.jsxs)("option",{value:e.code,className:"jsx-c8e67e6c32d144f",children:[e.flag," ",e.code," (",e.country,")"]},t))})]}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"phone",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f flex space-x-2",children:[(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f w-20 px-3 py-3 auth-input rounded-lg text-center text-gray-300",children:n}),(0,r.jsx)("input",{type:"tel",id:"phone",value:d,onChange:e=>u(e.target.value),placeholder:"1234567890",required:!0,className:"jsx-c8e67e6c32d144f flex-1 px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]})]}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Sending Code..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-paper-plane mr-2"}),"Send Verification Code"]})})]})]}),"otp"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsxs)("button",{onClick:k,className:"jsx-c8e67e6c32d144f text-yellow-400 hover:text-yellow-200 transition-colors mb-4",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-arrow-left mr-2"}),"Back"]}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Enter Verification Code"}),(0,r.jsxs)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:["We sent a 6-digit code to ",n,d]})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),N()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"otp",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Verification Code"}),(0,r.jsx)("input",{type:"text",id:"otp",value:h,onChange:e=>f(e.target.value),placeholder:"123456",maxLength:6,required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none"})]}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Verifying..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-check mr-2"}),"Verify Code"]})})]})]}),"profile"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f mb-6",children:[(0,r.jsxs)("button",{onClick:k,className:"jsx-c8e67e6c32d144f text-yellow-400 hover:text-yellow-200 transition-colors mb-4",children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-arrow-left mr-2"}),"Back"]}),(0,r.jsx)("h2",{className:"jsx-c8e67e6c32d144f text-2xl font-bold mb-2 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Create Your Profile"}),(0,r.jsx)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:"Tell us a bit about yourself"})]}),(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),F()},className:"jsx-c8e67e6c32d144f space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"name",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Full Name"}),(0,r.jsx)("input",{type:"text",id:"name",value:x,onChange:e=>m(e.target.value),placeholder:"John Doe",required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]}),(0,r.jsxs)("div",{className:"jsx-c8e67e6c32d144f",children:[(0,r.jsx)("label",{htmlFor:"username",className:"jsx-c8e67e6c32d144f block text-gray-300 text-sm font-medium mb-2",children:"Username"}),(0,r.jsx)("input",{type:"text",id:"username",value:p,onChange:e=>g(e.target.value),placeholder:"johndoe",required:!0,className:"jsx-c8e67e6c32d144f w-full px-4 py-3 auth-input rounded-lg text-white placeholder-gray-400 focus:outline-none"})]}),(0,r.jsx)("button",{type:"submit",disabled:j,className:"jsx-c8e67e6c32d144f w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-spinner fa-spin mr-2"}),"Creating Profile..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("i",{className:"jsx-c8e67e6c32d144f fas fa-user-plus mr-2"}),"Complete Setup"]})})]})]})]})}),(0,r.jsx)("div",{className:"jsx-c8e67e6c32d144f text-center mt-8",children:(0,r.jsxs)("p",{className:"jsx-c8e67e6c32d144f text-gray-400 text-sm",children:["By continuing, you agree to our"," ",(0,r.jsx)(c(),{href:"/terms",className:"text-yellow-400 hover:text-yellow-200 transition-colors",children:"Terms"})," and"," ",(0,r.jsx)(c(),{href:"/privacy",className:"text-yellow-400 hover:text-yellow-200 transition-colors",children:"Privacy Policy"})]})})]})})})]})}},2269:(e,t,s)=>{"use strict";var r=s(1890);s(8375);var n=s(2115),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),o=void 0!==r&&r.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,a=void 0===n?o:n;c(i(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(c(i(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&c(i(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(r,s):n.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return u[r]||(u[r]="jsx-"+d(e+"-"+s)),u[r]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return u[s]||(u[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[s]}var x=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,n=t.optimizeForSpeed,a=void 0!==n&&n;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:a}),this._sheet.inject(),r&&"boolean"==typeof a&&(this._sheet.setOptimizeForSpeed(a),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),r=s.styleId,n=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var a=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=a,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return a.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var n=h(r,s);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return f(n,e)}):[f(n,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=n.createContext(null);m.displayName="StyleSheetContext";var p=a.default.useInsertionEffect||a.default.useLayoutEffect,g="undefined"!=typeof window?new x:void 0;function b(e){var t=g||n.useContext(m);return t&&("undefined"==typeof window?t.add(e):p(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}b.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=b},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},8375:()=>{},9137:(e,t,s)=>{"use strict";e.exports=s(2269).style}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(10)),_N_E=e.O()}]);