{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\n\nexport default function HomePage() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .feature-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .feature-card:hover {\n          transform: translateY(-5px);\n          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);\n        }\n\n        .btn-hover:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen hero-pattern\">\n        {/* Navigation */}\n        <nav className=\"bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-50\">\n          <div className=\"container mx-auto px-6 py-3 flex justify-between items-center\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"text-yellow-400 text-3xl mr-2\">\n                <i className=\"fas fa-comment-dollar\"></i>\n              </div>\n              <span className=\"font-bold text-2xl gold-gradient\">BoGuani</span>\n            </Link>\n            <div className=\"hidden md:flex space-x-8\">\n              <a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a>\n              <a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\">About</a>\n              <a href=\"#how-it-works\" className=\"hover:text-yellow-400 transition-colors\">How It Works</a>\n              <a href=\"#pricing\" className=\"hover:text-yellow-400 transition-colors\">Pricing</a>\n              <a href=\"#contact\" className=\"hover:text-yellow-400 transition-colors\">Contact</a>\n              <Link href=\"/auth\" className=\"bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors\">\n                Get Started\n              </Link>\n            </div>\n            <button\n              className=\"md:hidden text-yellow-400\"\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n            >\n              <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>\n            </button>\n          </div>\n\n          {/* Mobile Menu */}\n          {isMenuOpen && (\n            <div className=\"md:hidden bg-gray-900 bg-opacity-95 backdrop-blur-md\">\n              <div className=\"container mx-auto px-6 py-4 flex flex-col space-y-4\">\n                <a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\" onClick={() => setIsMenuOpen(false)}>Features</a>\n                <a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\" onClick={() => setIsMenuOpen(false)}>About</a>\n                <a href=\"#how-it-works\" className=\"hover:text-yellow-400 transition-colors\" onClick={() => setIsMenuOpen(false)}>How It Works</a>\n                <a href=\"#pricing\" className=\"hover:text-yellow-400 transition-colors\" onClick={() => setIsMenuOpen(false)}>Pricing</a>\n                <a href=\"#contact\" className=\"hover:text-yellow-400 transition-colors\" onClick={() => setIsMenuOpen(false)}>Contact</a>\n                <Link href=\"/auth\" className=\"bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors text-center\">\n                  Get Started\n                </Link>\n              </div>\n            </div>\n          )}\n        </nav>\n\n        {/* Hero Section */}\n        <section className=\"min-h-screen flex items-center justify-center px-6 pt-20\">\n          <div className=\"w-full max-w-6xl\">\n            <div className=\"text-center mb-16\">\n              <motion.div\n                className=\"w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <i className=\"fas fa-comment-dollar text-5xl text-purple-900\"></i>\n              </motion.div>\n              <h1 className=\"text-6xl md:text-7xl font-bold mb-6 gold-gradient\">\n                BoGuani\n              </h1>\n              <p className=\"text-2xl text-gray-300 mb-4\">Messenger of Value</p>\n              <p className=\"text-xl text-gray-400 italic mb-8\">&quot;Speak Gold. Share Value.&quot;</p>\n              <p className=\"text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12\">\n                The world's first value-based communication platform. Send messages, transfer money,\n                and make calls with military-grade encryption and Taíno-inspired wisdom.\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-6 justify-center\">\n                <Link\n                  href=\"/auth\"\n                  className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover\"\n                >\n                  <i className=\"fas fa-rocket mr-2\"></i>\n                  Start Messaging\n                </Link>\n                <Link\n                  href=\"/downloads\"\n                  className=\"bg-transparent border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all btn-hover\"\n                >\n                  <i className=\"fas fa-download mr-2\"></i>\n                  Download App\n                </Link>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* What is BoGuani Section */}\n        <section id=\"about\" className=\"py-20 bg-gradient-to-br from-purple-900/40 to-black/40\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">What is BoGuani?</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto mb-8\"></div>\n              <p className=\"text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed\">\n                BoGuani is the world's first value-based communication platform, inspired by ancient Taíno wisdom\n                where messages carried both meaning and worth. We've reimagined this concept for the digital age.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              <div>\n                <h3 className=\"text-3xl font-bold mb-6 text-yellow-400\">The Ancient Wisdom</h3>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  The name \"BoGuani\" comes from the ancient Taíno civilization, where communication and value\n                  exchange were intrinsically linked. In their culture, every message carried weight, meaning,\n                  and often tangible value.\n                </p>\n                <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                  We've brought this sacred tradition into the modern world, creating a platform where your\n                  words truly carry worth - literally and figuratively.\n                </p>\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4\">\n                    <i className=\"fas fa-leaf text-purple-900 text-xl\"></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-yellow-400\">Sacred Communication</h4>\n                    <p className=\"text-gray-400 text-sm\">Every message is treated with reverence and security</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4\">\n                    <i className=\"fas fa-coins text-purple-900 text-xl\"></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-yellow-400\">Value Exchange</h4>\n                    <p className=\"text-gray-400 text-sm\">Send money as naturally as sending words</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-8 rounded-2xl gold-border\">\n                <div className=\"text-center mb-6\">\n                  <div className=\"w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-comment-dollar text-3xl text-purple-900\"></i>\n                  </div>\n                  <h4 className=\"text-2xl font-bold gold-gradient mb-2\">Modern Innovation</h4>\n                  <p className=\"text-gray-300\">Ancient wisdom meets cutting-edge technology</p>\n                </div>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                    <span className=\"text-gray-300\">End-to-end encryption</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                    <span className=\"text-gray-300\">Instant money transfers</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                    <span className=\"text-gray-300\">HD voice & video calls</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                    <span className=\"text-gray-300\">Cross-platform compatibility</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section id=\"features\" className=\"py-20 bg-gray-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">Powerful Features</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto mb-8\"></div>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Everything you need for secure communication and value exchange in one platform\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-shield-alt text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">Military-Grade Security</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    256-bit AES encryption ensures your messages and transactions are completely secure and private.\n                  </p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-bolt text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">Instant Transfers</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    Send money instantly to anyone, anywhere. As simple as sending a text message.\n                  </p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-video text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">HD Voice & Video</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    Crystal-clear voice and video calls powered by advanced WebRTC technology.\n                  </p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-users text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">Group Payments</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    Split bills, share expenses, and manage group finances effortlessly.\n                  </p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-globe text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">Cross-Platform</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    Available on iOS, Android, Desktop, and Web. Your conversations sync everywhere.\n                  </p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-clock text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-xl font-bold mb-4 gold-gradient\">24/7 Support</h3>\n                  <p className=\"text-gray-300 leading-relaxed\">\n                    Round-the-clock customer support to help you with any questions or issues.\n                  </p>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n\n        {/* How It Works Section */}\n        <section id=\"how-it-works\" className=\"py-20 bg-gradient-to-br from-purple-900/40 to-black/40\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">How It Works</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto mb-8\"></div>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Get started with BoGuani in three simple steps\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <motion.div\n                className=\"text-center\"\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.1 }}\n              >\n                <div className=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl\">\n                  1\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 gold-gradient\">Sign Up</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Create your account with just your phone number. We'll verify it with a secure SMS code.\n                </p>\n              </motion.div>\n\n              <motion.div\n                className=\"text-center\"\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n              >\n                <div className=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl\">\n                  2\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 gold-gradient\">Connect</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Add your contacts and start secure conversations. Import from your phone or invite friends.\n                </p>\n              </motion.div>\n\n              <motion.div\n                className=\"text-center\"\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n              >\n                <div className=\"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl\">\n                  3\n                </div>\n                <h3 className=\"text-2xl font-bold mb-4 gold-gradient\">Share Value</h3>\n                <p className=\"text-gray-300 leading-relaxed\">\n                  Send messages, make calls, and transfer money - all with the same level of security and ease.\n                </p>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n\n        {/* Pricing Section */}\n        <section id=\"pricing\" className=\"py-20 bg-gray-900\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">Simple Pricing</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto mb-8\"></div>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Choose the plan that works best for you. All plans include end-to-end encryption.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto\">\n              {/* Free Plan */}\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <h3 className=\"text-2xl font-bold mb-4\">Free</h3>\n                  <div className=\"text-4xl font-bold mb-6 gold-gradient\">$0</div>\n                  <p className=\"text-gray-300 mb-8\">Perfect for personal use</p>\n\n                  <ul className=\"space-y-4 mb-8 text-left\">\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>Unlimited messaging</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>Voice & video calls</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>$100/month transfers</span>\n                    </li>\n                  </ul>\n\n                  <Link href=\"/auth\" className=\"w-full bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block\">\n                    Get Started\n                  </Link>\n                </div>\n              </motion.div>\n\n              {/* Pro Plan */}\n              <motion.div\n                className=\"bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl p-8 text-gray-900 relative\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold\">\n                  Most Popular\n                </div>\n                <div className=\"text-center\">\n                  <h3 className=\"text-2xl font-bold mb-4\">Pro</h3>\n                  <div className=\"text-4xl font-bold mb-6\">$9.99</div>\n                  <p className=\"text-gray-700 mb-8\">For power users</p>\n\n                  <ul className=\"space-y-4 mb-8 text-left\">\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-purple-600 mr-3\"></i>\n                      <span>Everything in Free</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-purple-600 mr-3\"></i>\n                      <span>$5,000/month transfers</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-purple-600 mr-3\"></i>\n                      <span>Priority support</span>\n                    </li>\n                  </ul>\n\n                  <Link href=\"/auth\" className=\"w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block\">\n                    Start Free Trial\n                  </Link>\n                </div>\n              </motion.div>\n\n              {/* Business Plan */}\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-400/30\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <h3 className=\"text-2xl font-bold mb-4\">Business</h3>\n                  <div className=\"text-4xl font-bold mb-6 gold-gradient\">$29.99</div>\n                  <p className=\"text-gray-300 mb-8\">For teams and organizations</p>\n\n                  <ul className=\"space-y-4 mb-8 text-left\">\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>Everything in Pro</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>Unlimited transfers</span>\n                    </li>\n                    <li className=\"flex items-center\">\n                      <i className=\"fas fa-check text-yellow-400 mr-3\"></i>\n                      <span>Admin controls</span>\n                    </li>\n                  </ul>\n\n                  <Link href=\"/contact\" className=\"w-full gold-border bg-transparent px-6 py-3 rounded-full font-semibold transition-all hover:scale-105 inline-block\">\n                    <span className=\"text-yellow-400\">Contact Sales</span>\n                  </Link>\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        </section>\n\n        {/* Contact Section */}\n        <section id=\"contact\" className=\"py-20 bg-gradient-to-br from-purple-900/40 to-black/40\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"text-center mb-16\">\n              <h2 className=\"text-4xl md:text-5xl font-bold mb-6 gold-gradient\">Get In Touch</h2>\n              <div className=\"w-20 h-1 bg-yellow-400 mx-auto mb-8\"></div>\n              <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n                Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto\">\n              {/* Contact Info */}\n              <div>\n                <h3 className=\"text-2xl font-bold mb-8 gold-gradient\">Contact Information</h3>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4\">\n                      <i className=\"fas fa-envelope text-purple-900\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-yellow-400\">Email</h4>\n                      <p className=\"text-gray-300\"><EMAIL></p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4\">\n                      <i className=\"fas fa-phone text-purple-900\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-yellow-400\">Phone</h4>\n                      <p className=\"text-gray-300\">+****************</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4\">\n                      <i className=\"fas fa-map-marker-alt text-purple-900\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-yellow-400\">Address</h4>\n                      <p className=\"text-gray-300\">123 Innovation Street<br />San Francisco, CA 94105</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"mt-8\">\n                  <h4 className=\"text-xl font-semibold mb-4 text-yellow-400\">Follow Us</h4>\n                  <div className=\"flex space-x-4\">\n                    <a href=\"https://twitter.com/boguani\" target=\"_blank\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors\">\n                      <i className=\"fab fa-twitter text-yellow-400\"></i>\n                    </a>\n                    <a href=\"https://facebook.com/boguani\" target=\"_blank\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors\">\n                      <i className=\"fab fa-facebook-f text-yellow-400\"></i>\n                    </a>\n                    <a href=\"https://instagram.com/boguani\" target=\"_blank\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors\">\n                      <i className=\"fab fa-instagram text-yellow-400\"></i>\n                    </a>\n                    <a href=\"https://linkedin.com/company/boguani\" target=\"_blank\" className=\"w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors\">\n                      <i className=\"fab fa-linkedin-in text-yellow-400\"></i>\n                    </a>\n                  </div>\n                </div>\n              </div>\n\n              {/* Contact Form */}\n              <div className=\"bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-8 rounded-2xl gold-border\">\n                <h3 className=\"text-2xl font-bold mb-6 gold-gradient\">Send us a Message</h3>\n\n                <form className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-gray-300 text-sm font-medium mb-2\">First Name</label>\n                      <input\n                        type=\"text\"\n                        className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                        placeholder=\"John\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-gray-300 text-sm font-medium mb-2\">Last Name</label>\n                      <input\n                        type=\"text\"\n                        className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                        placeholder=\"Doe\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-gray-300 text-sm font-medium mb-2\">Email</label>\n                    <input\n                      type=\"email\"\n                      className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-gray-300 text-sm font-medium mb-2\">Subject</label>\n                    <input\n                      type=\"text\"\n                      className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                      placeholder=\"How can we help?\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-gray-300 text-sm font-medium mb-2\">Message</label>\n                    <textarea\n                      rows={5}\n                      className=\"w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400\"\n                      placeholder=\"Tell us about your project or question...\"\n                    ></textarea>\n                  </div>\n\n                  <button\n                    type=\"submit\"\n                    className=\"w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover\"\n                  >\n                    <i className=\"fas fa-paper-plane mr-2\"></i>\n                    Send Message\n                  </button>\n                </form>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Footer */}\n        <footer className=\"bg-gray-900 py-12\">\n          <div className=\"container mx-auto px-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\">\n              {/* Company Info */}\n              <div>\n                <Link href=\"/\" className=\"flex items-center mb-4\">\n                  <div className=\"text-yellow-400 text-2xl mr-2\">\n                    <i className=\"fas fa-comment-dollar\"></i>\n                  </div>\n                  <span className=\"font-bold text-xl gold-gradient\">BoGuani</span>\n                </Link>\n                <p className=\"text-gray-400 mb-4\">Messenger of Value</p>\n                <p className=\"text-gray-400 text-sm leading-relaxed\">\n                  Where ancient wisdom meets modern technology. Experience the future of value-based communication.\n                </p>\n              </div>\n\n              {/* Product Links */}\n              <div>\n                <h3 className=\"font-semibold mb-4 text-yellow-400\">Product</h3>\n                <ul className=\"space-y-2 text-gray-400\">\n                  <li><a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a></li>\n                  <li><Link href=\"/security\" className=\"hover:text-yellow-400 transition-colors\">Security</Link></li>\n                  <li><Link href=\"/pricing\" className=\"hover:text-yellow-400 transition-colors\">Pricing</Link></li>\n                  <li><Link href=\"/downloads\" className=\"hover:text-yellow-400 transition-colors\">Downloads</Link></li>\n                </ul>\n              </div>\n\n              {/* Company Links */}\n              <div>\n                <h3 className=\"font-semibold mb-4 text-yellow-400\">Company</h3>\n                <ul className=\"space-y-2 text-gray-400\">\n                  <li><a href=\"#about\" className=\"hover:text-yellow-400 transition-colors\">About</a></li>\n                  <li><Link href=\"/careers\" className=\"hover:text-yellow-400 transition-colors\">Careers</Link></li>\n                  <li><Link href=\"/blog\" className=\"hover:text-yellow-400 transition-colors\">Blog</Link></li>\n                  <li><a href=\"#contact\" className=\"hover:text-yellow-400 transition-colors\">Contact</a></li>\n                </ul>\n              </div>\n\n              {/* Support Links */}\n              <div>\n                <h3 className=\"font-semibold mb-4 text-yellow-400\">Support</h3>\n                <ul className=\"space-y-2 text-gray-400\">\n                  <li><Link href=\"/support\" className=\"hover:text-yellow-400 transition-colors\">Help Center</Link></li>\n                  <li><Link href=\"/guides\" className=\"hover:text-yellow-400 transition-colors\">Guides</Link></li>\n                  <li><Link href=\"/api-docs\" className=\"hover:text-yellow-400 transition-colors\">API Docs</Link></li>\n                  <li><Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-colors\">Privacy</Link></li>\n                  <li><Link href=\"/terms\" className=\"hover:text-yellow-400 transition-colors\">Terms</Link></li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center\">\n              <p className=\"text-gray-400 text-sm mb-4 md:mb-0\">\n                © 2024 BoGuani. All rights reserved.\n              </p>\n              <div className=\"flex space-x-6 text-gray-400 text-sm\">\n                <Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-colors\">Privacy Policy</Link>\n                <Link href=\"/terms\" className=\"hover:text-yellow-400 transition-colors\">Terms of Service</Link>\n                <Link href=\"/security\" className=\"hover:text-yellow-400 transition-colors\">Security</Link>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE;;;;;;0BAyCE,6LAAC;0DAAc;;kCAEb,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC;0FAAc;0DACb,cAAA,6LAAC;8FAAY;;;;;;;;;;;0DAEf,6LAAC;0FAAe;0DAAmC;;;;;;;;;;;;kDAErD,6LAAC;kFAAc;;0DACb,6LAAC;gDAAE,MAAK;0FAAsB;0DAA0C;;;;;;0DACxE,6LAAC;gDAAE,MAAK;0FAAmB;0DAA0C;;;;;;0DACrE,6LAAC;gDAAE,MAAK;0FAA0B;0DAA0C;;;;;;0DAC5E,6LAAC;gDAAE,MAAK;0FAAqB;0DAA0C;;;;;;0DACvE,6LAAC;gDAAE,MAAK;0FAAqB;0DAA0C;;;;;;0DACvE,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAA2G;;;;;;;;;;;;kDAI1I,6LAAC;wCAEC,SAAS,IAAM,cAAc,CAAC;kFADpB;kDAGV,cAAA,6LAAC;sFAAa,CAAC,IAAI,EAAE,aAAa,aAAa,UAAU,QAAQ,CAAC;;;;;;;;;;;;;;;;;4BAKrE,4BACC,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;4CAAE,MAAK;4CAAgE,SAAS,IAAM,cAAc;sFAAvE;sDAA+E;;;;;;sDAC7G,6LAAC;4CAAE,MAAK;4CAA6D,SAAS,IAAM,cAAc;sFAAvE;sDAA+E;;;;;;sDAC1G,6LAAC;4CAAE,MAAK;4CAAoE,SAAS,IAAM,cAAc;sFAAvE;sDAA+E;;;;;;sDACjH,6LAAC;4CAAE,MAAK;4CAA+D,SAAS,IAAM,cAAc;sFAAvE;sDAA+E;;;;;;sDAC5G,6LAAC;4CAAE,MAAK;4CAA+D,SAAS,IAAM,cAAc;sFAAvE;sDAA+E;;;;;;sDAC5G,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAuH;;;;;;;;;;;;;;;;;;;;;;;kCAS5J,6LAAC;kEAAkB;kCACjB,cAAA,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAY;;;;;;;;;;;kDAEf,6LAAC;kFAAa;kDAAoD;;;;;;kDAGlE,6LAAC;kFAAY;kDAA8B;;;;;;kDAC3C,6LAAC;kFAAY;kDAAoC;;;;;;kDACjD,6LAAC;kFAAY;kDAAgE;;;;;;kDAK7E,6LAAC;kFAAc;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;kGAAY;;;;;;oDAAyB;;;;;;;0DAGxC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;;kEAEV,6LAAC;kGAAY;;;;;;oDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlD,6LAAC;wBAAQ,IAAG;kEAAkB;kCAC5B,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoD;;;;;;sDAClE,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAA0D;;;;;;;;;;;;8CAMzE,6LAAC;8EAAc;;sDACb,6LAAC;;;8DACC,6LAAC;8FAAa;8DAA0C;;;;;;8DACxD,6LAAC;8FAAY;8DAAqC;;;;;;8DAKlD,6LAAC;8FAAY;8DAAqC;;;;;;8DAIlD,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACb,cAAA,6LAAC;0GAAY;;;;;;;;;;;sEAEf,6LAAC;;;8EACC,6LAAC;8GAAa;8EAAgC;;;;;;8EAC9C,6LAAC;8GAAY;8EAAwB;;;;;;;;;;;;;;;;;;8DAGzC,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACb,cAAA,6LAAC;0GAAY;;;;;;;;;;;sEAEf,6LAAC;;;8EACC,6LAAC;8GAAa;8EAAgC;;;;;;8EAC9C,6LAAC;8GAAY;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;sDAK3C,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;sEACb,cAAA,6LAAC;0GAAY;;;;;;;;;;;sEAEf,6LAAC;sGAAa;sEAAwC;;;;;;sEACtD,6LAAC;sGAAY;sEAAgB;;;;;;;;;;;;8DAE/B,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;;8EACb,6LAAC;8GAAY;;;;;;8EACb,6LAAC;8GAAe;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAY;;;;;;8EACb,6LAAC;8GAAe;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAY;;;;;;8EACb,6LAAC;8GAAe;8EAAgB;;;;;;;;;;;;sEAElC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAY;;;;;;8EACb,6LAAC;8GAAe;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5C,6LAAC;wBAAQ,IAAG;kEAAqB;kCAC/B,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoD;;;;;;sDAClE,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;8EAAc;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;sDAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;sDAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;sDAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;sDAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;sDAMjD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAY;;;;;;;;;;;kEAEf,6LAAC;kGAAa;kEAAuC;;;;;;kEACrD,6LAAC;kGAAY;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,6LAAC;wBAAQ,IAAG;kEAAyB;kCACnC,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoD;;;;;;sDAClE,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;8EAAc;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,6LAAC;8FAAc;8DAA0J;;;;;;8DAGzK,6LAAC;8FAAa;8DAAwC;;;;;;8DACtD,6LAAC;8FAAY;8DAAgC;;;;;;;;;;;;sDAK/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,6LAAC;8FAAc;8DAA0J;;;;;;8DAGzK,6LAAC;8FAAa;8DAAwC;;;;;;8DACtD,6LAAC;8FAAY;8DAAgC;;;;;;;;;;;;sDAK/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,6LAAC;8FAAc;8DAA0J;;;;;;8DAGzK,6LAAC;8FAAa;8DAAwC;;;;;;8DACtD,6LAAC;8FAAY;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrD,6LAAC;wBAAQ,IAAG;kEAAoB;kCAC9B,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoD;;;;;;sDAClE,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;8EAAc;;sDAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAa;kEAA0B;;;;;;kEACxC,6LAAC;kGAAc;kEAAwC;;;;;;kEACvD,6LAAC;kGAAY;kEAAqB;;;;;;kEAElC,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;0EAER,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;0EAER,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;;;;;;;kEAIV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAmH;;;;;;;;;;;;;;;;;sDAOpJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,6LAAC;8FAAc;8DAA4H;;;;;;8DAG3I,6LAAC;8FAAc;;sEACb,6LAAC;sGAAa;sEAA0B;;;;;;sEACxC,6LAAC;sGAAc;sEAA0B;;;;;;sEACzC,6LAAC;sGAAY;sEAAqB;;;;;;sEAElC,6LAAC;sGAAa;;8EACZ,6LAAC;8GAAa;;sFACZ,6LAAC;sHAAY;;;;;;sFACb,6LAAC;;sFAAK;;;;;;;;;;;;8EAER,6LAAC;8GAAa;;sFACZ,6LAAC;sHAAY;;;;;;sFACb,6LAAC;;sFAAK;;;;;;;;;;;;8EAER,6LAAC;8GAAa;;sFACZ,6LAAC;sHAAY;;;;;;sFACb,6LAAC;;sFAAK;;;;;;;;;;;;;;;;;;sEAIV,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAK;4DAAQ,WAAU;sEAAuH;;;;;;;;;;;;;;;;;;sDAOxJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,YAAY;gDAAE,UAAU;4CAAI;sDAE5B,cAAA,6LAAC;0FAAc;;kEACb,6LAAC;kGAAa;kEAA0B;;;;;;kEACxC,6LAAC;kGAAc;kEAAwC;;;;;;kEACvD,6LAAC;kGAAY;kEAAqB;;;;;;kEAElC,6LAAC;kGAAa;;0EACZ,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;0EAER,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;0EAER,6LAAC;0GAAa;;kFACZ,6LAAC;kHAAY;;;;;;kFACb,6LAAC;;kFAAK;;;;;;;;;;;;;;;;;;kEAIV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAC9B,cAAA,6LAAC;sGAAe;sEAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS9C,6LAAC;wBAAQ,IAAG;kEAAoB;kCAC9B,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAa;sDAAoD;;;;;;sDAClE,6LAAC;sFAAc;;;;;;sDACf,6LAAC;sFAAY;sDAA0C;;;;;;;;;;;;8CAKzD,6LAAC;8EAAc;;sDAEb,6LAAC;;;8DACC,6LAAC;8FAAa;8DAAwC;;;;;;8DAEtD,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EACb,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;;;sFACC,6LAAC;sHAAa;sFAAgC;;;;;;sFAC9C,6LAAC;sHAAY;sFAAgB;;;;;;;;;;;;;;;;;;sEAIjC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EACb,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;;;sFACC,6LAAC;sHAAa;sFAAgC;;;;;;sFAC9C,6LAAC;sHAAY;sFAAgB;;;;;;;;;;;;;;;;;;sEAIjC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EACb,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;;;sFACC,6LAAC;sHAAa;sFAAgC;;;;;;sFAC9C,6LAAC;sHAAY;;gFAAgB;8FAAqB,6LAAC;;;;;;;gFAAK;;;;;;;;;;;;;;;;;;;;;;;;;8DAK9D,6LAAC;8FAAc;;sEACb,6LAAC;sGAAa;sEAA6C;;;;;;sEAC3D,6LAAC;sGAAc;;8EACb,6LAAC;oEAAE,MAAK;oEAA8B,QAAO;8GAAmB;8EAC9D,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;oEAAE,MAAK;oEAA+B,QAAO;8GAAmB;8EAC/D,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;oEAAE,MAAK;oEAAgC,QAAO;8GAAmB;8EAChE,cAAA,6LAAC;kHAAY;;;;;;;;;;;8EAEf,6LAAC;oEAAE,MAAK;oEAAuC,QAAO;8GAAmB;8EACvE,cAAA,6LAAC;kHAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOrB,6LAAC;sFAAc;;8DACb,6LAAC;8FAAa;8DAAwC;;;;;;8DAEtD,6LAAC;8FAAe;;sEACd,6LAAC;sGAAc;;8EACb,6LAAC;;;sFACC,6LAAC;sHAAgB;sFAA+C;;;;;;sFAChE,6LAAC;4EACC,MAAK;4EAEL,aAAY;sHADF;;;;;;;;;;;;8EAId,6LAAC;;;sFACC,6LAAC;sHAAgB;sFAA+C;;;;;;sFAChE,6LAAC;4EACC,MAAK;4EAEL,aAAY;sHADF;;;;;;;;;;;;;;;;;;sEAMhB,6LAAC;;;8EACC,6LAAC;8GAAgB;8EAA+C;;;;;;8EAChE,6LAAC;oEACC,MAAK;oEAEL,aAAY;8GADF;;;;;;;;;;;;sEAKd,6LAAC;;;8EACC,6LAAC;8GAAgB;8EAA+C;;;;;;8EAChE,6LAAC;oEACC,MAAK;oEAEL,aAAY;8GADF;;;;;;;;;;;;sEAKd,6LAAC;;;8EACC,6LAAC;8GAAgB;8EAA+C;;;;;;8EAChE,6LAAC;oEACC,MAAM;oEAEN,aAAY;8GADF;;;;;;;;;;;;sEAKd,6LAAC;4DACC,MAAK;sGACK;;8EAEV,6LAAC;8GAAY;;;;;;gEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,6LAAC;kEAAiB;kCAChB,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDAEb,6LAAC;;;8DACC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;sGAAc;sEACb,cAAA,6LAAC;0GAAY;;;;;;;;;;;sEAEf,6LAAC;sGAAe;sEAAkC;;;;;;;;;;;;8DAEpD,6LAAC;8FAAY;8DAAqB;;;;;;8DAClC,6LAAC;8FAAY;8DAAwC;;;;;;;;;;;;sDAMvD,6LAAC;;;8DACC,6LAAC;8FAAa;8DAAqC;;;;;;8DACnD,6LAAC;8FAAa;;sEACZ,6LAAC;;sEAAG,cAAA,6LAAC;gEAAE,MAAK;0GAAsB;0EAA0C;;;;;;;;;;;sEAC5E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAY,WAAU;0EAA0C;;;;;;;;;;;sEAC/E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA0C;;;;;;;;;;;sEAC9E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAa,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;;sDAKpF,6LAAC;;;8DACC,6LAAC;8FAAa;8DAAqC;;;;;;8DACnD,6LAAC;8FAAa;;sEACZ,6LAAC;;sEAAG,cAAA,6LAAC;gEAAE,MAAK;0GAAmB;0EAA0C;;;;;;;;;;;sEACzE,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA0C;;;;;;;;;;;sEAC9E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAQ,WAAU;0EAA0C;;;;;;;;;;;sEAC3E,6LAAC;;sEAAG,cAAA,6LAAC;gEAAE,MAAK;0GAAqB;0EAA0C;;;;;;;;;;;;;;;;;;;;;;;sDAK/E,6LAAC;;;8DACC,6LAAC;8FAAa;8DAAqC;;;;;;8DACnD,6LAAC;8FAAa;;sEACZ,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA0C;;;;;;;;;;;sEAC9E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAU,WAAU;0EAA0C;;;;;;;;;;;sEAC7E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAY,WAAU;0EAA0C;;;;;;;;;;;sEAC/E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA0C;;;;;;;;;;;sEAC9E,6LAAC;;sEAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAS,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAKlF,6LAAC;8EAAc;;sDACb,6LAAC;sFAAY;sDAAqC;;;;;;sDAGlD,6LAAC;sFAAc;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA0C;;;;;;8DAC1E,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;8DAA0C;;;;;;8DACxE,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAY,WAAU;8DAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3F;GAlrBwB;KAAA", "debugId": null}}]}