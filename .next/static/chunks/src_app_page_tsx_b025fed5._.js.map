{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <div className=\"min-h-screen text-white\" style={{\n      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',\n      fontFamily: 'Montserrat, sans-serif'\n    }}>\n      {/* Navigation */}\n      <nav className=\"fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20\">\n        <div className=\"container mx-auto px-6 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <div className=\"text-yellow-500 text-3xl mr-3\">\n              <i className=\"fas fa-comment-dollar\"></i>\n            </div>\n            <span className=\"font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n              BoGuani\n            </span>\n          </div>\n          \n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex space-x-8\">\n            <a href=\"#features\" className=\"hover:text-yellow-400 transition-colors\">Features</a>\n            <Link href=\"/downloads\" className=\"hover:text-yellow-400 transition-colors\">Downloads</Link>\n            <Link href=\"/support\" className=\"hover:text-yellow-400 transition-colors\">Support</Link>\n            <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all\">\n              Open Web App\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button \n            className=\"md:hidden text-yellow-400\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden bg-gradient-to-r from-purple-900/95 to-black/95 backdrop-blur-md border-t border-yellow-500/20\">\n            <div className=\"px-6 py-4 space-y-4\">\n              <a href=\"#features\" className=\"block hover:text-yellow-400 transition-colors\">Features</a>\n              <Link href=\"/downloads\" className=\"block hover:text-yellow-400 transition-colors\">Downloads</Link>\n              <Link href=\"/support\" className=\"block hover:text-yellow-400 transition-colors\">Support</Link>\n              <Link href=\"/auth\" className=\"block bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-2 rounded-full font-semibold text-center\">\n                Open Web App\n              </Link>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',\n        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'\n      }}>\n        {/* Background decorative elements */}\n        <div className=\"absolute top-20 left-20 w-32 h-32 bg-yellow-400 opacity-10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-20 w-40 h-40 bg-yellow-400 opacity-5 rounded-full blur-3xl animate-pulse\" style={{animationDelay: '2s'}}></div>\n        <div className=\"absolute top-1/2 left-1/4 w-24 h-24 bg-yellow-400 opacity-8 rounded-full blur-2xl animate-pulse\" style={{animationDelay: '4s'}}></div>\n\n        <div className=\"container mx-auto px-6 py-20 flex flex-col lg:flex-row items-center\">\n          {/* Left Content */}\n          <motion.div \n            className=\"lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0\"\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n              <span className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                BoGuani\n              </span>\n              <br />\n              <span className=\"text-white\">Messenger of Value</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed\">\n              Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all transform hover:scale-105 hover:shadow-2xl\">\n                <i className=\"fas fa-rocket mr-2\"></i>\n                Start Messaging\n              </Link>\n              <Link href=\"/downloads\" className=\"border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all transform hover:scale-105\">\n                <i className=\"fas fa-download mr-2\"></i>\n                Download App\n              </Link>\n            </div>\n          </motion.div>\n\n          {/* Right Content - App Mockup */}\n          <motion.div \n            className=\"lg:w-1/2 flex justify-center\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n          >\n            <div className=\"relative\">\n              <div className=\"w-80 h-96 bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg rounded-3xl border border-yellow-500/20 p-6 shadow-2xl\">\n                {/* Mock Chat Interface */}\n                <div className=\"flex items-center mb-6 pb-4 border-b border-yellow-500/20\">\n                  <div className=\"w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-3\">\n                    <i className=\"fas fa-user text-purple-900\"></i>\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-white\">Alex Rivera</h3>\n                    <p className=\"text-sm text-gray-400\">Online</p>\n                  </div>\n                </div>\n                \n                <div className=\"space-y-4\">\n                  <div className=\"bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 p-3 rounded-2xl rounded-bl-sm\">\n                    <p className=\"text-white text-sm\">Hey! Can you send me $50 for dinner? 🍕</p>\n                  </div>\n                  <div className=\"bg-gradient-to-r from-purple-600/30 to-purple-800/30 p-3 rounded-2xl rounded-br-sm ml-8\">\n                    <p className=\"text-white text-sm\">Sure! Sending now 💰</p>\n                    <div className=\"mt-2 p-2 bg-yellow-400/20 rounded-lg\">\n                      <p className=\"text-yellow-200 text-xs font-semibold\">💸 $50.00 sent securely</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-20 relative\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'\n      }}>\n        <div className=\"container mx-auto px-6\">\n          <motion.div \n            className=\"text-center mb-16\"\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              <span className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                Revolutionary Features\n              </span>\n            </h2>\n            <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n              Experience the perfect blend of secure messaging and instant value transfer\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {[\n              {\n                icon: 'fas fa-shield-alt',\n                title: 'End-to-End Encryption',\n                description: 'Military-grade encryption ensures your messages and transactions remain completely private and secure.'\n              },\n              {\n                icon: 'fas fa-bolt',\n                title: 'Instant Transfers',\n                description: 'Send money as easily as sending a text message. Lightning-fast transfers with real-time notifications.'\n              },\n              {\n                icon: 'fas fa-users',\n                title: 'Group Payments',\n                description: 'Split bills, share expenses, and manage group finances seamlessly within your chat conversations.'\n              },\n              {\n                icon: 'fas fa-mobile-alt',\n                title: 'Cross-Platform',\n                description: 'Available on all devices - iOS, Android, and Web. Your conversations sync perfectly everywhere.'\n              },\n              {\n                icon: 'fas fa-chart-line',\n                title: 'Smart Analytics',\n                description: 'Track your spending, analyze patterns, and make informed financial decisions with built-in insights.'\n              },\n              {\n                icon: 'fas fa-globe',\n                title: 'Global Reach',\n                description: 'Send messages and money worldwide with support for multiple currencies and international transfers.'\n              }\n            ].map((feature, index) => (\n              <motion.div\n                key={index}\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 hover:transform hover:scale-105\"\n                initial={{ opacity: 0, y: 50 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: index * 0.1 }}\n                viewport={{ once: true }}\n              >\n                <div className=\"w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mb-6\">\n                  <i className={`${feature.icon} text-2xl text-purple-900`}></i>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-white\">{feature.title}</h3>\n                <p className=\"text-gray-300 leading-relaxed\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 relative overflow-hidden\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'\n      }}>\n        <div className=\"absolute top-0 left-1/2 w-96 h-96 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-x-1/2\"></div>\n        \n        <div className=\"container mx-auto px-6 text-center relative z-10\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              <span className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">\n                Ready to Transform\n              </span>\n              <br />\n              <span className=\"text-white\">Your Communication?</span>\n            </h2>\n            <p className=\"text-xl text-gray-300 mb-8 max-w-2xl mx-auto\">\n              Join thousands of users who are already experiencing the future of value-based messaging.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link href=\"/auth\" className=\"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all transform hover:scale-105 hover:shadow-2xl\">\n                <i className=\"fas fa-rocket mr-2\"></i>\n                Get Started Now\n              </Link>\n              <Link href=\"/downloads\" className=\"border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all transform hover:scale-105\">\n                <i className=\"fas fa-download mr-2\"></i>\n                Download Apps\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-16 relative overflow-hidden\" style={{\n        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'\n      }}>\n        <div className=\"absolute top-0 left-1/2 w-96 h-96 bg-yellow-400 opacity-3 rounded-full blur-3xl transform -translate-x-1/2\"></div>\n\n        <div className=\"container mx-auto px-6 relative z-10\">\n          <div className=\"flex flex-col md:flex-row justify-between items-start mb-12\">\n            <div className=\"mb-8 md:mb-0\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"text-yellow-400 text-3xl mr-3\">\n                  <i className=\"fas fa-comment-dollar\"></i>\n                </div>\n                <span className=\"font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent\">BoGuani</span>\n              </div>\n              <p className=\"text-gray-300 text-lg mb-4\">Messenger of Value</p>\n              <p className=\"text-gray-400 max-w-sm\">Where Words Carry Worth - Experience the future of value-based communication.</p>\n            </div>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              <div>\n                <h3 className=\"font-bold mb-4 text-yellow-200 text-lg\">Product</h3>\n                <ul className=\"space-y-3 text-gray-400\">\n                  <li><a href=\"#features\" className=\"hover:text-yellow-400 transition-all duration-300\">Features</a></li>\n                  <li><Link href=\"/downloads\" className=\"hover:text-yellow-400 transition-all duration-300\">Downloads</Link></li>\n                  <li><Link href=\"/auth\" className=\"hover:text-yellow-400 transition-all duration-300\">Web App</Link></li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"font-bold mb-4 text-yellow-200 text-lg\">Company</h3>\n                <ul className=\"space-y-3 text-gray-400\">\n                  <li><a href=\"#about\" className=\"hover:text-yellow-400 transition-all duration-300\">About</a></li>\n                  <li><Link href=\"/contact\" className=\"hover:text-yellow-400 transition-all duration-300\">Contact</Link></li>\n                  <li><Link href=\"/support\" className=\"hover:text-yellow-400 transition-all duration-300\">Support</Link></li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"font-bold mb-4 text-yellow-200 text-lg\">Resources</h3>\n                <ul className=\"space-y-3 text-gray-400\">\n                  <li><Link href=\"/help\" className=\"hover:text-yellow-400 transition-all duration-300\">Help Center</Link></li>\n                  <li><Link href=\"/guides\" className=\"hover:text-yellow-400 transition-all duration-300\">Guides</Link></li>\n                  <li><Link href=\"/api\" className=\"hover:text-yellow-400 transition-all duration-300\">API Docs</Link></li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"font-bold mb-4 text-yellow-200 text-lg\">Legal</h3>\n                <ul className=\"space-y-3 text-gray-400\">\n                  <li><Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-all duration-300\">Privacy</Link></li>\n                  <li><Link href=\"/terms\" className=\"hover:text-yellow-400 transition-all duration-300\">Terms</Link></li>\n                  <li><Link href=\"/security\" className=\"hover:text-yellow-400 transition-all duration-300\">Security</Link></li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"border-t border-yellow-400 border-opacity-20 pt-8 flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-gray-400 mb-4 md:mb-0\">© 2024 BoGuani. All rights reserved.</p>\n            <div className=\"flex space-x-8 text-gray-400\">\n              <Link href=\"/privacy\" className=\"hover:text-yellow-400 transition-all duration-300\">Privacy Policy</Link>\n              <Link href=\"/terms\" className=\"hover:text-yellow-400 transition-all duration-300\">Terms of Service</Link>\n              <Link href=\"/cookies\" className=\"hover:text-yellow-400 transition-all duration-300\">Cookie Policy</Link>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAI,WAAU;QAA0B,OAAO;YAC9C,YAAY;YACZ,YAAY;QACd;;0BAEE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAK,WAAU;kDAAkG;;;;;;;;;;;;0CAMpH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAA0C;;;;;;kDACxE,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAA0C;;;;;;kDAC5E,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA0C;;;;;;kDAC1E,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAQ,WAAU;kDAA+J;;;;;;;;;;;;0CAM9L,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,cAAc,CAAC;0CAE9B,cAAA,6LAAC;oCAAE,WAAW,CAAC,IAAI,EAAE,aAAa,aAAa,UAAU,QAAQ,CAAC;;;;;;;;;;;;;;;;;oBAKrE,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAgD;;;;;;8CAC9E,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAgD;;;;;;8CAClF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgD;;;;;;8CAChF,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAwH;;;;;;;;;;;;;;;;;;;;;;;0BAS7J,6LAAC;gBAAQ,WAAU;gBAAyE,OAAO;oBACjG,YAAY;oBACZ,iBAAiB;gBACnB;;kCAEE,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAoG,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;kCAC/I,6LAAC;wBAAI,WAAU;wBAAkG,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;kCAE7I,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAA+E;;;;;;0DAG/F,6LAAC;;;;;0DACD,6LAAC;gDAAK,WAAU;0DAAa;;;;;;;;;;;;kDAE/B,6LAAC;wCAAE,WAAU;kDAAyD;;;;;;kDAGtE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;;kEAC3B,6LAAC;wDAAE,WAAU;;;;;;oDAAyB;;;;;;;0DAGxC,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,6LAAC;wDAAE,WAAU;;;;;;oDAA2B;;;;;;;;;;;;;;;;;;;0CAO9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2B;;;;;;0EACzC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;sEAAqB;;;;;;;;;;;kEAEpC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAClC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAE,WAAU;8EAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWrE,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;gBAAiB,OAAO;oBACvD,YAAY;gBACd;0BACE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;8CACZ,cAAA,6LAAC;wCAAK,WAAU;kDAA+E;;;;;;;;;;;8CAIjG,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM;oCACN,OAAO;oCACP,aAAa;gCACf;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAW,GAAG,QAAQ,IAAI,CAAC,yBAAyB,CAAC;;;;;;;;;;;sDAE1D,6LAAC;4CAAG,WAAU;sDAAqC,QAAQ,KAAK;;;;;;sDAChE,6LAAC;4CAAE,WAAU;sDAAiC,QAAQ,WAAW;;;;;;;mCAX5D;;;;;;;;;;;;;;;;;;;;;0BAmBf,6LAAC;gBAAQ,WAAU;gBAAiC,OAAO;oBACzD,YAAY;gBACd;;kCACE,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAA+E;;;;;;sDAG/F,6LAAC;;;;;sDACD,6LAAC;4CAAK,WAAU;sDAAa;;;;;;;;;;;;8CAE/B,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,6LAAC;oDAAE,WAAU;;;;;;gDAAyB;;;;;;;sDAGxC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;8DAChC,6LAAC;oDAAE,WAAU;;;;;;gDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,6LAAC;gBAAO,WAAU;gBAAiC,OAAO;oBACxD,YAAY;gBACd;;kCACE,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAE,WAAU;;;;;;;;;;;kEAEf,6LAAC;wDAAK,WAAU;kEAAkG;;;;;;;;;;;;0DAEpH,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;kDAGxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG,cAAA,6LAAC;oEAAE,MAAK;oEAAY,WAAU;8EAAoD;;;;;;;;;;;0EACtF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAa,WAAU;8EAAoD;;;;;;;;;;;0EAC1F,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAQ,WAAU;8EAAoD;;;;;;;;;;;;;;;;;;;;;;;0DAIzF,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG,cAAA,6LAAC;oEAAE,MAAK;oEAAS,WAAU;8EAAoD;;;;;;;;;;;0EACnF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAoD;;;;;;;;;;;0EACxF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAoD;;;;;;;;;;;;;;;;;;;;;;;0DAI5F,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAQ,WAAU;8EAAoD;;;;;;;;;;;0EACrF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAU,WAAU;8EAAoD;;;;;;;;;;;0EACvF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAO,WAAU;8EAAoD;;;;;;;;;;;;;;;;;;;;;;;0DAIxF,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAW,WAAU;8EAAoD;;;;;;;;;;;0EACxF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAS,WAAU;8EAAoD;;;;;;;;;;;0EACtF,6LAAC;0EAAG,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;oEAAY,WAAU;8EAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoD;;;;;;0DACpF,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAoD;;;;;;0DAClF,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlG;GAxTwB;KAAA", "debugId": null}}]}