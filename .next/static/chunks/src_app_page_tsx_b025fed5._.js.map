{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/ChatPay/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nexport default function HomePage() {\n  return (\n    <>\n      <style jsx global>{`\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');\n\n        body {\n          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);\n          font-family: 'Montserrat', sans-serif;\n        }\n\n        .gold-gradient {\n          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);\n          -webkit-background-clip: text;\n          background-clip: text;\n          color: transparent;\n        }\n\n        .gold-border {\n          border: 2px solid transparent;\n          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,\n                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;\n        }\n\n        .feature-card {\n          transition: transform 0.3s ease, box-shadow 0.3s ease;\n        }\n\n        .feature-card:hover {\n          transform: translateY(-5px);\n          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);\n        }\n\n        .btn-hover:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);\n        }\n\n        .hero-pattern {\n          background-image: url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n        }\n      `}</style>\n\n      <div className=\"text-white min-h-screen hero-pattern\">\n        {/* Hero Section */}\n        <div className=\"min-h-screen flex items-center justify-center px-6\">\n          <div className=\"w-full max-w-6xl\">\n\n            {/* Header */}\n            <div className=\"text-center mb-16\">\n              <div className=\"w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300\">\n                <i className=\"fas fa-comment-dollar text-5xl text-purple-900\"></i>\n              </div>\n              <h1 className=\"text-6xl md:text-7xl font-bold mb-6 gold-gradient\">\n                BoGuani\n              </h1>\n              <p className=\"text-2xl text-gray-300 mb-4\">Messenger of Value</p>\n              <p className=\"text-xl text-gray-400 italic mb-8\">&quot;Speak Gold. Share Value.&quot;</p>\n              <p className=\"text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n                Experience the future of value-based communication with end-to-end encryption,\n                instant money transfers, and crystal-clear voice & video calls.\n              </p>\n            </div>\n\n            {/* Main Action Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n\n              {/* Open Web Version Card */}\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl gold-border shadow-2xl feature-card\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-globe text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4 gold-gradient\">Open Web Version</h3>\n                  <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                    Start messaging instantly in your browser. No downloads required.\n                  </p>\n                  <Link\n                    href=\"/auth\"\n                    className=\"w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover inline-block\"\n                  >\n                    <i className=\"fas fa-rocket mr-2\"></i>\n                    Launch BoGuani\n                  </Link>\n                </div>\n              </motion.div>\n\n              {/* Download App Card */}\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl gold-border shadow-2xl feature-card\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-download text-2xl text-purple-900\"></i>\n                  </div>\n                  <h3 className=\"text-2xl font-bold mb-4 gold-gradient\">Download Apps</h3>\n                  <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                    Get the full experience on iOS, Android, or Desktop.\n                  </p>\n                  <Link\n                    href=\"/downloads\"\n                    className=\"w-full bg-transparent border-2 border-yellow-400 text-yellow-400 px-6 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all btn-hover inline-block\"\n                  >\n                    <i className=\"fas fa-mobile-alt mr-2\"></i>\n                    Choose Platform\n                  </Link>\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Feature Highlights */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-16\">\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-shield-alt text-lg text-purple-900\"></i>\n                  </div>\n                  <h4 className=\"text-lg font-semibold mb-2 gold-gradient\">End-to-End Encryption</h4>\n                  <p className=\"text-gray-400 text-sm\">Military-grade security for all your messages</p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-dollar-sign text-lg text-purple-900\"></i>\n                  </div>\n                  <h4 className=\"text-lg font-semibold mb-2 gold-gradient\">Instant Money Transfers</h4>\n                  <p className=\"text-gray-400 text-sm\">Send money as easily as sending a message</p>\n                </div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card\"\n                whileHover={{ scale: 1.05 }}\n                transition={{ duration: 0.3 }}\n              >\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center\">\n                    <i className=\"fas fa-video text-lg text-purple-900\"></i>\n                  </div>\n                  <h4 className=\"text-lg font-semibold mb-2 gold-gradient\">HD Voice & Video</h4>\n                  <p className=\"text-gray-400 text-sm\">Crystal-clear calls with WebRTC technology</p>\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Quick Links */}\n            <div className=\"text-center\">\n              <div className=\"flex flex-wrap justify-center gap-4\">\n                <Link href=\"/security\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors text-sm\">\n                  <i className=\"fas fa-lock mr-1\"></i> Security\n                </Link>\n                <Link href=\"/pricing\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors text-sm\">\n                  <i className=\"fas fa-tag mr-1\"></i> Pricing\n                </Link>\n                <Link href=\"/support\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors text-sm\">\n                  <i className=\"fas fa-question-circle mr-1\"></i> Support\n                </Link>\n                <Link href=\"/blog\" className=\"text-yellow-400 hover:text-yellow-200 transition-colors text-sm\">\n                  <i className=\"fas fa-newspaper mr-1\"></i> Blog\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,qBACE;;;;;;0BAyCE,6LAAC;0DAAc;0BAEb,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;;0CAGb,6LAAC;0EAAc;;kDACb,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAY;;;;;;;;;;;kDAEf,6LAAC;kFAAa;kDAAoD;;;;;;kDAGlE,6LAAC;kFAAY;kDAA8B;;;;;;kDAC3C,6LAAC;kFAAY;kDAAoC;;;;;;kDACjD,6LAAC;kFAAY;kDAA0D;;;;;;;;;;;;0CAOzE,6LAAC;0EAAc;;kDAGb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;8FAAa;8DAAwC;;;;;;8DACtD,6LAAC;8FAAY;8DAAqC;;;;;;8DAGlD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;sGAAY;;;;;;wDAAyB;;;;;;;;;;;;;;;;;;kDAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;8FAAa;8DAAwC;;;;;;8DACtD,6LAAC;8FAAY;8DAAqC;;;;;;8DAGlD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC;sGAAY;;;;;;wDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0CAQlD,6LAAC;0EAAc;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;8FAAa;8DAA2C;;;;;;8DACzD,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;;;;;;kDAIzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;8FAAa;8DAA2C;;;;;;8DACzD,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;;;;;;kDAIzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAY;;;;;;;;;;;8DAEf,6LAAC;8FAAa;8DAA2C;;;;;;8DACzD,6LAAC;8FAAY;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAc;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;;8DAC/B,6LAAC;8FAAY;;;;;;gDAAuB;;;;;;;sDAEtC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,6LAAC;8FAAY;;;;;;gDAAsB;;;;;;;sDAErC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,6LAAC;8FAAY;;;;;;gDAAkC;;;;;;;sDAEjD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,6LAAC;8FAAY;;;;;;gDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D;KAvLwB", "debugId": null}}]}