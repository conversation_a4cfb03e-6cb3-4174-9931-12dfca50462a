(()=>{var e={};e.id=287,e.ids=[287],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3811:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/ChatPay/src/app/api-docs/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/ChatPay/src/app/api-docs/page.tsx","default")},3873:e=>{"use strict";e.exports=require("path")},5592:(e,s,a)=>{Promise.resolve().then(a.bind(a,7345))},6545:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>x,pages:()=>b,routeModule:()=>f,tree:()=>o});var t=a(5239),d=a(8088),l=a(8170),r=a.n(l),n=a(893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);a.d(s,i);let o={children:["",{children:["api-docs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3811)),"/Volumes/Apps/Websites/ChatPay/src/app/api-docs/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Volumes/Apps/Websites/ChatPay/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,b=["/Volumes/Apps/Websites/ChatPay/src/app/api-docs/page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},f=new t.AppPageRouteModule({definition:{kind:d.RouteKind.APP_PAGE,page:"/api-docs/page",pathname:"/api-docs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},7345:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(687),d=a(6180),l=a.n(d),r=a(5814),n=a.n(r),i=a(3210);function o(){let[e,s]=(0,i.useState)("getting-started");return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l(),{id:"4f52adf51b20bbfd",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-moz-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-o-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:linear-gradient(135deg,#1E1E24 0%,#2D1B4E 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}"}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd text-white min-h-screen hero-pattern",children:[(0,t.jsx)("nav",{className:"jsx-4f52adf51b20bbfd bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd container mx-auto px-6 py-3 flex justify-between items-center",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center",children:[(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd text-yellow-400 text-3xl mr-2",children:(0,t.jsx)("i",{className:"jsx-4f52adf51b20bbfd fas fa-comment-dollar"})}),(0,t.jsx)("span",{className:"jsx-4f52adf51b20bbfd font-bold text-2xl gold-gradient",children:"BoGuani"})]}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd hidden md:flex space-x-8",children:[(0,t.jsx)(n(),{href:"/#features",className:"hover:text-yellow-400 transition-colors",children:"Features"}),(0,t.jsx)(n(),{href:"/#about",className:"hover:text-yellow-400 transition-colors",children:"About"}),(0,t.jsx)(n(),{href:"/#download",className:"hover:text-yellow-400 transition-colors",children:"Download"}),(0,t.jsx)(n(),{href:"/support",className:"hover:text-yellow-400 transition-colors",children:"Support"})]})]})}),(0,t.jsx)("section",{className:"jsx-4f52adf51b20bbfd pt-24 pb-16",children:(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd container mx-auto px-6",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd text-center mb-16",children:[(0,t.jsx)("h1",{className:"jsx-4f52adf51b20bbfd text-5xl lg:text-6xl font-bold mb-6 gold-gradient",children:"API Documentation"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-xl text-gray-300 max-w-3xl mx-auto",children:"Build powerful integrations with BoGuani's secure messaging and payment APIs."})]})})}),(0,t.jsx)("section",{className:"jsx-4f52adf51b20bbfd py-20 bg-purple-900",children:(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd container mx-auto px-6",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd lg:w-1/4",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-purple-800 rounded-xl p-6 sticky top-24",children:[(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-6 text-yellow-400",children:"Documentation"}),(0,t.jsxs)("nav",{className:"jsx-4f52adf51b20bbfd space-y-2",children:[(0,t.jsx)("button",{onClick:()=>s("getting-started"),className:`jsx-4f52adf51b20bbfd w-full text-left px-4 py-2 rounded-lg transition-colors ${"getting-started"===e?"bg-yellow-400 text-gray-900":"text-gray-300 hover:text-yellow-400"}`,children:"Getting Started"}),(0,t.jsx)("button",{onClick:()=>s("authentication"),className:`jsx-4f52adf51b20bbfd w-full text-left px-4 py-2 rounded-lg transition-colors ${"authentication"===e?"bg-yellow-400 text-gray-900":"text-gray-300 hover:text-yellow-400"}`,children:"Authentication"}),(0,t.jsx)("button",{onClick:()=>s("messaging"),className:`jsx-4f52adf51b20bbfd w-full text-left px-4 py-2 rounded-lg transition-colors ${"messaging"===e?"bg-yellow-400 text-gray-900":"text-gray-300 hover:text-yellow-400"}`,children:"Messaging API"}),(0,t.jsx)("button",{onClick:()=>s("payments"),className:`jsx-4f52adf51b20bbfd w-full text-left px-4 py-2 rounded-lg transition-colors ${"payments"===e?"bg-yellow-400 text-gray-900":"text-gray-300 hover:text-yellow-400"}`,children:"Payments API"}),(0,t.jsx)("button",{onClick:()=>s("webhooks"),className:`jsx-4f52adf51b20bbfd w-full text-left px-4 py-2 rounded-lg transition-colors ${"webhooks"===e?"bg-yellow-400 text-gray-900":"text-gray-300 hover:text-yellow-400"}`,children:"Webhooks"})]})]})}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd lg:w-3/4",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-purple-800 rounded-xl p-8",children:["getting-started"===e&&(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd",children:[(0,t.jsx)("h2",{className:"jsx-4f52adf51b20bbfd text-3xl font-bold mb-6 text-yellow-400",children:"Getting Started"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 mb-6",children:"Welcome to the BoGuani API! Our REST API allows you to integrate secure messaging and payment functionality into your applications."}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Base URL"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg mb-6",children:(0,t.jsx)("code",{className:"jsx-4f52adf51b20bbfd text-green-400",children:"https://api.boguani.com/v1"})}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Quick Start"}),(0,t.jsxs)("ol",{className:"jsx-4f52adf51b20bbfd list-decimal list-inside space-y-2 text-gray-300 mb-6",children:[(0,t.jsx)("li",{className:"jsx-4f52adf51b20bbfd",children:"Sign up for a BoGuani Business account"}),(0,t.jsx)("li",{className:"jsx-4f52adf51b20bbfd",children:"Generate your API keys in the dashboard"}),(0,t.jsx)("li",{className:"jsx-4f52adf51b20bbfd",children:"Make your first API call"}),(0,t.jsx)("li",{className:"jsx-4f52adf51b20bbfd",children:"Start building!"})]}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Example Request"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`curl -X GET "https://api.boguani.com/v1/user/profile" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`})})]}),"authentication"===e&&(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd",children:[(0,t.jsx)("h2",{className:"jsx-4f52adf51b20bbfd text-3xl font-bold mb-6 text-yellow-400",children:"Authentication"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 mb-6",children:"BoGuani uses API keys to authenticate requests. Include your API key in the Authorization header."}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"API Key Types"}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd space-y-4 mb-6",children:[(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"jsx-4f52adf51b20bbfd font-semibold text-yellow-400 mb-2",children:"Public Key"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 text-sm",children:"Used for client-side operations like user authentication"})]}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"jsx-4f52adf51b20bbfd font-semibold text-yellow-400 mb-2",children:"Secret Key"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 text-sm",children:"Used for server-side operations like sending messages and processing payments"})]})]}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Authentication Header"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:(0,t.jsx)("code",{className:"jsx-4f52adf51b20bbfd text-green-400",children:"Authorization: Bearer sk_live_your_secret_key_here"})})]}),"messaging"===e&&(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd",children:[(0,t.jsx)("h2",{className:"jsx-4f52adf51b20bbfd text-3xl font-bold mb-6 text-yellow-400",children:"Messaging API"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 mb-6",children:"Send and receive encrypted messages programmatically."}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Send Message"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg mb-6",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`POST /v1/messages

{
  "recipient": "+**********",
  "message": "Hello from BoGuani API!",
  "type": "text",
  "encrypted": true
}`})}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Get Messages"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg mb-6",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`GET /v1/messages?conversation_id=conv_123

Response:
{
  "messages": [
    {
      "id": "msg_456",
      "sender": "+**********",
      "recipient": "+**********",
      "message": "Hello!",
      "timestamp": "2024-01-15T10:30:00Z",
      "encrypted": true
    }
  ]
}`})})]}),"payments"===e&&(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd",children:[(0,t.jsx)("h2",{className:"jsx-4f52adf51b20bbfd text-3xl font-bold mb-6 text-yellow-400",children:"Payments API"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 mb-6",children:"Process secure money transfers within conversations."}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Send Payment"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg mb-6",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`POST /v1/payments

{
  "recipient": "+**********",
  "amount": 50.00,
  "currency": "USD",
  "message": "Lunch money",
  "payment_method": "bank_account"
}`})}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Payment Status"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`GET /v1/payments/pay_123

Response:
{
  "id": "pay_123",
  "status": "completed",
  "amount": 50.00,
  "currency": "USD",
  "sender": "+**********",
  "recipient": "+**********",
  "created_at": "2024-01-15T10:30:00Z"
}`})})]}),"webhooks"===e&&(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd",children:[(0,t.jsx)("h2",{className:"jsx-4f52adf51b20bbfd text-3xl font-bold mb-6 text-yellow-400",children:"Webhooks"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 mb-6",children:"Receive real-time notifications about messages and payments."}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Webhook Events"}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd space-y-4 mb-6",children:[(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"jsx-4f52adf51b20bbfd font-semibold text-yellow-400 mb-2",children:"message.received"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 text-sm",children:"Triggered when a new message is received"})]}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"jsx-4f52adf51b20bbfd font-semibold text-yellow-400 mb-2",children:"payment.completed"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 text-sm",children:"Triggered when a payment is successfully processed"})]}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"jsx-4f52adf51b20bbfd font-semibold text-yellow-400 mb-2",children:"payment.failed"}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-300 text-sm",children:"Triggered when a payment fails"})]})]}),(0,t.jsx)("h3",{className:"jsx-4f52adf51b20bbfd text-xl font-semibold mb-4 text-yellow-400",children:"Webhook Payload"}),(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd bg-gray-900 p-4 rounded-lg",children:(0,t.jsx)("pre",{className:"jsx-4f52adf51b20bbfd text-green-400 text-sm",children:`{
  "event": "message.received",
  "data": {
    "id": "msg_456",
    "sender": "+**********",
    "recipient": "+**********",
    "message": "Hello!",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}`})})]})]})})]})})}),(0,t.jsx)("footer",{className:"jsx-4f52adf51b20bbfd bg-gray-900 py-12",children:(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd container mx-auto px-6",children:(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd mb-6 md:mb-0",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center",children:[(0,t.jsx)("div",{className:"jsx-4f52adf51b20bbfd text-yellow-400 text-2xl mr-2",children:(0,t.jsx)("i",{className:"jsx-4f52adf51b20bbfd fas fa-comment-dollar"})}),(0,t.jsx)("span",{className:"jsx-4f52adf51b20bbfd font-bold text-xl gold-gradient",children:"BoGuani"})]}),(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-400 mt-2",children:"Messenger of Value"})]}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd text-center",children:[(0,t.jsx)("p",{className:"jsx-4f52adf51b20bbfd text-gray-400 text-sm",children:"\xa9 2024 BoGuani. All rights reserved."}),(0,t.jsxs)("div",{className:"jsx-4f52adf51b20bbfd flex space-x-6 text-gray-400 text-sm mt-2",children:[(0,t.jsx)(n(),{href:"/privacy",className:"hover:text-yellow-400 transition-colors",children:"Privacy Policy"}),(0,t.jsx)(n(),{href:"/terms",className:"hover:text-yellow-400 transition-colors",children:"Terms of Service"}),(0,t.jsx)(n(),{href:"/security",className:"hover:text-yellow-400 transition-colors",children:"Security"})]})]})]})})})]})]})}},8744:(e,s,a)=>{Promise.resolve().then(a.bind(a,3811))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[447,938,711,814,279],()=>a(6545));module.exports=t})();