(()=>{var e={};e.id=457,e.ids=[457],e.modules={512:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return b},defaultHead:function(){return x}});let a=s(4985),r=s(740),i=s(687),n=r._(s(3210)),l=a._(s(7755)),o=s(4959),c=s(9513),d=s(4604);function x(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function m(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}s(148);let u=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:s}=t;return e.reduce(m,[]).reverse().concat(x(s).reverse()).filter(function(){let e=new Set,t=new Set,s=new Set,a={};return r=>{let i=!0,n=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){n=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?i=!1:t.add(r.type);break;case"meta":for(let e=0,t=u.length;e<t;e++){let t=u[e];if(r.props.hasOwnProperty(t))if("charSet"===t)s.has(t)?i=!1:s.add(t);else{let e=r.props[t],s=a[t]||new Set;("name"!==t||!n)&&s.has(e)?i=!1:(s.add(e),a[t]=s)}}}return i}}()).reverse().map((e,t)=>{let a=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!s&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,n.default.cloneElement(e,t)}return n.default.cloneElement(e,{key:a})})}let b=function(e){let{children:t}=e,s=(0,n.useContext)(o.AmpStateContext),a=(0,n.useContext)(c.HeadManagerContext);return(0,i.jsx)(l.default,{reduceComponentsToState:h,headManager:a,inAmpMode:(0,d.isInAmpMode)(s),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1261:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},getImageProps:function(){return l}});let a=s(4985),r=s(4953),i=s(6533),n=a._(s(1933));function l(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,s]of Object.entries(t))void 0===s&&delete t[e];return{props:t}}let o=i.Image},1480:(e,t)=>{"use strict";function s(e){let{widthInt:t,heightInt:s,blurWidth:a,blurHeight:r,blurDataURL:i,objectFit:n}=e,l=a?40*a:t,o=r?40*r:s,c=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return s}})},1933:(e,t)=>{"use strict";function s(e){var t;let{config:s,src:a,width:r,quality:i}=e,n=i||(null==(t=s.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return s.path+"?url="+encodeURIComponent(a)+"&w="+r+"&q="+n+(a.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),s.__next_img_default=!0;let a=s},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{VALID_LOADERS:function(){return s},imageConfigDefault:function(){return a}});let s=["default","imgix","cloudinary","akamai","custom"],a={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3430:(e,t,s)=>{Promise.resolve().then(s.bind(s,8973))},3873:e=>{"use strict";e.exports=require("path")},4604:(e,t)=>{"use strict";function s(e){let{ampFirst:t=!1,hybrid:s=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||s&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return s}})},4953:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),s(148);let a=s(1480),r=s(2756),i=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var s,o;let c,d,x,{src:m,sizes:u,unoptimized:h=!1,priority:b=!1,loading:f,className:g,quality:p,width:j,height:y,fill:v=!1,style:N,overrideSrc:w,onLoad:k,onLoadingComplete:S,placeholder:C="empty",blurDataURL:P,fetchPriority:E,decoding:A="async",layout:D,objectFit:$,objectPosition:_,lazyBoundary:M,lazyRoot:z,...I}=e,{imgConf:R,showAltText:O,blurComplete:F,defaultLoader:L}=t,V=R||r.imageConfigDefault;if("allSizes"in V)c=V;else{let e=[...V.deviceSizes,...V.imageSizes].sort((e,t)=>e-t),t=V.deviceSizes.sort((e,t)=>e-t),a=null==(s=V.qualities)?void 0:s.sort((e,t)=>e-t);c={...V,allSizes:e,deviceSizes:t,qualities:a}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let T=I.loader||L;delete I.loader,delete I.srcSet;let B="__next_img_default"in T;if(B){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=T;T=t=>{let{config:s,...a}=t;return e(a)}}if(D){"fill"===D&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[D];e&&(N={...N,...e});let t={responsive:"100vw",fill:"100vw"}[D];t&&!u&&(u=t)}let q="",H=l(j),G=l(y);if((o=m)&&"object"==typeof o&&(n(o)||void 0!==o.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,x=e.blurHeight,P=P||e.blurDataURL,q=e.src,!v)if(H||G){if(H&&!G){let t=H/e.width;G=Math.round(e.height*t)}else if(!H&&G){let t=G/e.height;H=Math.round(e.width*t)}}else H=e.width,G=e.height}let U=!b&&("lazy"===f||void 0===f);(!(m="string"==typeof m?m:q)||m.startsWith("data:")||m.startsWith("blob:"))&&(h=!0,U=!1),c.unoptimized&&(h=!0),B&&!c.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(h=!0);let W=l(p),X=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:$,objectPosition:_}:{},O?{}:{color:"transparent"},N),J=F||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,a.getImageBlurSvg)({widthInt:H,heightInt:G,blurWidth:d,blurHeight:x,blurDataURL:P||"",objectFit:X.objectFit})+'")':'url("'+C+'")',Q=i.includes(X.objectFit)?"fill"===X.objectFit?"100% 100%":"cover":X.objectFit,K=J?{backgroundSize:Q,backgroundPosition:X.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},Y=function(e){let{config:t,src:s,unoptimized:a,width:r,quality:i,sizes:n,loader:l}=e;if(a)return{src:s,srcSet:void 0,sizes:void 0};let{widths:o,kind:c}=function(e,t,s){let{deviceSizes:a,allSizes:r}=e;if(s){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let a;a=e.exec(s);)t.push(parseInt(a[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=a[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:a,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,n),d=o.length-1;return{sizes:n||"w"!==c?n:"100vw",srcSet:o.map((e,a)=>l({config:t,src:s,quality:i,width:e})+" "+("w"===c?e:a+1)+c).join(", "),src:l({config:t,src:s,quality:i,width:o[d]})}}({config:c,src:m,unoptimized:h,width:H,quality:W,sizes:u,loader:T});return{props:{...I,loading:U?"lazy":f,fetchPriority:E,width:H,height:G,decoding:A,className:g,style:{...X,...K},sizes:Y.sizes,srcSet:Y.srcSet,src:w||Y.src},meta:{unoptimized:h,priority:b,placeholder:C,fill:v}}}},4959:(e,t,s)=>{"use strict";e.exports=s(4041).vendored.contexts.AmpContext},6189:(e,t,s)=>{"use strict";var a=s(5773);s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},6533:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return y}});let a=s(4985),r=s(740),i=s(687),n=r._(s(3210)),l=a._(s(1215)),o=a._(s(512)),c=s(4953),d=s(2756),x=s(7903);s(148);let m=s(9148),u=a._(s(1933)),h=s(3038),b={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function f(e,t,s,a,r,i,n){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==s?void 0:s.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let a=!1,r=!1;s.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>a,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{a=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==a?void 0:a.current)&&a.current(e)}}))}function g(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let p=(0,n.forwardRef)((e,t)=>{let{src:s,srcSet:a,sizes:r,height:l,width:o,decoding:c,className:d,style:x,fetchPriority:m,placeholder:u,loading:b,unoptimized:p,fill:j,onLoadRef:y,onLoadingCompleteRef:v,setBlurComplete:N,setShowAltText:w,sizesInput:k,onLoad:S,onError:C,...P}=e,E=(0,n.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&f(e,u,y,v,N,p,k))},[s,u,y,v,N,C,p,k]),A=(0,h.useMergedRef)(t,E);return(0,i.jsx)("img",{...P,...g(m),loading:b,width:o,height:l,decoding:c,"data-nimg":j?"fill":"1",className:d,style:x,sizes:r,srcSet:a,src:s,ref:A,onLoad:e=>{f(e.currentTarget,u,y,v,N,p,k)},onError:e=>{w(!0),"empty"!==u&&N(!0),C&&C(e)}})});function j(e){let{isAppRouter:t,imgAttributes:s}=e,a={as:"image",imageSrcSet:s.srcSet,imageSizes:s.sizes,crossOrigin:s.crossOrigin,referrerPolicy:s.referrerPolicy,...g(s.fetchPriority)};return t&&l.default.preload?(l.default.preload(s.src,a),null):(0,i.jsx)(o.default,{children:(0,i.jsx)("link",{rel:"preload",href:s.srcSet?void 0:s.src,...a},"__nimg-"+s.src+s.srcSet+s.sizes)})}let y=(0,n.forwardRef)((e,t)=>{let s=(0,n.useContext)(m.RouterContext),a=(0,n.useContext)(x.ImageConfigContext),r=(0,n.useMemo)(()=>{var e;let t=b||a||d.imageConfigDefault,s=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:s,deviceSizes:r,qualities:i}},[a]),{onLoad:l,onLoadingComplete:o}=e,h=(0,n.useRef)(l);(0,n.useEffect)(()=>{h.current=l},[l]);let f=(0,n.useRef)(o);(0,n.useEffect)(()=>{f.current=o},[o]);let[g,y]=(0,n.useState)(!1),[v,N]=(0,n.useState)(!1),{props:w,meta:k}=(0,c.getImgProps)(e,{defaultLoader:u.default,imgConf:r,blurComplete:g,showAltText:v});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p,{...w,unoptimized:k.unoptimized,placeholder:k.placeholder,fill:k.fill,onLoadRef:h,onLoadingCompleteRef:f,setBlurComplete:y,setShowAltText:N,sizesInput:e.sizes,ref:t}),k.priority?(0,i.jsx)(j,{isAppRouter:!s,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6990:(e,t,s)=>{Promise.resolve().then(s.bind(s,7657))},7657:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var a=s(687),r=s(6180),i=s.n(r),n=s(3210),l=s(6189),o=s(6001),c=s(2157),d=s(2789),x=s(2743),m=s(1279),u=s(8171),h=s(2582);class b extends n.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,u.s)(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=s-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:s,root:r}){let i=(0,n.useId)(),l=(0,n.useRef)(null),o=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:c}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:e,height:a,top:n,left:d,right:x}=o.current;if(t||!l.current||!e||!a)return;let m="left"===s?`left: ${d}`:`right: ${x}`;l.current.dataset.motionPopId=i;let u=document.createElement("style");c&&(u.nonce=c);let h=r??document.head;return h.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${m}px !important;
            top: ${n}px !important;
          }
        `),()=>{h.removeChild(u),h.contains(u)&&h.removeChild(u)}},[t]),(0,a.jsx)(b,{isPresent:t,childRef:l,sizeRef:o,children:n.cloneElement(e,{ref:l})})}let g=({children:e,initial:t,isPresent:s,onExitComplete:r,custom:i,presenceAffectsLayout:l,mode:o,anchorX:c,root:x})=>{let u=(0,d.M)(p),h=(0,n.useId)(),b=!0,g=(0,n.useMemo)(()=>(b=!1,{id:h,initial:t,isPresent:s,custom:i,onExitComplete:e=>{for(let t of(u.set(e,!0),u.values()))if(!t)return;r&&r()},register:e=>(u.set(e,!1),()=>u.delete(e))}),[s,u,r]);return l&&b&&(g={...g}),(0,n.useMemo)(()=>{u.forEach((e,t)=>u.set(t,!1))},[s]),n.useEffect(()=>{s||u.size||!r||r()},[s]),"popLayout"===o&&(e=(0,a.jsx)(f,{isPresent:s,anchorX:c,root:x,children:e})),(0,a.jsx)(m.t.Provider,{value:g,children:e})};function p(){return new Map}var j=s(6044);let y=e=>e.key||"";function v(e){let t=[];return n.Children.forEach(e,e=>{(0,n.isValidElement)(e)&&t.push(e)}),t}let N=({children:e,custom:t,initial:s=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:l="sync",propagate:o=!1,anchorX:m="left",root:u})=>{let[h,b]=(0,j.xQ)(o),f=(0,n.useMemo)(()=>v(e),[e]),p=o&&!h?[]:f.map(y),N=(0,n.useRef)(!0),w=(0,n.useRef)(f),k=(0,d.M)(()=>new Map),[S,C]=(0,n.useState)(f),[P,E]=(0,n.useState)(f);(0,x.E)(()=>{N.current=!1,w.current=f;for(let e=0;e<P.length;e++){let t=y(P[e]);p.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[P,p.length,p.join("-")]);let A=[];if(f!==S){let e=[...f];for(let t=0;t<P.length;t++){let s=P[t],a=y(s);p.includes(a)||(e.splice(t,0,s),A.push(s))}return"wait"===l&&A.length&&(e=A),E(v(e)),C(f),null}let{forceRender:D}=(0,n.useContext)(c.L);return(0,a.jsx)(a.Fragment,{children:P.map(e=>{let n=y(e),c=(!o||!!h)&&(f===P||p.includes(n));return(0,a.jsx)(g,{isPresent:c,initial:(!N.current||!!s)&&void 0,custom:t,presenceAffectsLayout:i,mode:l,root:u,onExitComplete:c?void 0:()=>{if(!k.has(n))return;k.set(n,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(D?.(),E(w.current),o&&b?.(),r&&r())},anchorX:m,children:e},n)})})};var w=s(1261),k=s.n(w);function S({isOpen:e,onClose:t,currentUser:s,bankBalance:r,connectedBank:i}){let[l,c]=(0,n.useState)("profile"),[d,x]=(0,n.useState)({name:s?.name||"",username:s?.username||"",phone:s?.phone||"",email:"<EMAIL>",status:s?.status||"",bio:"Messenger of Value",avatar:s?.avatar||""}),[m,u]=(0,n.useState)({enterToSend:!0,readReceipts:!0,typingIndicators:!0,lastSeen:!0,mediaAutoDownload:!0,soundEnabled:!0,vibrationEnabled:!0,messagePreview:!0,groupNotifications:!0,archiveChats:!1}),[h,b]=(0,n.useState)({instantTransfers:!0,paymentNotifications:!0,transactionLimits:{daily:5e3,monthly:25e3},autoSave:!1,savingsGoal:1e3,requirePinForPayments:!0,biometricAuth:!0}),[f,g]=(0,n.useState)({twoFactorAuth:!0,biometricLogin:!0,sessionTimeout:30,deviceVerification:!0,encryptionLevel:"military",backupMessages:!0,screenLock:!0,incognitoMode:!1}),[p,j]=(0,n.useState)({pushNotifications:!0,emailNotifications:!1,smsNotifications:!1,soundAlerts:!0,vibration:!0,showPreviews:!0,quietHours:{enabled:!1,start:"22:00",end:"07:00"},priorityContacts:[]}),[y,v]=(0,n.useState)({profileVisibility:"contacts",lastSeenVisibility:"contacts",statusVisibility:"everyone",readReceiptSharing:!0,blockUnknownNumbers:!1,dataCollection:!1,analyticsSharing:!1,locationSharing:!1}),[w,k]=(0,n.useState)({theme:"dark",accentColor:"gold",fontSize:"medium",chatWallpaper:"default",bubbleStyle:"transparent",animationsEnabled:!0,compactMode:!1,highContrast:!1});return e?(0,a.jsx)(N,{children:(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,children:(0,a.jsx)(o.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] border border-gray-600/50 professional-shadow overflow-hidden",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsxs)("div",{className:"w-64 bg-gradient-to-b from-gray-900/90 to-gray-800/90 border-r border-gray-600/30 p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-white gold-gradient",children:"Settings"}),(0,a.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors p-2 rounded-full hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,a.jsx)("nav",{className:"space-y-2",children:[{id:"profile",name:"Profile",icon:"fas fa-user"},{id:"chat",name:"Chat",icon:"fas fa-comments"},{id:"banking",name:"Banking",icon:"fas fa-university"},{id:"security",name:"Security",icon:"fas fa-shield-alt"},{id:"notifications",name:"Notifications",icon:"fas fa-bell"},{id:"privacy",name:"Privacy",icon:"fas fa-lock"},{id:"appearance",name:"Appearance",icon:"fas fa-palette"},{id:"advanced",name:"Advanced",icon:"fas fa-cogs"}].map(e=>(0,a.jsxs)("button",{onClick:()=>c(e.id),className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 ${l===e.id?"bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 text-yellow-400":"text-gray-400 hover:text-white hover:bg-gray-700/50"}`,children:[(0,a.jsx)("i",{className:`${e.icon} text-lg`}),(0,a.jsx)("span",{className:"font-medium",children:e.name})]},e.id))})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-8",children:["profile"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Profile Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your personal information and profile appearance"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Profile Picture"}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-gray-900 font-bold text-2xl professional-shadow",children:d.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-6 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300",children:"Upload Photo"}),(0,a.jsx)("button",{className:"block text-gray-400 hover:text-white transition-colors text-sm",children:"Remove Photo"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Personal Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name"}),(0,a.jsx)("input",{type:"text",value:d.name,onChange:e=>x({...d,name:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Username"}),(0,a.jsx)("input",{type:"text",value:d.username,onChange:e=>x({...d,username:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:d.phone,onChange:e=>x({...d,phone:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:d.email,onChange:e=>x({...d,email:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Status & Bio"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Message"}),(0,a.jsx)("input",{type:"text",value:d.status,onChange:e=>x({...d,status:e.target.value}),placeholder:"Available for chat",className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio"}),(0,a.jsx)("textarea",{value:d.bio,onChange:e=>x({...d,bio:e.target.value}),placeholder:"Tell others about yourself...",rows:3,className:"w-full px-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none resize-none"})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)("button",{className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-8 py-3 rounded-xl font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"fas fa-save mr-2"}),"Save Changes"]})})]}),"chat"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Chat Settings"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize your messaging experience and preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Message Behavior"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enter to Send"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Press Enter to send messages (Shift+Enter for new line)"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,enterToSend:!m.enterToSend}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.enterToSend?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.enterToSend?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Read Receipts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you've read messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,readReceipts:!m.readReceipts}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.readReceipts?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.readReceipts?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Typing Indicators"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show when you're typing to others"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,typingIndicators:!m.typingIndicators}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.typingIndicators?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.typingIndicators?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Last Seen"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show your last seen status to contacts"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,lastSeen:!m.lastSeen}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.lastSeen?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.lastSeen?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Media & Files"}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Download Media"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically download photos and videos"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,mediaAutoDownload:!m.mediaAutoDownload}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.mediaAutoDownload?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.mediaAutoDownload?"translate-x-6":"translate-x-1"}`})})]})})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Chat Notifications"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for new messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,soundEnabled:!m.soundEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.soundEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.soundEnabled?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Vibration"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Vibrate for new messages"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,vibrationEnabled:!m.vibrationEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.vibrationEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.vibrationEnabled?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Message Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>u({...m,messagePreview:!m.messagePreview}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${m.messagePreview?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${m.messagePreview?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"banking"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Banking & Payments"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Manage your financial settings and payment preferences"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Connected Bank Accounts"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl border border-green-400/20",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("i",{className:"fas fa-university text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:i}),(0,a.jsx)("p",{className:"text-green-400 text-sm",children:"Primary Account • Verified"}),(0,a.jsxs)("p",{className:"text-gray-400 text-sm",children:["Balance: $",r.toLocaleString()]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{className:"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-edit"})}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-red-400 transition-colors p-2 rounded-lg hover:bg-gray-700/50",children:(0,a.jsx)("i",{className:"fas fa-trash"})})]})]}),(0,a.jsxs)("button",{className:"w-full p-4 border-2 border-dashed border-gray-600 rounded-xl text-gray-400 hover:text-white hover:border-yellow-400 transition-all duration-300",children:[(0,a.jsx)("i",{className:"fas fa-plus mr-2"}),"Add New Bank Account"]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Payment Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Instant Transfers"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable instant money transfers (small fee may apply)"})]}),(0,a.jsx)("button",{onClick:()=>b({...h,instantTransfers:!h.instantTransfers}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${h.instantTransfers?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${h.instantTransfers?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Payment Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Get notified for all payment activities"})]}),(0,a.jsx)("button",{onClick:()=>b({...h,paymentNotifications:!h.paymentNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${h.paymentNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${h.paymentNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Require PIN for Payments"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Require PIN verification for all payments"})]}),(0,a.jsx)("button",{onClick:()=>b({...h,requirePinForPayments:!h.requirePinForPayments}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${h.requirePinForPayments?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${h.requirePinForPayments?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint/face ID for payments"})]}),(0,a.jsx)("button",{onClick:()=>b({...h,biometricAuth:!h.biometricAuth}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${h.biometricAuth?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${h.biometricAuth?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Transaction Limits"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Daily Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:h.transactionLimits.daily,onChange:e=>b({...h,transactionLimits:{...h.transactionLimits,daily:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Monthly Limit"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:h.transactionLimits.monthly,onChange:e=>b({...h,transactionLimits:{...h.transactionLimits,monthly:parseInt(e.target.value)}}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Savings & Goals"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Auto-Save"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically save spare change from transactions"})]}),(0,a.jsx)("button",{onClick:()=>b({...h,autoSave:!h.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${h.autoSave?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${h.autoSave?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Savings Goal"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-yellow-400",children:"$"}),(0,a.jsx)("input",{type:"number",value:h.savingsGoal,onChange:e=>b({...h,savingsGoal:parseInt(e.target.value)}),className:"w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none",placeholder:"1000"})]})]})]})]})]}),"security"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Security & Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Protect your account with advanced security features"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Authentication"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,twoFactorAuth:!f.twoFactorAuth}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.twoFactorAuth?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.twoFactorAuth?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Biometric Login"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Use fingerprint or face ID to log in"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,biometricLogin:!f.biometricLogin}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.biometricLogin?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.biometricLogin?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Device Verification"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Verify new devices before allowing access"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,deviceVerification:!f.deviceVerification}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.deviceVerification?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.deviceVerification?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsxs)("select",{value:f.sessionTimeout,onChange:e=>g({...f,sessionTimeout:parseInt(e.target.value)}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:15,children:"15 minutes"}),(0,a.jsx)("option",{value:30,children:"30 minutes"}),(0,a.jsx)("option",{value:60,children:"1 hour"}),(0,a.jsx)("option",{value:120,children:"2 hours"}),(0,a.jsx)("option",{value:0,children:"Never"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Encryption & Data"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Encryption Level"}),(0,a.jsxs)("select",{value:f.encryptionLevel,onChange:e=>g({...f,encryptionLevel:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"standard",children:"Standard (128-bit)"}),(0,a.jsx)("option",{value:"enhanced",children:"Enhanced (256-bit)"}),(0,a.jsx)("option",{value:"military",children:"Military Grade (AES-256)"})]}),(0,a.jsxs)("p",{className:"text-green-400 text-sm mt-2 flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"Current: Military Grade AES-256 Encryption"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Backup Messages"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Securely backup your messages to the cloud"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,backupMessages:!f.backupMessages}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.backupMessages?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.backupMessages?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Screen Lock"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Lock app when switching to other apps"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,screenLock:!f.screenLock}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.screenLock?"bg-green-500":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.screenLock?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Incognito Mode"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Hide message previews and disable screenshots"})]}),(0,a.jsx)("button",{onClick:()=>g({...f,incognitoMode:!f.incognitoMode}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${f.incognitoMode?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${f.incognitoMode?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Security Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-key text-blue-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Change Password"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-mobile-alt text-green-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Manage Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-history text-yellow-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Login History"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-sign-out-alt text-red-400 mr-3"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Sign Out All Devices"})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]})]}),"notifications"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Notifications"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control how and when you receive notifications"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"General"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive notifications on this device"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,pushNotifications:!p.pushNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.pushNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.pushNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Receive important updates via email"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,emailNotifications:!p.emailNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.emailNotifications?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.emailNotifications?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Sound Alerts"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Play sound for notifications"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,soundAlerts:!p.soundAlerts}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.soundAlerts?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.soundAlerts?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Show Previews"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Show message content in notifications"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,showPreviews:!p.showPreviews}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.showPreviews?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.showPreviews?"translate-x-6":"translate-x-1"}`})})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Quiet Hours"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Enable Quiet Hours"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Mute notifications during specified hours"})]}),(0,a.jsx)("button",{onClick:()=>j({...p,quietHours:{...p.quietHours,enabled:!p.quietHours.enabled}}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${p.quietHours.enabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${p.quietHours.enabled?"translate-x-6":"translate-x-1"}`})})]}),p.quietHours.enabled&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Start Time"}),(0,a.jsx)("input",{type:"time",value:p.quietHours.start,onChange:e=>j({...p,quietHours:{...p.quietHours,start:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"End Time"}),(0,a.jsx)("input",{type:"time",value:p.quietHours.end,onChange:e=>j({...p,quietHours:{...p.quietHours,end:e.target.value}}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none"})]})]})]})]})]}),"privacy"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Privacy"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Control who can see your information and activity"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Visibility"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Profile Visibility"}),(0,a.jsxs)("select",{value:y.profileVisibility,onChange:e=>v({...y,profileVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Last Seen"}),(0,a.jsxs)("select",{value:y.lastSeenVisibility,onChange:e=>v({...y,lastSeenVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Status Visibility"}),(0,a.jsxs)("select",{value:y.statusVisibility,onChange:e=>v({...y,statusVisibility:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"everyone",children:"Everyone"}),(0,a.jsx)("option",{value:"contacts",children:"My Contacts"}),(0,a.jsx)("option",{value:"nobody",children:"Nobody"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data & Analytics"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Data Collection"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow collection of usage data for improvements"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,dataCollection:!y.dataCollection}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.dataCollection?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.dataCollection?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Analytics Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Share anonymous analytics data"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,analyticsSharing:!y.analyticsSharing}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.analyticsSharing?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.analyticsSharing?"translate-x-6":"translate-x-1"}`})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Location Sharing"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Allow location sharing in messages"})]}),(0,a.jsx)("button",{onClick:()=>v({...y,locationSharing:!y.locationSharing}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${y.locationSharing?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${y.locationSharing?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"appearance"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Appearance"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Customize the look and feel of BoGuani"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"dark"}),className:`p-4 rounded-xl border-2 transition-all ${"dark"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Dark"})]}),(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"light"}),className:`p-4 rounded-xl border-2 transition-all ${"light"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Light"})]}),(0,a.jsxs)("button",{onClick:()=>k({...w,theme:"auto"}),className:`p-4 rounded-xl border-2 transition-all ${"auto"===w.theme?"border-yellow-400 bg-yellow-400/10":"border-gray-600 hover:border-gray-500"}`,children:[(0,a.jsx)("div",{className:"w-full h-16 bg-gradient-to-r from-gray-900 via-gray-500 to-gray-100 rounded-lg mb-2"}),(0,a.jsx)("p",{className:"text-white text-sm font-medium",children:"Auto"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Customization"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Accent Color"}),(0,a.jsx)("div",{className:"grid grid-cols-6 gap-3",children:["gold","blue","green","purple","red","orange"].map(e=>(0,a.jsx)("button",{onClick:()=>k({...w,accentColor:e}),className:`w-12 h-12 rounded-full border-2 transition-all ${w.accentColor===e?"border-white scale-110":"border-gray-600"} ${"gold"===e?"bg-yellow-400":"blue"===e?"bg-blue-500":"green"===e?"bg-green-500":"purple"===e?"bg-purple-500":"red"===e?"bg-red-500":"bg-orange-500"}`},e))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Font Size"}),(0,a.jsxs)("select",{value:w.fontSize,onChange:e=>k({...w,fontSize:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"}),(0,a.jsx)("option",{value:"extra-large",children:"Extra Large"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Message Bubble Style"}),(0,a.jsxs)("select",{value:w.bubbleStyle,onChange:e=>k({...w,bubbleStyle:e.target.value}),className:"w-full px-4 py-3 chat-input rounded-xl text-white focus:outline-none",children:[(0,a.jsx)("option",{value:"transparent",children:"Transparent"}),(0,a.jsx)("option",{value:"solid",children:"Solid"}),(0,a.jsx)("option",{value:"gradient",children:"Gradient"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"text-white font-medium",children:"Animations"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Enable smooth animations and transitions"})]}),(0,a.jsx)("button",{onClick:()=>k({...w,animationsEnabled:!w.animationsEnabled}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${w.animationsEnabled?"bg-yellow-400":"bg-gray-600"}`,children:(0,a.jsx)("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${w.animationsEnabled?"translate-x-6":"translate-x-1"}`})})]})]})]})]}),"advanced"===l&&(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Advanced"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Advanced settings and developer options"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Data Management"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-600/20 to-blue-500/20 rounded-xl border border-blue-400/30 hover:border-blue-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-download text-blue-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Export Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Download your messages and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-green-600/20 to-green-500/20 rounded-xl border border-green-400/30 hover:border-green-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-upload text-green-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Import Data"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Import messages from other apps"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-yellow-600/20 to-yellow-500/20 rounded-xl border border-yellow-400/30 hover:border-yellow-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-broom text-yellow-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Clear Cache"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Free up storage space"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Developer Options"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-purple-600/20 to-purple-500/20 rounded-xl border border-purple-400/30 hover:border-purple-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-code text-purple-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"API Settings"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Configure API endpoints and keys"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-indigo-600/20 to-indigo-500/20 rounded-xl border border-indigo-400/30 hover:border-indigo-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-bug text-indigo-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Debug Logs"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"View and export debug information"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-teal-600/20 to-teal-500/20 rounded-xl border border-teal-400/30 hover:border-teal-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-flask text-teal-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Beta Features"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Enable experimental features"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"Account Actions"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-orange-600/20 to-orange-500/20 rounded-xl border border-orange-400/30 hover:border-orange-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-user-times text-orange-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Deactivate Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Temporarily disable your account"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-between p-4 bg-gradient-to-r from-red-600/20 to-red-500/20 rounded-xl border border-red-400/30 hover:border-red-400/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-trash text-red-400 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("span",{className:"text-white font-medium block",children:"Delete Account"}),(0,a.jsx)("span",{className:"text-gray-400 text-sm",children:"Permanently delete your account and data"})]})]}),(0,a.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl p-6 border border-gray-600/30",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-white mb-6",children:"App Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Version"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"1.0.0"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Build"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"2024.01.15"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Platform"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Web"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Encryption"}),(0,a.jsxs)("span",{className:"text-green-400 font-medium flex items-center",children:[(0,a.jsx)("i",{className:"fas fa-shield-alt mr-2"}),"AES-256 Active"]})]})]})]})]})]})})]})})})}):null}let C=[{id:"1",name:"John Doe",username:"johndoe",phone:"+1234567890",isOnline:!0,lastSeen:"2 min ago",avatar:"JD",status:"Available for chat"},{id:"2",name:"Jane Smith",username:"janesmith",phone:"+1234567891",isOnline:!1,lastSeen:"1 hour ago",avatar:"JS",status:"Busy with work"},{id:"3",name:"Mike Johnson",username:"mikej",phone:"+1234567892",isOnline:!0,lastSeen:"now",avatar:"MJ",status:"Ready to receive payments"},{id:"4",name:"Sarah Wilson",username:"sarahw",phone:"+1234567893",isOnline:!1,lastSeen:"30 min ago",avatar:"SW",status:"At the gym"}];function P(){let e=(0,l.useRouter)(),[t,s]=(0,n.useState)(null),[r,c]=(0,n.useState)(null),[d,x]=(0,n.useState)([]),[m,u]=(0,n.useState)([]),[h,b]=(0,n.useState)(""),[f,g]=(0,n.useState)(!1),[p,j]=(0,n.useState)(""),[y,v]=(0,n.useState)(!0),[w,C]=(0,n.useState)(""),[P,E]=(0,n.useState)(!1),[A,D]=(0,n.useState)(!1),[$,_]=(0,n.useState)({isActive:!1,type:"voice"}),[M,z]=(0,n.useState)(!1),[I,R]=(0,n.useState)(null),[O]=(0,n.useState)(2847.32),[F]=(0,n.useState)("Chase ****1234"),[L,V]=(0,n.useState)(!1),T=(0,n.useRef)(null),B=(0,n.useRef)(null),q=(0,n.useRef)(null),H=e=>{if(e?.preventDefault(),!h.trim()||!r||!t)return;let s={id:Date.now().toString(),senderId:t.id,text:h,timestamp:new Date,status:"sending",type:"text"},a=[...d,s];x(a),b(""),setTimeout(()=>{x(e=>e.map(e=>e.id===s.id?{...e,status:"delivered"}:e))},1e3);let i=m.map(e=>e.id===r.id?{...e,lastMessage:h,timestamp:new Date,unreadCount:0,messages:[...a]}:e);u(i),c(i.find(e=>e.id===r.id)||null)};if(y||!t)return(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"Loading chat..."})]})});let G=e=>e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),U=e=>e.toLocaleDateString([],{weekday:"long",year:"numeric",month:"long",day:"numeric"}),W=e=>{switch(e){case"sending":return(0,a.jsx)("span",{className:"text-gray-400",children:"\uD83D\uDD52"});case"sent":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓"});case"delivered":return(0,a.jsx)("span",{className:"text-gray-400",children:"✓✓"});case"read":return(0,a.jsx)("span",{className:"text-blue-500",children:"✓✓"});default:return null}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i(),{id:"7110b046d83e55cc",children:"@import url(\"https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap\");body{background:-webkit-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-moz-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:-o-linear-gradient(315deg,#1E1E24 0%,#2D1B4E 100%);background:linear-gradient(135deg,#1E1E24 0%,#2D1B4E 100%);font-family:\"Montserrat\",sans-serif}.gold-gradient{background:-webkit-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-moz-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:-o-linear-gradient(left,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);background:linear-gradient(90deg,#D4AF37 0%,#F2D675 50%,#D4AF37 100%);-webkit-background-clip:text;background-clip:text;color:transparent}.gold-border{border:2px solid transparent;background:-webkit-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-webkit-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-moz-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-moz-linear-gradient(left,#D4AF37,#F2D675)border-box;background:-o-linear-gradient(#2D1B4E,#2D1B4E)padding-box,-o-linear-gradient(left,#D4AF37,#F2D675)border-box;background:linear-gradient(#2D1B4E,#2D1B4E)padding-box,linear-gradient(90deg,#D4AF37,#F2D675)border-box}.hero-pattern{background-image:url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")}.message-bubble{-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(212,175,55,.2)}.message-bubble:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 8px 25px rgba(0,0,0,.3);-moz-box-shadow:0 8px 25px rgba(0,0,0,.3);box-shadow:0 8px 25px rgba(0,0,0,.3);border-color:rgba(212,175,55,.4)}.message-bubble-sent{background:rgba(212,175,55,.15);border-color:rgba(212,175,55,.3)}.message-bubble-received{background:rgba(45,27,78,.4);border-color:rgba(107,114,128,.3)}.chat-input{background:rgba(17,24,39,.8);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(75,85,99,.5);-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.chat-input:focus{border-color:#D4AF37;-webkit-box-shadow:0 0 0 3px rgba(212,175,55,.1);-moz-box-shadow:0 0 0 3px rgba(212,175,55,.1);box-shadow:0 0 0 3px rgba(212,175,55,.1);background:rgba(17,24,39,.9)}.sidebar-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:-o-linear-gradient(top,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);background:linear-gradient(180deg,rgba(17,24,39,.95)0%,rgba(31,41,55,.95)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border-right:1px solid rgba(75,85,99,.3)}.chat-panel{background:-webkit-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-moz-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:-o-linear-gradient(top,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);background:linear-gradient(180deg,rgba(17,24,39,.8)0%,rgba(31,41,55,.8)100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}.encryption-indicator{-webkit-animation:pulse 2s infinite;-moz-animation:pulse 2s infinite;-o-animation:pulse 2s infinite;animation:pulse 2s infinite}@-webkit-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-moz-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@-o-keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}@keyframes pulse{0%,100%{opacity:1}50%{opacity:.7}}.bank-balance{background:-webkit-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-moz-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:-o-linear-gradient(315deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);background:linear-gradient(135deg,rgba(34,197,94,.1)0%,rgba(21,128,61,.1)100%);border:1px solid rgba(34,197,94,.3)}.professional-shadow{-webkit-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);-moz-box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2);box-shadow:0 10px 40px rgba(0,0,0,.3),0 4px 12px rgba(0,0,0,.2)}"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 text-white hero-pattern",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc w-full md:w-1/3 sidebar-panel flex flex-col professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex justify-between items-center sticky top-0 z-10 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg professional-shadow",children:t.name.split(" ").map(e=>e[0]).join("")}),(0,a.jsx)("div",{title:"End-to-End Encrypted",className:"jsx-7110b046d83e55cc absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 encryption-indicator"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:t.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:["@",t.username]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"E2E Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>z(!0),title:"Settings",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-lg"})}),(0,a.jsx)("button",{onClick:()=>{s(null),u([]),x([]),c(null),e.push("/auth")},title:"Logout",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-red-400 p-2 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-sign-out-alt text-lg"})})]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search conversations...",value:w,onChange:e=>C(e.target.value),className:"jsx-7110b046d83e55cc w-full px-4 py-3 pl-10 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none"}),(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"}),w&&(0,a.jsx)("button",{onClick:()=>C(""),className:"jsx-7110b046d83e55cc absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-transparent",children:m.map(e=>(0,a.jsx)(o.P.div,{className:`p-4 border-b border-gray-700/30 cursor-pointer transition-all duration-300 hover:bg-gray-800/50 ${r?.id===e.id?"bg-gray-800/70 border-l-4 border-l-yellow-400":""}`,onClick:()=>{c(e),x(e.messages)},whileHover:{x:6},transition:{duration:.3,ease:"easeOut"},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:e.participant.avatar||e.participant.name.split(" ").map(e=>e[0]).join("")}),e.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"}),e.unreadCount>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute -top-1 -right-1 w-5 h-5 bg-yellow-400 text-gray-900 rounded-full flex items-center justify-center text-xs font-bold",children:e.unreadCount})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3 flex-1",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:e.participant.name}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:G(e.timestamp)})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-400 truncate",children:e.lastMessage}),e.participant.status&&(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 italic mt-1",children:e.participant.status})]})]})},e.id))}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/95 to-gray-800/95 backdrop-blur-md",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc bank-balance rounded-xl p-4 backdrop-blur-lg",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm font-medium text-gray-300",children:F})]}),(0,a.jsx)("button",{onClick:()=>z(!0),title:"Manage Bank Account",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-cog text-sm"})})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:"Available Balance"}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xl font-bold text-green-400",children:["$",O.toLocaleString("en-US",{minimumFractionDigits:2})]})]}),(0,a.jsxs)("button",{onClick:()=>g(!0),className:"jsx-7110b046d83e55cc bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 px-4 py-2 rounded-lg font-semibold hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send"]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mt-2 flex items-center text-xs text-gray-500",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt mr-1 text-green-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Secured by Plaid & 256-bit AES encryption"})]})]})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex flex-col chat-panel",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-b border-gray-600/30 flex items-center justify-between bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md sticky top-0 z-10 professional-shadow",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-white font-semibold professional-shadow",children:r.participant.avatar||r.participant.name.split(" ").map(e=>e[0]).join("")}),r.participant.isOnline&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-0 right-0 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-900"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc ml-3",children:[(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc font-semibold text-white",children:r.participant.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400",children:r.participant.isOnline?r.participant.isTyping?"Typing...":"Online":`Last seen ${r.participant.lastSeen}`}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-1"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"Encrypted"})]})]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>_({isActive:!0,type:"voice",participant:r.participant}),title:"Voice Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-green-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone text-lg"})}),(0,a.jsx)("button",{onClick:()=>{_({isActive:!0,type:"video",participant:r.participant}),V(!0)},title:"Video Call",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-blue-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-video text-lg"})}),(0,a.jsx)("button",{onClick:()=>z(!0),title:"User Info",className:"jsx-7110b046d83e55cc text-gray-400 hover:text-yellow-400 p-3 rounded-full hover:bg-gray-700/50 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-info-circle text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 overflow-y-auto p-4 space-y-4",children:[d.length>0&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center mb-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:U(d[0].timestamp)})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc space-y-4",children:d.map((e,s)=>{let r=e.senderId===t.id,i=0===s||new Date(e.timestamp).toDateString()!==new Date(d[s-1].timestamp).toDateString();return(0,a.jsxs)(o.P.div,{className:"space-y-1",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[i&&0!==s&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc text-center my-4",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 bg-purple-800/50 px-3 py-1 rounded-full",children:U(e.timestamp)})}),(0,a.jsx)("div",{className:`jsx-7110b046d83e55cc flex ${r?"justify-end":"justify-start"}`,children:(0,a.jsxs)("div",{className:`jsx-7110b046d83e55cc max-w-xs lg:max-w-md px-4 py-3 rounded-2xl message-bubble ${r?"message-bubble-sent text-white rounded-tr-none":"message-bubble-received text-white rounded-tl-none"}`,children:["payment"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mb-2 p-3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl border border-green-400/30 backdrop-blur-lg",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-white text-sm"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm font-semibold text-green-400",children:["Payment Sent: $",e.amount?.toFixed(2)," ",e.currency]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 mt-1",children:"Secured by Plaid • Instant Transfer"})]})]}),"image"===e.type&&e.imageUrl&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-2",children:(0,a.jsx)(k(),{src:e.imageUrl,alt:"Shared image",width:300,height:200,className:"rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity",onClick:()=>window.open(e.imageUrl,"_blank")})}),"voice"===e.type&&(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center space-x-3 p-3 bg-gradient-to-r from-gray-700/40 to-gray-800/40 rounded-xl border border-gray-600/30 backdrop-blur-lg",children:[(0,a.jsx)("button",{className:"jsx-7110b046d83e55cc w-10 h-10 bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 rounded-full flex items-center justify-center hover:from-yellow-300 hover:to-yellow-100 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-play text-sm"})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 h-2 bg-gray-600/50 rounded-full",children:(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc h-full w-1/3 bg-gradient-to-r from-yellow-400 to-yellow-200 rounded-full"})}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-gray-400 font-medium",children:e.duration})]}),("text"===e.type||"payment"===e.type)&&(0,a.jsx)("p",{className:`jsx-7110b046d83e55cc ${"payment"===e.type?"text-sm":""} leading-relaxed`,children:e.text}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-end mt-2 space-x-2",children:[(0,a.jsx)("span",{className:`jsx-7110b046d83e55cc text-xs ${r?"text-gray-400":"text-gray-500"} font-medium`,children:G(e.timestamp)}),r&&(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc ml-1",children:W(e.status)}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock text-xs"})})]})]})})]},e.id)})}),(0,a.jsx)("div",{ref:T,className:"jsx-7110b046d83e55cc"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-4 border-t border-gray-600/30 bg-gradient-to-r from-gray-900/90 to-gray-800/90 backdrop-blur-md professional-shadow",children:[I&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-3 p-3 bg-purple-800/50 rounded-lg border-l-4 border-yellow-400",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-yellow-400 font-semibold",children:["Replying to ",I.senderId===t.id?"yourself":r.participant.name]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-sm text-gray-300 truncate",children:I.text})]}),(0,a.jsx)("button",{onClick:()=>R(null),className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times"})})]})}),(0,a.jsx)(N,{children:A&&(0,a.jsxs)(o.P.div,{className:"mb-3 flex space-x-2",initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},children:[(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-image text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Photo"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-file text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Document"})]}),(0,a.jsxs)("button",{className:"jsx-7110b046d83e55cc flex items-center space-x-2 bg-purple-700 hover:bg-purple-600 px-3 py-2 rounded-lg transition-colors",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-map-marker-alt text-yellow-400"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-white",children:"Location"})]})]})}),(0,a.jsxs)("form",{onSubmit:H,className:"jsx-7110b046d83e55cc flex items-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>D(!A),className:"jsx-7110b046d83e55cc p-3 text-gray-400 hover:text-yellow-400 hover:bg-gray-700/50 rounded-full transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-plus text-lg"})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex-1 relative",children:[(0,a.jsx)("textarea",{value:h,onChange:e=>b(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),H(e))},placeholder:"Type a message...",rows:1,style:{minHeight:"48px",maxHeight:"120px"},className:"jsx-7110b046d83e55cc w-full py-3 px-4 pr-20 chat-input rounded-2xl text-white placeholder-gray-500 focus:outline-none resize-none"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc absolute right-3 bottom-3 flex items-center space-x-2",children:[(0,a.jsx)("button",{type:"button",onClick:()=>E(!P),className:"jsx-7110b046d83e55cc text-gray-500 hover:text-yellow-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-smile text-lg"})}),(0,a.jsx)("button",{type:"button",title:"Voice Message",className:"jsx-7110b046d83e55cc text-gray-500 hover:text-blue-400 transition-all duration-300",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-microphone text-lg"})})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>g(!0),title:"Send Money via Plaid",className:"jsx-7110b046d83e55cc p-3 bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white rounded-full transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-dollar-sign text-lg"})}),(0,a.jsx)("button",{type:"submit",disabled:!h.trim(),className:`jsx-7110b046d83e55cc p-3 rounded-full transition-all duration-300 professional-shadow ${h.trim()?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane text-lg"})})]})]})]})]}):(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc flex-1 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center p-8",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-20 h-20 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-comment-dollar text-3xl text-purple-900"})}),(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-medium text-white mb-2",children:"Welcome to BoGuani"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-300",children:"Select a conversation to start messaging"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-yellow-400 text-sm mt-2 italic",children:'"Speak Gold. Share Value."'})]})})}),(0,a.jsx)(N,{children:$.isActive&&(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black z-50 flex items-center justify-center backdrop-blur-lg",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc text-center text-white",children:["video"===$.type&&L&&(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-8",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative w-80 h-60 bg-gray-800 rounded-2xl overflow-hidden professional-shadow",children:[(0,a.jsx)("video",{ref:q,autoPlay:!0,playsInline:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute bottom-4 right-4 w-20 h-16 bg-gray-700 rounded-lg overflow-hidden",children:(0,a.jsx)("video",{ref:B,autoPlay:!0,playsInline:!0,muted:!0,className:"jsx-7110b046d83e55cc w-full h-full object-cover"})})]})}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc w-32 h-32 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-6 professional-shadow",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-3xl font-bold",children:$.participant?.avatar||$.participant?.name.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("h2",{className:"jsx-7110b046d83e55cc text-3xl font-semibold mb-2",children:$.participant?.name}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-center mb-2",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2"}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-green-400 text-sm",children:"End-to-End Encrypted"})]}),(0,a.jsx)("p",{className:"jsx-7110b046d83e55cc text-gray-400 mb-8 text-lg",children:"video"===$.type?"Video calling...":"Voice calling..."}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-center space-x-6",children:[(0,a.jsx)("button",{onClick:()=>_(e=>({...e,isMuted:!e.isMuted})),className:`jsx-7110b046d83e55cc w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${$.isMuted?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"}`,children:(0,a.jsx)("i",{className:`jsx-7110b046d83e55cc fas ${$.isMuted?"fa-microphone-slash":"fa-microphone"} text-xl`})}),"video"===$.type&&(0,a.jsx)("button",{onClick:()=>_(e=>({...e,isVideoOff:!e.isVideoOff})),className:`jsx-7110b046d83e55cc w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 professional-shadow ${$.isVideoOff?"bg-red-600 hover:bg-red-700":"bg-gray-700 hover:bg-gray-600"}`,children:(0,a.jsx)("i",{className:`jsx-7110b046d83e55cc fas ${$.isVideoOff?"fa-video-slash":"fa-video"} text-xl`})}),(0,a.jsx)("button",{onClick:()=>{_({isActive:!1,type:"voice"}),V(!1)},className:"jsx-7110b046d83e55cc w-16 h-16 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-all duration-300 professional-shadow",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-phone-slash text-xl"})})]})]})})})]}),(0,a.jsx)(S,{isOpen:M,onClose:()=>z(!1),currentUser:t,bankBalance:O,connectedBank:F}),f&&(0,a.jsx)(o.P.div,{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsx)(o.P.div,{className:"bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-lg rounded-2xl shadow-2xl w-full max-w-md border border-gray-600/50 professional-shadow",initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc p-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc",children:[(0,a.jsx)("h3",{className:"jsx-7110b046d83e55cc text-xl font-semibold text-white gold-gradient",children:"Send Payment"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center mt-1",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-shield-alt text-green-400 mr-2 text-sm"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:"Secured by Plaid"})]})]}),(0,a.jsx)("button",{onClick:()=>{g(!1),j("")},className:"jsx-7110b046d83e55cc text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-times text-xl"})})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mb-4 p-3 bg-gradient-to-r from-gray-800/50 to-gray-700/50 rounded-xl border border-gray-600/30",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-university text-green-400 mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-sm text-gray-300",children:F})]}),(0,a.jsxs)("span",{className:"jsx-7110b046d83e55cc text-sm text-green-400 font-semibold",children:["$",O.toLocaleString()]})]})}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc mb-6",children:[(0,a.jsx)("label",{htmlFor:"amount",className:"jsx-7110b046d83e55cc block text-sm font-medium text-gray-300 mb-2",children:"Amount (USD)"}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc relative",children:[(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc text-yellow-400 text-lg font-semibold",children:"$"})}),(0,a.jsx)("input",{type:"number",name:"amount",id:"amount",value:p,onChange:e=>j(e.target.value),placeholder:"0.00",step:"0.01",min:"0.01",className:"jsx-7110b046d83e55cc w-full pl-8 pr-4 py-3 chat-input rounded-xl text-white placeholder-gray-500 focus:outline-none text-lg"})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-between items-center mt-2",children:[(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-gray-500",children:["Sending to: ",r?.participant.name]}),(0,a.jsxs)("p",{className:"jsx-7110b046d83e55cc text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-bolt mr-1"}),"Instant Transfer"]})]})]}),(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{g(!1),j("")},className:"jsx-7110b046d83e55cc px-6 py-3 bg-gray-700 text-white rounded-xl hover:bg-gray-600 transition-all duration-300",children:"Cancel"}),(0,a.jsxs)("button",{onClick:()=>{if(!p||!r||!t)return;let e=[...d,{id:Date.now().toString(),senderId:t.id,text:`Payment of $${p}`,timestamp:new Date,status:"sent",type:"payment",amount:parseFloat(p),currency:"USD"}];x(e),g(!1),j("");let s=m.map(t=>t.id===r.id?{...t,lastMessage:`Payment of $${p}`,timestamp:new Date,unreadCount:0,messages:e}:t);u(s),c(s.find(e=>e.id===r.id)||null)},disabled:!p||0>=parseFloat(p),className:`jsx-7110b046d83e55cc px-6 py-3 rounded-xl font-semibold transition-all duration-300 professional-shadow ${p&&parseFloat(p)>0?"bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-900 hover:from-yellow-300 hover:to-yellow-100":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-paper-plane mr-2"}),"Send $",p||"0.00"]})]}),(0,a.jsx)("div",{className:"jsx-7110b046d83e55cc mt-4 p-3 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg border border-green-400/20",children:(0,a.jsxs)("div",{className:"jsx-7110b046d83e55cc flex items-center text-xs text-green-400",children:[(0,a.jsx)("i",{className:"jsx-7110b046d83e55cc fas fa-lock mr-2"}),(0,a.jsx)("span",{className:"jsx-7110b046d83e55cc",children:"256-bit AES encryption • FDIC insured • Instant settlement"})]})})]})})})]})}Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),Date.now(),C[0],C[1],Date.now()},7755:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let a=s(3210),r=()=>{},i=()=>{};function n(e){var t;let{headManager:s,reduceComponentsToState:n}=e;function l(){if(s&&s.mountedInstances){let t=a.Children.toArray(Array.from(s.mountedInstances).filter(Boolean));s.updateHead(n(t,e))}}return null==s||null==(t=s.mountedInstances)||t.add(e.children),l(),r(()=>{var t;return null==s||null==(t=s.mountedInstances)||t.add(e.children),()=>{var t;null==s||null==(t=s.mountedInstances)||t.delete(e.children)}}),r(()=>(s&&(s._pendingUpdate=l),()=>{s&&(s._pendingUpdate=l)})),i(()=>(s&&s._pendingUpdate&&(s._pendingUpdate(),s._pendingUpdate=null),()=>{s&&s._pendingUpdate&&(s._pendingUpdate(),s._pendingUpdate=null)})),null}},7903:(e,t,s)=>{"use strict";e.exports=s(4041).vendored.contexts.ImageConfigContext},8973:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,s)=>{"use strict";e.exports=s(4041).vendored.contexts.RouterContext},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(e,t,s)=>{"use strict";e.exports=s(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")},9937:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(5239),r=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8973)),"/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Volumes/Apps/Websites/ChatPay/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Volumes/Apps/Websites/ChatPay/src/app/chat/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/chat/page",pathname:"/chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,938,711,1,279],()=>s(9937));module.exports=a})();