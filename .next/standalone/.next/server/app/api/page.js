(()=>{var e={};e.id=877,e.ids=[877],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},2903:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3992:(e,t,s)=>{Promise.resolve().then(s.bind(s,8481))},4395:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/ChatPay/src/app/api/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/ChatPay/src/app/api/page.tsx","default")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>n});var r=s(7413),a=s(1980),l=s.n(a);s(1135);let n={title:"BoGuani - Messenger of Value",description:"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",keywords:"messaging, payments, secure chat, money transfer, encrypted messaging, BoGuani",authors:[{name:"BoGuani Team"}],openGraph:{title:"BoGuani - Messenger of Value",description:"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",type:"website"}};function i({children:e}){return(0,r.jsxs)("html",{lang:"en",className:l().variable,children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"stylesheet",href:"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css",integrity:"sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==",crossOrigin:"anonymous",referrerPolicy:"no-referrer"}),(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),(0,r.jsx)("meta",{name:"theme-color",content:"#2D1B4E"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),(0,r.jsx)("body",{className:`${l().variable} antialiased`,children:e})]})}},5483:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5239),a=s(8088),l=s(8170),n=s.n(l),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["api",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4395)),"/Volumes/Apps/Websites/ChatPay/src/app/api/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"/Volumes/Apps/Websites/ChatPay/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Volumes/Apps/Websites/ChatPay/src/app/api/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/api/page",pathname:"/api",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5839:()=>{},6615:()=>{},8436:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},8481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(687),a=s(5814),l=s.n(a),n=s(6001);function i(){return(0,r.jsx)("div",{className:"min-h-screen text-white",style:{background:"linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)",fontFamily:"Montserrat, sans-serif"},children:(0,r.jsxs)("div",{className:"relative min-h-screen",style:{background:"linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)",backgroundImage:"radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)"},children:[(0,r.jsx)("nav",{className:"fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-4 flex justify-between items-center",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)(l(),{href:"/",className:"flex items-center",children:[(0,r.jsx)("div",{className:"text-yellow-500 text-3xl mr-3",children:(0,r.jsx)("i",{className:"fas fa-comment-dollar"})}),(0,r.jsx)("span",{className:"font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"BoGuani"})]})}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsx)(l(),{href:"/",className:"hover:text-yellow-400 transition-colors",children:"Home"}),(0,r.jsx)(l(),{href:"/guides",className:"hover:text-yellow-400 transition-colors",children:"Guides"}),(0,r.jsx)(l(),{href:"/support",className:"hover:text-yellow-400 transition-colors",children:"Support"}),(0,r.jsx)(l(),{href:"/auth",className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all",children:"Open Web App"})]})]})}),(0,r.jsx)("div",{className:"pt-24 pb-16 px-6",children:(0,r.jsxs)("div",{className:"container mx-auto max-w-6xl",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center",children:(0,r.jsx)("i",{className:"fas fa-code text-3xl text-purple-900"})}),(0,r.jsx)("h1",{className:"text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"API Documentation"}),(0,r.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Integrate BoGuani's secure messaging and payment features into your applications with our powerful API."})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Quick Start"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-purple-900 font-bold",children:"1"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Get API Key"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Register your application and get your API credentials"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-purple-900 font-bold",children:"2"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Authenticate"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Use OAuth 2.0 or API key authentication"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-purple-900 font-bold",children:"3"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2 text-yellow-200",children:"Start Building"}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:"Send messages and process payments programmatically"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:[(0,r.jsxs)(n.P.div,{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20",initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("i",{className:"fas fa-comments text-yellow-400 text-2xl mr-3"}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-yellow-200",children:"Messaging API"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4",children:"Send and receive encrypted messages programmatically"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-green-400",children:"POST /api/v1/messages"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Send a message"})]}),(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-blue-400",children:"GET /api/v1/messages"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Retrieve messages"})]}),(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-yellow-400",children:"PUT /api/v1/messages/:id"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Update message status"})]})]})]}),(0,r.jsxs)(n.P.div,{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("i",{className:"fas fa-credit-card text-yellow-400 text-2xl mr-3"}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-yellow-200",children:"Payments API"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4",children:"Process secure payments and transfers"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-green-400",children:"POST /api/v1/payments"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Create payment"})]}),(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-blue-400",children:"GET /api/v1/payments/:id"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Get payment status"})]}),(0,r.jsxs)("div",{className:"bg-purple-900/50 p-3 rounded-lg",children:[(0,r.jsx)("code",{className:"text-purple-400",children:"POST /api/v1/payments/:id/refund"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Refund payment"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Code Examples"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4 text-yellow-200",children:"JavaScript"}),(0,r.jsx)("div",{className:"bg-black p-4 rounded-lg overflow-x-auto",children:(0,r.jsx)("pre",{className:"text-sm",children:(0,r.jsx)("code",{className:"text-gray-300",children:`const boguani = require('boguani-sdk');

const client = new boguani.Client({
  apiKey: 'your-api-key'
});

// Send a message
const message = await client.messages.send({
  to: '+1234567890',
  text: 'Hello from BoGuani!'
});`})})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4 text-yellow-200",children:"Python"}),(0,r.jsx)("div",{className:"bg-black p-4 rounded-lg overflow-x-auto",children:(0,r.jsx)("pre",{className:"text-sm",children:(0,r.jsx)("code",{className:"text-gray-300",children:`import boguani

client = boguani.Client(
    api_key='your-api-key'
)

# Send a payment
payment = client.payments.create({
    'amount': 100.00,
    'currency': 'USD',
    'recipient': '+1234567890'
})`})})})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"SDKs & Libraries"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[{name:"JavaScript",icon:"fab fa-js-square",install:"npm install boguani-sdk"},{name:"Python",icon:"fab fa-python",install:"pip install boguani"},{name:"PHP",icon:"fab fa-php",install:"composer require boguani/sdk"},{name:"Java",icon:"fab fa-java",install:"Maven & Gradle"}].map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:`${e.icon} text-4xl text-yellow-400 mb-3`}),(0,r.jsx)("h3",{className:"font-semibold text-yellow-200",children:e.name}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.install})]},t))})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent",children:"Ready to Build?"}),(0,r.jsx)("p",{className:"text-gray-300 mb-6",children:"Get your API credentials and start integrating BoGuani today."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(l(),{href:"/auth",className:"bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all",children:[(0,r.jsx)("i",{className:"fas fa-key mr-2"}),"Get API Key"]}),(0,r.jsxs)(l(),{href:"/guides",className:"border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all",children:[(0,r.jsx)("i",{className:"fas fa-book mr-2"}),"View Guides"]})]})]})})]})})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9144:(e,t,s)=>{Promise.resolve().then(s.bind(s,4395))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,938,711,814,1],()=>s(5483));module.exports=r})();