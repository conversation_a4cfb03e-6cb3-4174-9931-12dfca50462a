<!DOCTYPE html><html lang="en" class="__variable_b5b07c"><head><meta charSet="utf-8"/><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/4f05ba3a6752a328-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/b838bbb2bb93f6bc.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-165557b0f5d7fa83.js"/><script src="/_next/static/chunks/4bd1b696-1962bfe149af46cd.js" async=""></script><script src="/_next/static/chunks/684-11131023a922e922.js" async=""></script><script src="/_next/static/chunks/main-app-4d7ee7ae65394df9.js" async=""></script><script src="/_next/static/chunks/874-6052627df6fde20c.js" async=""></script><script src="/_next/static/chunks/408-749237e46e8c0cbf.js" async=""></script><script src="/_next/static/chunks/app/api/page-b9c411e3eb2a2270.js" async=""></script><meta name="next-size-adjust" content=""/><meta name="theme-color" content="#2D1B4E"/><link rel="preconnect" href="https://fonts.googleapis.com"/><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous"/><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerPolicy="no-referrer"/><title>BoGuani - Messenger of Value</title><meta name="description" content="Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers."/><meta name="author" content="BoGuani Team"/><meta name="keywords" content="messaging, payments, secure chat, money transfer, encrypted messaging, BoGuani"/><meta property="og:title" content="BoGuani - Messenger of Value"/><meta property="og:description" content="Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers."/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary"/><meta name="twitter:title" content="BoGuani - Messenger of Value"/><meta name="twitter:description" content="Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_b5b07c antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen text-white" style="background:linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);font-family:Montserrat, sans-serif"><div class="relative min-h-screen" style="background:linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%);background-image:radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)"><nav class="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20"><div class="container mx-auto px-6 py-4 flex justify-between items-center"><div class="flex items-center"><a class="flex items-center" href="/"><div class="text-yellow-500 text-3xl mr-3"><i class="fas fa-comment-dollar"></i></div><span class="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">BoGuani</span></a></div><div class="flex space-x-6"><a class="hover:text-yellow-400 transition-colors" href="/">Home</a><a class="hover:text-yellow-400 transition-colors" href="/guides">Guides</a><a class="hover:text-yellow-400 transition-colors" href="/support">Support</a><a class="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all" href="/auth">Open Web App</a></div></div></nav><div class="pt-24 pb-16 px-6"><div class="container mx-auto max-w-6xl"><div class="text-center mb-16"><div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center"><i class="fas fa-code text-3xl text-purple-900"></i></div><h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">API Documentation</h1><p class="text-xl text-gray-300 max-w-3xl mx-auto">Integrate BoGuani&#x27;s secure messaging and payment features into your applications with our powerful API.</p></div><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12"><h2 class="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Quick Start</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-6"><div class="text-center"><div class="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center"><span class="text-purple-900 font-bold">1</span></div><h3 class="text-lg font-semibold mb-2 text-yellow-200">Get API Key</h3><p class="text-gray-300 text-sm">Register your application and get your API credentials</p></div><div class="text-center"><div class="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center"><span class="text-purple-900 font-bold">2</span></div><h3 class="text-lg font-semibold mb-2 text-yellow-200">Authenticate</h3><p class="text-gray-300 text-sm">Use OAuth 2.0 or API key authentication</p></div><div class="text-center"><div class="w-12 h-12 mx-auto mb-4 bg-yellow-400 rounded-full flex items-center justify-center"><span class="text-purple-900 font-bold">3</span></div><h3 class="text-lg font-semibold mb-2 text-yellow-200">Start Building</h3><p class="text-gray-300 text-sm">Send messages and process payments programmatically</p></div></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20" style="opacity:0;transform:translateX(-50px)"><div class="flex items-center mb-4"><i class="fas fa-comments text-yellow-400 text-2xl mr-3"></i><h3 class="text-2xl font-bold text-yellow-200">Messaging API</h3></div><p class="text-gray-300 mb-4">Send and receive encrypted messages programmatically</p><div class="space-y-3"><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-green-400">POST /api/v1/messages</code><p class="text-gray-400 text-sm mt-1">Send a message</p></div><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-blue-400">GET /api/v1/messages</code><p class="text-gray-400 text-sm mt-1">Retrieve messages</p></div><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-yellow-400">PUT /api/v1/messages/:id</code><p class="text-gray-400 text-sm mt-1">Update message status</p></div></div></div><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20" style="opacity:0;transform:translateX(50px)"><div class="flex items-center mb-4"><i class="fas fa-credit-card text-yellow-400 text-2xl mr-3"></i><h3 class="text-2xl font-bold text-yellow-200">Payments API</h3></div><p class="text-gray-300 mb-4">Process secure payments and transfers</p><div class="space-y-3"><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-green-400">POST /api/v1/payments</code><p class="text-gray-400 text-sm mt-1">Create payment</p></div><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-blue-400">GET /api/v1/payments/:id</code><p class="text-gray-400 text-sm mt-1">Get payment status</p></div><div class="bg-purple-900/50 p-3 rounded-lg"><code class="text-purple-400">POST /api/v1/payments/:id/refund</code><p class="text-gray-400 text-sm mt-1">Refund payment</p></div></div></div></div><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12"><h2 class="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Code Examples</h2><div class="grid grid-cols-1 lg:grid-cols-2 gap-8"><div><h3 class="text-xl font-semibold mb-4 text-yellow-200">JavaScript</h3><div class="bg-black p-4 rounded-lg overflow-x-auto"><pre class="text-sm"><code class="text-gray-300">const boguani = require(&#x27;boguani-sdk&#x27;);

const client = new boguani.Client({
  apiKey: &#x27;your-api-key&#x27;
});

// Send a message
const message = await client.messages.send({
  to: &#x27;+1234567890&#x27;,
  text: &#x27;Hello from BoGuani!&#x27;
});</code></pre></div></div><div><h3 class="text-xl font-semibold mb-4 text-yellow-200">Python</h3><div class="bg-black p-4 rounded-lg overflow-x-auto"><pre class="text-sm"><code class="text-gray-300">import boguani

client = boguani.Client(
    api_key=&#x27;your-api-key&#x27;
)

# Send a payment
payment = client.payments.create({
    &#x27;amount&#x27;: 100.00,
    &#x27;currency&#x27;: &#x27;USD&#x27;,
    &#x27;recipient&#x27;: &#x27;+1234567890&#x27;
})</code></pre></div></div></div></div><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-12"><h2 class="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">SDKs &amp; Libraries</h2><div class="grid grid-cols-2 md:grid-cols-4 gap-6"><div class="text-center"><i class="fab fa-js-square text-4xl text-yellow-400 mb-3"></i><h3 class="font-semibold text-yellow-200">JavaScript</h3><p class="text-gray-400 text-sm">npm install boguani-sdk</p></div><div class="text-center"><i class="fab fa-python text-4xl text-yellow-400 mb-3"></i><h3 class="font-semibold text-yellow-200">Python</h3><p class="text-gray-400 text-sm">pip install boguani</p></div><div class="text-center"><i class="fab fa-php text-4xl text-yellow-400 mb-3"></i><h3 class="font-semibold text-yellow-200">PHP</h3><p class="text-gray-400 text-sm">composer require boguani/sdk</p></div><div class="text-center"><i class="fab fa-java text-4xl text-yellow-400 mb-3"></i><h3 class="font-semibold text-yellow-200">Java</h3><p class="text-gray-400 text-sm">Maven &amp; Gradle</p></div></div></div><div class="text-center"><div class="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto"><h3 class="text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Ready to Build?</h3><p class="text-gray-300 mb-6">Get your API credentials and start integrating BoGuani today.</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><a class="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all" href="/auth"><i class="fas fa-key mr-2"></i>Get API Key</a><a class="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all" href="/guides"><i class="fas fa-book mr-2"></i>View Guides</a></div></div></div></div></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-165557b0f5d7fa83.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[5139,[\"874\",\"static/chunks/874-6052627df6fde20c.js\",\"408\",\"static/chunks/408-749237e46e8c0cbf.js\",\"877\",\"static/chunks/app/api/page-b9c411e3eb2a2270.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\nb:I[4911,[],\"AsyncMetadataOutlet\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[9665,[],\"MetadataBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/media/4f05ba3a6752a328-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/b838bbb2bb93f6bc.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"wU73OVke3tJIUDEsp2gdv\",\"p\":\"\",\"c\":[\"\",\"api\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"api\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/b838bbb2bb93f6bc.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"className\":\"__variable_b5b07c\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"stylesheet\",\"href\":\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\",\"integrity\":\"sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==\",\"crossOrigin\":\"anonymous\",\"referrerPolicy\":\"no-referrer\"}],[\"$\",\"meta\",null,{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1.0\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#2D1B4E\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.googleapis.com\"}],[\"$\",\"link\",null,{\"rel\":\"preconnect\",\"href\":\"https://fonts.gstatic.com\",\"crossOrigin\":\"anonymous\"}]]}],[\"$\",\"body\",null,{\"className\":\"__variable_b5b07c antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}],{\"children\":[\"api\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"6T25Sw-neT8C98-eYF-l_v\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n10:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"BoGuani - Messenger of Value\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"BoGuani Team\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"messaging, payments, secure chat, money transfer, encrypted messaging, BoGuani\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"BoGuani - Messenger of Value\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"7\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"8\",{\"name\":\"twitter:title\",\"content\":\"BoGuani - Messenger of Value\"}],[\"$\",\"meta\",\"9\",{\"name\":\"twitter:description\",\"content\":\"Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.\"}],[\"$\",\"link\",\"10\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n14:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>