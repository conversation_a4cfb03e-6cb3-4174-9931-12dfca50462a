"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return SystemIcon;
    }
});
const _jsxruntime = require("react/jsx-runtime");
function SystemIcon() {
    return /*#__PURE__*/ (0, _jsxruntime.jsx)("svg", {
        width: "16",
        height: "16",
        strokeLinejoin: "round",
        children: /*#__PURE__*/ (0, _jsxruntime.jsx)("path", {
            fill: "currentColor",
            fillRule: "evenodd",
            d: "M0 2a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8.5a1 1 0 0 1-1 1H8.75v3h1.75V16h-5v-1.5h1.75v-3H1a1 1 0 0 1-1-1V2Zm1.5.5V10h13V2.5h-13Z",
            clipRule: "evenodd"
        })
    });
}

if ((typeof exports.default === 'function' || (typeof exports.default === 'object' && exports.default !== null)) && typeof exports.default.__esModule === 'undefined') {
  Object.defineProperty(exports.default, '__esModule', { value: true });
  Object.assign(exports.default, exports);
  module.exports = exports.default;
}

//# sourceMappingURL=system-icon.js.map