# JWT Secret for authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Firebase Configuration
FIREBASE_API_KEY=AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0
FIREBASE_AUTH_DOMAIN=chatpay-4922e.firebaseapp.com
FIREBASE_PROJECT_ID=chatpay-4922e
FIREBASE_STORAGE_BUCKET=chatpay-4922e.appspot.com
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:a94ed4b6baaf7a654492c8
FIREBASE_MEASUREMENT_ID=G-GGBK4R7EFV

# Plaid API Configuration
PLAID_CLIENT_ID=your-plaid-client-id
PLAID_SECRET=your-plaid-secret-key
PLAID_ENCRYPTION_KEY=your-plaid-encryption-key

# Twilio Configuration for SMS
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=09f5e32c69ee3131b553a3caee157bd8
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Supabase Configuration (recommended for Heroku)
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Database Configuration (Heroku will provide DATABASE_URL automatically)
DATABASE_URL=postgresql://username:password@localhost:5432/nexuspay

# Port Configuration (Heroku sets this automatically)
PORT=5173

# App Configuration
APP_URL=http://localhost:5173
NODE_ENV=development

# For Heroku deployment, set these in your Heroku config vars:
# heroku config:set JWT_SECRET=your-actual-secret
# heroku config:set PLAID_CLIENT_ID=your-plaid-client-id
# heroku config:set PLAID_SECRET=your-plaid-secret
# heroku config:set TWILIO_ACCOUNT_SID=your-twilio-sid
# heroku config:set TWILIO_AUTH_TOKEN=your-twilio-token
# heroku config:set SUPABASE_URL=your-supabase-url
# heroku config:set SUPABASE_ANON_KEY=your-supabase-anon-key
