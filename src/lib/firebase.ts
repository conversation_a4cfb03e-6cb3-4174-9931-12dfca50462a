import { initializeApp, getApps, getApp } from "firebase/app";
import { 
  getAuth, 
  RecaptchaVerifier, 
  setPersistence,
  browserLocalPersistence,
  type Auth,
  type UserCredential,
  type ConfirmationResult
} from "firebase/auth";
import { 
  getFirestore, 
  connectFirestoreEmulator,
  enableIndexedDbPersistence,
  type Firestore
} from "firebase/firestore";

declare global {
  interface Window {
    recaptchaVerifier?: RecaptchaVerifier;
    confirmationResult?: ConfirmationResult;
  }
}

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyBqMCY_vblZIApBW5I6aShR1iVQIUtnXJ0",
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "chatpay-4922e.firebaseapp.com",
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "chatpay-4922e",
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "chatpay-4922e.firebasestorage.app",
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "211600276697",
  appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:211600276697:web:a94ed4b6baaf7a654492c8",
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID || "G-GGBK4R7EFV"
};

// Initialize Firebase
let app;
let auth: Auth;
let db: Firestore;

// Only initialize Firebase on the client side
if (typeof window !== 'undefined' && !getApps().length) {
  try {
    // Initialize Firebase if it hasn't been initialized yet
    app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
    auth = getAuth(app);
    db = getFirestore(app);
    
    // Set persistence
    setPersistence(auth, browserLocalPersistence).catch((error) => {
      console.error('Error setting persistence:', error);
    });
    
    // Connect to emulator in development
    if (import.meta.env.DEV && window.location.hostname === 'localhost') {
      try {
        // Connect Auth emulator
        import('firebase/auth').then(({ connectAuthEmulator }) => {
          try {
            connectAuthEmulator(auth, "http://localhost:9099", { disableWarnings: true });
            console.log('Connected to Auth Emulator!');
          } catch (emulatorError) {
            console.error('Error connecting to auth emulator:', emulatorError);
          }
        });

        // Connect Firestore emulator
        connectFirestoreEmulator(db, 'localhost', 8080);
        console.log('Connected to Firestore Emulator!');
      } catch (error) {
        console.error('Error initializing emulators:', error);
      }
    }
    
    // Enable offline persistence
    enableIndexedDbPersistence(db).catch((err) => {
      if (err.code === 'failed-precondition') {
        // Multiple tabs open, persistence can only be enabled in one tab at a time
        console.warn('Multiple tabs open, persistence can only be enabled in one tab at a time.');
      } else if (err.code === 'unimplemented') {
        // The current browser does not support all of the features required
        console.warn('The current browser does not support all features required for offline persistence');
      }
    });
    
  } catch (error) {
    console.error('Firebase initialization error:', error);
  }
}

// Export Firebase services
export { 
  auth, 
  db, 
  RecaptchaVerifier, 
  app as default 
};

export type { UserCredential };
