import CryptoJS from 'crypto-js';

export class EncryptionManager {
  private static instance: EncryptionManager;
  private secretKey: string;

  private constructor() {
    // In production, this should be derived from user's authentication
    this.secretKey = this.generateSecretKey();
  }

  public static getInstance(): EncryptionManager {
    if (!EncryptionManager.instance) {
      EncryptionManager.instance = new EncryptionManager();
    }
    return EncryptionManager.instance;
  }

  private generateSecretKey(): string {
    // In production, derive this from user's authentication token
    return CryptoJS.lib.WordArray.random(256/8).toString();
  }

  public setUserKey(userKey: string): void {
    this.secretKey = userKey;
  }

  public encryptMessage(message: string): string {
    try {
      const encrypted = CryptoJS.AES.encrypt(message, this.secretKey).toString();
      return encrypted;
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  public decryptMessage(encryptedMessage: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedMessage, this.secretKey);
      const originalMessage = decrypted.toString(CryptoJS.enc.Utf8);
      
      if (!originalMessage) {
        throw new Error('Failed to decrypt message');
      }
      
      return originalMessage;
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt message');
    }
  }

  public encryptFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        try {
          const fileData = event.target?.result as string;
          const encrypted = CryptoJS.AES.encrypt(fileData, this.secretKey).toString();
          resolve(encrypted);
        } catch (error) {
          reject(error);
        }
      };
      
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  }

  public decryptFile(encryptedData: string): string {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.secretKey);
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('File decryption error:', error);
      throw new Error('Failed to decrypt file');
    }
  }

  public generateKeyPair(): { publicKey: string; privateKey: string } {
    // Simplified key pair generation for demo
    // In production, use proper asymmetric encryption
    const publicKey = CryptoJS.lib.WordArray.random(256/8).toString();
    const privateKey = CryptoJS.lib.WordArray.random(256/8).toString();
    
    return { publicKey, privateKey };
  }

  public hashPassword(password: string, salt?: string): string {
    const saltToUse = salt || CryptoJS.lib.WordArray.random(128/8).toString();
    const hash = CryptoJS.PBKDF2(password, saltToUse, {
      keySize: 256/32,
      iterations: 10000
    });
    
    return saltToUse + ':' + hash.toString();
  }

  public verifyPassword(password: string, hashedPassword: string): boolean {
    try {
      const [salt, hash] = hashedPassword.split(':');
      const testHash = CryptoJS.PBKDF2(password, salt, {
        keySize: 256/32,
        iterations: 10000
      });
      
      return hash === testHash.toString();
    } catch (error) {
      console.error('Password verification error:', error);
      return false;
    }
  }

  public generateSessionToken(): string {
    return CryptoJS.lib.WordArray.random(256/8).toString();
  }

  public encryptPaymentData(paymentData: Record<string, unknown>): string {
    try {
      const jsonString = JSON.stringify(paymentData);
      return this.encryptMessage(jsonString);
    } catch (error) {
      console.error('Payment encryption error:', error);
      throw new Error('Failed to encrypt payment data');
    }
  }

  public decryptPaymentData(encryptedData: string): Record<string, unknown> {
    try {
      const decryptedString = this.decryptMessage(encryptedData);
      return JSON.parse(decryptedString);
    } catch (error) {
      console.error('Payment decryption error:', error);
      throw new Error('Failed to decrypt payment data');
    }
  }
}

export const encryptionManager = EncryptionManager.getInstance();
