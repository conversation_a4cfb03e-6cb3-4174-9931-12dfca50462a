import sodium from 'libsodium-wrappers';

export interface EncryptedMessage {
  ciphertext: string;
  nonce: string;
}

export interface KeyPair {
  publicKey: string;
  privateKey: string;
}

class EncryptionService {
  private initialized = false;

  async init() {
    if (!this.initialized) {
      await sodium.ready;
      this.initialized = true;
    }
  }

  // Generate a new key pair for a user
  async generateKeyPair(): Promise<KeyPair> {
    await this.init();
    const keyPair = sodium.crypto_box_keypair();
    
    return {
      publicKey: sodium.to_base64(keyPair.publicKey),
      privateKey: sodium.to_base64(keyPair.privateKey)
    };
  }

  // Encrypt a message for a specific recipient
  async encryptMessage(
    message: string, 
    recipientPublicKey: string, 
    senderPrivateKey: string
  ): Promise<EncryptedMessage> {
    await this.init();
    
    const messageBytes = sodium.from_string(message);
    const recipientPubKey = sodium.from_base64(recipientPublicKey);
    const senderPrivKey = sodium.from_base64(senderPrivateKey);
    
    const nonce = sodium.randombytes_buf(sodium.crypto_box_NONCEBYTES);
    const ciphertext = sodium.crypto_box_easy(messageBytes, nonce, recipientPubKey, senderPrivKey);
    
    return {
      ciphertext: sodium.to_base64(ciphertext),
      nonce: sodium.to_base64(nonce)
    };
  }

  // Decrypt a message
  async decryptMessage(
    encryptedMessage: EncryptedMessage,
    senderPublicKey: string,
    recipientPrivateKey: string
  ): Promise<string> {
    await this.init();
    
    const ciphertext = sodium.from_base64(encryptedMessage.ciphertext);
    const nonce = sodium.from_base64(encryptedMessage.nonce);
    const senderPubKey = sodium.from_base64(senderPublicKey);
    const recipientPrivKey = sodium.from_base64(recipientPrivateKey);
    
    const decrypted = sodium.crypto_box_open_easy(ciphertext, nonce, senderPubKey, recipientPrivKey);
    return sodium.to_string(decrypted);
  }

  // Encrypt sensitive data (like Plaid tokens) with a secret key
  async encryptData(data: string, secretKey: string): Promise<EncryptedMessage> {
    await this.init();
    
    const dataBytes = sodium.from_string(data);
    const key = sodium.crypto_generichash(32, sodium.from_string(secretKey));
    const nonce = sodium.randombytes_buf(sodium.crypto_secretbox_NONCEBYTES);
    
    const ciphertext = sodium.crypto_secretbox_easy(dataBytes, nonce, key);
    
    return {
      ciphertext: sodium.to_base64(ciphertext),
      nonce: sodium.to_base64(nonce)
    };
  }

  // Decrypt sensitive data
  async decryptData(encryptedData: EncryptedMessage, secretKey: string): Promise<string> {
    await this.init();
    
    const ciphertext = sodium.from_base64(encryptedData.ciphertext);
    const nonce = sodium.from_base64(encryptedData.nonce);
    const key = sodium.crypto_generichash(32, sodium.from_string(secretKey));
    
    const decrypted = sodium.crypto_secretbox_open_easy(ciphertext, nonce, key);
    return sodium.to_string(decrypted);
  }
}

export const encryption = new EncryptionService();
