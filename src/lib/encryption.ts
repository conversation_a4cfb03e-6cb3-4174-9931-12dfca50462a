import sodium from 'libsodium-wrappers';

export interface EncryptedMessage {
  ciphertext: string;
  nonce: string;
  tag?: string; // For AES-GCM
  ephemeralPublicKey?: string; // For forward secrecy
  senderIdentityKey: string; // For double ratchet
  previousChainLength: number; // For message chain validation
  messageNumber: number; // For message ordering
}

export interface BaseKeyPair {
  publicKey: string;
  privateKey: string;
}

export interface KeyPair extends BaseKeyPair {
  keyId?: string; // Unique identifier for key rotation
  createdAt?: number; // Timestamp for key expiration
}

export interface SessionKeys {
  rootKey: string;
  chainKey: string;
  publicKey: string;
  privateKey: string;
}

export interface SignedPreKey extends BaseKeyPair {
  keyId: number;
  signature: string;
  createdAt?: number;
}

export interface OneTimePreKey extends BaseKeyPair {
  keyId: number;
  used: boolean;
  createdAt: number;
}

export interface IdentityKeyPair extends KeyPair {
  deviceId: string;
}

// Encryption constants
const ALGORITHM = 'AES-GCM';
const IV_LENGTH = 12; // 96 bits for GCM
const AUTH_TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 16;
const ITERATIONS = 210000; // Recommended for PBKDF2
const KEY_LENGTH = 32; // 256 bits
const DIGEST = 'SHA-256';
const MAX_SKIP = 1000; // Maximum number of message keys to store for out-of-order messages
const MAX_SESSION_AGE = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

interface SessionState {
  rootKey: ArrayBuffer;
  sendingChain: {
    chainKey: ArrayBuffer;
    chainType: 'sending' | 'receiving';
    keyIndex: number;
  };
  receivingChain?: {
    chainKey: ArrayBuffer;
    chainType: 'sending' | 'receiving';
    keyIndex: number;
  };
  sendingRatchetKey: CryptoKeyPair;
  receivingRatchetKey?: CryptoKeyPair;
  prevChainLength: number;
  messageNumber: number;
  sessionId: string;
  createdAt: number;
  lastUsed: number;
}

class EncryptionService {
  private initialized = false;
  private sessions: Map<string, SessionState> = new Map();
  private pendingMessageKeys: Map<string, Map<number, CryptoKey>> = new Map();
  private identityKeyPair?: KeyPair;
  private signedPreKey?: SignedPreKey;
  private oneTimePreKeys: Map<number, OneTimePreKey> = new Map();
  private maxSkip = MAX_SKIP;
  private maxSessionAge = MAX_SESSION_AGE;

  // Singleton instance for easy access
  private static instance: EncryptionService;

  public static getInstance(): EncryptionService {
    if (!EncryptionService.instance) {
      EncryptionService.instance = new EncryptionService();
    }
    return EncryptionService.instance;
  }

  private constructor() {}

  async init() {
    if (!this.initialized) {
      await sodium.ready;
      await this.loadOrGenerateIdentityKey();
      await this.generatePreKeys();
      this.initialized = true;
    }
  }

  private async loadOrGenerateIdentityKey() {
    // In a real app, load from secure storage
    const storedKey = localStorage.getItem('identityKey');
    if (storedKey) {
      this.identityKeyPair = JSON.parse(storedKey);
    } else {
      this.identityKeyPair = await this.generateKeyPair();
      localStorage.setItem('identityKey', JSON.stringify(this.identityKeyPair));
    }
  }

  private async generatePreKeys() {
    // Generate signed pre-key
    const signedPreKey = await this.generateKeyPair();
    const signature = await this.sign(signedPreKey.publicKey, this.identityKeyPair!.privateKey);
    this.signedPreKey = {
      ...signedPreKey,
      keyId: Date.now(),
      signature: sodium.to_base64(signature)
    };

    // Generate one-time pre-keys
    for (let i = 0; i < 100; i++) {
      const oneTimeKey = await this.generateKeyPair();
      this.oneTimePreKeys.set(i, {
        ...oneTimeKey,
        keyId: i,
        used: false,
        createdAt: Date.now()
      });
    }
  }

  private async sign(data: string, privateKey: string): Promise<Uint8Array> {
    await this.init();
    return sodium.crypto_sign_detached(
      sodium.from_string(data),
      sodium.from_base64(privateKey)
    );
  }

  // Double Ratchet Algorithm Implementation
  private async initializeSession(theirIdentityKey: string, theirSignedPreKey: SignedPreKey, theirOneTimeKey?: OneTimePreKey): Promise<string> {
    await this.init();
    
    // 1. Generate ECDH shared secrets
    const dh1 = await this.calculateDH(theirSignedPreKey.publicKey, this.identityKeyPair!.privateKey);
    const dh2 = await this.calculateDH(theirIdentityKey, this.signedPreKey!.privateKey);
    const dh3 = theirOneTimeKey ? 
      await this.calculateDH(theirOneTimeKey.publicKey, this.signedPreKey!.privateKey) : 
      new Uint8Array(32);
    
    // 2. KDF chain for root and chain keys
    const masterSecret = new Uint8Array([...dh1, ...dh2, ...(theirOneTimeKey ? dh3 : [])]);
    const derivedKeys = await this.deriveKeys(masterSecret);
    
    // 3. Create session state
    const sessionId = crypto.randomUUID();
    const now = Date.now();
    
    const session: SessionState = {
      rootKey: derivedKeys.rootKey,
      sendingChain: {
        chainKey: derivedKeys.chainKey,
        chainType: 'sending',
        keyIndex: 0
      },
      sendingRatchetKey: await this.generateECDHKeyPair(),
      prevChainLength: 0,
      messageNumber: 0,
      sessionId,
      createdAt: now,
      lastUsed: now
    };
    
    this.sessions.set(sessionId, session);
    return sessionId;
  }

  private async calculateDH(publicKey: string, privateKey: string): Promise<Uint8Array> {
    const sharedKey = sodium.crypto_scalarmult(
      sodium.from_base64(privateKey),
      sodium.from_base64(publicKey)
    );
    return sodium.crypto_generichash(32, sharedKey);
  }

  private async deriveKeys(secret: Uint8Array): Promise<{ rootKey: ArrayBuffer; chainKey: ArrayBuffer; }> {
    const hkdf = await this.hkdf(secret, new Uint8Array(32), 'RootChainKey');
    return {
      rootKey: hkdf.slice(0, 32),
      chainKey: hkdf.slice(32, 64)
    };
  }

  private async hkdf(secret: Uint8Array, salt: Uint8Array, info: string): Promise<ArrayBuffer> {
    const key = await window.crypto.subtle.importKey(
      'raw',
      secret,
      { name: 'HKDF' },
      false,
      ['deriveBits']
    );

    return window.crypto.subtle.deriveBits(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt,
        info: new TextEncoder().encode(info)
      },
      key,
      512 // 64 bytes (512 bits)
    );
  }

  private async generateECDHKeyPair(): Promise<CryptoKeyPair> {
    return window.crypto.subtle.generateKey(
      {
        name: 'ECDH',
        namedCurve: 'P-256'
      },
      true,
      ['deriveKey', 'deriveBits']
    );
  }

  // Generate a new key pair for a user (X25519 for key exchange)
  async generateKeyPair(): Promise<KeyPair> {
    await this.init();
    const keyPair = sodium.crypto_box_keypair();
    
    return {
      publicKey: sodium.to_base64(keyPair.publicKey),
      privateKey: sodium.to_base64(keyPair.privateKey),
      keyId: crypto.randomUUID(),
      createdAt: Date.now()
    };
  }

  // Encrypt a message using the Double Ratchet Algorithm
  async encryptMessage(sessionId: string, plaintext: string, recipientPublicKey: string): Promise<EncryptedMessage> {
    await this.init();
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    // Generate message key from chain
    const messageKey = await this.deriveMessageKey(session.sendingChain.chainKey);
    
    // Encrypt the message
    const iv = window.crypto.getRandomValues(new Uint8Array(12));
    const encrypted = await window.crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      messageKey,
      new TextEncoder().encode(plaintext)
    );

    // Update session state
    session.sendingChain.keyIndex++;
    session.messageNumber++;
    session.lastUsed = Date.now();

    // Prepare the message
    return {
      ciphertext: sodium.to_base64(new Uint8Array(encrypted)),
      nonce: sodium.to_base64(iv),
      senderIdentityKey: this.identityKeyPair!.publicKey,
      previousChainLength: session.prevChainLength,
      messageNumber: session.messageNumber,
      ephemeralPublicKey: await this.exportPublicKey(session.sendingRatchetKey.publicKey)
    };
  }

  // Decrypt a message using the Double Ratchet Algorithm
  private async deriveMessageKey(chainKey: ArrayBuffer): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      chainKey,
      'HKDF',
      false,
      ['deriveKey']
    );

    return window.crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: new Uint8Array(),
        info: new TextEncoder().encode('MessageKey')
      },
      keyMaterial,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  private async handleOutOfOrderMessages(session: SessionState, encryptedMessage: EncryptedMessage): Promise<void> {
    const pendingKeys = this.pendingMessageKeys.get(session.sessionId) || new Map();
    
    // If we have a pending key for this message number, use it
    if (pendingKeys.has(encryptedMessage.messageNumber)) {
      const key = pendingKeys.get(encryptedMessage.messageNumber)!;
      pendingKeys.delete(encryptedMessage.messageNumber);
      return key;
    }

    // If message is too far in the future, reject it
    if (encryptedMessage.messageNumber > session.prevChainLength + this.maxSkip) {
      throw new Error('Message too far in the future');
    }
  }

  private async exportPublicKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('raw', key);
    return sodium.to_base64(new Uint8Array(exported));
  }

  async decryptMessage(sessionId: string, encryptedMessage: EncryptedMessage): Promise<string> {
    await this.init();
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    // Handle out-of-order messages
    await this.handleOutOfOrderMessages(session, encryptedMessage);

    // Get the message key
    let messageKey: CryptoKey;
    if (encryptedMessage.messageNumber > session.prevChainLength) {
      // Normal message in the current chain
      const chainKey = session.receivingChain?.chainKey;
      if (!chainKey) {
        throw new Error('No receiving chain available');
      }
      
      messageKey = await this.deriveMessageKey(chainKey);
      
      // Update session state
      session.prevChainLength = encryptedMessage.messageNumber;
      session.lastUsed = Date.now();
    } else {
      // Try to get from pending keys
      const pendingKeys = this.pendingMessageKeys.get(sessionId) || new Map();
      const key = pendingKeys.get(encryptedMessage.messageNumber);
      if (!key) {
        throw new Error('Message key not found');
      }
      messageKey = key;
      pendingKeys.delete(encryptedMessage.messageNumber);
    }

    // Decrypt the message
    const iv = sodium.from_base64(encryptedMessage.nonce);
    const ciphertext = sodium.from_base64(encryptedMessage.ciphertext);
    
    try {
      const decrypted = await window.crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        messageKey,
        ciphertext
      );
      
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt message');
    }
  }

  // Decrypt data with AES-256-GCM
  async decrypt(encryptedData: string, key: CryptoKey): Promise<string> {
    const data = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
    
    const iv = data.slice(0, IV_LENGTH);
    const authTag = data.slice(IV_LENGTH, IV_LENGTH + AUTH_TAG_LENGTH);
    const ciphertext = data.slice(IV_LENGTH + AUTH_TAG_LENGTH);
    
    // Combine ciphertext and auth tag for WebCrypto
    const encrypted = new Uint8Array(ciphertext.length + AUTH_TAG_LENGTH);
    encrypted.set(ciphertext);
    encrypted.set(authTag, ciphertext.length);
    
    const decrypted = await window.crypto.subtle.decrypt(
      { name: ALGORITHM, iv, tagLength: AUTH_TAG_LENGTH * 8 },
      key,
      encrypted
    );
    
    return new TextDecoder().decode(decrypted);
  }

  // Encrypt a message for a specific recipient using X25519 + XSalsa20-Poly1305
  async encryptWithKeyPair(
    message: string, 
    recipientPublicKey: string, 
    senderPrivateKey: string
  ): Promise<EncryptedMessage> {
    await this.init();
    
    const messageBytes = sodium.from_string(message);
    const recipientPubKey = sodium.from_base64(recipientPublicKey);
    const senderPrivKey = sodium.from_base64(senderPrivateKey);
    
    const nonce = sodium.randombytes_buf(sodium.crypto_box_NONCEBYTES);
    const ciphertext = sodium.crypto_box_easy(messageBytes, nonce, recipientPubKey, senderPrivKey);
    
    return {
      ciphertext: sodium.to_base64(ciphertext),
      nonce: sodium.to_base64(nonce),
      senderIdentityKey: this.identityKeyPair!.publicKey,
      previousChainLength: 0,
      messageNumber: 0
    };
  }

  // Decrypt a message using X25519 + XSalsa20-Poly1305
  async decryptWithKeyPair(
    encryptedMessage: EncryptedMessage,
    senderPublicKey: string,
    recipientPrivateKey: string
  ): Promise<string> {
    await this.init();
    
    const ciphertext = sodium.from_base64(encryptedMessage.ciphertext);
    const nonce = sodium.from_base64(encryptedMessage.nonce);
    const senderPubKey = sodium.from_base64(senderPublicKey);
    const recipientPrivKey = sodium.from_base64(recipientPrivateKey);
    
    const decrypted = sodium.crypto_box_open_easy(ciphertext, nonce, senderPubKey, recipientPrivKey);
    return sodium.to_string(decrypted);
  }

  // Create HMAC signature
  async createSignature(data: string, secret: string): Promise<string> {
    await this.init();
    const key = await window.crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(secret),
      { name: 'HMAC', hash: DIGEST },
      false,
      ['sign']
    );
    
    const signature = await window.crypto.subtle.sign(
      'HMAC',
      key,
      new TextEncoder().encode(data)
    );
    
    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  }

  // Verify HMAC signature
  async verifySignature(data: string, signature: string, secret: string): Promise<boolean> {
    try {
      const key = await window.crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(secret),
        { name: 'HMAC', hash: DIGEST },
        false,
        ['verify']
      );
      
      const sigBytes = Uint8Array.from(atob(signature), c => c.charCodeAt(0));
      
      return await window.crypto.subtle.verify(
        'HMAC',
        key,
        sigBytes,
        new TextEncoder().encode(data)
      );
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  // Generate a secure random string
  async generateRandomString(length: number = 32): Promise<string> {
    await this.init();
    const randomBytes = sodium.randombytes_buf(length);
    return sodium.to_base64(randomBytes).slice(0, length);
  }

  // Generate a random symmetric key for AES-GCM
  async generateKey(): Promise<CryptoKey> {
    return window.crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true,
      ['encrypt', 'decrypt']
    );
  }
}

// Singleton instance for simplified API access
const encryptionService = EncryptionService.getInstance();

// Export the encryption service instance and its methods
export default encryptionService;

// Export individual methods for easier imports
export const encrypt = encryptionService.encryptMessage.bind(encryptionService);
export const decrypt = encryptionService.decryptMessage.bind(encryptionService);
export const generateKey = encryptionService.generateKey.bind(encryptionService);
export const generateKeyPair = encryptionService.generateKeyPair.bind(encryptionService);
export const createSignature = encryptionService.createSignature.bind(encryptionService);
export const verifySignature = encryptionService.verifySignature.bind(encryptionService);
export const encryptWithKeyPair = encryptionService.encryptWithKeyPair.bind(encryptionService);
export const decryptWithKeyPair = encryptionService.decryptWithKeyPair.bind(encryptionService);
