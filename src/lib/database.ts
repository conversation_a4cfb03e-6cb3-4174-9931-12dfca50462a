import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseKey);

// Database types
export interface User {
  id: string;
  phone: string;
  handle: string;
  name: string;
  avatar?: string;
  public_key: string;
  private_key_encrypted: string;
  created_at: string;
  last_seen: string;
  is_online: boolean;
}

export interface Chat {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: string;
  chat_id: string;
  sender_id: string;
  recipient_id: string;
  content: string;
  message_type: 'text' | 'payment' | 'image';
  encrypted_content?: any;
  payment_data?: any;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  created_at: string;
  updated_at: string;
}

export interface BankAccount {
  id: string;
  user_id: string;
  plaid_access_token_encrypted: string;
  account_id: string;
  account_name: string;
  account_mask?: string;
  account_type?: string;
  account_subtype?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Database functions
export class DatabaseService {
  // User operations
  async createUser(userData: Omit<User, 'id' | 'created_at' | 'last_seen' | 'is_online'>) {
    const { data, error } = await supabase
      .from('users')
      .insert([userData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserByPhone(phone: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('phone', phone)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async getUserById(id: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  async updateUserOnlineStatus(userId: string, isOnline: boolean) {
    const { error } = await supabase
      .from('users')
      .update({ 
        is_online: isOnline,
        last_seen: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) throw error;
  }

  // Chat operations
  async createChat() {
    const { data, error } = await supabase
      .from('chats')
      .insert([{}])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async addChatParticipant(chatId: string, userId: string) {
    const { error } = await supabase
      .from('chat_participants')
      .insert([{ chat_id: chatId, user_id: userId }]);

    if (error) throw error;
  }

  async getUserChats(userId: string) {
    const { data, error } = await supabase
      .from('chat_participants')
      .select(`
        chat_id,
        chats (
          id,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', userId);

    if (error) throw error;
    return data;
  }

  // Message operations
  async createMessage(messageData: Omit<Message, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('messages')
      .insert([messageData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getChatMessages(chatId: string, limit = 50) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) throw error;
    return data;
  }

  async updateMessageStatus(messageId: string, status: Message['status']) {
    const { error } = await supabase
      .from('messages')
      .update({ status })
      .eq('id', messageId);

    if (error) throw error;
  }

  // Bank account operations
  async createBankAccount(accountData: Omit<BankAccount, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('bank_accounts')
      .insert([accountData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async getUserBankAccounts(userId: string) {
    const { data, error } = await supabase
      .from('bank_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) throw error;
    return data;
  }

  // OTP operations
  async storeOTP(phone: string, otp: string, expiresAt: Date) {
    const { error } = await supabase
      .from('otp_verifications')
      .insert([{
        phone,
        otp,
        expires_at: expiresAt.toISOString()
      }]);

    if (error) throw error;
  }

  async verifyOTP(phone: string, otp: string) {
    const { data, error } = await supabase
      .from('otp_verifications')
      .select('*')
      .eq('phone', phone)
      .eq('otp', otp)
      .eq('verified', false)
      .gte('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    
    if (data) {
      // Mark as verified
      await supabase
        .from('otp_verifications')
        .update({ verified: true })
        .eq('id', data.id);
      
      return true;
    }
    
    return false;
  }

  // Session operations
  async createSession(userId: string, token: string, expiresAt: Date) {
    const { error } = await supabase
      .from('sessions')
      .insert([{
        user_id: userId,
        token,
        expires_at: expiresAt.toISOString()
      }]);

    if (error) throw error;
  }

  async getSessionByToken(token: string) {
    const { data, error } = await supabase
      .from('sessions')
      .select('*')
      .eq('token', token)
      .gte('expires_at', new Date().toISOString())
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return data;
  }

  async deleteSession(token: string) {
    const { error } = await supabase
      .from('sessions')
      .delete()
      .eq('token', token);

    if (error) throw error;
  }
}

export const db = new DatabaseService();
