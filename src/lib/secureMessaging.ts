import { encrypt, decrypt, generateKey, generate<PERSON><PERSON><PERSON>air, type EncryptedMessage } from './encryption';
import { db } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  updateDoc, 
  collection, 
  query, 
  orderBy, 
  getDocs,
  Timestamp,
  type DocumentData,
  type DocumentReference,
  type QueryDocumentSnapshot,
  type DocumentSnapshot,
  type QuerySnapshot,
  type QueryConstraint,
  type Firestore,
  type DocumentReference as FirestoreDocRef
} from 'firebase/firestore';

// Define the base message data interface
export interface MessageData {
  content: string;
  type: 'text' | 'payment';
  timestamp: string;
  senderId: string;
  paymentData?: {
    amount: number;
    status: string;
    transactionId: string;
    note?: string;
  };
}

// Define the message interface that extends the base data
export interface Message extends Omit<MessageData, 'timestamp'> {
  id: string;
  chatId: string;
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
  read: boolean;
}

// Define the base chat data interface
export interface ChatData {
  participants: string[];
  lastMessage?: {
    content: string;
    timestamp: Timestamp;
    senderId: string;
    type: 'text' | 'payment';
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
  encryptedKeys: Record<string, string>;
}

// Define the chat interface that extends the base data
export interface Chat extends Omit<ChatData, 'createdAt' | 'updatedAt' | 'lastMessage'> {
  id: string;
  lastMessage?: {
    content: string;
    timestamp: Date;
    senderId: string;
    type: 'text' | 'payment';
  };
  createdAt: Date;
  updatedAt: Date;
}

// For backward compatibility
export type IMessage = Message;
export type IChat = Chat;
export type IMessageData = MessageData;
export type IChatData = ChatData;



class SecureMessaging {
  private messageKeys: Map<string, CryptoKey>;

  constructor() {
    this.messageKeys = new Map();
  }

  // Initialize a new chat with key exchange
  async initializeChat(chatId: string, participants: string[]): Promise<CryptoKey> {
    try {
      // Generate a new symmetric key for this chat
      const chatKey = await generateKey();
      
      // Store the key in memory
      this.messageKeys.set(chatId, chatKey);
      
      // In a real app, you would also encrypt this key with each participant's public key
      // and store it in Firestore
      
      return chatKey;
    } catch (error) {
      console.error('Error initializing chat:', error);
      throw error;
    }
  }

  // Send an encrypted message
  async sendMessage(
    chatId: string, 
    senderId: string, 
    content: string, 
    type: 'text' | 'payment' = 'text',
    paymentData?: any
  ): Promise<string> {
    try {
      const chatKey = this.messageKeys.get(chatId);
      
      if (!chatKey) {
        throw new Error('No encryption key found for this chat');
      }
      
      // Create the message data
      const messageData: MessageData = {
        content,
        type,
        timestamp: new Date().toISOString(),
        senderId,
        ...(paymentData && { paymentData })
      };

      // Convert the message data to a string
      const messageString = JSON.stringify(messageData);
      
      // In a real app, you would encrypt the message here
      // For now, we'll just store the message as is
      const encryptedContent = messageString;

      // Store the encrypted message in Firestore
      const messageRef = doc(collection(db, 'chats', chatId, 'messages'));
      await setDoc(messageRef, {
        encryptedContent,
        senderId,
        timestamp: Timestamp.now(),
        type,
        status: 'sent' as const
      });

      // Update the chat's last message
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          content: type === 'payment' ? 'Payment' : content,
          timestamp: Timestamp.now(),
          senderId,
          type
        },
        updatedAt: Timestamp.now()
      });

      return messageRef.id;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Get messages for a chat
  async getMessages(chatId: string, userId: string): Promise<Message[]> {
    try {
      let chatKey = this.messageKeys.get(chatId);
      
      // If we don't have the key in memory, try to get it from Firestore
      if (!chatKey) {
        const chatRef = doc(db, 'chats', chatId);
        const chatDoc = await getDoc(chatRef);
        
        if (!chatDoc.exists()) {
          throw new Error('Chat not found');
        }
        
        const chatData = chatDoc.data() as ChatData;
        if (!chatData.encryptedKeys || !chatData.encryptedKeys[userId]) {
          throw new Error('No encryption key found for this user in chat');
        }
        
        // In a real app, you would decrypt the chat key with the user's private key
        // For now, we'll just create a new key
        chatKey = await generateKey();
        this.messageKeys.set(chatId, chatKey);
      }
      
      const messagesRef = collection(db, 'chats', chatId, 'messages');
      const q = query(messagesRef, orderBy('timestamp', 'asc'));
      const querySnapshot = await getDocs(q);
      
      const messages: Message[] = [];
      
      for (const doc of querySnapshot.docs) {
        const data = doc.data();
        
        // Skip if no encrypted content
        if (!data.encryptedContent) {
          console.warn('Message has no encrypted content, skipping');
          continue;
        }
        
        try {
          // Create encrypted message object
          const encryptedMessage: EncryptedMessage = {
            ciphertext: data.encryptedContent,
            nonce: data.nonce || '',
            senderIdentityKey: data.senderIdentityKey || '',
            previousChainLength: data.previousChainLength || 0,
            messageNumber: data.messageNumber || 0,
          };
          
          // In a real app, you would decrypt the message here
          // For now, we'll just use the content as is
          const decryptedContent = data.encryptedContent;
const messageData = JSON.parse(decryptedContent) as MessageData;
          
          // Ensure the read property is always defined
          const messageRead = typeof data.read === 'boolean' ? data.read : false;
          
          // Create message object
const message: Message = {
            id: doc.id,
            chatId,
            content: messageData.content,
            type: messageData.type,
            senderId: messageData.senderId,
            timestamp: new Date(messageData.timestamp),
            status: data.status || 'delivered',
            read: messageRead,
            ...(messageData.paymentData && { paymentData: messageData.paymentData })
          };
          
          messages.push(message);
        } catch (error) {
          console.error('Error processing message:', error);
          continue;
        }
      }
      
      return messages;
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  // Helper method to get a user's public key (simplified)
  private async getUserPublicKey(userId: string): Promise<CryptoKey> {
    // In a real app, you'd fetch this from your server
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      const userData = userDoc.data() as { publicKey?: string };
      if (userData.publicKey) {
        return this.deserializeKey(userData.publicKey);
      }
    }
    
    throw new Error('User public key not found');
  }

  // Helper method to serialize a CryptoKey to string
  private async serializeKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('raw', key);
    const exportedArray = new Uint8Array(exported);
    return btoa(String.fromCharCode(...Array.from(exportedArray)));
  }

  // Helper method to deserialize a string to CryptoKey
  private async deserializeKey(keyData: string): Promise<CryptoKey> {
    const binaryString = atob(keyData);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return window.crypto.subtle.importKey(
      'raw',
      bytes,
      { name: 'AES-GCM' },
      false,
      ['encrypt', 'decrypt']
    );
  }
}

// Export the secure messaging instance
export const secureMessaging = new SecureMessaging();


