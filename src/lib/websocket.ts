import { io, type Socket } from 'socket.io-client';
import { writable, type Writable } from 'svelte/store';
import type { EncryptedMessage } from './encryption.js';

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  recipientId: string;
  content: string;
  type: 'text' | 'payment' | 'image';
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  encrypted?: EncryptedMessage;
  paymentData?: {
    amount: number;
    status: 'pending' | 'completed' | 'failed';
    transactionId?: string;
  };
}

export interface Chat {
  id: string;
  participants: string[];
  lastMessage?: Message;
  unreadCount: number;
  isTyping: boolean;
  typingUsers: string[];
}

export interface User {
  id: string;
  handle: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen: Date;
}

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  // Stores
  public messages: Writable<Message[]> = writable([]);
  public chats: Writable<Chat[]> = writable([]);
  public onlineUsers: Writable<User[]> = writable([]);
  public connectionStatus: Writable<'connecting' | 'connected' | 'disconnected'> = writable('disconnected');
  public typingIndicators: Writable<Record<string, string[]>> = writable({});

  connect(token: string) {
    if (this.socket?.connected) {
      return;
    }

    this.connectionStatus.set('connecting');

    // Connect to the same server as the SvelteKit app
    const wsUrl = typeof window !== 'undefined'
      ? window.location.origin
      : 'http://localhost:5173';

    this.socket = io(wsUrl, {
      auth: {
        token
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      this.connectionStatus.set('connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      this.connectionStatus.set('disconnected');
      this.attemptReconnect();
    });

    this.socket.on('message', (message: Message) => {
      this.messages.update(messages => {
        const existingIndex = messages.findIndex(m => m.id === message.id);
        if (existingIndex >= 0) {
          messages[existingIndex] = message;
        } else {
          messages.push(message);
        }
        return messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      });
    });

    this.socket.on('message_status', (data: { messageId: string; status: Message['status'] }) => {
      this.messages.update(messages => {
        const message = messages.find(m => m.id === data.messageId);
        if (message) {
          message.status = data.status;
        }
        return messages;
      });
    });

    this.socket.on('typing_start', (data: { chatId: string; userId: string; userName: string }) => {
      this.typingIndicators.update(indicators => {
        if (!indicators[data.chatId]) {
          indicators[data.chatId] = [];
        }
        if (!indicators[data.chatId].includes(data.userName)) {
          indicators[data.chatId].push(data.userName);
        }
        return indicators;
      });
    });

    this.socket.on('typing_stop', (data: { chatId: string; userId: string; userName: string }) => {
      this.typingIndicators.update(indicators => {
        if (indicators[data.chatId]) {
          indicators[data.chatId] = indicators[data.chatId].filter(name => name !== data.userName);
          if (indicators[data.chatId].length === 0) {
            delete indicators[data.chatId];
          }
        }
        return indicators;
      });
    });

    this.socket.on('user_online', (user: User) => {
      this.onlineUsers.update(users => {
        const existingIndex = users.findIndex(u => u.id === user.id);
        if (existingIndex >= 0) {
          users[existingIndex] = user;
        } else {
          users.push(user);
        }
        return users;
      });
    });

    this.socket.on('user_offline', (userId: string) => {
      this.onlineUsers.update(users => users.filter(u => u.id !== userId));
    });

    this.socket.on('chat_updated', (chat: Chat) => {
      this.chats.update(chats => {
        const existingIndex = chats.findIndex(c => c.id === chat.id);
        if (existingIndex >= 0) {
          chats[existingIndex] = chat;
        } else {
          chats.push(chat);
        }
        return chats.sort((a, b) => {
          const aTime = a.lastMessage?.timestamp.getTime() || 0;
          const bTime = b.lastMessage?.timestamp.getTime() || 0;
          return bTime - aTime;
        });
      });
    });
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.socket?.connect();
      }, Math.pow(2, this.reconnectAttempts) * 1000);
    }
  }

  sendMessage(message: Omit<Message, 'id' | 'timestamp' | 'status'>) {
    if (!this.socket?.connected) {
      throw new Error('Not connected to server');
    }

    const fullMessage: Message = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      status: 'sending'
    };

    // Add to local store immediately
    this.messages.update(messages => [...messages, fullMessage]);

    // Send to server
    this.socket.emit('send_message', fullMessage);

    return fullMessage;
  }

  startTyping(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_start', { chatId });
    }
  }

  stopTyping(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit('typing_stop', { chatId });
    }
  }

  markMessageAsRead(messageId: string) {
    if (this.socket?.connected) {
      this.socket.emit('mark_read', { messageId });
    }
  }

  joinChat(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit('join_chat', { chatId });
    }
  }

  leaveChat(chatId: string) {
    if (this.socket?.connected) {
      this.socket.emit('leave_chat', { chatId });
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.connectionStatus.set('disconnected');
  }
}

export const websocket = new WebSocketService();
