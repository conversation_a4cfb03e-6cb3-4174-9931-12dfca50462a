export interface User {
  id: string;
  uid: string;
  name: string;
  email: string;
  avatar?: string;
  lastSeen?: Date;
  status?: 'online' | 'offline' | 'away';
}

export interface PaymentData {
  id?: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'sending' | 'sent' | 'refunded' | 'cancelled';
  timestamp: Date;
  note?: string;
  recipientId: string;
  senderId: string;
  metadata?: Record<string, any>;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  recipientId: string;
  content: string;
  type: 'text' | 'payment' | 'system';
  timestamp: Date;
  read: boolean;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  paymentData?: PaymentData;
}

export interface Chat {
  id: string;
  participants: string[];
  lastMessage?: Message | string | null;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
  encryptedKeys: Record<string, string>;
  isTyping: boolean;
  typingUsers: string[];
}

export interface BankAccount {
  id: string;
  name: string;
  last4: string;
  type: string;
  balance: number;
  mask: string;
  subtype: string;
  isConnected: boolean;
  [key: string]: any; // Allow additional properties
}

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
export type MessageType = 'text' | 'payment' | 'system';
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'sending' | 'sent' | 'refunded' | 'cancelled';
