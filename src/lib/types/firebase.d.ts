import type { ConfirmationResult, RecaptchaVerifier } from 'firebase/auth';

export interface ExtendedRecaptchaVerifier extends RecaptchaVerifier {
  clear: () => void;
  render: () => Promise<number>;
  verify: () => Promise<string>;
  _reset: () => void;
  _render: () => Promise<number>;
  _verify: (onSuccess: (response: string) => void, onError: (error: Error) => void) => void;
}

declare global {
  interface Window {
    recaptchaVerifier?: ExtendedRecaptchaVerifier;
    confirmationResult?: ConfirmationResult;
  }
}

export {};
