import Peer from 'simple-peer';

export interface WebRTCConnection {
  peer: Peer.Instance;
  stream?: MediaStream;
  isInitiator: boolean;
}

export class WebRTCManager {
  private connections: Map<string, WebRTCConnection> = new Map();
  private localStream: MediaStream | null = null;
  private onIncomingCall?: (callerId: string, isVideo: boolean) => void;
  private onCallEnded?: (callerId: string) => void;

  constructor() {
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    // Setup socket.io event handlers for signaling
    // This will be implemented when we add Socket.IO
  }

  async initializeMedia(video: boolean = false): Promise<MediaStream> {
    try {
      const constraints = {
        audio: true,
        video: video ? { width: 640, height: 480 } : false
      };

      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      return this.localStream;
    } catch (error) {
      console.error('Error accessing media devices:', error);
      throw new Error('Could not access camera/microphone');
    }
  }

  async startCall(targetUserId: string, isVideo: boolean = false): Promise<void> {
    if (!this.localStream) {
      await this.initializeMedia(isVideo);
    }

    const peer = new Peer({
      initiator: true,
      trickle: false,
      stream: this.localStream || undefined
    });

    const connection: WebRTCConnection = {
      peer,
      stream: this.localStream || undefined,
      isInitiator: true
    };

    this.connections.set(targetUserId, connection);

    peer.on('signal', (data) => {
      // Send signal to target user via Socket.IO
      console.log('Sending signal to:', targetUserId, data);
      // socket.emit('call-signal', { targetUserId, signal: data, isVideo });
    });

    peer.on('stream', (remoteStream) => {
      console.log('Received remote stream');
      connection.stream = remoteStream;
      this.handleRemoteStream(targetUserId, remoteStream);
    });

    peer.on('error', (error) => {
      console.error('Peer error:', error);
      this.endCall(targetUserId);
    });

    peer.on('close', () => {
      console.log('Peer connection closed');
      this.endCall(targetUserId);
    });
  }

  async answerCall(callerId: string, signal: RTCSessionDescriptionInit): Promise<void> {
    if (!this.localStream) {
      await this.initializeMedia(false); // Start with audio only
    }

    const peer = new Peer({
      initiator: false,
      trickle: false,
      stream: this.localStream || undefined
    });

    const connection: WebRTCConnection = {
      peer,
      stream: this.localStream || undefined,
      isInitiator: false
    };

    this.connections.set(callerId, connection);

    peer.on('signal', (data) => {
      // Send answer signal back to caller
      console.log('Sending answer signal to:', callerId, data);
      // socket.emit('call-answer', { callerId, signal: data });
    });

    peer.on('stream', (remoteStream) => {
      console.log('Received remote stream from caller');
      connection.stream = remoteStream;
      this.handleRemoteStream(callerId, remoteStream);
    });

    peer.on('error', (error) => {
      console.error('Peer error:', error);
      this.endCall(callerId);
    });

    peer.on('close', () => {
      console.log('Peer connection closed');
      this.endCall(callerId);
    });

    peer.signal(signal);
  }

  endCall(userId: string): void {
    const connection = this.connections.get(userId);
    if (connection) {
      connection.peer.destroy();
      this.connections.delete(userId);
      
      if (this.onCallEnded) {
        this.onCallEnded(userId);
      }
    }
  }

  endAllCalls(): void {
    this.connections.forEach((_, userId) => {
      this.endCall(userId);
    });

    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
  }

  toggleMute(userId: string): boolean {
    const connection = this.connections.get(userId);
    if (connection && this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        return !audioTrack.enabled; // Return true if muted
      }
    }
    return false;
  }

  toggleVideo(userId: string): boolean {
    const connection = this.connections.get(userId);
    if (connection && this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        return !videoTrack.enabled; // Return true if video off
      }
    }
    return false;
  }

  private handleRemoteStream(userId: string, _stream: MediaStream): void {
    // This will be handled by the React component
    console.log('Remote stream received from:', userId);
  }

  setOnIncomingCall(callback: (callerId: string, isVideo: boolean) => void): void {
    this.onIncomingCall = callback;
  }

  setOnCallEnded(callback: (callerId: string) => void): void {
    this.onCallEnded = callback;
  }

  getConnection(userId: string): WebRTCConnection | undefined {
    return this.connections.get(userId);
  }

  isInCall(userId: string): boolean {
    return this.connections.has(userId);
  }
}

export const webrtcManager = new WebRTCManager();
