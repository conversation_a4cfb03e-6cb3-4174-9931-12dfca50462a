import { randomBytes, createCipher<PERSON>, createDecipher<PERSON>, createHash, createHmac } from 'crypto';
import { subtle } from 'crypto';

// AES-256-GCM encryption with 96-bit IV and 128-bit auth tag
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // 96 bits for GCM
const AUTH_TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 16;
const ITERATIONS = 100000;
const KEY_LENGTH = 32; // 256 bits
const DIGEST = 'sha256';

// Generate a secure random key
const generateKey = () => randomBytes(32);

// Derive a key from password using PBKDF2
const deriveKey = async (password, salt) => {
  const keyMaterial = await subtle.importKey(
    'raw',
    new TextEncoder().encode(password),
    'PBKDF2',
    false,
    ['deriveBits', 'deriveKey']
  );

  return subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: salt || randomBytes(SALT_LENGTH),
      iterations: ITERATIONS,
      hash: 'SHA-256',
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
};

// Encrypt data with AES-256-GCM
export const encrypt = async (data, key) => {
  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv(ALGORITHM, key, iv, { authTagLength: AUTH_TAG_LENGTH });
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const authTag = cipher.getAuthTag();
  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
};

// Decrypt data with AES-256-GCM
export const decrypt = async (encryptedData, key) => {
  const [ivHex, authTagHex, encryptedText] = encryptedData.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');
  const decipher = createDecipheriv(ALGORITHM, key, iv, { authTagLength: AUTH_TAG_LENGTH });
  decipher.setAuthTag(authTag);
  let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return JSON.parse(decrypted);
};

// Generate Ed25519 key pair for authentication
export const generateKeyPair = async () => {
  const keyPair = await subtle.generateKey(
    {
      name: 'ECDSA',
      namedCurve: 'P-256',
    },
    true,
    ['sign', 'verify']
  );
  return keyPair;
};

// Create HMAC signature
export const createSignature = (data, secret) => {
  const hmac = createHmac('sha256', secret);
  hmac.update(data);
  return hmac.digest('hex');
};

// Verify HMAC signature
export const verifySignature = (data, signature, secret) => {
  const expectedSignature = createSignature(data, secret);
  return signature === expectedSignature;
};

// Generate X25519 key pair for key exchange
export const generateKeyExchangePair = async () => {
  const keyPair = await subtle.generateKey(
    {
      name: 'ECDH',
      namedCurve: 'X25519',
    },
    true,
    ['deriveKey', 'deriveBits']
  );
  return keyPair;
};

export default {
  generateKey,
  deriveKey,
  encrypt,
  decrypt,
  generateKeyPair,
  createSignature,
  verifySignature,
  generateKeyExchangePair,
};
