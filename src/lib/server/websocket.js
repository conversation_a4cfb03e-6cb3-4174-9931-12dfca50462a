import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';

let io;

export function initializeWebSocket(server) {
  io = new Server(server, {
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? process.env.APP_URL 
        : ["http://localhost:5173", "http://localhost:4173"],
      methods: ["GET", "POST"]
    }
  });

  // Authentication middleware
  io.use((socket, next) => {
    const token = socket.handshake.auth.token;
    
    if (!token) {
      return next(new Error('Authentication error'));
    }

    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key');
      socket.userId = decoded.userId;
      next();
    } catch (err) {
      next(new Error('Authentication error'));
    }
  });

  // Connection handling
  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} connected`);

    // Join user to their personal room
    socket.join(`user:${socket.userId}`);

    // Handle joining chat rooms
    socket.on('join_chat', ({ chatId }) => {
      socket.join(`chat:${chatId}`);
      console.log(`User ${socket.userId} joined chat ${chatId}`);
    });

    // Handle leaving chat rooms
    socket.on('leave_chat', ({ chatId }) => {
      socket.leave(`chat:${chatId}`);
      console.log(`User ${socket.userId} left chat ${chatId}`);
    });

    // Handle sending messages
    socket.on('send_message', (message) => {
      // Broadcast to chat room
      socket.to(`chat:${message.chatId}`).emit('message', message);
      
      // Send delivery confirmation back to sender
      socket.emit('message_status', {
        messageId: message.id,
        status: 'delivered'
      });

      console.log(`Message sent from ${socket.userId} to chat ${message.chatId}`);
    });

    // Handle typing indicators
    socket.on('typing_start', ({ chatId }) => {
      socket.to(`chat:${chatId}`).emit('typing_start', {
        chatId,
        userId: socket.userId,
        userName: socket.userName || 'User'
      });
    });

    socket.on('typing_stop', ({ chatId }) => {
      socket.to(`chat:${chatId}`).emit('typing_stop', {
        chatId,
        userId: socket.userId,
        userName: socket.userName || 'User'
      });
    });

    // Handle marking messages as read
    socket.on('mark_read', ({ messageId }) => {
      // In a real app, you'd update the database here
      // Then notify the sender
      socket.broadcast.emit('message_status', {
        messageId,
        status: 'read'
      });
    });

    // Handle user going online/offline
    socket.on('user_status', ({ status }) => {
      socket.broadcast.emit(status === 'online' ? 'user_online' : 'user_offline', {
        userId: socket.userId,
        status
      });
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} disconnected`);
      
      // Notify others that user went offline
      socket.broadcast.emit('user_offline', socket.userId);
    });
  });

  return io;
}

export function getIO() {
  if (!io) {
    throw new Error('WebSocket server not initialized');
  }
  return io;
}
