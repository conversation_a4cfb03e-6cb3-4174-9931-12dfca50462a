<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { messagingService, contacts } from '$lib/stores/messaging';
  import TainoIcons from './TainoIcons.svelte';

  export let isOpen = false;

  const dispatch = createEventDispatcher();

  let isImporting = false;
  let importedContacts: any[] = [];
  let showResults = false;
  let boguaniUsers = 0;

  function handleClose() {
    if (!isImporting) {
      dispatch('close');
      resetModal();
    }
  }

  function resetModal() {
    importedContacts = [];
    showResults = false;
    boguaniUsers = 0;
    isImporting = false;
  }

  async function handleImportContacts() {
    isImporting = true;

    try {
      // In a real app, this would access the device's contacts
      // For demo, we'll simulate the import
      const mockContacts = await simulateContactImport();
      
      // Send to backend for processing
      const response = await fetch('/api/contacts/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('boguani_token')}`
        },
        body: JSON.stringify({ contacts: mockContacts })
      });

      if (response.ok) {
        const result = await response.json();
        importedContacts = result.contacts;
        boguaniUsers = result.boguaniUsers;
        showResults = true;

        // Update the contacts store
        contacts.set(importedContacts);
      } else {
        throw new Error('Failed to import contacts');
      }
    } catch (error) {
      console.error('Contact import failed:', error);
      alert('Failed to import contacts. Please try again.');
    } finally {
      isImporting = false;
    }
  }

  async function simulateContactImport() {
    // Simulate accessing device contacts
    return [
      { name: 'Maria Rodriguez', phoneNumbers: [{ number: '+1234567891' }] },
      { name: 'Carlos Mendez', phoneNumbers: [{ number: '+1234567892' }] },
      { name: 'Ana Delgado', phoneNumbers: [{ number: '+1234567893' }] },
      { name: 'Roberto Silva', phoneNumbers: [{ number: '+1555123456' }] },
      { name: 'Sofia Martinez', phoneNumbers: [{ number: '+1555654321' }] },
      { name: 'Diego Fernandez', phoneNumbers: [{ number: '+1555987654' }] },
      { name: 'Isabella Torres', phoneNumbers: [{ number: '+1555456789' }] },
      { name: 'Miguel Santos', phoneNumbers: [{ number: '+1555321654' }] }
    ];
  }

  async function handleStartChat(contact: any) {
    try {
      // Create a new chat room with this contact
      const chatId = await messagingService.createChatRoom([contact], 'direct');
      
      // Close modal and notify parent
      dispatch('chatCreated', { chatId, contact });
      handleClose();
    } catch (error) {
      console.error('Failed to start chat:', error);
      alert('Failed to start chat. Please try again.');
    }
  }

  function handleInviteToBoGuani(contact: any) {
    // In a real app, this would send an SMS invitation
    const message = `Hey ${contact.name}! I'm using BoGuani for secure messaging and payments. Download it here: https://boguani.app`;
    
    // Simulate SMS sending
    console.log(`📱 Sending invitation SMS to ${contact.phoneNumber}: ${message}`);
    alert(`Invitation sent to ${contact.name}!`);
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
    <div class="bg-gradient-to-br from-purple-900 to-purple-800 rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden">
      
      <!-- Background Taíno Elements -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-4 right-4">
          <TainoIcons icon="feather" size="lg" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-4 left-4">
          <TainoIcons icon="bird" size="md" opacity={0.08} color="#B8860B" />
        </div>
      </div>

      <!-- Header -->
      <div class="p-6 border-b border-purple-700/50 relative z-10">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">Import Contacts</h3>
              <p class="text-sm text-gray-300">Find friends on BoGuani</p>
            </div>
          </div>
          <button
            on:click={handleClose}
            disabled={isImporting}
            class="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors disabled:opacity-50"
          >
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6 relative z-10">
        
        {#if !showResults}
          <!-- Import Prompt -->
          <div class="text-center py-8">
            <div class="w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
              </svg>
            </div>
            
            <h4 class="text-xl font-bold text-white mb-3">Find Your Friends</h4>
            <p class="text-gray-300 mb-6 leading-relaxed">
              Import your phone contacts to see who's already on BoGuani and invite others to join.
            </p>

            <!-- Privacy Notice -->
            <div class="bg-purple-800/50 rounded-lg p-4 mb-6 text-left">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 rounded-full bg-gold-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg class="w-4 h-4 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h5 class="text-sm font-semibold text-white mb-1">Privacy Protected</h5>
                  <p class="text-xs text-gray-300">
                    Your contacts are encrypted and only used to find existing BoGuani users. We never store or share your contact information.
                  </p>
                </div>
              </div>
            </div>

            <button
              on:click={handleImportContacts}
              disabled={isImporting}
              class="w-full py-4 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center space-x-3"
            >
              {#if isImporting}
                <svg class="animate-spin w-5 h-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Importing Contacts...</span>
              {:else}
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                </svg>
                <span>Import Contacts</span>
              {/if}
            </button>
          </div>
        {:else}
          <!-- Import Results -->
          <div>
            <!-- Summary -->
            <div class="bg-gradient-to-r from-gold-500/20 to-gold-600/20 rounded-lg p-4 mb-6 border border-gold-500/30">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-lg font-semibold text-white">Import Complete!</h4>
                  <p class="text-sm text-gray-300">
                    Found {boguaniUsers} friends on BoGuani out of {importedContacts.length} contacts
                  </p>
                </div>
                <div class="w-12 h-12 rounded-full bg-gold-500 flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- Contacts List -->
            <div class="space-y-3 max-h-64 overflow-y-auto">
              {#each importedContacts as contact}
                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700/50">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center">
                      <span class="text-white font-semibold text-sm">
                        {contact.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h5 class="font-medium text-white">{contact.name}</h5>
                      <p class="text-xs text-gray-400">{contact.phoneNumber}</p>
                    </div>
                    {#if contact.isBoGuaniUser}
                      <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                        <span class="text-xs text-green-400 font-medium">On BoGuani</span>
                      </div>
                    {/if}
                  </div>

                  <div class="flex items-center space-x-2">
                    {#if contact.isBoGuaniUser}
                      <button
                        on:click={() => handleStartChat(contact)}
                        class="px-3 py-1.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white text-xs font-medium rounded-lg transition-all transform hover:scale-105"
                      >
                        Chat
                      </button>
                    {:else}
                      <button
                        on:click={() => handleInviteToBoGuani(contact)}
                        class="px-3 py-1.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-400 hover:to-blue-500 text-white text-xs font-medium rounded-lg transition-all transform hover:scale-105"
                      >
                        Invite
                      </button>
                    {/if}
                  </div>
                </div>
              {/each}
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3 pt-6">
              <button
                on:click={handleClose}
                class="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors"
              >
                Done
              </button>
              <button
                on:click={() => { showResults = false; handleImportContacts(); }}
                class="flex-1 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all transform hover:scale-105"
              >
                Refresh
              </button>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}
