<script lang="ts">
  import { onMount } from 'svelte';
  import type { Message } from '../stores/messaging';
  import TainoIcons from './TainoIcons.svelte';

  export let message: Message;
  export let isOwn: boolean = false;
  export let showAvatar: boolean = true;

  let bubbleElement: HTMLDivElement;

  onMount(() => {
    // Animate bubble entrance
    if (bubbleElement) {
      bubbleElement.style.opacity = '0';
      bubbleElement.style.transform = 'translateY(20px)';
      
      requestAnimationFrame(() => {
        bubbleElement.style.transition = 'all 0.3s ease-out';
        bubbleElement.style.opacity = '1';
        bubbleElement.style.transform = 'translateY(0)';
      });
    }
  });

  function formatTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  function getStatusIcon(status: string): string {
    switch (status) {
      case 'sending': return '⏳';
      case 'sent': return '✓';
      case 'delivered': return '✓✓';
      case 'read': return '✓✓';
      default: return '';
    }
  }

  function getPaymentStatusColor(status: string): string {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'pending': return 'text-yellow-400';
      case 'failed': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }
</script>

<div 
  bind:this={bubbleElement}
  class="chat-bubble-container {isOwn ? 'own-message' : 'other-message'}"
>
  <!-- Avatar for other users -->
  {#if !isOwn && showAvatar}
    <div class="avatar-container">
      <div class="avatar">
        {message.senderName.charAt(0).toUpperCase()}
      </div>
    </div>
  {/if}

  <!-- Message Content -->
  <div class="message-content">
    <!-- Sender name for group chats -->
    {#if !isOwn}
      <div class="sender-name">
        {message.senderName}
      </div>
    {/if}

    <!-- Message Bubble -->
    <div class="bubble {isOwn ? 'bubble-own' : 'bubble-other'} {message.type}">
      <!-- Taíno decorative element -->
      <div class="taino-decoration">
        <TainoIcons icon="spiral" size="sm" opacity={0.1} />
      </div>

      {#if message.type === 'text'}
        <div class="text-content">
          {message.content}
        </div>
      
      {:else if message.type === 'payment'}
        <div class="payment-content">
          <div class="payment-header">
            <div class="payment-icon">💰</div>
            <div class="payment-amount">
              ${message.paymentData?.amount?.toFixed(2) || '0.00'}
            </div>
          </div>
          
          <div class="payment-details">
            <div class="payment-description">
              {message.content || 'Payment'}
            </div>
            <div class="payment-status {getPaymentStatusColor(message.paymentData?.status || 'pending')}">
              {message.paymentData?.status || 'pending'}
            </div>
          </div>
        </div>

      {:else if message.type === 'call'}
        <div class="call-content">
          <div class="call-icon">
            {message.callData?.type === 'video' ? '📹' : '📞'}
          </div>
          <div class="call-details">
            <div class="call-type">
              {message.callData?.type === 'video' ? 'Video Call' : 'Voice Call'}
            </div>
            <div class="call-status">
              {#if message.callData?.status === 'completed'}
                Duration: {Math.floor((message.callData?.duration || 0) / 60)}:{String((message.callData?.duration || 0) % 60).padStart(2, '0')}
              {:else}
                {message.callData?.status || 'ended'}
              {/if}
            </div>
          </div>
        </div>

      {:else if message.type === 'system'}
        <div class="system-content">
          <TainoIcons icon="star" size="sm" opacity={0.6} />
          <span>{message.content}</span>
        </div>
      {/if}

      <!-- Message metadata -->
      <div class="message-meta">
        <span class="timestamp">{formatTime(message.timestamp)}</span>
        
        {#if isOwn}
          <span class="status-indicator {message.status}">
            {getStatusIcon(message.status)}
          </span>
        {/if}
      </div>
    </div>
  </div>

  <!-- Taíno background decoration -->
  <div class="background-decoration">
    <TainoIcons icon="wave" size="lg" opacity={0.05} />
  </div>
</div>

<style>
  .chat-bubble-container {
    display: flex;
    margin-bottom: 1rem;
    position: relative;
    max-width: 85%;
  }

  .own-message {
    flex-direction: row-reverse;
    margin-left: auto;
  }

  .other-message {
    flex-direction: row;
    margin-right: auto;
  }

  .avatar-container {
    flex-shrink: 0;
    margin-right: 0.75rem;
  }

  .avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #D4AF37, #B8860B);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #1a1a1a;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
  }

  .message-content {
    flex: 1;
    min-width: 0;
  }

  .sender-name {
    font-size: 0.75rem;
    color: #D4AF37;
    margin-bottom: 0.25rem;
    margin-left: 0.75rem;
    font-weight: 500;
  }

  .bubble {
    position: relative;
    padding: 0.75rem 1rem;
    border-radius: 1.25rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    overflow: hidden;
  }

  .bubble-own {
    background: linear-gradient(135deg, 
      rgba(212, 175, 55, 0.9) 0%, 
      rgba(184, 134, 11, 0.8) 100%);
    color: #1a1a1a;
    border-bottom-right-radius: 0.5rem;
    margin-left: 1rem;
  }

  .bubble-other {
    background: linear-gradient(135deg, 
      rgba(55, 65, 81, 0.9) 0%, 
      rgba(75, 85, 99, 0.8) 100%);
    color: #f3f4f6;
    border-bottom-left-radius: 0.5rem;
    margin-right: 1rem;
  }

  .taino-decoration {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    pointer-events: none;
  }

  .text-content {
    font-size: 0.95rem;
    line-height: 1.4;
    word-wrap: break-word;
  }

  .payment-content {
    min-width: 200px;
  }

  .payment-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .payment-icon {
    font-size: 1.5rem;
  }

  .payment-amount {
    font-size: 1.25rem;
    font-weight: bold;
  }

  .payment-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
  }

  .payment-description {
    opacity: 0.8;
  }

  .payment-status {
    font-weight: 500;
    text-transform: capitalize;
  }

  .call-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 150px;
  }

  .call-icon {
    font-size: 1.5rem;
  }

  .call-details {
    flex: 1;
  }

  .call-type {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .call-status {
    font-size: 0.875rem;
    opacity: 0.8;
  }

  .system-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-style: italic;
    opacity: 0.8;
    font-size: 0.875rem;
  }

  .message-meta {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    opacity: 0.7;
  }

  .timestamp {
    color: inherit;
  }

  .status-indicator {
    font-size: 0.75rem;
  }

  .status-indicator.sending {
    opacity: 0.5;
  }

  .status-indicator.sent {
    color: #9ca3af;
  }

  .status-indicator.delivered {
    color: #60a5fa;
  }

  .status-indicator.read {
    color: #34d399;
  }

  .background-decoration {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: -1;
  }

  /* Message type specific styles */
  .bubble.payment {
    border: 2px solid rgba(34, 197, 94, 0.3);
  }

  .bubble.call {
    border: 2px solid rgba(59, 130, 246, 0.3);
  }

  .bubble.system {
    background: linear-gradient(135deg, 
      rgba(139, 69, 19, 0.6) 0%, 
      rgba(160, 82, 45, 0.5) 100%);
    border: 1px solid rgba(212, 175, 55, 0.3);
    text-align: center;
    margin: 0 auto;
  }

  /* Hover effects */
  .bubble:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  }

  .bubble:hover .taino-decoration {
    opacity: 0.3;
  }
</style>
