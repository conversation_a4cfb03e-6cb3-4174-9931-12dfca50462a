<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';

  const dispatch = createEventDispatcher<{
    openWebChat: void;
  }>();

  let heroElement: HTMLElement;
  let isVisible = false;

  onMount(() => {
    if (!browser) return;

    // Animate hero on load
    setTimeout(() => {
      isVisible = true;
    }, 200);
  });

  function handleOpenWebChat() {
    dispatch('openWebChat');
  }

  function downloadMobile() {
    // For PWA installation
    window.location.href = '/auth';
  }

  function downloadDesktop() {
    // For desktop app (future)
    window.location.href = '/auth';
  }

  function watchDemo() {
    // Scroll to features section
    document.querySelector('#features')?.scrollIntoView({ behavior: 'smooth' });
  }
</script>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-purple-950 via-purple-900 to-indigo-950">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <!-- Floating Taíno Elements -->
    <div class="absolute top-20 left-10 animate-float">
      <TainoIcons icon="sun" size="xl" opacity={0.1} color="#D4AF37" />
    </div>
    <div class="absolute top-40 right-20 animate-float-delayed">
      <TainoIcons icon="spiral" size="lg" opacity={0.08} color="#B8860B" />
    </div>
    <div class="absolute bottom-32 left-32 animate-float">
      <TainoIcons icon="feather" size="md" opacity={0.06} color="#D4AF37" />
    </div>
    <div class="absolute top-1/2 right-10 animate-float-delayed">
      <TainoIcons icon="bird" size="lg" opacity={0.07} color="#B8860B" />
    </div>
    <div class="absolute bottom-20 right-40 animate-float">
      <TainoIcons icon="wave" size="xl" opacity={0.09} color="#D4AF37" />
    </div>
    <div class="absolute top-3/4 left-16 animate-float-delayed">
      <TainoIcons icon="turtle" size="md" opacity={0.05} color="#D4AF37" />
    </div>

    <!-- Gradient Orbs -->
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-gold-500/20 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-gold-500/20 to-purple-600/20 rounded-full blur-3xl animate-pulse-slow-delayed"></div>
    <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-indigo-600/15 to-purple-500/15 rounded-full blur-2xl animate-pulse-slow"></div>
  </div>
  
  <!-- Content -->
  <div bind:this={heroElement} class="relative z-10 text-center px-6 max-w-6xl mx-auto {isVisible ? 'animate-fade-in-up' : 'opacity-0 translate-y-8'}">
    <!-- Logo/Brand -->
    <div class="mb-12">
      <div class="inline-flex items-center space-x-6 mb-8">
        <div class="w-24 h-24 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center shadow-2xl border-4 border-gold-400/30">
          <TainoIcons icon="sun" size="lg" opacity={1} color="#FFFFFF" />
        </div>
        <div class="text-left">
          <h1 class="text-7xl md:text-9xl font-bold bg-gradient-to-r from-gold-400 via-gold-300 to-gold-500 bg-clip-text text-transparent font-serif">
            BoGuani
          </h1>
          <p class="text-2xl md:text-3xl text-purple-200 font-medium">Messenger of Value</p>
        </div>
      </div>
    </div>
    
    <!-- Main Headline -->
    <div class="mb-12">
      <h2 class="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-8 leading-tight">
        Where Words
        <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">
          Carry Worth
        </span>
      </h2>

      <!-- Subtitle -->
      <p class="text-xl md:text-2xl text-purple-200 mb-8 max-w-5xl mx-auto leading-relaxed">
        Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
        Send messages that matter, share moments that count, and transfer value instantly -
        all protected by sacred-level encryption.
      </p>

      <!-- Tagline -->
      <div class="mb-8">
        <p class="text-3xl md:text-4xl font-bold text-gold-400 mb-4">"Speak Gold. Share Value."</p>
        <div class="w-40 h-1 bg-gradient-to-r from-transparent via-gold-400 to-transparent mx-auto"></div>
      </div>
    </div>
    
    <!-- CTA Buttons -->
    <div class="flex flex-col lg:flex-row gap-6 justify-center items-center mb-16">
      <!-- Primary CTA -->
      <button
        on:click={handleOpenWebChat}
        class="group relative px-10 py-5 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-black font-bold text-xl rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-gold-500/40"
      >
        <span class="relative z-10 flex items-center space-x-3">
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
          </svg>
          <span>Open BoGuani Web Version</span>
        </span>
        <div class="absolute inset-0 bg-gradient-to-r from-gold-300 to-gold-400 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </button>

      <!-- Download Buttons -->
      <div class="flex flex-col sm:flex-row gap-4">
        <button
          on:click={downloadMobile}
          class="group px-6 py-4 bg-gradient-to-r from-purple-700 to-purple-800 hover:from-purple-600 hover:to-purple-700 text-white font-semibold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg border border-purple-500/30"
        >
          <span class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
            <span>Download Mobile App</span>
          </span>
        </button>

        <button
          on:click={downloadDesktop}
          class="group px-6 py-4 bg-gradient-to-r from-indigo-700 to-indigo-800 hover:from-indigo-600 hover:to-indigo-700 text-white font-semibold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg border border-indigo-500/30"
        >
          <span class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
            <span>Download Desktop</span>
          </span>
        </button>
      </div>

      <!-- Watch Demo -->
      <button
        on:click={watchDemo}
        class="group px-6 py-4 border-2 border-purple-400 hover:border-gold-400 text-purple-200 hover:text-gold-400 font-semibold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 backdrop-blur-sm"
      >
        <span class="flex items-center space-x-2">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
          <span>Watch Demo</span>
        </span>
      </button>
    </div>
    
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-fade-in">
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">256-bit</div>
        <div class="text-gray-200">End-to-End Encryption</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">Instant</div>
        <div class="text-gray-200">Money Transfers</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">24/7</div>
        <div class="text-gray-200">Global Support</div>
      </div>
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
    </svg>
  </div>
</section>
