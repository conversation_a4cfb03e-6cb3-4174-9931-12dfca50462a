<script lang="ts">
  import { createEventDispatcher, onMount } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';

  const dispatch = createEventDispatcher<{
    openWebChat: void;
  }>();

  let heroElement: HTMLElement;
  let isVisible = false;

  onMount(() => {
    if (!browser) return;

    // Animate hero on load
    setTimeout(() => {
      isVisible = true;
    }, 200);
  });

  function handleOpenWebChat() {
    dispatch('openWebChat');
  }

  function downloadMobile() {
    // For PWA installation
    window.location.href = '/auth';
  }

  function downloadDesktop() {
    // For desktop app (future)
    window.location.href = '/auth';
  }

  function watchDemo() {
    // Scroll to features section
    document.querySelector('#features')?.scrollIntoView({ behavior: 'smooth' });
  }
</script>

<!-- Epic Hero Section -->
<section class="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-950 via-purple-950 to-indigo-950">

  <!-- Cinematic Background -->
  <div class="absolute inset-0">
    <!-- Animated Grid -->
    <div class="absolute inset-0 bg-grid-pattern opacity-10 animate-grid-move"></div>

    <!-- Floating Particles -->
    <div class="absolute inset-0">
      {#each Array(20) as _, i}
        <div
          class="absolute w-1 h-1 bg-gold-400 rounded-full animate-float-particle"
          style="left: {Math.random() * 100}%; top: {Math.random() * 100}%; animation-delay: {Math.random() * 5}s; animation-duration: {3 + Math.random() * 4}s;"
        ></div>
      {/each}
    </div>

    <!-- Epic Gradient Orbs -->
    <div class="absolute top-0 left-0 w-[800px] h-[800px] bg-gradient-radial from-purple-500/30 via-purple-600/20 to-transparent rounded-full blur-3xl animate-pulse-epic"></div>
    <div class="absolute bottom-0 right-0 w-[600px] h-[600px] bg-gradient-radial from-gold-500/25 via-gold-600/15 to-transparent rounded-full blur-3xl animate-pulse-epic-delayed"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[400px] h-[400px] bg-gradient-radial from-indigo-500/20 via-indigo-600/10 to-transparent rounded-full blur-2xl animate-pulse-epic"></div>

    <!-- Floating Taíno Elements -->
    <div class="absolute top-20 left-20 animate-float-3d">
      <TainoIcons icon="sun" size="xl" opacity={0.15} color="#D4AF37" />
    </div>
    <div class="absolute top-40 right-32 animate-float-3d-delayed">
      <TainoIcons icon="spiral" size="lg" opacity={0.12} color="#B8860B" />
    </div>
    <div class="absolute bottom-40 left-40 animate-float-3d">
      <TainoIcons icon="feather" size="md" opacity={0.1} color="#D4AF37" />
    </div>
    <div class="absolute top-1/2 right-16 animate-float-3d-delayed">
      <TainoIcons icon="bird" size="lg" opacity={0.13} color="#B8860B" />
    </div>
    <div class="absolute bottom-32 right-1/3 animate-float-3d">
      <TainoIcons icon="wave" size="xl" opacity={0.14} color="#D4AF37" />
    </div>
  </div>

  <!-- Spotlight Effect -->
  <div class="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/40"></div>
  
  <!-- Epic Main Content -->
  <div bind:this={heroElement} class="relative z-20 flex items-center justify-center min-h-screen px-6">
    <div class="max-w-7xl mx-auto text-center {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-12'}">

      <!-- Floating Logo -->
      <div class="mb-16 relative">
        <div class="inline-block relative">
          <!-- Glow Effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-gold-400 to-gold-600 rounded-full blur-2xl opacity-30 animate-pulse-glow"></div>

          <!-- Main Logo -->
          <div class="relative w-32 h-32 md:w-40 md:h-40 rounded-full bg-gradient-to-br from-gold-400 via-gold-500 to-gold-600 flex items-center justify-center shadow-2xl border-4 border-gold-300/50 backdrop-blur-sm">
            <TainoIcons icon="sun" size="xl" opacity={1} color="#FFFFFF" />

            <!-- Rotating Ring -->
            <div class="absolute inset-0 rounded-full border-2 border-gold-300/30 animate-spin-slow"></div>
            <div class="absolute inset-2 rounded-full border border-gold-200/20 animate-spin-reverse"></div>
          </div>

          <!-- Floating Particles around Logo -->
          <div class="absolute -top-4 -right-4 w-3 h-3 bg-gold-400 rounded-full animate-bounce"></div>
          <div class="absolute -bottom-4 -left-4 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
          <div class="absolute top-1/2 -left-8 w-1 h-1 bg-gold-300 rounded-full animate-ping"></div>
        </div>
      </div>

      <!-- Epic Brand Name -->
      <div class="mb-12">
        <h1 class="text-8xl md:text-9xl lg:text-[12rem] font-black bg-gradient-to-r from-gold-300 via-gold-400 to-gold-500 bg-clip-text text-transparent font-serif leading-none tracking-tight drop-shadow-2xl">
          BoGuani
        </h1>

        <!-- Subtitle with Animation -->
        <div class="mt-6 relative">
          <div class="text-3xl md:text-4xl lg:text-5xl font-bold text-white/90 tracking-wide">
            Messenger of
            <span class="relative inline-block">
              <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">Value</span>
              <span class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-gold-400 to-gold-600 rounded-full animate-pulse block"></span>
            </span>
          </div>
        </div>
      </div>

      <!-- Epic Tagline -->
      <div class="mb-16 relative">
        <div class="bg-gradient-to-r from-purple-900/50 via-purple-800/50 to-purple-900/50 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-purple-700/30 shadow-2xl">
          <h2 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
            Where Words
            <span class="block bg-gradient-to-r from-gold-300 via-gold-400 to-gold-500 bg-clip-text text-transparent">
              Carry Worth
            </span>
          </h2>

          <p class="text-xl md:text-2xl lg:text-3xl text-purple-100 max-w-4xl mx-auto leading-relaxed mb-8">
            Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
            Send messages that matter, share moments that count, and transfer value instantly.
          </p>

          <!-- Sacred Quote -->
          <div class="relative">
            <div class="text-2xl md:text-3xl lg:text-4xl font-bold text-gold-400 mb-4 italic">
              "Speak Gold. Share Value."
            </div>
            <div class="w-24 h-1 bg-gradient-to-r from-transparent via-gold-400 to-transparent mx-auto"></div>
          </div>
        </div>
      </div>
    
    <!-- Main Headline -->
    <div class="mb-12">
      <h2 class="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-8 leading-tight">
        Where Words
        <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">
          Carry Worth
        </span>
      </h2>

      <!-- Subtitle -->
      <p class="text-xl md:text-2xl text-purple-200 mb-8 max-w-5xl mx-auto leading-relaxed">
        Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
        Send messages that matter, share moments that count, and transfer value instantly -
        all protected by sacred-level encryption.
      </p>

      <!-- Tagline -->
      <div class="mb-8">
        <p class="text-3xl md:text-4xl font-bold text-gold-400 mb-4">"Speak Gold. Share Value."</p>
        <div class="w-40 h-1 bg-gradient-to-r from-transparent via-gold-400 to-transparent mx-auto"></div>
      </div>
    </div>
    
      <!-- Epic CTA Buttons -->
      <div class="flex flex-col lg:flex-row gap-8 justify-center items-center mb-20">

        <!-- Primary CTA - Launch Web App -->
        <button
          on:click={handleOpenWebChat}
          class="group relative overflow-hidden px-12 py-6 bg-gradient-to-r from-gold-400 via-gold-500 to-gold-600 hover:from-gold-300 hover:via-gold-400 hover:to-gold-500 text-black font-black text-xl md:text-2xl rounded-2xl transition-all duration-500 transform hover:scale-110 shadow-2xl hover:shadow-gold-500/50"
        >
          <!-- Button Background Animation -->
          <div class="absolute inset-0 bg-gradient-to-r from-gold-200 via-gold-300 to-gold-400 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <!-- Shine Effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

          <span class="relative z-10 flex items-center space-x-4">
            <svg class="w-8 h-8 group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
            </svg>
            <span>Launch BoGuani Now</span>
            <svg class="w-6 h-6 group-hover:translate-x-2 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </span>
        </button>

        <!-- Download Options -->
        <div class="flex flex-col sm:flex-row gap-4">
          <!-- Mobile Download -->
          <button
            on:click={downloadMobile}
            class="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 hover:from-purple-500 hover:via-purple-600 hover:to-purple-700 text-white font-bold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-xl border border-purple-400/30"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-purple-400 via-purple-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span class="relative z-10 flex items-center space-x-3">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 011 1v8a2 2 0 01-2 2H6a2 2 0 01-2-2V7zM8 11a1 1 0 100 2h4a1 1 0 100-2H8z" />
              </svg>
              <span>Mobile App</span>
            </span>
          </button>

          <!-- Desktop Download -->
          <button
            on:click={downloadDesktop}
            class="group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-indigo-600 via-indigo-700 to-indigo-800 hover:from-indigo-500 hover:via-indigo-600 hover:to-indigo-700 text-white font-bold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-xl border border-indigo-400/30"
          >
            <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 via-indigo-500 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span class="relative z-10 flex items-center space-x-3">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
              </svg>
              <span>Desktop App</span>
            </span>
          </button>
        </div>

        <!-- Watch Demo -->
        <button
          on:click={watchDemo}
          class="group relative px-8 py-4 border-2 border-purple-400/50 hover:border-gold-400 text-purple-200 hover:text-gold-400 font-semibold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 backdrop-blur-xl bg-white/5 hover:bg-gold-400/10"
        >
          <span class="flex items-center space-x-3">
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
            </svg>
            <span>Watch Demo</span>
          </span>
        </button>
      </div>
    
      <!-- Epic Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">

        <!-- Stat 1 -->
        <div class="group text-center bg-gradient-to-br from-purple-800/30 via-purple-700/30 to-purple-900/30 backdrop-blur-xl rounded-3xl p-8 border border-purple-600/30 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl">
          <div class="w-16 h-16 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="text-4xl md:text-5xl font-black text-gold-400 mb-3 group-hover:text-gold-300 transition-colors duration-300">256-bit</div>
          <div class="text-lg text-purple-200 font-medium">End-to-End Encryption</div>
          <div class="text-sm text-purple-400 mt-2">Military-grade security</div>
        </div>

        <!-- Stat 2 -->
        <div class="group text-center bg-gradient-to-br from-purple-800/30 via-purple-700/30 to-purple-900/30 backdrop-blur-xl rounded-3xl p-8 border border-purple-600/30 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl">
          <div class="w-16 h-16 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="text-4xl md:text-5xl font-black text-gold-400 mb-3 group-hover:text-gold-300 transition-colors duration-300">Instant</div>
          <div class="text-lg text-purple-200 font-medium">Money Transfers</div>
          <div class="text-sm text-purple-400 mt-2">Send money like texting</div>
        </div>

        <!-- Stat 3 -->
        <div class="group text-center bg-gradient-to-br from-purple-800/30 via-purple-700/30 to-purple-900/30 backdrop-blur-xl rounded-3xl p-8 border border-purple-600/30 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl">
          <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="text-4xl md:text-5xl font-black text-gold-400 mb-3 group-hover:text-gold-300 transition-colors duration-300">24/7</div>
          <div class="text-lg text-purple-200 font-medium">Global Support</div>
          <div class="text-sm text-purple-400 mt-2">Always here for you</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Epic Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
    <div class="flex flex-col items-center space-y-2 animate-bounce">
      <div class="text-white/70 text-sm font-medium">Discover More</div>
      <div class="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center">
        <div class="w-1 h-3 bg-gold-400 rounded-full mt-2 animate-pulse"></div>
      </div>
    </div>
  </div>
</section>
