<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  const dispatch = createEventDispatcher<{
    openWebChat: void;
  }>();
  
  function handleOpenWebChat() {
    dispatch('openWebChat');
  }
</script>

<!-- Hero Section -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-800 via-primary-600 to-primary-400 opacity-90"></div>
  <div class="absolute inset-0 bg-dots opacity-30"></div>
  
  <style>
    .bg-dots {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
  </style>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400 bg-opacity-20 rounded-full animate-bounce"></div>
  <div class="absolute top-40 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
  <div class="absolute bottom-40 left-20 w-12 h-12 bg-yellow-200 bg-opacity-30 rounded-full animate-bounce" style="animation-delay: 1s"></div>
  <div class="absolute bottom-20 right-10 w-24 h-24 bg-purple-400 bg-opacity-20 rounded-full animate-pulse" style="animation-delay: 2s"></div>
  
  <!-- Content -->
  <div class="relative z-10 text-center px-6 max-w-6xl mx-auto">
    <!-- Logo -->
    <div class="mb-8 animate-fade-in">
      <div class="inline-flex items-center justify-center w-24 h-24 rounded-full bg-white bg-opacity-10 backdrop-blur-md border border-white border-opacity-20 mb-6">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </div>
      <h1 class="text-6xl md:text-8xl font-bold text-white mb-4 font-serif">
        <span class="text-gold">Bo</span>Guani
      </h1>
    </div>
    
    <!-- Tagline -->
    <div class="mb-12 animate-slide-up">
      <h2 class="text-2xl md:text-4xl font-semibold text-white mb-6">
        <span class="text-gold">Messenger of Value</span> - Where Words Carry Worth
      </h2>
      <p class="text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
        Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
        Send messages that matter, share moments that count, and transfer value instantly - all protected by sacred-level encryption.
      </p>
      <p class="text-lg text-gold mt-4 font-medium">
        "Speak Gold. Share Value."
      </p>
    </div>
    
    <!-- CTA Buttons -->
    <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16 animate-bounce-in">
      <button 
        class="group relative px-12 py-4 bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-bold text-xl rounded-full transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-yellow-400/50"
        on:click={handleOpenWebChat}
      >
        <span class="relative z-10 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Open BoGuani Web Version
        </span>
        <div class="absolute inset-0 bg-gradient-to-r from-yellow-300 to-yellow-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </button>
      
      <button class="px-12 py-4 bg-transparent border-2 border-white text-white font-bold text-xl rounded-full hover:bg-white hover:text-primary transition-all duration-300 transform hover:scale-105">
        Watch Demo
      </button>
    </div>
    
    <!-- Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto animate-fade-in">
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">256-bit</div>
        <div class="text-gray-200">End-to-End Encryption</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">Instant</div>
        <div class="text-gray-200">Money Transfers</div>
      </div>
      <div class="text-center">
        <div class="text-4xl font-bold text-yellow-400 mb-2">24/7</div>
        <div class="text-gray-200">Global Support</div>
      </div>
    </div>
  </div>
  
  <!-- Scroll Indicator -->
  <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
    </svg>
  </div>
</section>
