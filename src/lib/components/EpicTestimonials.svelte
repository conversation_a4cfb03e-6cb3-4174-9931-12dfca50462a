<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';
  
  let currentTestimonial = 0;
  let isVisible = false;
  
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Small Business Owner",
      location: "San Juan, Puerto Rico",
      avatar: "👩🏽‍💼",
      quote: "BoGuan<PERSON> has revolutionized how I handle payments with my customers. The Taíno-inspired design makes me proud of my heritage while conducting business.",
      rating: 5
    },
    {
      name: "<PERSON>", 
      role: "Freelance Designer",
      location: "Miami, Florida",
      avatar: "👨🏽‍💻",
      quote: "The security is incredible and the user experience is flawless. I can send money to my family in the Caribbean instantly. It's like magic!",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "University Student",
      location: "New York, NY",
      avatar: "👩🏽‍🎓",
      quote: "Finally, a messaging app that understands our culture! The gold accents and Taíno symbols make every conversation feel special and meaningful.",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "Tech Entrepreneur",
      location: "Los Angeles, CA", 
      avatar: "👨🏽‍💼",
      quote: "<PERSON><PERSON>uan<PERSON> combines the best of modern fintech with cultural authenticity. The encryption is military-grade and the design is absolutely beautiful.",
      rating: 5
    }
  ];
  
  onMount(() => {
    if (!browser) return;
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isVisible = true;
        }
      });
    }, { threshold: 0.3 });
    
    const element = document.getElementById('epic-testimonials');
    if (element) observer.observe(element);
    
    // Auto-rotate testimonials
    const interval = setInterval(() => {
      currentTestimonial = (currentTestimonial + 1) % testimonials.length;
    }, 5000);
    
    return () => clearInterval(interval);
  });
  
  function selectTestimonial(index: number) {
    currentTestimonial = index;
  }
</script>

<!-- Epic Testimonials Section -->
<section id="epic-testimonials" class="py-24 bg-gradient-to-br from-indigo-950 via-purple-950 to-slate-950 relative overflow-hidden">
  
  <!-- Background Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <!-- Animated Grid -->
    <div class="absolute inset-0 bg-grid-pattern opacity-5 animate-grid-move"></div>
    
    <!-- Floating Taíno Elements -->
    <div class="absolute top-32 left-16 animate-float-3d">
      <TainoIcons icon="feather" size="xl" opacity={0.08} color="#D4AF37" />
    </div>
    <div class="absolute bottom-40 right-24 animate-float-3d-delayed">
      <TainoIcons icon="bird" size="lg" opacity={0.06} color="#B8860B" />
    </div>
    <div class="absolute top-1/2 left-1/4 animate-float-3d">
      <TainoIcons icon="spiral" size="md" opacity={0.05} color="#D4AF37" />
    </div>
    
    <!-- Epic Gradient Orbs -->
    <div class="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-radial from-gold-500/15 via-gold-600/8 to-transparent rounded-full blur-3xl animate-pulse-epic"></div>
    <div class="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-radial from-purple-500/20 via-purple-600/10 to-transparent rounded-full blur-3xl animate-pulse-epic-delayed"></div>
  </div>
  
  <div class="container mx-auto px-6 relative z-10">
    
    <!-- Section Header -->
    <div class="text-center mb-20 {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 mb-8 shadow-2xl animate-pulse-glow">
        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
        </svg>
      </div>
      
      <h2 class="text-5xl md:text-7xl font-bold text-white mb-8 font-serif">
        Loved by <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">Our Community</span>
      </h2>
      
      <p class="text-xl md:text-2xl text-purple-200 max-w-4xl mx-auto leading-relaxed">
        Real stories from real people who've discovered the power of meaningful communication with BoGuani.
      </p>
    </div>
    
    <!-- Main Testimonial Display -->
    <div class="max-w-6xl mx-auto mb-16 {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}" style="animation-delay: 200ms;">
      
      <div class="bg-gradient-to-br from-purple-800/40 via-purple-700/40 to-purple-900/40 backdrop-blur-xl rounded-3xl p-12 border border-purple-600/30 shadow-2xl relative overflow-hidden">
        
        <!-- Shine Effect -->
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
        
        <!-- Quote Icon -->
        <div class="absolute top-8 left-8 w-16 h-16 rounded-full bg-gradient-to-br from-gold-500/20 to-gold-600/20 flex items-center justify-center">
          <svg class="w-8 h-8 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        
        <!-- Testimonial Content -->
        <div class="relative z-10 text-center">
          
          <!-- Avatar -->
          <div class="w-24 h-24 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-8 text-4xl shadow-2xl">
            {testimonials[currentTestimonial].avatar}
          </div>
          
          <!-- Quote -->
          <blockquote class="text-2xl md:text-3xl text-white font-medium leading-relaxed mb-8 italic">
            "{testimonials[currentTestimonial].quote}"
          </blockquote>
          
          <!-- Rating -->
          <div class="flex justify-center space-x-1 mb-6">
            {#each Array(testimonials[currentTestimonial].rating) as _}
              <svg class="w-6 h-6 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            {/each}
          </div>
          
          <!-- Author Info -->
          <div class="text-center">
            <div class="text-xl font-bold text-white mb-1">{testimonials[currentTestimonial].name}</div>
            <div class="text-gold-400 font-medium mb-1">{testimonials[currentTestimonial].role}</div>
            <div class="text-purple-300 text-sm">{testimonials[currentTestimonial].location}</div>
          </div>
        </div>
        
        <!-- Floating Particles -->
        <div class="absolute top-4 right-4 w-2 h-2 bg-gold-400 rounded-full animate-ping"></div>
        <div class="absolute bottom-4 left-4 w-1 h-1 bg-purple-400 rounded-full animate-pulse"></div>
      </div>
    </div>
    
    <!-- Testimonial Navigation -->
    <div class="flex justify-center space-x-4 mb-16 {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}" style="animation-delay: 400ms;">
      {#each testimonials as testimonial, index}
        <button
          on:click={() => selectTestimonial(index)}
          class="group relative w-16 h-16 rounded-full transition-all duration-300 transform hover:scale-110 {currentTestimonial === index ? 'bg-gradient-to-br from-gold-500 to-gold-600 shadow-lg' : 'bg-purple-800/50 hover:bg-purple-700/50'}"
        >
          <div class="text-2xl">{testimonial.avatar}</div>
          {#if currentTestimonial === index}
            <div class="absolute inset-0 rounded-full border-2 border-gold-300 animate-pulse"></div>
          {/if}
        </button>
      {/each}
    </div>
    
    <!-- Community Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}" style="animation-delay: 600ms;">
      
      <div class="text-center">
        <div class="text-3xl font-black text-gold-400 mb-2">10K+</div>
        <div class="text-sm text-purple-300">Happy Users</div>
      </div>
      
      <div class="text-center">
        <div class="text-3xl font-black text-gold-400 mb-2">$2M+</div>
        <div class="text-sm text-purple-300">Transferred</div>
      </div>
      
      <div class="text-center">
        <div class="text-3xl font-black text-gold-400 mb-2">50+</div>
        <div class="text-sm text-purple-300">Countries</div>
      </div>
      
      <div class="text-center">
        <div class="text-3xl font-black text-gold-400 mb-2">99.9%</div>
        <div class="text-sm text-purple-300">Uptime</div>
      </div>
    </div>
  </div>
</section>
