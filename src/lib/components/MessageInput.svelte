<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  
  export let placeholder: string = 'Type a message';
  export let disabled: boolean = false;
  
  let message = '';
  let isTyping = false;
  let typingTimeout: NodeJS.Timeout;
  
  const dispatch = createEventDispatcher<{
    send: { content: string; type: 'text' | 'payment' };
    typing: { isTyping: boolean };
    emoji: void;
    attachment: void;
  }>();
  
  function handleInput() {
    if (!isTyping) {
      isTyping = true;
      dispatch('typing', { isTyping: true });
    }
    
    clearTimeout(typingTimeout);
    typingTimeout = setTimeout(() => {
      isTyping = false;
      dispatch('typing', { isTyping: false });
    }, 1000);
  }
  
  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }
  
  function sendMessage() {
    if (!message.trim() || disabled) return;
    
    // Check if message contains payment pattern
    const paymentMatch = message.match(/\$(\d+(?:\.\d{2})?)/);
    
    if (paymentMatch) {
      const amount = parseFloat(paymentMatch[1]);
      dispatch('send', { 
        content: message.trim(), 
        type: 'payment'
      });
    } else {
      dispatch('send', { 
        content: message.trim(), 
        type: 'text' 
      });
    }
    
    message = '';
    
    if (isTyping) {
      isTyping = false;
      dispatch('typing', { isTyping: false });
    }
  }
  
  function handleEmojiClick() {
    dispatch('emoji');
  }
  
  function handleAttachmentClick() {
    dispatch('attachment');
  }
</script>

<div
  class="p-3 glass-effect border-t border-gray-700 animate-slide-up"
>
  <div class="flex items-center gap-2">
    <!-- Emoji Button -->
    <button 
      class="p-2 text-gray-400 hover:text-white focus:outline-none transition-colors duration-200 rounded-full hover:bg-dark-light hover:bg-opacity-30"
      on:click={handleEmojiClick}
      disabled={disabled}
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </button>
    
    <!-- Attachment Button -->
    <button 
      class="p-2 text-gray-400 hover:text-white focus:outline-none transition-colors duration-200 rounded-full hover:bg-dark-light hover:bg-opacity-30"
      on:click={handleAttachmentClick}
      disabled={disabled}
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
      </svg>
    </button>
    
    <!-- Message Input -->
    <input 
      type="text" 
      bind:value={message}
      on:input={handleInput}
      on:keypress={handleKeyPress}
      placeholder={placeholder}
      disabled={disabled}
      class="flex-1 py-2 px-4 bg-dark-light bg-opacity-50 rounded-full focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400 transition-all duration-200 focus:bg-opacity-70"
    />
    
    <!-- Send Button -->
    <button 
      class="p-2 bg-primary hover:bg-primary-dark text-white rounded-full focus:outline-none transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
      on:click={sendMessage}
      disabled={disabled || !message.trim()}
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>
  
  <!-- Payment Hint -->
  {#if message.includes('$')}
    <div
      class="mt-2 text-xs text-gold-light flex items-center gap-1 animate-fade-in"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>This will send a payment request</span>
    </div>
  {/if}
</div>
