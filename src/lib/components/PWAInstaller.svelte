<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';

  let deferredPrompt: any = null;
  let showInstallPrompt = false;
  let isInstalled = false;
  let isIOS = false;
  let isStandalone = false;

  onMount(() => {
    if (!browser) return;

    // Check if already installed
    isStandalone = window.matchMedia('(display-mode: standalone)').matches || 
                   (window.navigator as any).standalone === true;

    // Check if iOS
    isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    // Register service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('✅ Service Worker registered:', registration);
        })
        .catch((error) => {
          console.error('❌ Service Worker registration failed:', error);
        });
    }

    // Listen for beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('📱 PWA install prompt available');
      e.preventDefault();
      deferredPrompt = e;
      showInstallPrompt = true;
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('🎉 PWA was installed');
      isInstalled = true;
      showInstallPrompt = false;
      deferredPrompt = null;
    });

    // Check if already installed
    if (isStandalone) {
      isInstalled = true;
      showInstallPrompt = false;
    }
  });

  async function handleInstall() {
    if (!deferredPrompt) return;

    try {
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Wait for the user to respond
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('✅ User accepted the install prompt');
      } else {
        console.log('❌ User dismissed the install prompt');
      }
      
      deferredPrompt = null;
      showInstallPrompt = false;
    } catch (error) {
      console.error('❌ Install failed:', error);
    }
  }

  function handleDismiss() {
    showInstallPrompt = false;
    // Hide for this session
    sessionStorage.setItem('pwa-install-dismissed', 'true');
  }

  // Check if user already dismissed this session
  $: if (browser && sessionStorage.getItem('pwa-install-dismissed')) {
    showInstallPrompt = false;
  }
</script>

{#if showInstallPrompt && !isInstalled && !isStandalone}
  <div class="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
    <div class="bg-gradient-to-br from-purple-900 to-purple-800 rounded-2xl shadow-2xl border border-purple-700/50 overflow-hidden">
      
      <!-- Background Decoration -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-2 right-2">
          <TainoIcons icon="feather" size="md" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-2 left-2">
          <TainoIcons icon="spiral" size="sm" opacity={0.08} color="#B8860B" />
        </div>
      </div>

      <div class="p-4 relative z-10">
        <div class="flex items-start space-x-3">
          <!-- App Icon -->
          <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center flex-shrink-0">
            <TainoIcons icon="sun" size="md" opacity={1} color="#FFFFFF" />
          </div>

          <!-- Content -->
          <div class="flex-1 min-w-0">
            <h3 class="text-lg font-bold text-white mb-1">Install BoGuani</h3>
            <p class="text-sm text-gray-300 mb-3">
              Add BoGuani to your home screen for quick access and offline messaging.
            </p>

            <!-- Features -->
            <div class="space-y-1 mb-4">
              <div class="flex items-center space-x-2 text-xs text-gray-400">
                <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span>Works offline</span>
              </div>
              <div class="flex items-center space-x-2 text-xs text-gray-400">
                <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span>Push notifications</span>
              </div>
              <div class="flex items-center space-x-2 text-xs text-gray-400">
                <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
                <span>Native app experience</span>
              </div>
            </div>

            <!-- Buttons -->
            <div class="flex space-x-2">
              <button
                on:click={handleInstall}
                class="flex-1 py-2 px-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white text-sm font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                Install App
              </button>
              <button
                on:click={handleDismiss}
                class="py-2 px-3 bg-gray-700 hover:bg-gray-600 text-white text-sm font-medium rounded-lg transition-colors"
              >
                Later
              </button>
            </div>
          </div>

          <!-- Close Button -->
          <button
            on:click={handleDismiss}
            class="w-6 h-6 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors flex-shrink-0"
          >
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

{#if isIOS && !isStandalone && showInstallPrompt}
  <!-- iOS Install Instructions -->
  <div class="fixed bottom-4 left-4 right-4 z-50">
    <div class="bg-gradient-to-br from-blue-900 to-blue-800 rounded-2xl shadow-2xl border border-blue-700/50 p-4">
      <div class="flex items-start space-x-3">
        <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center flex-shrink-0">
          <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </div>

        <div class="flex-1">
          <h3 class="text-lg font-bold text-white mb-2">Install BoGuani</h3>
          <div class="space-y-2 text-sm text-gray-300">
            <div class="flex items-center space-x-2">
              <span>1.</span>
              <span>Tap the share button</span>
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
              </svg>
            </div>
            <div class="flex items-center space-x-2">
              <span>2.</span>
              <span>Select "Add to Home Screen"</span>
            </div>
            <div class="flex items-center space-x-2">
              <span>3.</span>
              <span>Tap "Add" to install</span>
            </div>
          </div>

          <button
            on:click={handleDismiss}
            class="mt-3 py-2 px-4 bg-blue-600 hover:bg-blue-500 text-white text-sm font-medium rounded-lg transition-colors"
          >
            Got it
          </button>
        </div>

        <button
          on:click={handleDismiss}
          class="w-6 h-6 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors flex-shrink-0"
        >
          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </div>
  </div>
{/if}
