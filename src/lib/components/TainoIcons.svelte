<script lang="ts">
  export let icon: 'sun' | 'moon' | 'bird' | 'wave' | 'spiral' | 'tree' | 'mountain' | 'star' | 'feather' | 'turtle';
  export let size: 'sm' | 'md' | 'lg' | 'xl' = 'md';
  export let opacity: number = 0.3;
  export let color: string = 'currentColor';

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };
</script>

<div class="taino-icon {sizeClasses[size]}" style="opacity: {opacity}; color: {color}">
  {#if icon === 'sun'}
    <!-- Taíno Sun Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="50" cy="50" r="20" fill="currentColor"/>
      <path d="M50 10 L55 25 L50 30 L45 25 Z" fill="currentColor"/>
      <path d="M90 50 L75 45 L70 50 L75 55 Z" fill="currentColor"/>
      <path d="M50 90 L45 75 L50 70 L55 75 Z" fill="currentColor"/>
      <path d="M10 50 L25 55 L30 50 L25 45 Z" fill="currentColor"/>
      <path d="M78.5 21.5 L68.5 28.5 L65 25 L71 18 Z" fill="currentColor"/>
      <path d="M78.5 78.5 L71 82 L65 75 L68.5 71.5 Z" fill="currentColor"/>
      <path d="M21.5 78.5 L28.5 71.5 L32 75 L25 82 Z" fill="currentColor"/>
      <path d="M21.5 21.5 L25 18 L32 25 L28.5 28.5 Z" fill="currentColor"/>
    </svg>
  {:else if icon === 'moon'}
    <!-- Taíno Moon Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M30 20 Q20 30 20 50 Q20 70 30 80 Q45 75 55 60 Q60 45 55 30 Q45 25 30 20 Z" fill="currentColor"/>
      <circle cx="35" cy="35" r="3" fill="currentColor" opacity="0.7"/>
      <circle cx="42" cy="45" r="2" fill="currentColor" opacity="0.7"/>
      <circle cx="38" cy="55" r="2.5" fill="currentColor" opacity="0.7"/>
    </svg>
  {:else if icon === 'bird'}
    <!-- Taíno Bird Symbol (Coquí inspired) -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M20 50 Q30 30 50 35 Q70 40 80 30 L85 35 Q75 45 60 45 L70 55 Q80 65 75 75 L70 70 Q60 60 45 65 Q30 70 20 50 Z" fill="currentColor"/>
      <circle cx="45" cy="45" r="3" fill="currentColor" opacity="0.8"/>
      <path d="M25 45 Q20 40 15 45 Q20 50 25 45 Z" fill="currentColor"/>
    </svg>
  {:else if icon === 'wave'}
    <!-- Taíno Wave Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 50 Q25 30 40 50 Q55 70 70 50 Q85 30 100 50 L95 55 Q80 35 65 55 Q50 75 35 55 Q20 35 5 55 Z" fill="currentColor"/>
      <path d="M10 65 Q25 45 40 65 Q55 85 70 65 Q85 45 100 65 L95 70 Q80 50 65 70 Q50 90 35 70 Q20 50 5 70 Z" fill="currentColor" opacity="0.6"/>
    </svg>
  {:else if icon === 'spiral'}
    <!-- Taíno Spiral Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M50 50 Q60 40 70 50 Q80 60 70 70 Q60 80 50 70 Q40 60 50 50 Q55 45 60 50 Q65 55 60 60 Q55 65 50 60 Q45 55 50 50" 
            stroke="currentColor" stroke-width="3" fill="none"/>
      <circle cx="50" cy="50" r="2" fill="currentColor"/>
    </svg>
  {:else if icon === 'tree'}
    <!-- Taíno Tree Symbol (Ceiba) -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="45" y="60" width="10" height="30" fill="currentColor"/>
      <path d="M50 60 Q30 40 20 30 Q35 35 50 50 Q65 35 80 30 Q70 40 50 60 Z" fill="currentColor"/>
      <path d="M50 50 Q25 30 15 20 Q30 25 50 40 Q70 25 85 20 Q75 30 50 50 Z" fill="currentColor" opacity="0.8"/>
      <path d="M50 40 Q20 20 10 10 Q25 15 50 30 Q75 15 90 10 Q80 20 50 40 Z" fill="currentColor" opacity="0.6"/>
    </svg>
  {:else if icon === 'mountain'}
    <!-- Taíno Mountain Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10 80 L30 40 L50 60 L70 20 L90 80 Z" fill="currentColor"/>
      <path d="M15 75 L25 50 L35 65 L45 35 L55 55 L65 25 L75 45 L85 75 Z" fill="currentColor" opacity="0.7"/>
    </svg>
  {:else if icon === 'star'}
    <!-- Taíno Star Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M50 10 L55 35 L80 35 L60 50 L70 75 L50 60 L30 75 L40 50 L20 35 L45 35 Z" fill="currentColor"/>
      <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8"/>
    </svg>
  {:else if icon === 'feather'}
    <!-- Taíno Feather Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M50 90 L48 20 Q50 10 52 20 L54 90" stroke="currentColor" stroke-width="2" fill="none"/>
      <path d="M48 30 Q35 25 30 35 Q35 40 48 35" fill="currentColor"/>
      <path d="M52 30 Q65 25 70 35 Q65 40 52 35" fill="currentColor"/>
      <path d="M48 45 Q30 40 25 50 Q30 55 48 50" fill="currentColor" opacity="0.8"/>
      <path d="M52 45 Q70 40 75 50 Q70 55 52 50" fill="currentColor" opacity="0.8"/>
      <path d="M48 60 Q25 55 20 65 Q25 70 48 65" fill="currentColor" opacity="0.6"/>
      <path d="M52 60 Q75 55 80 65 Q75 70 52 65" fill="currentColor" opacity="0.6"/>
    </svg>
  {:else if icon === 'turtle'}
    <!-- Taíno Turtle Symbol -->
    <svg viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
      <ellipse cx="50" cy="55" rx="30" ry="20" fill="currentColor"/>
      <circle cx="50" cy="55" r="15" fill="currentColor" opacity="0.8"/>
      <circle cx="45" cy="50" r="3" fill="currentColor" opacity="0.6"/>
      <circle cx="55" cy="50" r="3" fill="currentColor" opacity="0.6"/>
      <circle cx="50" cy="60" r="4" fill="currentColor" opacity="0.6"/>
      <ellipse cx="30" cy="45" rx="8" ry="5" fill="currentColor"/>
      <ellipse cx="70" cy="45" rx="8" ry="5" fill="currentColor"/>
      <ellipse cx="30" cy="65" rx="8" ry="5" fill="currentColor"/>
      <ellipse cx="70" cy="65" rx="8" ry="5" fill="currentColor"/>
      <ellipse cx="50" cy="35" rx="6" ry="8" fill="currentColor"/>
    </svg>
  {/if}
</div>

<style>
  .taino-icon {
    display: inline-block;
    transition: all 0.3s ease;
  }
  
  .taino-icon:hover {
    transform: scale(1.1);
    opacity: 0.8 !important;
  }
</style>
