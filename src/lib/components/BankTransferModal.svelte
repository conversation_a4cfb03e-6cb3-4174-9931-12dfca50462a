<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import type { BankAccount } from '../plaid.js';
  
  export let isOpen: boolean = false;
  export let recipientName: string = '';
  export let recipientHandle: string = '';
  export let accounts: BankAccount[] = [];
  export let defaultAmount: number = 0;
  
  let amount = defaultAmount;
  let selectedAccount: BankAccount | null = accounts[0] || null;
  let note = '';
  let isProcessing = false;
  
  const dispatch = createEventDispatcher<{
    close: void;
    transfer: {
      amount: number;
      accountId: string;
      note: string;
    };
  }>();
  
  function closeModal() {
    if (!isProcessing) {
      dispatch('close');
    }
  }
  
  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  }
  
  async function handleTransfer() {
    if (!selectedAccount || amount <= 0 || isProcessing) return;
    
    isProcessing = true;
    
    try {
      dispatch('transfer', {
        amount,
        accountId: selectedAccount.id,
        note: note.trim()
      });
    } finally {
      isProcessing = false;
    }
  }
  
  $: if (defaultAmount !== amount && defaultAmount > 0) {
    amount = defaultAmount;
  }
</script>

{#if isOpen}
  <!-- Modal Backdrop -->
  <div
    class="modal-backdrop fixed inset-0 flex items-center justify-center z-50 animate-fade-in"
    on:click={handleBackdropClick}
  >
    <!-- Modal Content -->
    <div
      class="glass-effect rounded-xl shadow-2xl w-full max-w-md mx-4 border border-gray-700 animate-bounce-in"
    >
      <!-- Header -->
      <div class="p-5 border-b border-gray-700">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <span class="text-gold-light mr-2">Send</span> Money
          </h3>
          <button 
            class="text-gray-400 hover:text-white focus:outline-none transition-colors duration-200"
            on:click={closeModal}
            disabled={isProcessing}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Body -->
      <div class="p-5">
        <!-- Recipient Info -->
        <div class="flex items-center mb-5">
          <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
            {recipientName.charAt(0).toUpperCase()}
          </div>
          <div>
            <h4 class="font-medium text-white">{recipientName}</h4>
            <p class="text-xs text-gray-400">@{recipientHandle}</p>
          </div>
        </div>
        
        <!-- Amount Input -->
        <div class="mb-5">
          <label class="block text-gray-300 text-sm font-medium mb-2">Amount</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gold-light text-lg">$</span>
            </div>
            <input 
              type="number" 
              bind:value={amount}
              min="0.01"
              step="0.01"
              disabled={isProcessing}
              class="pl-8 block w-full rounded-lg bg-dark-light bg-opacity-50 border border-gray-700 p-2.5 focus:outline-none focus:ring-1 focus:ring-primary-light focus:border-primary-light text-white disabled:opacity-50" 
              placeholder="0.00"
            />
          </div>
        </div>
        
        <!-- Account Selection -->
        {#if accounts.length > 0}
          <div class="mb-5">
            <label class="block text-gray-300 text-sm font-medium mb-2">From</label>
            <div class="space-y-2">
              {#each accounts as account}
                <button
                  class="w-full glass-effect-light rounded-lg p-3 flex items-center border border-gray-700 hover:bg-dark-light hover:bg-opacity-70 transition-colors duration-200 {selectedAccount?.id === account.id ? 'ring-1 ring-primary-light' : ''}"
                  on:click={() => selectedAccount = account}
                  disabled={isProcessing}
                >
                  <div class="w-8 h-8 bg-primary-light bg-opacity-30 rounded-full flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                    </svg>
                  </div>
                  <div class="flex-1 text-left">
                    <h4 class="font-medium text-sm text-white">{account.name}</h4>
                    <p class="text-xs text-gray-400">**** {account.mask}</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm text-white">${account.balance.toFixed(2)}</p>
                  </div>
                </button>
              {/each}
            </div>
          </div>
        {:else}
          <div class="mb-5 p-4 bg-yellow-900 bg-opacity-30 border border-yellow-600 rounded-lg">
            <p class="text-yellow-200 text-sm">No bank accounts connected. Please connect a bank account first.</p>
          </div>
        {/if}
        
        <!-- Note Input -->
        <div class="mb-5">
          <label class="block text-gray-300 text-sm font-medium mb-2">Note (optional)</label>
          <input 
            type="text" 
            bind:value={note}
            disabled={isProcessing}
            class="block w-full rounded-lg bg-dark-light bg-opacity-50 border border-gray-700 p-2.5 focus:outline-none focus:ring-1 focus:ring-primary-light focus:border-primary-light text-white disabled:opacity-50" 
            placeholder="What's it for?"
          />
        </div>
      </div>
      
      <!-- Footer -->
      <div class="p-4 bg-dark-DEFAULT bg-opacity-70 rounded-b-xl border-t border-gray-700">
        <button 
          class="w-full gold-gradient text-dark-dark py-2.5 rounded-lg hover:opacity-90 focus:outline-none transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          on:click={handleTransfer}
          disabled={isProcessing || !selectedAccount || amount <= 0 || accounts.length === 0}
        >
          {#if isProcessing}
            <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          {:else}
            Send ${amount.toFixed(2)}
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}
