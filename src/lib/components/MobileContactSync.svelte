<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';

  export let isOpen = false;

  const dispatch = createEventDispatcher();

  let isSupported = false;
  let isPermissionGranted = false;
  let contacts: any[] = [];
  let isLoading = false;

  $: if (browser) {
    // Check if Contact API is supported
    isSupported = 'contacts' in navigator && 'ContactsManager' in window;
  }

  async function requestContactPermission() {
    if (!isSupported) {
      alert('Contact access is not supported on this device. You can manually add contacts instead.');
      return;
    }

    try {
      isLoading = true;
      
      // Request contact access (this is a newer API, may not be widely supported)
      const props = ['name', 'tel'];
      const opts = { multiple: true };
      
      // @ts-ignore - Contact API is experimental
      const contactList = await navigator.contacts.select(props, opts);
      
      contacts = contactList.map((contact: any) => ({
        name: contact.name?.[0] || 'Unknown',
        phoneNumbers: contact.tel || []
      }));

      isPermissionGranted = true;
      
      // Send contacts to parent
      dispatch('contactsImported', { contacts });
      
    } catch (error) {
      console.error('Contact access failed:', error);
      
      // Fallback: Show manual contact entry
      showManualContactEntry();
    } finally {
      isLoading = false;
    }
  }

  function showManualContactEntry() {
    // For devices that don't support contact API, show manual entry
    alert('Contact access not available. You can add contacts manually in the app.');
    dispatch('close');
  }

  function handleSkip() {
    dispatch('skip');
  }

  function handleClose() {
    dispatch('close');
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4">
    <div class="w-full max-w-md bg-gradient-to-br from-purple-900 to-purple-800 rounded-2xl shadow-2xl overflow-hidden">
      
      <!-- Background Decoration -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-4 right-4">
          <TainoIcons icon="feather" size="lg" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-4 left-4">
          <TainoIcons icon="bird" size="md" opacity={0.08} color="#B8860B" />
        </div>
      </div>

      <!-- Header -->
      <div class="p-6 border-b border-purple-700/50 relative z-10">
        <div class="text-center">
          <div class="w-16 h-16 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
            </svg>
          </div>
          <h2 class="text-2xl font-bold text-white mb-2">Sync Your Contacts</h2>
          <p class="text-gray-300">Find friends who are already using BoGuani</p>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6 relative z-10">
        
        {#if !isPermissionGranted}
          <!-- Permission Request -->
          <div class="text-center space-y-6">
            
            <!-- Benefits -->
            <div class="space-y-3">
              <div class="flex items-center space-x-3 text-sm text-gray-300">
                <div class="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
                <span>Find friends already on BoGuani</span>
              </div>
              
              <div class="flex items-center space-x-3 text-sm text-gray-300">
                <div class="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
                <span>Start conversations instantly</span>
              </div>
              
              <div class="flex items-center space-x-3 text-sm text-gray-300">
                <div class="w-6 h-6 rounded-full bg-green-500/20 flex items-center justify-center">
                  <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </div>
                <span>Send money to contacts easily</span>
              </div>
            </div>

            <!-- Privacy Notice -->
            <div class="bg-purple-800/50 rounded-lg p-4 text-left">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 rounded-full bg-gold-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg class="w-4 h-4 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 class="text-sm font-semibold text-white mb-1">Your Privacy is Protected</h4>
                  <p class="text-xs text-gray-300">
                    Contacts are processed securely and only used to find existing BoGuani users. We never store or share your contact information.
                  </p>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
              <button
                on:click={requestContactPermission}
                disabled={isLoading}
                class="w-full py-4 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center space-x-3"
              >
                {#if isLoading}
                  <svg class="animate-spin w-5 h-5 text-white" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Accessing Contacts...</span>
                {:else}
                  <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                  </svg>
                  <span>Sync Contacts</span>
                {/if}
              </button>

              <button
                on:click={handleSkip}
                class="w-full py-3 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors"
              >
                Skip for Now
              </button>
            </div>
          </div>
        {:else}
          <!-- Success State -->
          <div class="text-center space-y-4">
            <div class="w-16 h-16 rounded-full bg-green-500 flex items-center justify-center mx-auto">
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
            
            <div>
              <h3 class="text-xl font-bold text-white mb-2">Contacts Synced!</h3>
              <p class="text-gray-300">Found {contacts.length} contacts</p>
            </div>

            <button
              on:click={handleClose}
              class="w-full py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Continue to Chat
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}
