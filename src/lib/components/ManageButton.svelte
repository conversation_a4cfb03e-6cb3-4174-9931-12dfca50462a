<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { plaidService, bankAccounts } from '$lib/services/plaid';
  import { contacts } from '$lib/stores/messaging';
  import TainoIcons from './TainoIcons.svelte';

  const dispatch = createEventDispatcher();

  let showMenu = false;
  let showSubMenu = '';

  function toggleMenu() {
    showMenu = !showMenu;
    if (!showMenu) {
      showSubMenu = '';
    }
  }

  function handleAction(action: string, data?: any) {
    dispatch('action', { action, data });
    showMenu = false;
    showSubMenu = '';
  }

  function toggleSubMenu(menu: string) {
    showSubMenu = showSubMenu === menu ? '' : menu;
  }

  // Close menu when clicking outside
  function handleClickOutside(event: MouseEvent) {
    const target = event.target as HTMLElement;
    if (!target.closest('.manage-menu')) {
      showMenu = false;
      showSubMenu = '';
    }
  }
</script>

<svelte:window on:click={handleClickOutside} />

<div class="manage-menu relative">
  <!-- Manage Button -->
  <button
    on:click|stopPropagation={toggleMenu}
    class="group relative w-14 h-14 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 shadow-lg hover:shadow-gold-500/30 flex items-center justify-center transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-gold-500/30"
    aria-label="Manage"
  >
    <!-- Taíno Decoration -->
    <div class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-80 transition-opacity duration-300">
      <TainoIcons icon="spiral" size="sm" opacity={0.8} color="#FFFFFF" />
    </div>
    
    <!-- Main Icon -->
    <svg 
      class="w-7 h-7 text-white transition-transform duration-300 {showMenu ? 'rotate-45' : ''}" 
      fill="currentColor" 
      viewBox="0 0 20 20"
    >
      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
    </svg>
    
    <!-- Ripple Effect -->
    <div class="absolute inset-0 rounded-full bg-gold-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
  </button>

  <!-- Menu Dropdown -->
  {#if showMenu}
    <div class="absolute bottom-16 right-0 w-72 bg-gradient-to-br from-purple-900 to-purple-800 rounded-2xl shadow-2xl border border-purple-700/50 overflow-hidden z-50 animate-fade-in">
      
      <!-- Background Decoration -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-2 right-2">
          <TainoIcons icon="feather" size="md" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-2 left-2">
          <TainoIcons icon="wave" size="sm" opacity={0.08} color="#B8860B" />
        </div>
      </div>

      <!-- Header -->
      <div class="p-4 border-b border-purple-700/50 relative z-10">
        <h3 class="text-lg font-bold text-white flex items-center space-x-2">
          <TainoIcons icon="sun" size="sm" opacity={0.8} color="#D4AF37" />
          <span>BoGuani Manager</span>
        </h3>
        <p class="text-sm text-gray-300">Messenger of Value</p>
      </div>

      <!-- Menu Items -->
      <div class="p-2 space-y-1 relative z-10">
        
        <!-- Contacts Section -->
        <div class="relative">
          <button
            on:click|stopPropagation={() => toggleSubMenu('contacts')}
            class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-purple-700/50 transition-colors group"
          >
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
              </div>
              <div class="text-left">
                <p class="text-white font-medium">Contacts</p>
                <p class="text-xs text-gray-400">{$contacts.length} contacts</p>
              </div>
            </div>
            <svg class="w-4 h-4 text-gray-400 transition-transform {showSubMenu === 'contacts' ? 'rotate-90' : ''}" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>

          {#if showSubMenu === 'contacts'}
            <div class="ml-4 mt-1 space-y-1 border-l-2 border-purple-600/50 pl-3">
              <button
                on:click={() => handleAction('importContacts')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
                </svg>
                <span class="text-sm text-white">Import Contacts</span>
              </button>
              <button
                on:click={() => handleAction('newChat')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-white">New Chat</span>
              </button>
              <button
                on:click={() => handleAction('createGroup')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                </svg>
                <span class="text-sm text-white">Create Group</span>
              </button>
            </div>
          {/if}
        </div>

        <!-- Banking Section -->
        <div class="relative">
          <button
            on:click|stopPropagation={() => toggleSubMenu('banking')}
            class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-purple-700/50 transition-colors group"
          >
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 rounded-lg bg-green-500/20 flex items-center justify-center">
                <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                  <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="text-left">
                <p class="text-white font-medium">Banking</p>
                <p class="text-xs text-gray-400">{$bankAccounts.length} accounts</p>
              </div>
            </div>
            <svg class="w-4 h-4 text-gray-400 transition-transform {showSubMenu === 'banking' ? 'rotate-90' : ''}" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </button>

          {#if showSubMenu === 'banking'}
            <div class="ml-4 mt-1 space-y-1 border-l-2 border-purple-600/50 pl-3">
              <button
                on:click={() => handleAction('connectBank')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-white">Connect Bank</span>
              </button>
              <button
                on:click={() => handleAction('manageBanking')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-white">Manage Accounts</span>
              </button>
              <button
                on:click={() => handleAction('transactionHistory')}
                class="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-purple-600/30 transition-colors text-left"
              >
                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="text-sm text-white">Transaction History</span>
              </button>
            </div>
          {/if}
        </div>

        <!-- Settings Section -->
        <button
          on:click={() => handleAction('settings')}
          class="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-purple-700/50 transition-colors"
        >
          <div class="w-8 h-8 rounded-lg bg-gray-500/20 flex items-center justify-center">
            <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="text-left">
            <p class="text-white font-medium">Settings</p>
            <p class="text-xs text-gray-400">Privacy & preferences</p>
          </div>
        </button>

        <!-- Quick Actions -->
        <div class="border-t border-purple-700/50 pt-2 mt-2">
          <p class="text-xs text-gray-400 px-3 py-1 font-medium">Quick Actions</p>
          
          <div class="grid grid-cols-2 gap-2 p-2">
            <button
              on:click={() => handleAction('sendMoney')}
              class="flex flex-col items-center space-y-1 p-3 rounded-lg hover:bg-purple-700/50 transition-colors"
            >
              <div class="w-8 h-8 rounded-lg bg-gold-500/20 flex items-center justify-center">
                <svg class="w-5 h-5 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs text-white font-medium">Send $</span>
            </button>

            <button
              on:click={() => handleAction('requestMoney')}
              class="flex flex-col items-center space-y-1 p-3 rounded-lg hover:bg-purple-700/50 transition-colors"
            >
              <div class="w-8 h-8 rounded-lg bg-blue-500/20 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="text-xs text-white font-medium">Request $</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .animate-fade-in {
    animation: fadeIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
</style>
