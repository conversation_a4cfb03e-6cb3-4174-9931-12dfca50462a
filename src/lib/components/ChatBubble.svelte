<script lang="ts">
  import type { Message } from '../websocket.js';
  
  export let message: Message;
  export let isOwn: boolean = false;
  export let showAvatar: boolean = true;
  export let senderName: string = '';
  
  function formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }
  
  function getStatusIcon(status: Message['status']): string {
    switch (status) {
      case 'sending': return '⏳';
      case 'sent': return '✓';
      case 'delivered': return '✓✓';
      case 'read': return '✓✓';
      default: return '';
    }
  }
</script>

<div
  class="flex mb-4 {isOwn ? 'justify-end' : 'justify-start'} animate-fade-in"
>
  {#if !isOwn && showAvatar}
    <div class="w-8 h-8 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-2 text-sm">
      {senderName.charAt(0).toUpperCase()}
    </div>
  {/if}
  
  <div class="chat-bubble {isOwn ? 'sent' : 'received'} p-3 shadow-lg">
    {#if message.type === 'payment'}
      <div class="glass-effect-light rounded-lg p-3 mb-1 border border-primary-light">
        <div class="flex items-center justify-between">
          <span class="font-semibold text-gold-light">
            {isOwn ? 'Payment Sent' : 'Payment Received'}
          </span>
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            class="h-5 w-5 {message.paymentData?.status === 'completed' ? 'text-green-400' : message.paymentData?.status === 'failed' ? 'text-red-400' : 'text-yellow-400'}" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            {#if message.paymentData?.status === 'completed'}
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            {:else if message.paymentData?.status === 'failed'}
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            {:else}
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
            {/if}
          </svg>
        </div>
        <div class="text-lg font-bold text-white">
          ${message.paymentData?.amount?.toFixed(2) || '0.00'}
        </div>
        <div class="text-xs text-gray-300">
          {isOwn ? `To: ${senderName}` : `From: ${senderName}`}
        </div>
        {#if message.paymentData?.status === 'pending'}
          <div class="text-xs text-yellow-400 mt-1">Processing...</div>
        {:else if message.paymentData?.status === 'failed'}
          <div class="text-xs text-red-400 mt-1">Failed</div>
        {/if}
      </div>
    {:else if message.type === 'text'}
      <p class="{isOwn ? 'text-white' : 'text-gray-200'}">{message.content}</p>
    {:else if message.type === 'image'}
      <img src={message.content} alt="Shared image" class="max-w-xs rounded-lg" />
    {/if}
    
    <div class="chat-time text-right {isOwn ? 'text-gray-200' : 'text-gray-300'} flex items-center justify-end gap-1">
      <span>{formatTime(message.timestamp)}</span>
      {#if isOwn}
        <span class="text-xs {message.status === 'read' ? 'text-blue-400' : 'text-gray-400'}">
          {getStatusIcon(message.status)}
        </span>
      {/if}
    </div>
  </div>
</div>
