<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { webrtcService, callState, localStream, remoteStream } from '$lib/services/webrtc';
  import TainoIcons from './TainoIcons.svelte';

  let localVideo: HTMLVideoElement;
  let remoteVideo: HTMLVideoElement;
  let callDuration = 0;
  let callTimer: NodeJS.Timeout;

  $: if ($callState.isActive && $callState.status === 'connected') {
    startCallTimer();
  } else {
    stopCallTimer();
  }

  $: if ($localStream && localVideo) {
    localVideo.srcObject = $localStream;
  }

  $: if ($remoteStream && remoteVideo) {
    remoteVideo.srcObject = $remoteStream;
  }

  function startCallTimer() {
    if (callTimer) return;
    
    callTimer = setInterval(() => {
      callDuration++;
    }, 1000);
  }

  function stopCallTimer() {
    if (callTimer) {
      clearInterval(callTimer);
      callTimer = null;
    }
    callDuration = 0;
  }

  function formatDuration(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }

  function handleEndCall() {
    webrtcService.endCall();
  }

  function handleToggleMute() {
    webrtcService.toggleMute();
  }

  function handleToggleVideo() {
    webrtcService.toggleVideo();
  }

  function handleToggleSpeaker() {
    webrtcService.toggleSpeaker();
  }

  onDestroy(() => {
    stopCallTimer();
  });
</script>

{#if $callState.isActive}
  <div class="fixed inset-0 z-50 bg-black bg-opacity-95 flex items-center justify-center">
    <div class="w-full h-full max-w-4xl mx-auto relative">
      
      <!-- Call Header -->
      <div class="absolute top-0 left-0 right-0 z-20 p-6">
        <div class="flex items-center justify-between text-white">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center">
              {$callState.participant?.name?.charAt(0).toUpperCase() || 'U'}
            </div>
            <div>
              <h3 class="font-semibold text-lg">{$callState.participant?.name || 'Unknown'}</h3>
              <p class="text-sm text-gray-300">
                {#if $callState.status === 'connecting'}
                  Connecting...
                {:else if $callState.status === 'ringing'}
                  Ringing...
                {:else if $callState.status === 'connected'}
                  {formatDuration(callDuration)}
                {:else}
                  {$callState.status}
                {/if}
              </p>
            </div>
          </div>

          <!-- Call Type Indicator -->
          <div class="flex items-center space-x-2">
            <TainoIcons icon="wave" size="sm" opacity={0.6} color="#D4AF37" />
            <span class="text-sm text-gold-300">
              {$callState.callType === 'video' ? 'Video Call' : 'Voice Call'}
            </span>
          </div>
        </div>
      </div>

      <!-- Video Container -->
      {#if $callState.callType === 'video'}
        <div class="relative w-full h-full">
          <!-- Remote Video (Main) -->
          <video
            bind:this={remoteVideo}
            autoplay
            playsinline
            class="w-full h-full object-cover bg-gray-900"
          ></video>

          <!-- Local Video (Picture-in-Picture) -->
          <div class="absolute top-20 right-6 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-gold-500/30">
            <video
              bind:this={localVideo}
              autoplay
              playsinline
              muted
              class="w-full h-full object-cover"
            ></video>
          </div>

          <!-- Video Disabled Overlay -->
          {#if !$callState.isVideoEnabled}
            <div class="absolute inset-0 bg-gradient-to-br from-purple-900 to-purple-800 flex items-center justify-center">
              <div class="text-center">
                <div class="w-32 h-32 rounded-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center mx-auto mb-4">
                  <span class="text-4xl font-bold text-white">
                    {$callState.participant?.name?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
                <p class="text-white text-lg">{$callState.participant?.name || 'Unknown'}</p>
                <p class="text-gray-300">Video is off</p>
              </div>
            </div>
          {/if}
        </div>
      {:else}
        <!-- Voice Call UI -->
        <div class="w-full h-full bg-gradient-to-br from-purple-900 via-purple-800 to-purple-900 flex items-center justify-center relative overflow-hidden">
          <!-- Background Taíno Elements -->
          <div class="absolute inset-0 pointer-events-none">
            <div class="absolute top-20 left-20">
              <TainoIcons icon="spiral" size="xl" opacity={0.1} color="#D4AF37" />
            </div>
            <div class="absolute bottom-20 right-20">
              <TainoIcons icon="sun" size="lg" opacity={0.08} color="#B8860B" />
            </div>
            <div class="absolute top-1/2 left-1/4">
              <TainoIcons icon="feather" size="md" opacity={0.06} color="#D4AF37" />
            </div>
          </div>

          <!-- Call Content -->
          <div class="text-center z-10">
            <div class="w-48 h-48 rounded-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center mx-auto mb-8 shadow-2xl border-4 border-gold-500/30">
              <span class="text-6xl font-bold text-white">
                {$callState.participant?.name?.charAt(0).toUpperCase() || 'U'}
              </span>
            </div>
            <h2 class="text-3xl font-bold text-white mb-2">{$callState.participant?.name || 'Unknown'}</h2>
            <p class="text-xl text-gray-300 mb-8">
              {#if $callState.status === 'connecting'}
                Connecting...
              {:else if $callState.status === 'ringing'}
                Ringing...
              {:else if $callState.status === 'connected'}
                {formatDuration(callDuration)}
              {:else}
                {$callState.status}
              {/if}
            </p>

            <!-- Audio Visualizer -->
            {#if $callState.status === 'connected'}
              <div class="flex justify-center space-x-1 mb-8">
                {#each Array(5) as _, i}
                  <div 
                    class="w-1 bg-gold-400 rounded-full animate-pulse"
                    style="height: {Math.random() * 20 + 10}px; animation-delay: {i * 0.1}s"
                  ></div>
                {/each}
              </div>
            {/if}
          </div>
        </div>
      {/if}

      <!-- Call Controls -->
      <div class="absolute bottom-0 left-0 right-0 p-8">
        <div class="flex justify-center space-x-6">
          
          <!-- Mute Button -->
          <button
            on:click={handleToggleMute}
            class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 {$callState.isMuted ? 'bg-red-500 hover:bg-red-600' : 'bg-gray-700 hover:bg-gray-600'}"
          >
            {#if $callState.isMuted}
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM16.707 9.293a1 1 0 010 1.414L15.414 12l1.293 1.293a1 1 0 01-1.414 1.414L14 13.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 12l-1.293-1.293a1 1 0 011.414-1.414L14 10.586l1.293-1.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            {:else}
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            {/if}
          </button>

          <!-- Video Toggle (only for video calls) -->
          {#if $callState.callType === 'video'}
            <button
              on:click={handleToggleVideo}
              class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 {$callState.isVideoEnabled ? 'bg-gray-700 hover:bg-gray-600' : 'bg-red-500 hover:bg-red-600'}"
            >
              {#if $callState.isVideoEnabled}
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                </svg>
              {:else}
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A2 2 0 0018 13V7a1 1 0 00-1.447-.894l-2 1A1 1 0 0014 8v.586l-2-2V6a2 2 0 00-2-2H8.586l-2-2H10a4 4 0 014 4v.586l-2-2V6a2 2 0 00-2-2H8.586l-5.293-5.293z" clip-rule="evenodd" />
                </svg>
              {/if}
            </button>
          {/if}

          <!-- End Call Button -->
          <button
            on:click={handleEndCall}
            class="w-20 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
          >
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
          </button>

          <!-- Speaker Toggle -->
          <button
            on:click={handleToggleSpeaker}
            class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 {$callState.isSpeakerOn ? 'bg-gold-500 hover:bg-gold-600' : 'bg-gray-700 hover:bg-gray-600'}"
          >
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.797-3.793a1 1 0 011.617.793zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  video {
    background: #1a1a1a;
  }
  
  @keyframes pulse-audio {
    0%, 100% { height: 10px; }
    50% { height: 30px; }
  }
  
  .animate-pulse-audio {
    animation: pulse-audio 1s ease-in-out infinite;
  }
</style>
