<script lang="ts">
  import TainoIcons from './TainoIcons.svelte';
  
  function downloadMobile() {
    // For PWA installation
    window.location.href = '/auth';
  }
  
  function downloadDesktop() {
    // For desktop app (future)
    window.location.href = '/auth';
  }
  
  function downloadAndroid() {
    // For Android APK (future)
    window.location.href = '/auth';
  }
  
  function downloadIOS() {
    // For iOS App Store (future)
    window.location.href = '/auth';
  }
</script>

<!-- Download Section -->
<section class="py-24 bg-gradient-to-br from-purple-950 via-indigo-950 to-purple-900 relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-20 left-20 animate-float">
      <TainoIcons icon="sun" size="xl" opacity={0.08} color="#D4AF37" />
    </div>
    <div class="absolute top-40 right-32 animate-float-delayed">
      <TainoIcons icon="spiral" size="lg" opacity={0.06} color="#B8860B" />
    </div>
    <div class="absolute bottom-32 left-40 animate-float">
      <TainoIcons icon="feather" size="md" opacity={0.05} color="#D4AF37" />
    </div>
    <div class="absolute bottom-20 right-20 animate-float-delayed">
      <TainoIcons icon="bird" size="xl" opacity={0.07} color="#B8860B" />
    </div>
    
    <!-- Gradient Orbs -->
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-600/15 to-gold-500/15 rounded-full blur-3xl animate-pulse-slow"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-gold-500/15 to-purple-600/15 rounded-full blur-3xl animate-pulse-slow-delayed"></div>
  </div>
  
  <div class="container mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 mb-8 shadow-2xl">
        <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
        </svg>
      </div>
      
      <h2 class="text-5xl md:text-7xl font-bold text-white mb-8 font-serif">
        Download <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">BoGuani</span>
      </h2>
      
      <p class="text-xl md:text-2xl text-purple-200 max-w-4xl mx-auto leading-relaxed mb-8">
        Get BoGuani on all your devices. Available as a web app, mobile app, and desktop application.
        Start your journey where words carry worth.
      </p>
      
      <div class="w-32 h-1 bg-gradient-to-r from-transparent via-gold-400 to-transparent mx-auto"></div>
    </div>
    
    <!-- Download Options -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto mb-16">
      
      <!-- Mobile PWA -->
      <div class="group bg-gradient-to-br from-purple-800/50 to-purple-900/50 rounded-3xl p-8 backdrop-blur-sm border border-purple-700/50 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
        <div class="text-center">
          <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center mx-auto mb-6 group-hover:from-gold-500 group-hover:to-gold-600 transition-all duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 011 1v8a2 2 0 01-2 2H6a2 2 0 01-2-2V7zM8 11a1 1 0 100 2h4a1 1 0 100-2H8z" />
            </svg>
          </div>
          
          <h3 class="text-xl font-bold text-white mb-3">Mobile App</h3>
          <p class="text-purple-300 text-sm mb-6">Progressive Web App that works like a native mobile app</p>
          
          <div class="space-y-2 mb-6">
            <div class="flex items-center justify-center space-x-2 text-xs text-purple-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Works offline</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-purple-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Push notifications</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-purple-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Install to home screen</span>
            </div>
          </div>
          
          <button
            on:click={downloadMobile}
            class="w-full py-3 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            Install Mobile App
          </button>
        </div>
      </div>
      
      <!-- Desktop App -->
      <div class="group bg-gradient-to-br from-indigo-800/50 to-indigo-900/50 rounded-3xl p-8 backdrop-blur-sm border border-indigo-700/50 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
        <div class="text-center">
          <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-indigo-600 to-indigo-700 flex items-center justify-center mx-auto mb-6 group-hover:from-gold-500 group-hover:to-gold-600 transition-all duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
            </svg>
          </div>
          
          <h3 class="text-xl font-bold text-white mb-3">Desktop App</h3>
          <p class="text-indigo-300 text-sm mb-6">Native desktop application for Windows, Mac, and Linux</p>
          
          <div class="space-y-2 mb-6">
            <div class="flex items-center justify-center space-x-2 text-xs text-indigo-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Native performance</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-indigo-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>System integration</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-indigo-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Auto-updates</span>
            </div>
          </div>
          
          <button
            on:click={downloadDesktop}
            class="w-full py-3 bg-gradient-to-r from-indigo-600 to-indigo-700 hover:from-indigo-500 hover:to-indigo-600 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            Download Desktop
          </button>
        </div>
      </div>
      
      <!-- Android -->
      <div class="group bg-gradient-to-br from-green-800/50 to-green-900/50 rounded-3xl p-8 backdrop-blur-sm border border-green-700/50 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
        <div class="text-center">
          <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-600 to-green-700 flex items-center justify-center mx-auto mb-6 group-hover:from-gold-500 group-hover:to-gold-600 transition-all duration-300">
            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1521-.5676.416.416 0 00-.5676.1521l-2.0223 3.503C15.5902 8.2439 13.8533 7.8508 12 7.8508s-3.5902.3931-5.1367 1.0989L4.841 5.4467a.4161.4161 0 00-.5677-.1521.4157.4157 0 00-.1521.5676l1.9973 3.4592C2.6889 11.1867.3432 14.6589 0 18.761h24c-.3435-4.1021-2.6892-7.5743-6.1185-9.4396"/>
            </svg>
          </div>
          
          <h3 class="text-xl font-bold text-white mb-3">Android</h3>
          <p class="text-green-300 text-sm mb-6">Native Android app from Google Play Store</p>
          
          <div class="space-y-2 mb-6">
            <div class="flex items-center justify-center space-x-2 text-xs text-green-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Google Play Store</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-green-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Android 8.0+</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-green-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Coming soon</span>
            </div>
          </div>
          
          <button
            on:click={downloadAndroid}
            class="w-full py-3 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-500 hover:to-green-600 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            Get on Android
          </button>
        </div>
      </div>
      
      <!-- iOS -->
      <div class="group bg-gradient-to-br from-blue-800/50 to-blue-900/50 rounded-3xl p-8 backdrop-blur-sm border border-blue-700/50 hover:border-gold-400/50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
        <div class="text-center">
          <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center mx-auto mb-6 group-hover:from-gold-500 group-hover:to-gold-600 transition-all duration-300">
            <svg class="w-8 h-8 text-white" viewBox="0 0 24 24" fill="currentColor">
              <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
            </svg>
          </div>
          
          <h3 class="text-xl font-bold text-white mb-3">iOS</h3>
          <p class="text-blue-300 text-sm mb-6">Native iOS app from the App Store</p>
          
          <div class="space-y-2 mb-6">
            <div class="flex items-center justify-center space-x-2 text-xs text-blue-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>App Store</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-blue-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>iOS 14.0+</span>
            </div>
            <div class="flex items-center justify-center space-x-2 text-xs text-blue-400">
              <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
              <span>Coming soon</span>
            </div>
          </div>
          
          <button
            on:click={downloadIOS}
            class="w-full py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105"
          >
            Get on iOS
          </button>
        </div>
      </div>
    </div>
    
    <!-- Web Version CTA -->
    <div class="text-center">
      <div class="bg-gradient-to-br from-gold-500/20 to-gold-600/20 rounded-3xl p-12 backdrop-blur-sm border border-gold-400/30 max-w-4xl mx-auto">
        <div class="w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-8">
          <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </div>
        
        <h3 class="text-3xl md:text-4xl font-bold text-white mb-6">
          Or Try the <span class="text-gold-400">Web Version</span> Now
        </h3>
        
        <p class="text-xl text-purple-200 mb-8 max-w-2xl mx-auto">
          No download required. Start using BoGuani instantly in your browser with full functionality.
        </p>
        
        <a
          href="/auth"
          class="inline-flex items-center space-x-3 px-10 py-5 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-black font-bold text-xl rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-gold-500/40"
        >
          <span>Launch Web App</span>
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </a>
      </div>
    </div>
  </div>
</section>
