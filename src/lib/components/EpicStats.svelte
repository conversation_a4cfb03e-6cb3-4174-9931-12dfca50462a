<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import TainoIcons from './TainoIcons.svelte';
  
  let isVisible = false;
  let counters = [
    { current: 0, target: 256, suffix: '-bit', label: 'Encryption', description: 'Military-grade security' },
    { current: 0, target: 100, suffix: '%', label: 'Uptime', description: 'Always available' },
    { current: 0, target: 24, suffix: '/7', label: 'Support', description: 'Global assistance' }
  ];
  
  onMount(() => {
    if (!browser) return;
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting && !isVisible) {
          isVisible = true;
          animateCounters();
        }
      });
    }, { threshold: 0.3 });
    
    const element = document.getElementById('epic-stats');
    if (element) observer.observe(element);
  });
  
  function animateCounters() {
    counters.forEach((counter, index) => {
      const duration = 2000; // 2 seconds
      const steps = 60;
      const increment = counter.target / steps;
      let current = 0;
      
      const timer = setInterval(() => {
        current += increment;
        if (current >= counter.target) {
          current = counter.target;
          clearInterval(timer);
        }
        counters[index].current = Math.floor(current);
      }, duration / steps);
    });
  }
</script>

<!-- Epic Stats Section -->
<section id="epic-stats" class="py-24 bg-gradient-to-br from-slate-950 via-purple-950 to-indigo-950 relative overflow-hidden">
  
  <!-- Background Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <!-- Animated Grid -->
    <div class="absolute inset-0 bg-grid-pattern opacity-5 animate-grid-move"></div>
    
    <!-- Floating Elements -->
    <div class="absolute top-20 left-20 animate-float-3d">
      <TainoIcons icon="star" size="lg" opacity={0.08} color="#D4AF37" />
    </div>
    <div class="absolute top-40 right-32 animate-float-3d-delayed">
      <TainoIcons icon="moon" size="md" opacity={0.06} color="#B8860B" />
    </div>
    <div class="absolute bottom-32 left-40 animate-float-3d">
      <TainoIcons icon="tree" size="xl" opacity={0.07} color="#D4AF37" />
    </div>
    
    <!-- Epic Gradient Orbs -->
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-purple-500/20 via-purple-600/10 to-transparent rounded-full blur-3xl animate-pulse-epic"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-radial from-gold-500/15 via-gold-600/8 to-transparent rounded-full blur-3xl animate-pulse-epic-delayed"></div>
  </div>
  
  <div class="container mx-auto px-6 relative z-10">
    
    <!-- Section Header -->
    <div class="text-center mb-20 {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 mb-8 shadow-2xl animate-pulse-glow">
        <TainoIcons icon="sun" size="lg" opacity={1} color="#FFFFFF" />
      </div>
      
      <h2 class="text-5xl md:text-7xl font-bold text-white mb-8 font-serif">
        Trusted by <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">Thousands</span>
      </h2>
      
      <p class="text-xl md:text-2xl text-purple-200 max-w-4xl mx-auto leading-relaxed">
        Join the growing community of users who trust BoGuani for secure communication and instant value transfer.
      </p>
    </div>
    
    <!-- Epic Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-20">
      
      {#each counters as counter, index}
        <div class="group relative {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}" style="animation-delay: {index * 200}ms;">
          
          <!-- Card Background -->
          <div class="bg-gradient-to-br from-purple-800/40 via-purple-700/40 to-purple-900/40 backdrop-blur-xl rounded-3xl p-8 border border-purple-600/30 hover:border-gold-400/50 transition-all duration-500 transform hover:scale-105 shadow-2xl hover:shadow-gold-500/20 relative overflow-hidden">
            
            <!-- Shine Effect -->
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            
            <!-- Icon -->
            <div class="w-20 h-20 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
              {#if index === 0}
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                </svg>
              {:else if index === 1}
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              {:else}
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                </svg>
              {/if}
            </div>
            
            <!-- Counter -->
            <div class="text-center relative z-10">
              <div class="text-5xl md:text-6xl font-black text-gold-400 mb-3 group-hover:text-gold-300 transition-colors duration-300">
                {counter.current}{counter.suffix}
              </div>
              <div class="text-xl text-white font-bold mb-2">{counter.label}</div>
              <div class="text-sm text-purple-300">{counter.description}</div>
            </div>
            
            <!-- Floating Particles -->
            <div class="absolute top-4 right-4 w-2 h-2 bg-gold-400 rounded-full animate-ping"></div>
            <div class="absolute bottom-4 left-4 w-1 h-1 bg-purple-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      {/each}
    </div>
    
    <!-- Trust Indicators -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto {isVisible ? 'animate-epic-entrance' : 'opacity-0 translate-y-8'}" style="animation-delay: 800ms;">
      
      <!-- Bank Grade Security -->
      <div class="text-center group">
        <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-600 to-green-700 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
            <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="text-sm font-semibold text-white mb-1">Bank Grade</div>
        <div class="text-xs text-purple-300">Security</div>
      </div>
      
      <!-- GDPR Compliant -->
      <div class="text-center group">
        <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-600 to-blue-700 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="text-sm font-semibold text-white mb-1">GDPR</div>
        <div class="text-xs text-purple-300">Compliant</div>
      </div>
      
      <!-- ISO Certified -->
      <div class="text-center group">
        <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="text-sm font-semibold text-white mb-1">ISO</div>
        <div class="text-xs text-purple-300">Certified</div>
      </div>
      
      <!-- 24/7 Monitoring -->
      <div class="text-center group">
        <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-gold-600 to-gold-700 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="text-sm font-semibold text-white mb-1">24/7</div>
        <div class="text-xs text-purple-300">Monitoring</div>
      </div>
    </div>
  </div>
</section>
