<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { plaidService, bankAccounts } from '$lib/services/plaid';
  import TainoIcons from './TainoIcons.svelte';

  export let isOpen = false;
  export let recipientName = '';
  export let recipientPhone = '';
  export let isGroupChat = false;
  export let groupParticipants: Array<{id: string, name: string, phoneNumber: string}> = [];

  const dispatch = createEventDispatcher();

  let amount = '';
  let note = '';
  let selectedAccountId = '';
  let selectedRecipients: string[] = [];
  let isProcessing = false;
  let showBankSetup = false;

  // Initialize selected account
  $: if ($bankAccounts.length > 0 && !selectedAccountId) {
    selectedAccountId = $bankAccounts.find(acc => acc.isDefault)?.id || $bankAccounts[0]?.id || '';
  }

  // For group chats, initialize with all participants
  $: if (isGroupChat && groupParticipants.length > 0 && selectedRecipients.length === 0) {
    selectedRecipients = groupParticipants.map(p => p.id);
  }

  function handleClose() {
    if (!isProcessing) {
      dispatch('close');
      resetForm();
    }
  }

  function resetForm() {
    amount = '';
    note = '';
    selectedRecipients = [];
    isProcessing = false;
  }

  function toggleRecipient(participantId: string) {
    if (selectedRecipients.includes(participantId)) {
      selectedRecipients = selectedRecipients.filter(id => id !== participantId);
    } else {
      selectedRecipients = [...selectedRecipients, participantId];
    }
  }

  async function handleSendPayment() {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Please enter a valid amount');
      return;
    }

    if (!selectedAccountId) {
      alert('Please select a bank account');
      return;
    }

    if (isGroupChat && selectedRecipients.length === 0) {
      alert('Please select at least one recipient');
      return;
    }

    isProcessing = true;

    try {
      const paymentAmount = parseFloat(amount);
      
      if (isGroupChat) {
        // Split payment among selected recipients
        const splitAmount = paymentAmount / selectedRecipients.length;
        
        for (const recipientId of selectedRecipients) {
          const participant = groupParticipants.find(p => p.id === recipientId);
          if (participant) {
            await plaidService.sendMoney(
              participant.phoneNumber,
              splitAmount,
              `${note} (Split payment)`,
              selectedAccountId
            );
          }
        }
        
        dispatch('payment', {
          type: 'group',
          amount: paymentAmount,
          splitAmount,
          recipients: selectedRecipients,
          note,
          accountId: selectedAccountId
        });
      } else {
        // Single payment
        await plaidService.sendMoney(recipientPhone, paymentAmount, note, selectedAccountId);
        
        dispatch('payment', {
          type: 'single',
          amount: paymentAmount,
          recipient: recipientPhone,
          note,
          accountId: selectedAccountId
        });
      }

      handleClose();
    } catch (error) {
      console.error('Payment failed:', error);
      alert('Payment failed. Please try again.');
    } finally {
      isProcessing = false;
    }
  }

  async function handleConnectBank() {
    try {
      await plaidService.openPlaidLink();
      showBankSetup = false;
    } catch (error) {
      console.error('Failed to connect bank:', error);
      alert('Failed to connect bank account. Please try again.');
    }
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 z-50 bg-black bg-opacity-75 flex items-center justify-center p-4">
    <div class="bg-gradient-to-br from-purple-900 to-purple-800 rounded-2xl shadow-2xl w-full max-w-md relative overflow-hidden">
      
      <!-- Background Taíno Elements -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-4 right-4">
          <TainoIcons icon="spiral" size="lg" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-4 left-4">
          <TainoIcons icon="sun" size="md" opacity={0.08} color="#B8860B" />
        </div>
      </div>

      <!-- Header -->
      <div class="p-6 border-b border-purple-700/50 relative z-10">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-bold text-white">Send Money</h3>
              <p class="text-sm text-gray-300">
                {#if isGroupChat}
                  Split payment with {groupParticipants.length} people
                {:else}
                  To {recipientName}
                {/if}
              </p>
            </div>
          </div>
          <button
            on:click={handleClose}
            disabled={isProcessing}
            class="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors disabled:opacity-50"
          >
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 relative z-10">
        
        {#if $bankAccounts.length === 0}
          <!-- No Bank Account Setup -->
          <div class="text-center py-8">
            <div class="w-16 h-16 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <h4 class="text-lg font-semibold text-white mb-2">Connect Your Bank</h4>
            <p class="text-gray-300 mb-6">Connect a bank account to send money securely</p>
            <button
              on:click={handleConnectBank}
              class="px-6 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all duration-300 transform hover:scale-105"
            >
              Connect Bank Account
            </button>
          </div>
        {:else}
          <!-- Payment Form -->
          
          <!-- Amount Input -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Amount</label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-2xl font-bold text-gold-400">$</span>
              <input
                type="number"
                bind:value={amount}
                placeholder="0.00"
                step="0.01"
                min="0.01"
                disabled={isProcessing}
                class="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white text-xl font-semibold placeholder-gray-400 focus:border-gold-500 focus:ring-2 focus:ring-gold-500/20 transition-all disabled:opacity-50"
              />
            </div>
          </div>

          <!-- Group Recipients (for group chats) -->
          {#if isGroupChat}
            <div>
              <label class="block text-sm font-medium text-gray-300 mb-3">
                Split with ({selectedRecipients.length} selected)
              </label>
              <div class="space-y-2 max-h-32 overflow-y-auto">
                {#each groupParticipants as participant}
                  <label class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700/50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedRecipients.includes(participant.id)}
                      on:change={() => toggleRecipient(participant.id)}
                      disabled={isProcessing}
                      class="w-4 h-4 text-gold-500 bg-gray-700 border-gray-600 rounded focus:ring-gold-500 focus:ring-2"
                    />
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center text-white text-sm font-semibold">
                      {participant.name.charAt(0).toUpperCase()}
                    </div>
                    <span class="text-white">{participant.name}</span>
                  </label>
                {/each}
              </div>
              {#if selectedRecipients.length > 0 && amount}
                <p class="text-sm text-gray-400 mt-2">
                  Each person pays: <span class="text-gold-400 font-semibold">${(parseFloat(amount) / selectedRecipients.length).toFixed(2)}</span>
                </p>
              {/if}
            </div>
          {/if}

          <!-- Note -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Note (optional)</label>
            <input
              type="text"
              bind:value={note}
              placeholder="What's this for?"
              disabled={isProcessing}
              class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-gold-500 focus:ring-2 focus:ring-gold-500/20 transition-all disabled:opacity-50"
            />
          </div>

          <!-- Bank Account Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">From Account</label>
            <select
              bind:value={selectedAccountId}
              disabled={isProcessing}
              class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:border-gold-500 focus:ring-2 focus:ring-gold-500/20 transition-all disabled:opacity-50"
            >
              {#each $bankAccounts as account}
                <option value={account.id}>
                  {account.name} •••• {account.accountNumber.slice(-4)} - ${account.balance.toFixed(2)}
                </option>
              {/each}
            </select>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3 pt-4">
            <button
              on:click={handleClose}
              disabled={isProcessing}
              class="flex-1 py-3 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              on:click={handleSendPayment}
              disabled={isProcessing || !amount || parseFloat(amount) <= 0 || (isGroupChat && selectedRecipients.length === 0)}
              class="flex-1 py-3 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-semibold rounded-lg transition-all transform hover:scale-105 disabled:opacity-50 disabled:hover:scale-100 flex items-center justify-center space-x-2"
            >
              {#if isProcessing}
                <svg class="animate-spin w-5 h-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Sending...</span>
              {:else}
                <span>Send ${amount || '0.00'}</span>
              {/if}
            </button>
          </div>
        {/if}
      </div>
    </div>
  </div>
{/if}
