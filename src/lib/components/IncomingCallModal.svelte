<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { webrtcService, callState } from '$lib/services/webrtc';
  import TainoIcons from './TainoIcons.svelte';

  let isRinging = false;

  $: if ($callState.isIncoming && $callState.status === 'ringing') {
    startRinging();
  } else {
    stopRinging();
  }

  function startRinging() {
    if (isRinging) return;
    isRinging = true;

    // Play ringtone using Web Audio API
    try {
      if ((window as any).playRingtone) {
        const interval = setInterval(() => {
          if (isRinging) {
            (window as any).playRingtone();
          } else {
            clearInterval(interval);
          }
        }, 1000);
      }
    } catch (error) {
      console.log('Ringtone not available:', error);
    }

    // Vibrate if supported
    if ('vibrate' in navigator) {
      navigator.vibrate([1000, 500, 1000, 500, 1000]);
    }
  }

  function stopRinging() {
    isRinging = false;

    if ('vibrate' in navigator) {
      navigator.vibrate(0);
    }
  }

  function handleAnswer() {
    stopRinging();
    webrtcService.answerCall();
  }

  function handleDecline() {
    stopRinging();
    webrtcService.declineCall();
  }

  onMount(() => {
    // Create ringtone audio element - use a simple beep for now
    try {
      // Create a simple audio context beep instead of loading a file
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();

      // Create a simple ringtone function
      (window as any).playRingtone = () => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.5);
      };
    } catch (error) {
      console.log('Audio context not available:', error);
    }
  });

  onDestroy(() => {
    stopRinging();
  });
</script>

{#if $callState.isIncoming && $callState.status === 'ringing'}
  <div class="fixed inset-0 z-50 bg-black bg-opacity-95 flex items-center justify-center">
    <div class="w-full max-w-md mx-auto p-8">
      
      <!-- Background Taíno Elements -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        <div class="absolute top-20 left-10">
          <TainoIcons icon="spiral" size="xl" opacity={0.1} color="#D4AF37" />
        </div>
        <div class="absolute bottom-20 right-10">
          <TainoIcons icon="sun" size="lg" opacity={0.08} color="#B8860B" />
        </div>
        <div class="absolute top-1/2 left-1/4">
          <TainoIcons icon="wave" size="md" opacity={0.06} color="#D4AF37" />
        </div>
      </div>

      <!-- Incoming Call Content -->
      <div class="text-center relative z-10">
        
        <!-- Call Type Header -->
        <div class="mb-6">
          <div class="flex items-center justify-center space-x-2 mb-2">
            <TainoIcons icon="feather" size="sm" opacity={0.8} color="#D4AF37" />
            <span class="text-gold-300 text-sm font-medium">
              Incoming {$callState.callType === 'video' ? 'Video' : 'Voice'} Call
            </span>
          </div>
          <div class="w-16 h-1 bg-gradient-to-r from-transparent via-gold-400 to-transparent mx-auto"></div>
        </div>

        <!-- Caller Avatar -->
        <div class="relative mb-6">
          <div class="w-48 h-48 rounded-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center mx-auto shadow-2xl border-4 border-gold-500/30 animate-pulse">
            <span class="text-6xl font-bold text-white">
              {$callState.participant?.name?.charAt(0).toUpperCase() || 'U'}
            </span>
          </div>
          
          <!-- Ripple Animation -->
          <div class="absolute inset-0 rounded-full border-4 border-gold-400 animate-ping opacity-30"></div>
          <div class="absolute inset-4 rounded-full border-2 border-gold-300 animate-ping opacity-20" style="animation-delay: 0.5s"></div>
        </div>

        <!-- Caller Info -->
        <div class="mb-8">
          <h2 class="text-3xl font-bold text-white mb-2">
            {$callState.participant?.name || 'Unknown Caller'}
          </h2>
          <p class="text-lg text-gray-300">
            {$callState.participant?.phoneNumber || 'Unknown Number'}
          </p>
        </div>

        <!-- Ringing Indicator -->
        <div class="mb-12">
          <div class="flex justify-center space-x-2 mb-4">
            {#each Array(3) as _, i}
              <div 
                class="w-3 h-3 bg-gold-400 rounded-full animate-bounce"
                style="animation-delay: {i * 0.2}s"
              ></div>
            {/each}
          </div>
          <p class="text-gold-300 text-lg animate-pulse">Ringing...</p>
        </div>

        <!-- Call Actions -->
        <div class="flex justify-center space-x-12">
          
          <!-- Decline Button -->
          <button
            on:click={handleDecline}
            class="group relative w-20 h-20 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-red-500/30"
          >
            <!-- Decline Icon -->
            <svg class="w-10 h-10 text-white transform group-hover:rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
            
            <!-- Ripple Effect -->
            <div class="absolute inset-0 rounded-full bg-red-400 animate-ping opacity-20"></div>
          </button>

          <!-- Answer Button -->
          <button
            on:click={handleAnswer}
            class="group relative w-20 h-20 rounded-full bg-green-500 hover:bg-green-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-green-500/30"
          >
            <!-- Answer Icon -->
            <svg class="w-10 h-10 text-white transform group-hover:-rotate-12 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
            
            <!-- Ripple Effect -->
            <div class="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-20"></div>
          </button>
        </div>

        <!-- Action Labels -->
        <div class="flex justify-center space-x-12 mt-4">
          <span class="text-red-300 text-sm font-medium">Decline</span>
          <span class="text-green-300 text-sm font-medium">Answer</span>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 flex justify-center space-x-6">
          <!-- Message Button -->
          <button
            class="w-12 h-12 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110"
            aria-label="Send message"
          >
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Remind Me Button -->
          <button
            class="w-12 h-12 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110"
            aria-label="Remind me later"
          >
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  @keyframes ripple {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }


</style>
