<!-- Features Section -->
<section class="py-24 bg-gray-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute inset-0" style="background-image: url(&quot;data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E&quot;)"></div>
  </div>
  
  <div class="container mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <h2 class="text-5xl md:text-6xl font-bold text-white mb-6 font-serif">
        Why Choose <span class="text-gold">BoGuani</span>?
      </h2>
      <p class="text-xl text-gray-300 max-w-3xl mx-auto">
        Ancient wisdom meets modern technology. BoGuani channels the sacred Taíno tradition of value exchange through communication,
        creating a platform where every message carries meaning and worth.
      </p>
    </div>
    
    <!-- Features Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
      <!-- Feature 1: Secure Messaging -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-yellow-400 group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-400 group-hover:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">End-to-End Encryption</h3>
        <p class="text-gray-300 leading-relaxed">
          Military-grade 256-bit encryption ensures your messages are completely private. Not even we can read them.
        </p>
      </div>
      
      <!-- Feature 2: Instant Payments -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-gold bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gold group-hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Instant Money Transfer</h3>
        <p class="text-gray-300 leading-relaxed">
          Send money as easily as sending a message. Just type "$20" and it's done. No separate apps needed.
        </p>
      </div>
      
      <!-- Feature 3: Smart AI -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-yellow-400 group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-400 group-hover:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Smart AI Assistant</h3>
        <p class="text-gray-300 leading-relaxed">
          AI-powered features like smart replies, expense tracking, and fraud detection keep you safe and efficient.
        </p>
      </div>
      
      <!-- Feature 4: Cross-Platform -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-gold bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gold group-hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Cross-Platform Sync</h3>
        <p class="text-gray-300 leading-relaxed">
          Seamlessly sync across all your devices. Start a conversation on your phone, continue on your laptop.
        </p>
      </div>
      
      <!-- Feature 5: Group Features -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-yellow-400 group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-400 group-hover:text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Advanced Groups</h3>
        <p class="text-gray-300 leading-relaxed">
          Create groups with up to 10,000 members. Split bills, organize events, and manage group expenses effortlessly.
        </p>
      </div>
      
      <!-- Feature 6: Business Tools -->
      <div class="group glass-effect rounded-2xl p-8 hover:bg-primary hover:bg-opacity-10 transition-all duration-300 transform hover:-translate-y-2">
        <div class="w-16 h-16 bg-gold bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:bg-primary group-hover:bg-opacity-30 transition-all duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gold group-hover:text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">Business Integration</h3>
        <p class="text-gray-300 leading-relaxed">
          Accept payments, manage invoices, and communicate with customers all in one place. Perfect for small businesses.
        </p>
      </div>
    </div>
    
    <!-- Comparison Section -->
    <div class="glass-effect rounded-3xl p-12 mb-20">
      <h3 class="text-4xl font-bold text-white text-center mb-12 font-serif">
        How <span class="text-gold">BoGuani</span> Compares to The Competition
      </h3>
      
      <div class="overflow-x-auto">
        <table class="w-full text-left">
          <thead>
            <tr class="border-b border-gray-600">
              <th class="pb-4 text-gray-300 font-semibold">Feature</th>
              <th class="pb-4 text-center text-gold font-bold">BoGuani</th>
              <th class="pb-4 text-center text-gray-400">WhatsApp</th>
              <th class="pb-4 text-center text-gray-400">Telegram</th>
              <th class="pb-4 text-center text-gray-400">Cash App</th>
            </tr>
          </thead>
          <tbody class="text-gray-300">
            <tr class="border-b border-gray-700">
              <td class="py-4">End-to-End Encryption</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-yellow-400">Partial</td>
              <td class="py-4 text-center text-red-400">✗</td>
            </tr>
            <tr class="border-b border-gray-700">
              <td class="py-4">Instant Money Transfer</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-yellow-400">Limited</td>
              <td class="py-4 text-center text-red-400">✗</td>
              <td class="py-4 text-center text-green-400">✓</td>
            </tr>
            <tr class="border-b border-gray-700">
              <td class="py-4">Cross-Platform Sync</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-yellow-400">Mobile Only</td>
            </tr>
            <tr class="border-b border-gray-700">
              <td class="py-4">Business Tools</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-yellow-400">Basic</td>
              <td class="py-4 text-center text-yellow-400">Basic</td>
              <td class="py-4 text-center text-yellow-400">Basic</td>
            </tr>
            <tr>
              <td class="py-4">AI Assistant</td>
              <td class="py-4 text-center text-green-400">✓</td>
              <td class="py-4 text-center text-red-400">✗</td>
              <td class="py-4 text-center text-red-400">✗</td>
              <td class="py-4 text-center text-red-400">✗</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</section>
