<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { 
    getAuth,
    signInWithPhoneNumber, 
    RecaptchaVerifier, 
    type ConfirmationResult,
    type Auth
  } from 'firebase/auth';
  import { goto } from '$app/navigation';
  import type { ExtendedRecaptchaVerifier } from '$lib/types/firebase';

  // Component state
  let phoneNumber = '';
  let phoneNumberInput = '';
  let countryCode = '+1';
  let verificationCode = '';
  let loading = false;
  let message = '';
  let errorMsg = ''; // Renamed from errorMessage to avoid conflicts
  let verificationId = '';
  let recaptchaVerifier: ExtendedRecaptchaVerifier | null = null;
  let showVerificationCode = false;
  let confirmationResult: ConfirmationResult | null = null;

  // Common country codes
  const countryCodes = [
    { code: '+1', name: '🇺🇸 +1 (US)' },
    { code: '+44', name: '🇬🇧 +44 (UK)' },
    { code: '+61', name: '🇦🇺 +61 (AU)' },
    { code: '+33', name: '🇫🇷 +33 (FR)' },
    { code: '+49', name: '🇩🇪 +49 (DE)' },
    { code: '+81', name: '🇯🇵 +81 (JP)' },
    { code: '+86', name: '🇨🇳 +86 (CN)' },
    { code: '+91', name: '🇮🇳 +91 (IN)' },
  ];

  // Clean up reCAPTCHA on component destroy
  onDestroy(() => {
    if (recaptchaVerifier) {
      try {
        recaptchaVerifier.clear();
        console.log('reCAPTCHA verifier cleaned up');
      } catch (error) {
        console.error('Error cleaning up reCAPTCHA:', error);
      }
    }
  });

  // Initialize reCAPTCHA
  onMount(async () => {
    try {
      const auth = getAuth();
      console.log('Initializing reCAPTCHA...');
      
      // Clear any existing reCAPTCHA
      if (window.recaptchaVerifier) {
        try {
          window.recaptchaVerifier.clear();
        } catch (e) {
          console.warn('Could not clear existing reCAPTCHA:', e);
        }
      }

      // Create new reCAPTCHA verifier
      recaptchaVerifier = new RecaptchaVerifier(
        auth, // Auth instance must be first parameter
        'recaptcha-container',
        {
          size: 'invisible',
          callback: (response: string) => {
            console.log('reCAPTCHA verified:', response);
          },
          'expired-callback': () => {
            console.warn('reCAPTCHA expired');
            errorMsg = 'Verification expired. Please try again.';
          },
          'error-callback': (error: unknown) => {
            const errorMsgText = getErrorMessage(error);
            console.error('reCAPTCHA error:', errorMsgText);
            errorMsg = 'Verification error. Please try again.';
          }
        }
      ) as unknown as ExtendedRecaptchaVerifier;

      // Store verifier in window for debugging
      window.recaptchaVerifier = recaptchaVerifier;
      console.log('reCAPTCHA initialized');
      
    } catch (error) {
      console.error('Error initializing reCAPTCHA:', error);
      errorMsg = 'Failed to initialize verification. Please refresh the page.';
    }
  });

  // Format phone number as (*************
  function formatPhoneNumber(value: string): string {
    // Only allow numbers
    const numbers = value.replace(/\D/g, '');
    
    // Format as (*************
    let formatted = numbers.substring(0, 10);
    if (formatted.length > 6) {
      return `(${formatted.substring(0, 3)}) ${formatted.substring(3, 6)}-${formatted.substring(6, 10)}`;
    } else if (formatted.length > 3) {
      return `(${formatted.substring(0, 3)}) ${formatted.substring(3)}`;
    } else if (formatted.length > 0) {
      return `(${formatted}`;
    }
    return '';
  }

  // Handle phone number input with formatting
  function handlePhoneInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    phoneNumber = formatPhoneNumber(input.value);
  }

  // Format phone number to E.164 format
  const formatPhoneNumberToE164 = (number: string): string => {
    // Remove all non-digit characters
    const digits = number.replace(/\D/g, '');
    // Return in E.164 format
    return `${countryCode}${digits}`;
  };
  
  // Helper to safely access error properties
  const getErrorMessage = (error: unknown): string => {
    if (error instanceof Error) {
      return error.message;
    } else if (typeof error === 'string') {
      return error;
    } else if (error && typeof error === 'object' && 'message' in error) {
      return String(error.message);
    }
    return 'An unknown error occurred';
  };

  // Clear any previous messages when phone number changes
  $: if (phoneNumber) {
    errorMsg = '';
    message = '';
  }
  
  // Clear error message when verification code changes
  $: if (verificationCode) {
    errorMsg = '';
  }

  // Send verification code to the user's phone
  const sendVerificationCode = async () => {
    try {
      if (!phoneNumberInput) {
        throw new Error('Please enter a valid phone number with country code');
      }

      // Format phone number to E.164 format
      phoneNumber = formatPhoneNumberToE164(phoneNumberInput);
      const formattedPhoneNumber = phoneNumber;
      
      if (!recaptchaVerifier) {
        throw new Error('reCAPTCHA not initialized. Please refresh the page and try again.');
      }

      loading = true;
      message = 'Sending verification code...';
      
      console.log('Sending verification code to:', formattedPhoneNumber);
      
      // Get fresh auth instance
      const auth = getAuth();
      
      try {
        // Render the reCAPTCHA widget
        console.log('Rendering reCAPTCHA...');
        const recaptchaToken = await recaptchaVerifier.verify();
        console.log('reCAPTCHA token received:', recaptchaToken);
        
        // Request verification code
        console.log('Requesting verification code...');
        confirmationResult = await signInWithPhoneNumber(auth, formattedPhoneNumber, recaptchaVerifier);
        
        message = `Verification code sent to ${formattedPhoneNumber}`;
        console.log('Verification code sent successfully');
      } catch (error: unknown) {
        console.error('Error sending verification code:', error);
        
        // Handle specific error cases
        const errorMessage = getErrorMessage(error);
        if (errorMessage.includes('auth/too-many-requests')) {
          throw new Error('Too many attempts. Please try again later.');
        } else if (errorMessage.includes('auth/invalid-phone-number')) {
          throw new Error(`Invalid phone number format. Please use format: ${countryCode}XXXXXXXXXX`);
        } else if (errorMessage.includes('auth/quota-exceeded')) {
          throw new Error('SMS quota exceeded. Please try again later.');
        } else if (errorMessage.includes('auth/captcha-check-failed')) {
          throw new Error('reCAPTCHA verification failed. Please try again.');
        } else {
          throw new Error(`Failed to send verification code: ${errorMessage}`);
        }
      }
    } catch (error: unknown) {
      console.error('Error in sendVerificationCode:', error);
      errorMsg = getErrorMessage(error) || 'Failed to send verification code. Please try again.';
    } finally {
      loading = false;
    }
  }

  // Verify the code entered by the user
  const verifyCode = async () => {
    errorMsg = '';
    
    try {
      if (!verificationCode) {
        throw new Error('Please enter the verification code');
      }
      
      if (!confirmationResult) {
        throw new Error('No verification in progress. Please request a new code.');
      }
      
      loading = true;
      message = 'Verifying code...';
      
      console.log('Verifying code...');
      
      try {
        const result = await confirmationResult.confirm(verificationCode);
        console.log('Phone authentication successful:', result);
        
        // Clear sensitive data
        verificationCode = '';
        confirmationResult = null;
        
        // Redirect on success
        message = 'Phone number verified successfully!';
        await new Promise(resolve => setTimeout(resolve, 1000));
        goto('/dashboard');
      } catch (error: unknown) {
        console.error('Error verifying code:', error);
        const errorMessage = getErrorMessage(error);
        
        if (errorMessage.includes('auth/invalid-verification-code')) {
          throw new Error('Invalid verification code. Please try again.');
        } else if (errorMessage.includes('auth/code-expired')) {
          throw new Error('Verification code has expired. Please request a new one.');
        } else {
          throw new Error(`Verification failed: ${errorMessage}`);
        }
      }
    } catch (error: unknown) {
      console.error('Error in verifyCode:', error);
      errorMsg = getErrorMessage(error) || 'Failed to verify code. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<div class="min-h-screen flex items-center justify-center bg-gray-900 text-white p-4">
  <div class="w-full max-w-md bg-gray-800 rounded-lg shadow-lg p-8">
    <h1 class="text-3xl font-bold text-center mb-8 text-yellow-400">
      Phone Verification
    </h1>
    
    <!-- Hidden reCAPTCHA container -->
    <div id="recaptcha-container"></div>
    
    {#if !confirmationResult}
      <!-- Phone Number Input -->
      <div class="mb-6">
        <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">
          Phone Number
        </label>
        <div class="flex gap-2">
          <!-- Country Code Dropdown -->
          <select
            bind:value={countryCode}
            class="w-1/3 px-3 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            disabled={loading}
          >
            {#each countryCodes as country}
              <option value={country.code}>
                {country.name}
              </option>
            {/each}
          </select>
          
          <!-- Phone Number Input -->
          <input
            type="tel"
            id="phone"
            bind:value={phoneNumberInput}
            placeholder="(*************"
            class="flex-1 px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            disabled={loading}
          />
        </div>
        <p class="mt-1 text-xs text-gray-400">
          Example: {countryCode} 5551234567
        </p>
      </div>
      
      <!-- Send Verification Code Button -->
      <button
        on:click={sendVerificationCode}
        disabled={loading}
        class="w-full bg-yellow-500 text-gray-900 font-bold py-3 px-4 rounded-lg hover:bg-yellow-400 transition duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {#if loading}
          <span class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          </span>
        {:else}
          Send Verification Code
        {/if}
      </button>
      
      <div id="recaptcha-container" class="invisible absolute"></div>
      
    {:else}
      <!-- Verification Code Input -->
      <div class="mb-6">
        <label for="code" class="block text-sm font-medium mb-2">
          Enter Verification Code
        </label>
        <input
          type="text"
          id="code"
          bind:value={verificationCode}
          placeholder="123456"
          class="w-full px-4 py-3 rounded-lg bg-gray-700 border border-gray-600 focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400 focus:outline-none transition-colors"
          disabled={loading}
          on:keydown={(e) => e.key === 'Enter' && verifyCode()}
        />
      </div>
      
      <button
        on:click={verifyCode}
        disabled={loading}
        class="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-bold py-3 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        {#if loading}
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Verifying...
        {:else}
          Verify Code
        {/if}
      </button>
      
      <div class="mt-4 text-center">
        <button
          on:click={() => {
            confirmationResult = null;
            verificationCode = '';
          }}
          class="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors"
          disabled={loading}
        >
          Use a different number
        </button>
      </div>
    {/if}
    
    <!-- reCAPTCHA container (invisible) -->
    <div id="recaptcha-container"></div>
    
    <!-- Messages -->
    {#if errorMsg}
      <div class="mt-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-300 text-sm">
        {errorMsg}
      </div>
    {/if}
    
    {#if message}
      <div class="mt-4 p-3 bg-green-500/20 border border-green-500/50 rounded-lg text-green-300 text-sm">
        {message}
      </div>
    {/if}
  </div>
</div>

<style>
  /* Hide number input arrows */
  input[type=tel]::-webkit-inner-spin-button,
  input[type=tel]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type=tel] {
    -moz-appearance: textfield;
    appearance: textfield;
  }
</style>
