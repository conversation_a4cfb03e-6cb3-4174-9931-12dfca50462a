<script lang="ts">
  import { onMount } from 'svelte';
  import TainoIcons from './TainoIcons.svelte';
  
  let currentDemo = 0;
  let isPlaying = false;
  
  const demos = [
    {
      title: "Secure Messaging",
      description: "Send encrypted messages with Taíno-inspired design",
      image: "/demo/messaging.png",
      features: ["End-to-end encryption", "Beautiful UI", "Real-time delivery"]
    },
    {
      title: "Instant Payments",
      description: "Transfer money as easily as sending a text",
      image: "/demo/payments.png", 
      features: ["Type $20 to send", "Bank integration", "Instant transfers"]
    },
    {
      title: "Voice & Video Calls",
      description: "Crystal clear calls with WebRTC technology",
      image: "/demo/calls.png",
      features: ["HD video calls", "Voice calls", "Screen sharing"]
    }
  ];
  
  onMount(() => {
    const interval = setInterval(() => {
      if (isPlaying) {
        currentDemo = (currentDemo + 1) % demos.length;
      }
    }, 4000);
    
    return () => clearInterval(interval);
  });
  
  function playDemo() {
    isPlaying = !isPlaying;
  }
  
  function selectDemo(index: number) {
    currentDemo = index;
    isPlaying = false;
  }
</script>

<!-- Demo Section -->
<section id="demo" class="py-24 bg-gradient-to-br from-indigo-950 via-purple-950 to-purple-900 relative overflow-hidden">
  <!-- Background Elements -->
  <div class="absolute inset-0 pointer-events-none">
    <div class="absolute top-32 left-16 animate-float">
      <TainoIcons icon="feather" size="xl" opacity={0.08} color="#D4AF37" />
    </div>
    <div class="absolute bottom-40 right-24 animate-float-delayed">
      <TainoIcons icon="bird" size="lg" opacity={0.06} color="#B8860B" />
    </div>
    <div class="absolute top-1/2 left-1/4 w-96 h-96 bg-gradient-to-r from-gold-500/10 to-purple-600/10 rounded-full blur-3xl animate-pulse-slow"></div>
  </div>
  
  <div class="container mx-auto px-6 relative z-10">
    <!-- Section Header -->
    <div class="text-center mb-16">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 mb-8 shadow-2xl">
        <svg class="w-10 h-10 text-gold-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
        </svg>
      </div>
      
      <h2 class="text-5xl md:text-7xl font-bold text-white mb-8 font-serif">
        See <span class="bg-gradient-to-r from-gold-400 to-gold-600 bg-clip-text text-transparent">BoGuani</span> in Action
      </h2>
      
      <p class="text-xl md:text-2xl text-purple-200 max-w-4xl mx-auto leading-relaxed">
        Experience the future of communication where every message carries value and every interaction matters.
      </p>
    </div>
    
    <!-- Demo Interface -->
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        
        <!-- Demo Preview -->
        <div class="relative">
          <div class="bg-gradient-to-br from-purple-900/50 to-purple-800/50 rounded-3xl p-8 backdrop-blur-sm border border-purple-700/50 shadow-2xl">
            
            <!-- Phone Mockup -->
            <div class="bg-black rounded-3xl p-4 shadow-2xl">
              <div class="bg-gradient-to-br from-purple-950 to-purple-900 rounded-2xl overflow-hidden">
                
                <!-- Status Bar -->
                <div class="flex justify-between items-center px-6 py-3 bg-purple-900/50">
                  <div class="flex items-center space-x-2">
                    <div class="w-4 h-4 rounded-full bg-green-400"></div>
                    <span class="text-white text-sm font-medium">BoGuani</span>
                  </div>
                  <div class="text-white text-sm">9:41 AM</div>
                </div>
                
                <!-- Demo Content -->
                <div class="p-6 h-96 flex flex-col justify-center items-center text-center">
                  <div class="w-16 h-16 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 flex items-center justify-center mb-6">
                    <TainoIcons icon="sun" size="md" opacity={1} color="#FFFFFF" />
                  </div>
                  
                  <h3 class="text-2xl font-bold text-white mb-4">{demos[currentDemo].title}</h3>
                  <p class="text-purple-200 mb-6">{demos[currentDemo].description}</p>
                  
                  <!-- Features List -->
                  <div class="space-y-2">
                    {#each demos[currentDemo].features as feature}
                      <div class="flex items-center space-x-2 text-sm text-purple-300">
                        <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                        <span>{feature}</span>
                      </div>
                    {/each}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Floating Elements -->
            <div class="absolute -top-4 -right-4 w-8 h-8 bg-gold-400 rounded-full animate-bounce"></div>
            <div class="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-400 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        <!-- Demo Controls -->
        <div class="space-y-8">
          
          <!-- Play Button -->
          <div class="text-center">
            <button
              on:click={playDemo}
              class="group relative w-24 h-24 rounded-full bg-gradient-to-br from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-2xl hover:shadow-gold-500/40 mb-6"
            >
              {#if isPlaying}
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              {:else}
                <svg class="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                </svg>
              {/if}
            </button>
            
            <p class="text-purple-200 text-lg">
              {isPlaying ? 'Auto-playing demos' : 'Click to start demo'}
            </p>
          </div>
          
          <!-- Demo Selector -->
          <div class="space-y-4">
            {#each demos as demo, index}
              <button
                on:click={() => selectDemo(index)}
                class="w-full text-left p-6 rounded-2xl transition-all duration-300 transform hover:scale-105 {currentDemo === index ? 'bg-gradient-to-r from-gold-500/20 to-gold-600/20 border-2 border-gold-400/50' : 'bg-purple-800/30 border-2 border-purple-700/50 hover:border-purple-600/50'}"
              >
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center">
                    <span class="text-white font-bold">{index + 1}</span>
                  </div>
                  <div>
                    <h4 class="text-lg font-bold text-white mb-1">{demo.title}</h4>
                    <p class="text-purple-300 text-sm">{demo.description}</p>
                  </div>
                </div>
              </button>
            {/each}
          </div>
          
          <!-- CTA -->
          <div class="text-center pt-8">
            <a
              href="/auth"
              class="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white font-bold text-lg rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <span>Try BoGuani Now</span>
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
