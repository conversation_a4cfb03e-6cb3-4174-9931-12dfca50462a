<script lang="ts">
  import { webrtcService, callState } from '$lib/services/webrtc';
  import TainoIcons from './TainoIcons.svelte';

  export let participant: { id: string; name: string; phoneNumber: string } | null = null;
  export let size: 'sm' | 'md' | 'lg' = 'md';

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10', 
    lg: 'w-12 h-12'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  async function handleVoiceCall() {
    if (!participant) return;
    
    try {
      await webrtcService.initiateCall(participant.id, 'voice');
    } catch (error) {
      console.error('Failed to start voice call:', error);
      alert('Failed to start voice call. Please try again.');
    }
  }

  async function handleVideoCall() {
    if (!participant) return;
    
    try {
      await webrtcService.initiateCall(participant.id, 'video');
    } catch (error) {
      console.error('Failed to start video call:', error);
      alert('Failed to start video call. Please try again.');
    }
  }

  $: isCallActive = $callState.isActive;
  $: canMakeCall = participant && !isCallActive;
</script>

<div class="flex items-center space-x-2">
  <!-- Voice Call Button -->
  <button
    on:click={handleVoiceCall}
    disabled={!canMakeCall}
    class="group relative {sizeClasses[size]} rounded-full bg-gradient-to-br from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 transform hover:scale-110 disabled:hover:scale-100 shadow-lg hover:shadow-green-500/30"
    title="Voice Call"
  >
    <!-- Voice Call Icon -->
    <svg class="{iconSizes[size]} text-white" fill="currentColor" viewBox="0 0 20 20">
      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
    </svg>
    
    <!-- Hover Effect -->
    <div class="absolute inset-0 rounded-full bg-green-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
    
    <!-- Taíno Decoration -->
    <div class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-60 transition-opacity duration-300">
      <TainoIcons icon="spiral" size="sm" opacity={0.8} color="#D4AF37" />
    </div>
  </button>

  <!-- Video Call Button -->
  <button
    on:click={handleVideoCall}
    disabled={!canMakeCall}
    class="group relative {sizeClasses[size]} rounded-full bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-400 hover:to-blue-500 disabled:from-gray-500 disabled:to-gray-600 disabled:cursor-not-allowed flex items-center justify-center transition-all duration-300 transform hover:scale-110 disabled:hover:scale-100 shadow-lg hover:shadow-blue-500/30"
    title="Video Call"
  >
    <!-- Video Call Icon -->
    <svg class="{iconSizes[size]} text-white" fill="currentColor" viewBox="0 0 20 20">
      <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
    </svg>
    
    <!-- Hover Effect -->
    <div class="absolute inset-0 rounded-full bg-blue-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
    
    <!-- Taíno Decoration -->
    <div class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-60 transition-opacity duration-300">
      <TainoIcons icon="feather" size="sm" opacity={0.8} color="#D4AF37" />
    </div>
  </button>

  <!-- Call Status Indicator -->
  {#if isCallActive && $callState.participant?.id === participant?.id}
    <div class="flex items-center space-x-1 text-xs text-green-400">
      <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      <span>
        {#if $callState.status === 'connecting'}
          Connecting...
        {:else if $callState.status === 'ringing'}
          Ringing...
        {:else if $callState.status === 'connected'}
          In Call
        {:else}
          {$callState.status}
        {/if}
      </span>
    </div>
  {/if}
</div>

<style>
  button:disabled {
    opacity: 0.5;
  }
  
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
    }
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
</style>
