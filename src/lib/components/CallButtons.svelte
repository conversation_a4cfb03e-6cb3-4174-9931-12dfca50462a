<script lang="ts">
  import { webrtcService, callState } from '$lib/services/webrtc';
  import TainoIcons from './TainoIcons.svelte';

  export let participant: { id: string; name: string; phoneNumber: string } | null = null;

  async function handleVoiceCall() {
    if (!participant) return;
    
    try {
      await webrtcService.initiateCall(participant.id, 'voice');
    } catch (error) {
      console.error('Failed to start voice call:', error);
      alert('Failed to start voice call. Please try again.');
    }
  }

  async function handleVideoCall() {
    if (!participant) return;
    
    try {
      await webrtcService.initiateCall(participant.id, 'video');
    } catch (error) {
      console.error('Failed to start video call:', error);
      alert('Failed to start video call. Please try again.');
    }
  }

  $: isCallActive = $callState.isActive;
  $: canMakeCall = participant && !isCallActive;
</script>

<div class="flex items-center space-x-2">
  <!-- Voice Call Button -->
  <button
    on:click={handleVoiceCall}
    disabled={!canMakeCall}
    class="group relative p-2 text-gray-400 hover:text-white focus:outline-none transition-colors duration-200 rounded-full hover:bg-dark-light hover:bg-opacity-30"
    title="Voice Call"
    aria-label="Start audio call"
  >
    <!-- Voice Call Icon (using existing design) -->
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
    </svg>

    <!-- Taíno Decoration -->
    <div class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-60 transition-opacity duration-300">
      <TainoIcons icon="spiral" size="sm" opacity={0.8} color="#D4AF37" />
    </div>
  </button>

  <!-- Video Call Button -->
  <button
    on:click={handleVideoCall}
    disabled={!canMakeCall}
    class="group relative p-2 text-gray-400 hover:text-white focus:outline-none transition-colors duration-200 rounded-full hover:bg-dark-light hover:bg-opacity-30"
    title="Video Call"
    aria-label="Start video call"
  >
    <!-- Video Call Icon (using existing design) -->
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
    </svg>

    <!-- Taíno Decoration -->
    <div class="absolute -top-1 -right-1 opacity-0 group-hover:opacity-60 transition-opacity duration-300">
      <TainoIcons icon="feather" size="sm" opacity={0.8} color="#D4AF37" />
    </div>
  </button>

  <!-- Call Status Indicator -->
  {#if isCallActive && $callState.participant?.id === participant?.id}
    <div class="flex items-center space-x-1 text-xs text-green-400">
      <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      <span>
        {#if $callState.status === 'connecting'}
          Connecting...
        {:else if $callState.status === 'ringing'}
          Ringing...
        {:else if $callState.status === 'connected'}
          In Call
        {:else}
          {$callState.status}
        {/if}
      </span>
    </div>
  {/if}
</div>

<style>
  button:disabled {
    opacity: 0.5;
  }
  

</style>
