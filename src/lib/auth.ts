import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

export interface User {
  id: string;
  phone: string;
  handle: string;
  name: string;
  avatar?: string;
  publicKey: string;
  privateKey: string; // Encrypted with user's password
  createdAt: Date;
  lastSeen: Date;
  isOnline: boolean;
}

export interface AuthSession {
  userId: string;
  token: string;
  expiresAt: Date;
}

export interface LoginCredentials {
  phone: string;
  otp: string;
}

export interface SignupData {
  phone: string;
  handle: string;
  name: string;
  avatar?: string;
}

class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
  private readonly OTP_EXPIRY = 5 * 60 * 1000; // 5 minutes
  private otpStore = new Map<string, { otp: string; expiresAt: Date }>();

  // Generate OTP for phone verification
  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Store OTP temporarily
  storeOTP(phone: string, otp: string): void {
    const expiresAt = new Date(Date.now() + this.OTP_EXPIRY);
    this.otpStore.set(phone, { otp, expiresAt });
  }

  // Verify OTP
  verifyOTP(phone: string, otp: string): boolean {
    const stored = this.otpStore.get(phone);
    if (!stored) return false;
    
    if (new Date() > stored.expiresAt) {
      this.otpStore.delete(phone);
      return false;
    }
    
    const isValid = stored.otp === otp;
    if (isValid) {
      this.otpStore.delete(phone);
    }
    
    return isValid;
  }

  // Generate JWT token
  generateToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'access' },
      this.JWT_SECRET,
      { expiresIn: '7d' }
    );
  }

  // Verify JWT token
  verifyToken(token: string): { userId: string } | null {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as any;
      return { userId: decoded.userId };
    } catch {
      return null;
    }
  }

  // Generate QR code login token
  generateQRToken(): string {
    const qrData = {
      id: uuidv4(),
      type: 'qr_login',
      expiresAt: Date.now() + 5 * 60 * 1000 // 5 minutes
    };
    
    return jwt.sign(qrData, this.JWT_SECRET);
  }

  // Hash password for storage
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }

  // Verify password
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // Create user session
  createSession(userId: string): AuthSession {
    const token = this.generateToken(userId);
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    
    return {
      userId,
      token,
      expiresAt
    };
  }

  // Format phone number
  formatPhoneNumber(phone: string): string {
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');
    
    // Add country code if missing (assuming US)
    if (digits.length === 10) {
      return `+1${digits}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`;
    }
    
    return `+${digits}`;
  }

  // Validate phone number
  isValidPhoneNumber(phone: string): boolean {
    const formatted = this.formatPhoneNumber(phone);
    return /^\+\d{10,15}$/.test(formatted);
  }

  // Validate handle
  isValidHandle(handle: string): boolean {
    return /^[a-zA-Z0-9_]{3,20}$/.test(handle);
  }
}

export const auth = new AuthService();
