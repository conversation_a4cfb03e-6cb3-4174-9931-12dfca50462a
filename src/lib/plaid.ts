import { PlaidApi, Configuration, PlaidEnvironments } from 'plaid';
import encryption from './encryption.js';

export interface BankAccount {
  id: string;
  name: string;
  mask: string;
  type: string;
  subtype: string;
  balance: number;
  isConnected: boolean;
}

export interface Transaction {
  id: string;
  amount: number;
  description: string;
  date: Date;
  type: 'debit' | 'credit';
  status: 'pending' | 'completed' | 'failed';
  fromAccount?: string;
  toAccount?: string;
  toUser?: string;
  fromUser?: string;
  note?: string;
}

class PlaidService {
  private client: PlaidApi;
  private readonly ENCRYPTION_KEY = process.env.PLAID_ENCRYPTION_KEY || 'your-plaid-encryption-key';

  constructor() {
    const configuration = new Configuration({
      basePath: PlaidEnvironments.sandbox, // Use sandbox for development
      baseOptions: {
        headers: {
          'PLAID-CLIENT-ID': process.env.PLAID_CLIENT_ID || '',
          'PLAID-SECRET': process.env.PLAID_SECRET || '',
        },
      },
    });

    this.client = new PlaidApi(configuration);
  }

  // Create link token for Plaid Link
  async createLinkToken(userId: string): Promise<string> {
    try {
      const response = await this.client.linkTokenCreate({
        user: {
          client_user_id: userId,
        },
        client_name: 'NexusPay',
        products: ['transactions', 'auth'],
        country_codes: ['US'],
        language: 'en',
      });

      return response.data.link_token;
    } catch (error) {
      console.error('Error creating link token:', error);
      throw new Error('Failed to create link token');
    }
  }

  // Exchange public token for access token
  async exchangePublicToken(publicToken: string): Promise<string> {
    try {
      const response = await this.client.itemPublicTokenExchange({
        public_token: publicToken,
      });

      // Encrypt the access token before storing
      const encryptedToken = await encryption.encryptData(
        response.data.access_token,
        this.ENCRYPTION_KEY
      );

      return JSON.stringify(encryptedToken);
    } catch (error) {
      console.error('Error exchanging public token:', error);
      throw new Error('Failed to exchange public token');
    }
  }

  // Decrypt access token
  private async decryptAccessToken(encryptedToken: string): Promise<string> {
    try {
      const encryptedData = JSON.parse(encryptedToken);
      return await encryption.decryptData(encryptedData, this.ENCRYPTION_KEY);
    } catch (error) {
      console.error('Error decrypting access token:', error);
      throw new Error('Failed to decrypt access token');
    }
  }

  // Get account information
  async getAccounts(encryptedAccessToken: string): Promise<BankAccount[]> {
    try {
      const accessToken = await this.decryptAccessToken(encryptedAccessToken);
      
      const response = await this.client.accountsGet({
        access_token: accessToken,
      });

      return response.data.accounts.map(account => ({
        id: account.account_id,
        name: account.name,
        mask: account.mask || '',
        type: account.type,
        subtype: account.subtype || '',
        balance: account.balances.current || 0,
        isConnected: true,
      }));
    } catch (error) {
      console.error('Error getting accounts:', error);
      throw new Error('Failed to get accounts');
    }
  }

  // Get account balance
  async getBalance(encryptedAccessToken: string, accountId: string): Promise<number> {
    try {
      const accessToken = await this.decryptAccessToken(encryptedAccessToken);
      
      const response = await this.client.accountsGet({
        access_token: accessToken,
      });

      const account = response.data.accounts.find(acc => acc.account_id === accountId);
      return account?.balances.current || 0;
    } catch (error) {
      console.error('Error getting balance:', error);
      throw new Error('Failed to get balance');
    }
  }

  // Simulate money transfer (in production, use Plaid Transfer API)
  async transferMoney(
    fromAccountToken: string,
    toAccountToken: string,
    amount: number,
    description: string
  ): Promise<Transaction> {
    try {
      // In a real implementation, you would use Plaid's Transfer API
      // For now, we'll simulate the transaction
      
      const transaction: Transaction = {
        id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        amount,
        description,
        date: new Date(),
        type: 'debit',
        status: 'pending',
        note: description,
      };

      // Simulate processing delay
      setTimeout(() => {
        transaction.status = 'completed';
      }, 2000);

      return transaction;
    } catch (error) {
      console.error('Error transferring money:', error);
      throw new Error('Failed to transfer money');
    }
  }

  // Get recent transactions
  async getTransactions(encryptedAccessToken: string, accountId: string): Promise<Transaction[]> {
    try {
      const accessToken = await this.decryptAccessToken(encryptedAccessToken);
      
      const response = await this.client.transactionsGet({
        access_token: accessToken,
        start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        end_date: new Date(),
        account_ids: [accountId],
      });

      return response.data.transactions.map(txn => ({
        id: txn.transaction_id,
        amount: txn.amount,
        description: txn.name,
        date: new Date(txn.date),
        type: txn.amount > 0 ? 'debit' : 'credit',
        status: 'completed',
      }));
    } catch (error) {
      console.error('Error getting transactions:', error);
      throw new Error('Failed to get transactions');
    }
  }
}

export const plaid = new PlaidService();
