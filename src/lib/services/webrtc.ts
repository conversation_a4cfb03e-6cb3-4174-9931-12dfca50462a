import { writable } from 'svelte/store';
import { messagingService } from '../stores/messaging';

// Types
export interface CallState {
  isActive: boolean;
  isIncoming: boolean;
  isOutgoing: boolean;
  callType: 'voice' | 'video';
  participant?: {
    id: string;
    name: string;
    phoneNumber: string;
    avatar?: string;
  };
  duration: number;
  status: 'connecting' | 'ringing' | 'connected' | 'ended' | 'declined' | 'missed';
  isMuted: boolean;
  isVideoEnabled: boolean;
  isSpeakerOn: boolean;
}

// Stores
export const callState = writable<CallState>({
  isActive: false,
  isIncoming: false,
  isOutgoing: false,
  callType: 'voice',
  duration: 0,
  status: 'ended',
  isMuted: false,
  isVideoEnabled: false,
  isSpeakerOn: false
});

export const localStream = writable<MediaStream | null>(null);
export const remoteStream = writable<MediaStream | null>(null);

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStreamInstance: MediaStream | null = null;
  private remoteStreamInstance: MediaStream | null = null;
  private callTimer: NodeJS.Timeout | null = null;
  private ws: WebSocket | null = null;

  // ICE servers configuration
  private iceServers = [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    // Add TURN servers for production
    // { urls: 'turn:your-turn-server.com', username: 'user', credential: 'pass' }
  ];

  constructor() {
    this.initializeWebSocket();
  }

  private initializeWebSocket() {
    // Use the same WebSocket connection as messaging
    // In a real app, you'd share this connection
    try {
      this.ws = new WebSocket('ws://localhost:5003/ws');
      
      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleSignalingMessage(data);
      };
    } catch (error) {
      console.error('WebRTC WebSocket initialization failed:', error);
    }
  }

  private handleSignalingMessage(data: any) {
    switch (data.type) {
      case 'call_offer':
        this.handleIncomingCall(data);
        break;
      case 'call_answer':
        this.handleCallAnswer(data);
        break;
      case 'ice_candidate':
        this.handleIceCandidate(data);
        break;
      case 'call_end':
        this.endCall();
        break;
      case 'call_declined':
        this.handleCallDeclined();
        break;
    }
  }

  async initiateCall(participantId: string, callType: 'voice' | 'video') {
    try {
      // Get user media
      const constraints = {
        audio: true,
        video: callType === 'video'
      };

      this.localStreamInstance = await navigator.mediaDevices.getUserMedia(constraints);
      localStream.set(this.localStreamInstance);

      // Create peer connection
      this.createPeerConnection();

      // Add local stream to peer connection
      this.localStreamInstance.getTracks().forEach(track => {
        if (this.peerConnection && this.localStreamInstance) {
          this.peerConnection.addTrack(track, this.localStreamInstance);
        }
      });

      // Create offer
      const offer = await this.peerConnection!.createOffer();
      await this.peerConnection!.setLocalDescription(offer);

      // Update call state
      callState.update(state => ({
        ...state,
        isActive: true,
        isOutgoing: true,
        callType,
        status: 'connecting',
        isVideoEnabled: callType === 'video'
      }));

      // Send offer via WebSocket
      this.sendSignalingMessage({
        type: 'call_offer',
        to: participantId,
        offer: offer,
        callType
      });

      this.startCallTimer();

    } catch (error) {
      console.error('Failed to initiate call:', error);
      this.endCall();
    }
  }

  async answerCall() {
    try {
      const currentState = this.getCurrentCallState();
      
      const constraints = {
        audio: true,
        video: currentState.callType === 'video'
      };

      this.localStreamInstance = await navigator.mediaDevices.getUserMedia(constraints);
      localStream.set(this.localStreamInstance);

      // Add local stream to existing peer connection
      this.localStreamInstance.getTracks().forEach(track => {
        if (this.peerConnection && this.localStreamInstance) {
          this.peerConnection.addTrack(track, this.localStreamInstance);
        }
      });

      // Create answer
      const answer = await this.peerConnection!.createAnswer();
      await this.peerConnection!.setLocalDescription(answer);

      // Send answer
      this.sendSignalingMessage({
        type: 'call_answer',
        answer: answer
      });

      // Update call state
      callState.update(state => ({
        ...state,
        isIncoming: false,
        status: 'connected'
      }));

      this.startCallTimer();

    } catch (error) {
      console.error('Failed to answer call:', error);
      this.declineCall();
    }
  }

  declineCall() {
    this.sendSignalingMessage({
      type: 'call_declined'
    });

    callState.update(state => ({
      ...state,
      status: 'declined',
      isActive: false,
      isIncoming: false,
      isOutgoing: false
    }));

    this.cleanup();
  }

  endCall() {
    this.sendSignalingMessage({
      type: 'call_end'
    });

    callState.update(state => ({
      ...state,
      status: 'ended',
      isActive: false,
      isIncoming: false,
      isOutgoing: false
    }));

    this.cleanup();
  }

  toggleMute() {
    if (this.localStreamInstance) {
      const audioTrack = this.localStreamInstance.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        callState.update(state => ({
          ...state,
          isMuted: !audioTrack.enabled
        }));
      }
    }
  }

  toggleVideo() {
    if (this.localStreamInstance) {
      const videoTrack = this.localStreamInstance.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        callState.update(state => ({
          ...state,
          isVideoEnabled: videoTrack.enabled
        }));
      }
    }
  }

  toggleSpeaker() {
    callState.update(state => ({
      ...state,
      isSpeakerOn: !state.isSpeakerOn
    }));
    // In a real app, you'd change audio output device here
  }

  private createPeerConnection() {
    this.peerConnection = new RTCPeerConnection({
      iceServers: this.iceServers
    });

    // Handle ICE candidates
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalingMessage({
          type: 'ice_candidate',
          candidate: event.candidate
        });
      }
    };

    // Handle remote stream
    this.peerConnection.ontrack = (event) => {
      this.remoteStreamInstance = event.streams[0];
      remoteStream.set(this.remoteStreamInstance);
    };

    // Handle connection state changes
    this.peerConnection.onconnectionstatechange = () => {
      const state = this.peerConnection?.connectionState;
      console.log('Connection state:', state);
      
      if (state === 'connected') {
        callState.update(s => ({ ...s, status: 'connected' }));
      } else if (state === 'disconnected' || state === 'failed') {
        this.endCall();
      }
    };
  }

  private async handleIncomingCall(data: any) {
    // Create peer connection for incoming call
    this.createPeerConnection();

    // Set remote description
    await this.peerConnection!.setRemoteDescription(data.offer);

    // Update call state
    callState.update(state => ({
      ...state,
      isActive: true,
      isIncoming: true,
      callType: data.callType,
      participant: data.from,
      status: 'ringing'
    }));

    // Play ringtone (implement audio notification)
    this.playRingtone();
  }

  private async handleCallAnswer(data: any) {
    if (this.peerConnection) {
      await this.peerConnection.setRemoteDescription(data.answer);
      
      callState.update(state => ({
        ...state,
        status: 'connected'
      }));
    }
  }

  private async handleIceCandidate(data: any) {
    if (this.peerConnection) {
      await this.peerConnection.addIceCandidate(data.candidate);
    }
  }

  private handleCallDeclined() {
    callState.update(state => ({
      ...state,
      status: 'declined',
      isActive: false,
      isOutgoing: false
    }));
    this.cleanup();
  }

  private sendSignalingMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  private startCallTimer() {
    let duration = 0;
    this.callTimer = setInterval(() => {
      duration++;
      callState.update(state => ({ ...state, duration }));
    }, 1000);
  }

  private stopCallTimer() {
    if (this.callTimer) {
      clearInterval(this.callTimer);
      this.callTimer = null;
    }
  }

  private playRingtone() {
    // Implement ringtone audio
    const audio = new Audio('/sounds/ringtone.mp3');
    audio.loop = true;
    audio.play().catch(console.error);
  }

  private cleanup() {
    this.stopCallTimer();

    // Stop local stream
    if (this.localStreamInstance) {
      this.localStreamInstance.getTracks().forEach(track => track.stop());
      this.localStreamInstance = null;
      localStream.set(null);
    }

    // Clear remote stream
    this.remoteStreamInstance = null;
    remoteStream.set(null);

    // Close peer connection
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    // Reset call state after a delay
    setTimeout(() => {
      callState.update(state => ({
        ...state,
        isActive: false,
        isIncoming: false,
        isOutgoing: false,
        duration: 0,
        participant: undefined
      }));
    }, 2000);
  }

  private getCurrentCallState(): CallState {
    let currentState: CallState;
    callState.subscribe(state => currentState = state)();
    return currentState!;
  }
}

// Export singleton instance
export const webrtcService = new WebRTCService();
