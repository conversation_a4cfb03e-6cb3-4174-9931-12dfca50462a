import { writable } from 'svelte/store';

// Types
export interface BankAccount {
  id: string;
  name: string;
  institutionName: string;
  accountType: 'checking' | 'savings' | 'credit';
  balance: number;
  currency: string;
  accountNumber: string; // masked
  routingNumber?: string;
  isVerified: boolean;
  isDefault: boolean;
  plaidAccountId: string;
  plaidAccessToken: string;
}

export interface PaymentTransaction {
  id: string;
  fromAccountId: string;
  toRecipient: string; // phone number or email
  amount: number;
  currency: string;
  description?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
  completedAt?: string;
  failureReason?: string;
  type: 'send' | 'receive' | 'request';
}

export interface PlaidLinkResult {
  publicToken: string;
  metadata: {
    institution: {
      name: string;
      institution_id: string;
    };
    accounts: Array<{
      id: string;
      name: string;
      type: string;
      subtype: string;
    }>;
  };
}

// Stores
export const bankAccounts = writable<BankAccount[]>([]);
export const transactions = writable<PaymentTransaction[]>([]);
export const isPlaidLoading = writable(false);
export const plaidError = writable<string | null>(null);

class PlaidService {
  private plaidScript: HTMLScriptElement | null = null;
  private plaidHandler: any = null;

  constructor() {
    this.loadPlaidScript();
    this.loadLocalData();
  }

  private loadPlaidScript() {
    if (typeof window === 'undefined') return;

    this.plaidScript = document.createElement('script');
    this.plaidScript.src = 'https://cdn.plaid.com/link/v2/stable/link-initialize.js';
    this.plaidScript.onload = () => {
      console.log('🏦 Plaid script loaded');
    };
    document.head.appendChild(this.plaidScript);
  }

  private loadLocalData() {
    if (typeof window === 'undefined') return;

    // Load bank accounts from localStorage
    const savedAccounts = localStorage.getItem('boguani_bank_accounts');
    if (savedAccounts) {
      bankAccounts.set(JSON.parse(savedAccounts));
    }

    // Load transactions from localStorage
    const savedTransactions = localStorage.getItem('boguani_transactions');
    if (savedTransactions) {
      transactions.set(JSON.parse(savedTransactions));
    }
  }

  async initializePlaidLink(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined' || !window.Plaid) {
        reject(new Error('Plaid not available'));
        return;
      }

      // Get link token from backend
      this.getLinkToken()
        .then(linkToken => {
          this.plaidHandler = window.Plaid.create({
            token: linkToken,
            onSuccess: (publicToken: string, metadata: any) => {
              this.handlePlaidSuccess(publicToken, metadata);
              resolve();
            },
            onExit: (err: any, metadata: any) => {
              if (err) {
                console.error('Plaid Link exit error:', err);
                plaidError.set(err.error_message || 'Failed to connect bank account');
              }
              resolve();
            },
            onEvent: (eventName: string, metadata: any) => {
              console.log('Plaid event:', eventName, metadata);
            }
          });
        })
        .catch(reject);
    });
  }

  async openPlaidLink() {
    isPlaidLoading.set(true);
    plaidError.set(null);

    try {
      if (!this.plaidHandler) {
        await this.initializePlaidLink();
      }
      
      if (this.plaidHandler) {
        this.plaidHandler.open();
      }
    } catch (error) {
      console.error('Failed to open Plaid Link:', error);
      plaidError.set('Failed to open bank connection');
    } finally {
      isPlaidLoading.set(false);
    }
  }

  private async getLinkToken(): Promise<string> {
    try {
      const token = localStorage.getItem('boguani_token');
      const response = await fetch('/api/plaid/link-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get link token');
      }

      const data = await response.json();
      return data.linkToken;
    } catch (error) {
      console.error('Failed to get link token:', error);
      // Return mock token for development
      return 'link-sandbox-mock-token';
    }
  }

  private async handlePlaidSuccess(publicToken: string, metadata: PlaidLinkResult['metadata']) {
    try {
      const token = localStorage.getItem('boguani_token');
      const response = await fetch('/api/plaid/exchange-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          publicToken,
          metadata
        })
      });

      if (!response.ok) {
        throw new Error('Failed to exchange token');
      }

      const data = await response.json();
      
      // Create bank account objects
      const newAccounts: BankAccount[] = data.accounts.map((account: any) => ({
        id: `bank_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: account.name,
        institutionName: metadata.institution.name,
        accountType: account.subtype || 'checking',
        balance: account.balance || 0,
        currency: 'USD',
        accountNumber: `****${account.mask}`,
        isVerified: true,
        isDefault: false,
        plaidAccountId: account.account_id,
        plaidAccessToken: data.accessToken
      }));

      // Add to store
      bankAccounts.update(accounts => {
        const updated = [...accounts, ...newAccounts];
        // Set first account as default if no default exists
        if (!updated.some(acc => acc.isDefault) && updated.length > 0) {
          updated[0].isDefault = true;
        }
        localStorage.setItem('boguani_bank_accounts', JSON.stringify(updated));
        return updated;
      });

      console.log('✅ Bank accounts connected successfully');
      
    } catch (error) {
      console.error('Failed to handle Plaid success:', error);
      plaidError.set('Failed to connect bank account');
    }
  }

  async sendMoney(
    recipientPhone: string, 
    amount: number, 
    description?: string,
    fromAccountId?: string
  ): Promise<PaymentTransaction> {
    const user = JSON.parse(localStorage.getItem('boguani_user') || '{}');
    
    // Get default account if none specified
    let sourceAccount: BankAccount | undefined;
    bankAccounts.subscribe(accounts => {
      sourceAccount = fromAccountId 
        ? accounts.find(acc => acc.id === fromAccountId)
        : accounts.find(acc => acc.isDefault) || accounts[0];
    })();

    if (!sourceAccount) {
      throw new Error('No bank account available');
    }

    const transaction: PaymentTransaction = {
      id: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromAccountId: sourceAccount.id,
      toRecipient: recipientPhone,
      amount,
      currency: 'USD',
      description,
      status: 'pending',
      type: 'send',
      createdAt: new Date().toISOString()
    };

    // Add transaction to store
    transactions.update(txns => {
      const updated = [transaction, ...txns];
      localStorage.setItem('boguani_transactions', JSON.stringify(updated));
      return updated;
    });

    try {
      // Send to backend for processing
      const token = localStorage.getItem('boguani_token');
      const response = await fetch('/api/payments/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(transaction)
      });

      if (response.ok) {
        const result = await response.json();
        transaction.status = result.status || 'processing';
        
        // Update transaction status
        this.updateTransaction(transaction);
        
        // Simulate processing delay
        setTimeout(() => {
          transaction.status = 'completed';
          transaction.completedAt = new Date().toISOString();
          this.updateTransaction(transaction);
        }, 3000);
        
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      transaction.status = 'failed';
      transaction.failureReason = 'Network error';
      this.updateTransaction(transaction);
    }

    return transaction;
  }

  async requestMoney(
    fromPhone: string,
    amount: number,
    description?: string
  ): Promise<PaymentTransaction> {
    const transaction: PaymentTransaction = {
      id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fromAccountId: '', // Not applicable for requests
      toRecipient: fromPhone,
      amount,
      currency: 'USD',
      description,
      status: 'pending',
      type: 'request',
      createdAt: new Date().toISOString()
    };

    transactions.update(txns => {
      const updated = [transaction, ...txns];
      localStorage.setItem('boguani_transactions', JSON.stringify(updated));
      return updated;
    });

    // Send request notification
    try {
      const token = localStorage.getItem('boguani_token');
      await fetch('/api/payments/request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(transaction)
      });
    } catch (error) {
      console.error('Failed to send payment request:', error);
    }

    return transaction;
  }

  private updateTransaction(transaction: PaymentTransaction) {
    transactions.update(txns => {
      const index = txns.findIndex(t => t.id === transaction.id);
      if (index !== -1) {
        txns[index] = transaction;
      }
      localStorage.setItem('boguani_transactions', JSON.stringify(txns));
      return txns;
    });
  }

  async removeBankAccount(accountId: string) {
    bankAccounts.update(accounts => {
      const filtered = accounts.filter(acc => acc.id !== accountId);
      localStorage.setItem('boguani_bank_accounts', JSON.stringify(filtered));
      return filtered;
    });
  }

  async setDefaultAccount(accountId: string) {
    bankAccounts.update(accounts => {
      const updated = accounts.map(acc => ({
        ...acc,
        isDefault: acc.id === accountId
      }));
      localStorage.setItem('boguani_bank_accounts', JSON.stringify(updated));
      return updated;
    });
  }

  async getAccountBalance(accountId: string): Promise<number> {
    // In a real app, this would fetch live balance from Plaid
    let balance = 0;
    bankAccounts.subscribe(accounts => {
      const account = accounts.find(acc => acc.id === accountId);
      balance = account?.balance || 0;
    })();
    return balance;
  }
}

// Export singleton instance
export const plaidService = new PlaidService();

// Global Plaid types
declare global {
  interface Window {
    Plaid: any;
  }
}
