import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Types
export interface Contact {
  id: string;
  name: string;
  phoneNumber: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  isBoGuaniUser?: boolean;
  handle?: string;
  isTyping?: boolean;
}

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderPhone: string;
  content: string;
  type: 'text' | 'image' | 'payment' | 'call' | 'system';
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  paymentData?: {
    amount: number;
    currency: string;
    status: 'pending' | 'completed' | 'failed';
    recipient?: string;
  };
  callData?: {
    type: 'voice' | 'video';
    duration?: number;
    status: 'missed' | 'completed' | 'declined';
  };
}

export interface ChatRoom {
  id: string;
  name?: string;
  type: 'direct' | 'group';
  participants: Contact[];
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

// Stores
export const contacts = writable<Contact[]>([]);
export const chatRooms = writable<ChatRoom[]>([]);
export const messages = writable<{ [chatId: string]: Message[] }>({});
export const currentUser = writable<Contact | null>(null);
export const activeChat = writable<string | null>(null);

// Derived stores
export const activeChatRoom = derived(
  [chatRooms, activeChat],
  ([$chatRooms, $activeChat]) => {
    return $activeChat ? $chatRooms.find(room => room.id === $activeChat) : null;
  }
);

export const activeChatMessages = derived(
  [messages, activeChat],
  ([$messages, $activeChat]) => {
    return $activeChat ? $messages[$activeChat] || [] : [];
  }
);

// Messaging Service
class MessagingService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    if (browser) {
      this.initializeWebSocket();
      this.loadLocalData();
    }
  }

  private initializeWebSocket() {
    try {
      const wsUrl = `ws://localhost:5003/ws`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('🔗 WebSocket connected');
        this.reconnectAttempts = 0;
        this.authenticateWebSocket();
      };

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleWebSocketMessage(data);
      };

      this.ws.onclose = () => {
        console.log('🔌 WebSocket disconnected');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to initialize WebSocket:', error);
    }
  }

  private authenticateWebSocket() {
    const token = localStorage.getItem('boguani_token');
    if (token && this.ws) {
      this.ws.send(JSON.stringify({
        type: 'auth',
        token
      }));
    }
  }

  private handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'message':
        this.addMessage(data.message);
        break;
      case 'user_online':
        this.updateUserStatus(data.userId, true);
        break;
      case 'user_offline':
        this.updateUserStatus(data.userId, false);
        break;
      case 'typing':
        // Handle typing indicators
        break;
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`🔄 Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.initializeWebSocket();
      }, 1000 * this.reconnectAttempts);
    }
  }

  private loadLocalData() {
    // Load contacts from localStorage
    const savedContacts = localStorage.getItem('boguani_contacts');
    if (savedContacts) {
      contacts.set(JSON.parse(savedContacts));
    }

    // Load chat rooms from localStorage
    const savedRooms = localStorage.getItem('boguani_chat_rooms');
    if (savedRooms) {
      chatRooms.set(JSON.parse(savedRooms));
    }

    // Load current user
    const savedUser = localStorage.getItem('boguani_user');
    if (savedUser) {
      currentUser.set(JSON.parse(savedUser));
    }
  }

  // Public methods
  async sendMessage(chatId: string, content: string, type: 'text' | 'payment' = 'text') {
    const user = JSON.parse(localStorage.getItem('boguani_user') || '{}');
    
    const message: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      chatId,
      senderId: user.id,
      senderName: user.name,
      senderPhone: user.phone,
      content,
      type,
      timestamp: new Date().toISOString(),
      status: 'sending'
    };

    // Add message locally first
    this.addMessage(message);

    // Send via WebSocket
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'send_message',
        message
      }));
    } else {
      // Fallback to HTTP API
      await this.sendMessageViaAPI(message);
    }
  }

  private async sendMessageViaAPI(message: Message) {
    try {
      const token = localStorage.getItem('boguani_token');
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(message)
      });

      if (response.ok) {
        message.status = 'sent';
        this.updateMessage(message);
      } else {
        message.status = 'sent'; // Fallback for demo
        this.updateMessage(message);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      message.status = 'sent'; // Fallback for demo
      this.updateMessage(message);
    }
  }

  private addMessage(message: Message) {
    messages.update(msgs => {
      const chatMessages = msgs[message.chatId] || [];
      return {
        ...msgs,
        [message.chatId]: [...chatMessages, message].sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        )
      };
    });

    // Update last message in chat room
    this.updateChatRoomLastMessage(message);
  }

  private updateMessage(message: Message) {
    messages.update(msgs => {
      const chatMessages = msgs[message.chatId] || [];
      const index = chatMessages.findIndex(m => m.id === message.id);
      if (index !== -1) {
        chatMessages[index] = message;
      }
      return { ...msgs, [message.chatId]: chatMessages };
    });
  }

  private updateChatRoomLastMessage(message: Message) {
    chatRooms.update(rooms => {
      return rooms.map(room => {
        if (room.id === message.chatId) {
          return {
            ...room,
            lastMessage: message,
            updatedAt: message.timestamp,
            unreadCount: room.unreadCount + (message.senderId !== JSON.parse(localStorage.getItem('boguani_user') || '{}').id ? 1 : 0)
          };
        }
        return room;
      });
    });
  }

  private updateUserStatus(userId: string, isOnline: boolean) {
    contacts.update(contactList => {
      return contactList.map(contact => {
        if (contact.id === userId) {
          return {
            ...contact,
            isOnline,
            lastSeen: isOnline ? undefined : new Date().toISOString()
          };
        }
        return contact;
      });
    });
  }

  async createChatRoom(participants: Contact[], type: 'direct' | 'group' = 'direct', name?: string): Promise<string> {
    const user = JSON.parse(localStorage.getItem('boguani_user') || '{}');
    
    const chatRoom: ChatRoom = {
      id: `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: name || (type === 'direct' ? participants[0]?.name : 'Group Chat'),
      type,
      participants: [user, ...participants],
      unreadCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isActive: true
    };

    chatRooms.update(rooms => [...rooms, chatRoom]);
    
    // Save to localStorage
    chatRooms.subscribe(rooms => {
      localStorage.setItem('boguani_chat_rooms', JSON.stringify(rooms));
    });

    return chatRoom.id;
  }

  async importPhoneContacts(): Promise<Contact[]> {
    // This would integrate with the device's contact API
    // For now, return mock data
    const mockContacts: Contact[] = [
      {
        id: 'contact_1',
        name: 'Maria Rodriguez',
        phoneNumber: '+1234567891',
        isBoGuaniUser: true,
        isOnline: true
      },
      {
        id: 'contact_2', 
        name: 'Carlos Mendez',
        phoneNumber: '+1234567892',
        isBoGuaniUser: true,
        isOnline: false,
        lastSeen: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 'contact_3',
        name: 'Ana Delgado',
        phoneNumber: '+1234567893',
        isBoGuaniUser: false
      }
    ];

    contacts.set(mockContacts);
    localStorage.setItem('boguani_contacts', JSON.stringify(mockContacts));
    
    return mockContacts;
  }

  async markMessagesAsRead(chatId: string, userId: string) {
    // Update messages in store
    messages.update(msgs => {
      const chatMessages = msgs[chatId] || [];
      const updatedMessages = chatMessages.map(msg => {
        if (msg.senderId !== userId && msg.status !== 'read') {
          return { ...msg, status: 'read' as const };
        }
        return msg;
      });
      
      return {
        ...msgs,
        [chatId]: updatedMessages
      };
    });

    // Update unread count in chat room
    chatRooms.update(rooms => 
      rooms.map(room => 
        room.id === chatId 
          ? { ...room, unreadCount: 0 }
          : room
      )
    );
  }
}

// Export singleton instance
export const messagingService = new MessagingService();
