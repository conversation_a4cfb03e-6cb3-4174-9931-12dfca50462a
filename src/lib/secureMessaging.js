import { encrypt, decrypt, generateKey, generate<PERSON>ey<PERSON>air } from './encryption';
import { db } from './firebase';
import { doc, setDoc, getDoc, updateDoc, arrayUnion, serverTimestamp } from 'firebase/firestore';

class SecureMessaging {
  constructor() {
    this.messageKeys = new Map(); // In-memory storage for message keys
  }

  // Initialize a new chat with key exchange
  async initializeChat(chatId, participants) {
    try {
      // Generate a new key for this chat
      const chatKey = generateKey();
      const encryptedKeys = {};

      // For each participant, encrypt the chat key with their public key
      for (const participantId of participants) {
        // In a real app, you'd fetch the participant's public key from your server
        // For now, we'll simulate this
        const publicKey = await this.getUserPublicKey(participantId);
        // In a real implementation, you'd use the public key to encrypt the chat key
        // encryptedKeys[participantId] = await this.encryptWithPublicKey(chatKey, publicKey);
        encryptedKeys[participantId] = chatKey.toString('hex'); // Simplified for demo
      }

      // Store the encrypted keys in Firestore
      const chatRef = doc(db, 'chats', chatId);
      await setDoc(chatRef, {
        participants,
        encryptedKeys,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      // Store the key in memory for current session
      this.messageKeys.set(chatId, chatKey);

      return chatKey;
    } catch (error) {
      console.error('Error initializing chat:', error);
      throw error;
    }
  }

  // Send an encrypted message
  async sendMessage(chatId, senderId, content) {
    try {
      let chatKey = this.messageKeys.get(chatId);
      
      // If we don't have the key in memory, try to get it from Firestore
      if (!chatKey) {
        const chatRef = doc(db, 'chats', chatId);
        const chatDoc = await getDoc(chatRef);
        
        if (chatDoc.exists()) {
          const chatData = chatDoc.data();
          // In a real app, you'd decrypt this with the user's private key
          // For now, we'll just use the stored key directly
          const encryptedKey = chatData.encryptedKeys[senderId];
          chatKey = Buffer.from(encryptedKey, 'hex');
          this.messageKeys.set(chatId, chatKey);
        } else {
          throw new Error('Chat not found');
        }
      }

      // Encrypt the message
      const encryptedContent = await encrypt({
        content,
        timestamp: new Date().toISOString(),
        senderId
      }, chatKey);

      // Store the encrypted message in Firestore
      const messageRef = doc(db, 'chats', chatId, 'messages', Date.now().toString());
      await setDoc(messageRef, {
        encryptedContent,
        senderId,
        timestamp: serverTimestamp()
      });

      // Update the chat's last message
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          content: 'Encrypted message',
          timestamp: serverTimestamp(),
          senderId
        },
        updatedAt: serverTimestamp()
      });

      return messageRef.id;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Get messages for a chat
  async getMessages(chatId, userId) {
    try {
      // In a real app, you'd implement pagination here
      const messagesRef = collection(db, 'chats', chatId, 'messages');
      const q = query(messagesRef, orderBy('timestamp', 'asc'));
      const querySnapshot = await getDocs(q);
      
      const messages = [];
      const chatKey = this.messageKeys.get(chatId);
      
      if (!chatKey) {
        throw new Error('No decryption key available for this chat');
      }

      for (const doc of querySnapshot.docs) {
        const data = doc.data();
        try {
          const decryptedData = await decrypt(data.encryptedContent, chatKey);
          messages.push({
            id: doc.id,
            ...decryptedData,
            timestamp: data.timestamp?.toDate()
          });
        } catch (error) {
          console.error('Error decrypting message:', error);
          // Skip messages that can't be decrypted
          continue;
        }
      }

      return messages;
    } catch (error) {
      console.error('Error getting messages:', error);
      throw error;
    }
  }

  // Helper method to get a user's public key (simplified)
  async getUserPublicKey(userId) {
    // In a real app, you'd fetch this from your server
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    
    if (userDoc.exists()) {
      return userDoc.data().publicKey; // Assuming publicKey is stored in user document
    }
    
    throw new Error('User not found');
  }
}

export const secureMessaging = new SecureMessaging();
