'use client';

import Link from 'next/link';

export default function PrivacyPage() {
  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-4xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-user-shield text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Privacy Policy
              </h1>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Your privacy is fundamental to everything we do. Learn how we protect your personal information.
              </p>
              <p className="text-sm text-gray-400 mt-4">Last updated: January 1, 2024</p>
            </div>

            {/* Privacy Content */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg rounded-2xl border border-yellow-500/20 p-8 space-y-8">
              
              {/* Introduction */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Introduction</h2>
                <p className="text-gray-300 leading-relaxed">
                  At BoGuani, we believe privacy is a fundamental human right. This Privacy Policy explains how we collect, use, and protect your information when you use our messaging and payment platform. We are committed to transparency and giving you control over your personal data.
                </p>
              </section>

              {/* Information We Collect */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Information We Collect</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Account Information</h3>
                    <p className="text-gray-300">Phone number, name, username, and profile information you provide when creating your account.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Messages and Communications</h3>
                    <p className="text-gray-300">Your messages are end-to-end encrypted and we cannot access their content. We only store encrypted metadata necessary for message delivery.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Payment Information</h3>
                    <p className="text-gray-300">Payment details are processed by our secure payment partners. We do not store your full payment card information.</p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Usage Information</h3>
                    <p className="text-gray-300">Technical information about how you use our service, including device information and usage patterns (anonymized).</p>
                  </div>
                </div>
              </section>

              {/* How We Use Information */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">How We Use Your Information</h2>
                <ul className="space-y-3 text-gray-300">
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Provide and maintain our messaging and payment services</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Verify your identity and prevent fraud</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Improve our services and develop new features</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Comply with legal obligations and enforce our terms</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-check text-yellow-400 mr-3 mt-1"></i>
                    <span>Communicate with you about service updates and security</span>
                  </li>
                </ul>
              </section>

              {/* Data Protection */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">How We Protect Your Data</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">End-to-End Encryption</h3>
                    <p className="text-gray-300 text-sm">All messages are encrypted on your device and can only be decrypted by the intended recipient.</p>
                  </div>
                  <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Secure Storage</h3>
                    <p className="text-gray-300 text-sm">Your data is stored in encrypted databases with strict access controls and regular security audits.</p>
                  </div>
                  <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Zero Knowledge</h3>
                    <p className="text-gray-300 text-sm">We cannot access your message content or see your private conversations.</p>
                  </div>
                  <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                    <h3 className="text-lg font-semibold text-yellow-400 mb-2">Regular Audits</h3>
                    <p className="text-gray-300 text-sm">Our security practices are regularly reviewed by independent security firms.</p>
                  </div>
                </div>
              </section>

              {/* Your Rights */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Your Privacy Rights</h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <i className="fas fa-user-edit text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-gray-200">Access and Update</h3>
                      <p className="text-gray-300 text-sm">You can access and update your personal information through your account settings.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-trash-alt text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-gray-200">Delete Your Data</h3>
                      <p className="text-gray-300 text-sm">You can delete your account and associated data at any time through the app settings.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-download text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-gray-200">Data Portability</h3>
                      <p className="text-gray-300 text-sm">You can export your data in a machine-readable format upon request.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-ban text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-gray-200">Opt-Out</h3>
                      <p className="text-gray-300 text-sm">You can opt out of non-essential communications and data processing.</p>
                    </div>
                  </div>
                </div>
              </section>

              {/* Data Sharing */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Data Sharing</h2>
                <p className="text-gray-300 mb-4">We do not sell your personal information. We may share limited data only in these circumstances:</p>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <i className="fas fa-shield-alt text-yellow-400 mr-3 mt-1"></i>
                    <span>With service providers who help us operate our platform (under strict data protection agreements)</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-gavel text-yellow-400 mr-3 mt-1"></i>
                    <span>When required by law or to protect our users' safety</span>
                  </li>
                  <li className="flex items-start">
                    <i className="fas fa-handshake text-yellow-400 mr-3 mt-1"></i>
                    <span>In case of a business transfer (with your consent)</span>
                  </li>
                </ul>
              </section>

              {/* Contact */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Contact Us</h2>
                <p className="text-gray-300 mb-4">
                  If you have questions about this Privacy Policy or how we handle your data, please contact us:
                </p>
                <div className="bg-purple-900/50 p-4 rounded-lg border border-yellow-400/20">
                  <p className="text-gray-300">
                    <strong>Email:</strong> <EMAIL><br />
                    <strong>Address:</strong> BoGuani Privacy Team, 123 Security Street, Privacy City, PC 12345
                  </p>
                </div>
              </section>

              {/* Updates */}
              <section>
                <h2 className="text-2xl font-bold text-yellow-200 mb-4">Policy Updates</h2>
                <p className="text-gray-300">
                  We may update this Privacy Policy from time to time. We will notify you of any material changes through the app or by email. Your continued use of BoGuani after such changes constitutes acceptance of the updated policy.
                </p>
              </section>
            </div>

            {/* Footer Actions */}
            <div className="text-center mt-12">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/terms" className="border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-full font-semibold hover:bg-yellow-400 hover:text-purple-900 transition-all">
                  Read Terms of Service
                </Link>
                <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                  Contact Privacy Team
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
