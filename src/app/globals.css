@import "@tailwind base";
@tailwind components;
@tailwind utilities;

:root {
  --primary: #4F46E5;
  --primary-dark: #4338CA;
  --secondary: #10B981;
  --dark: #1F2937;
  --light: #F9FAFB;
  --gray: #6B7280;
  --font-montserrat: 'Montserrat', sans-serif;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-montserrat), system-ui, sans-serif;
}

body {
  @apply bg-white text-gray-900 font-sans;
  color: white;
  background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
  min-height: 100vh;
}

.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.btn {
  @apply px-6 py-3 rounded-lg font-medium transition-colors duration-200;
  font-family: inherit;
}

.btn-primary {
  @apply bg-indigo-600 text-white hover:bg-indigo-700;
}

.btn-outline {
  @apply border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-50;
}

.section {
  @apply py-20;
}

.section-title {
  @apply text-4xl font-bold text-center mb-12;
}

.feature-card {
  @apply bg-white rounded-xl shadow-lg p-8 transition-transform duration-300 hover:-translate-y-2;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: inherit;
}
