import type { <PERSON>ada<PERSON> } from "next";
import { Mont<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const montserrat = Montserrat({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-montserrat",
});

export const metadata: Metadata = {
  title: "BoGuani - Messenger of Value",
  description: "Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",
  keywords: "messaging, payments, secure chat, money transfer, encrypted messaging, BoGuani",
  authors: [{ name: "BoGuani Team" }],
  openGraph: {
    title: "BoGuani - Messenger of Value",
    description: "Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
      </head>
      <body className={`${montserrat.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
