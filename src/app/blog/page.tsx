'use client';

import Link from 'next/link';

export default function BlogPage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .btn-hover {
          transition: all 0.3s ease;
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: rgba(31, 41, 55, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-6 pt-20">
          <div className="w-full max-w-6xl">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">BoGuani Blog</h1>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Insights, updates, and stories from the future of value-based communication.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Article */}
        <section className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Featured Article</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            </div>
            <div className="max-w-4xl mx-auto">
              <div className="glass-card rounded-2xl overflow-hidden professional-shadow">
                <div className="h-64 bg-gradient-to-r from-yellow-400 to-yellow-500 flex items-center justify-center">
                  <div className="text-center">
                    <i className="fas fa-comment-dollar text-6xl text-purple-900 mb-4"></i>
                    <p className="text-purple-900 font-bold text-xl">Featured Story</p>
                  </div>
                </div>
                <div className="p-8">
                  <div className="flex items-center mb-6">
                    <span className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full text-sm font-bold mr-4">Featured</span>
                    <span className="text-gray-400 text-sm">December 15, 2024</span>
                  </div>
                  <h2 className="text-3xl font-bold mb-6 gold-gradient">The Future of Value-Based Communication</h2>
                  <p className="text-gray-300 mb-8 leading-relaxed">
                    Discover how BoGuani is revolutionizing the way we think about messaging by combining ancient wisdom with modern technology. Learn about our journey from concept to the secure, value-driven platform that&apos;s changing how people connect and share worth.
                  </p>
                  <Link href="/blog/future-of-value-communication" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover">
                    Read More
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Blog Posts Grid */}
        <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Latest Articles</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Stay updated with the latest insights and developments
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center">
                  <i className="fas fa-shield-alt text-4xl text-white"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Security</span>
                    <span className="text-gray-400 text-sm">Dec 10, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">End-to-End Encryption Explained</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">Understanding how BoGuani protects your messages with military-grade encryption.</p>
                  <Link href="/blog/encryption-explained" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>

              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-yellow-400 to-yellow-500 flex items-center justify-center">
                  <i className="fas fa-video text-4xl text-purple-900"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-yellow-600 to-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Product</span>
                    <span className="text-gray-400 text-sm">Dec 8, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Introducing Voice & Video Calls</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">Crystal-clear communication with the same security standards as our messaging.</p>
                  <Link href="/blog/voice-video-calls" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>

              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                  <i className="fas fa-dollar-sign text-4xl text-white"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-green-600 to-green-500 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Finance</span>
                    <span className="text-gray-400 text-sm">Dec 5, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Secure Money Transfers Made Simple</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">How BoGuani makes sending money as easy as sending a message.</p>
                  <Link href="/blog/money-transfers" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>

              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                  <i className="fas fa-leaf text-4xl text-white"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-blue-600 to-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Culture</span>
                    <span className="text-gray-400 text-sm">Dec 1, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Taíno Wisdom in Modern Tech</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">How ancient communication principles inspire our modern platform.</p>
                  <Link href="/blog/taino-wisdom" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>

              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                  <i className="fas fa-users text-4xl text-white"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-red-600 to-red-500 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Company</span>
                    <span className="text-gray-400 text-sm">Nov 28, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Building a Remote-First Team</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">Our journey to creating a globally distributed team focused on innovation.</p>
                  <Link href="/blog/remote-team" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>

              <article className="glass-card rounded-2xl overflow-hidden hover:scale-105 transition-all duration-300 professional-shadow">
                <div className="h-48 bg-gradient-to-br from-indigo-500 to-indigo-600 flex items-center justify-center">
                  <i className="fas fa-code text-4xl text-white"></i>
                </div>
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <span className="bg-gradient-to-r from-indigo-600 to-indigo-500 text-white px-3 py-1 rounded-full text-xs font-bold mr-3">Tech</span>
                    <span className="text-gray-400 text-sm">Nov 25, 2024</span>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">WebRTC: The Future of Real-Time Communication</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">Deep dive into the technology powering our voice and video calls.</p>
                  <Link href="/blog/webrtc-technology" className="text-yellow-400 hover:text-yellow-300 font-semibold transition-colors">Read More →</Link>
                </div>
              </article>
            </div>
          </div>
        </section>

        {/* Newsletter Signup */}
        <section className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="max-w-2xl mx-auto text-center">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Stay Updated</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 mb-12">Get the latest insights and updates from BoGuani delivered to your inbox.</p>

              <form className="flex flex-col sm:flex-row gap-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-6 py-4 bg-gray-800/50 rounded-full border border-gray-600 focus:border-yellow-400 focus:outline-none text-white placeholder-gray-400 backdrop-blur-md"
                />
                <button
                  type="submit"
                  className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover"
                >
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
              {/* Company Info */}
              <div>
                <Link href="/" className="flex items-center mb-4">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mb-4">Messenger of Value</p>
                <p className="text-gray-400 text-sm leading-relaxed">
                  Where ancient wisdom meets modern technology. Experience the future of value-based communication.
                </p>
              </div>

              {/* Product Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Product</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
                  <li><Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link></li>
                  <li><Link href="/pricing" className="hover:text-yellow-400 transition-colors">Pricing</Link></li>
                  <li><Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link></li>
                </ul>
              </div>

              {/* Company Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Company</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
                  <li><Link href="/careers" className="hover:text-yellow-400 transition-colors">Careers</Link></li>
                  <li><Link href="/blog" className="hover:text-yellow-400 transition-colors">Blog</Link></li>
                  <li><a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a></li>
                </ul>
              </div>

              {/* Support Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Support</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/support" className="hover:text-yellow-400 transition-colors">Help Center</Link></li>
                  <li><Link href="/guides" className="hover:text-yellow-400 transition-colors">Guides</Link></li>
                  <li><Link href="/api-docs" className="hover:text-yellow-400 transition-colors">API Docs</Link></li>
                  <li><Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link></li>
                  <li><Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 BoGuani. All rights reserved.
              </p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
