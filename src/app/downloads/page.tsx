'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function DownloadsPage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>
      <div className="text-white min-h-screen hero-pattern">

        {/* Navigation */}
        <nav className="bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-download text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Download BoGuani
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Get BoGuani on all your devices and start experiencing the future of value-based communication.
              </p>
            </div>

            {/* Download Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {/* Mobile Apps */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fab fa-apple text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">iOS App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Download BoGuani for iPhone and iPad. Optimized for iOS with native performance and seamless integration.
                </p>
                <Link href="/downloads/ios" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fab fa-app-store mr-2"></i>
                  App Store
                </Link>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fab fa-android text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Android App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Get BoGuani for Android devices. Full feature support with Material Design and Android-specific optimizations.
                </p>
                <Link href="/downloads/android" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fab fa-google-play mr-2"></i>
                  Google Play
                </Link>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-desktop text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Desktop App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Download BoGuani for Windows, macOS, and Linux. Full desktop experience with keyboard shortcuts and multi-window support.
                </p>
                <Link href="/downloads/desktop" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fas fa-download mr-2"></i>
                  Download
                </Link>
              </motion.div>
            </div>

            {/* Web App */}
            <div className="text-center mb-16">
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-globe text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Web App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Use BoGuani directly in your browser. No download required - access all features instantly from any device with an internet connection.
                </p>
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fas fa-rocket mr-2"></i>
                  Launch Web App
                </Link>
              </motion.div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {[
                {
                  icon: 'fas fa-sync',
                  title: 'Cross-Platform Sync',
                  description: 'Your messages and data sync seamlessly across all devices'
                },
                {
                  icon: 'fas fa-shield-alt',
                  title: 'End-to-End Encryption',
                  description: 'Military-grade security on every platform'
                },
                {
                  icon: 'fas fa-bolt',
                  title: 'Lightning Fast',
                  description: 'Optimized performance on all devices'
                },
                {
                  icon: 'fas fa-mobile-alt',
                  title: 'Mobile First',
                  description: 'Designed for mobile with desktop power'
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 + index * 0.1 }}
                >
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className={`${feature.icon} text-purple-900`}></i>
                  </div>
                  <h3 className="font-bold mb-2 text-yellow-200">{feature.title}</h3>
                  <p className="text-gray-300 text-sm">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* System Requirements */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent text-center">
                System Requirements
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fab fa-apple mr-2"></i>
                    iOS
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• iOS 14.0 or later</li>
                    <li>• iPhone 6s or newer</li>
                    <li>• iPad (5th generation) or newer</li>
                    <li>• 100 MB free storage</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fab fa-android mr-2"></i>
                    Android
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Android 7.0 (API level 24)</li>
                    <li>• 2 GB RAM minimum</li>
                    <li>• ARMv7 or ARM64 processor</li>
                    <li>• 150 MB free storage</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fas fa-desktop mr-2"></i>
                    Desktop
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Windows 10, macOS 10.14, or Linux</li>
                    <li>• 4 GB RAM minimum</li>
                    <li>• 500 MB free storage</li>
                    <li>• Internet connection required</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center mt-16">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Choose your platform and start experiencing the future of value-based messaging today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all">
                  <i className="fas fa-rocket mr-2"></i>
                  Try Web App Now
                </Link>
                <Link href="/support" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all">
                  <i className="fas fa-question-circle mr-2"></i>
                  Need Help?
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
