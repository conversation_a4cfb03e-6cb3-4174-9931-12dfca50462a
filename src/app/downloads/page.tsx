'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function DownloadsPage() {
  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-download text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Download BoGuani
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Get BoGuani on all your devices and start experiencing the future of value-based communication.
              </p>
            </div>

            {/* Download Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {/* Mobile Apps */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fab fa-apple text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">iOS App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Download BoGuani for iPhone and iPad. Optimized for iOS with native performance and seamless integration.
                </p>
                <Link href="/downloads/ios" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fab fa-app-store mr-2"></i>
                  App Store
                </Link>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fab fa-android text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Android App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Get BoGuani for Android devices. Full feature support with Material Design and Android-specific optimizations.
                </p>
                <Link href="/downloads/android" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fab fa-google-play mr-2"></i>
                  Google Play
                </Link>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center hover:border-yellow-500/40 transition-all duration-300"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-desktop text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Desktop App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Download BoGuani for Windows, macOS, and Linux. Full desktop experience with keyboard shortcuts and multi-window support.
                </p>
                <Link href="/downloads/desktop" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fas fa-download mr-2"></i>
                  Download
                </Link>
              </motion.div>
            </div>

            {/* Web App */}
            <div className="text-center mb-16">
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
              >
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-globe text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-yellow-200">Web App</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Use BoGuani directly in your browser. No download required - access all features instantly from any device with an internet connection.
                </p>
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fas fa-rocket mr-2"></i>
                  Launch Web App
                </Link>
              </motion.div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {[
                {
                  icon: 'fas fa-sync',
                  title: 'Cross-Platform Sync',
                  description: 'Your messages and data sync seamlessly across all devices'
                },
                {
                  icon: 'fas fa-shield-alt',
                  title: 'End-to-End Encryption',
                  description: 'Military-grade security on every platform'
                },
                {
                  icon: 'fas fa-bolt',
                  title: 'Lightning Fast',
                  description: 'Optimized performance on all devices'
                },
                {
                  icon: 'fas fa-mobile-alt',
                  title: 'Mobile First',
                  description: 'Designed for mobile with desktop power'
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 text-center"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 + index * 0.1 }}
                >
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className={`${feature.icon} text-purple-900`}></i>
                  </div>
                  <h3 className="font-bold mb-2 text-yellow-200">{feature.title}</h3>
                  <p className="text-gray-300 text-sm">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* System Requirements */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent text-center">
                System Requirements
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fab fa-apple mr-2"></i>
                    iOS
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• iOS 14.0 or later</li>
                    <li>• iPhone 6s or newer</li>
                    <li>• iPad (5th generation) or newer</li>
                    <li>• 100 MB free storage</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fab fa-android mr-2"></i>
                    Android
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Android 7.0 (API level 24)</li>
                    <li>• 2 GB RAM minimum</li>
                    <li>• ARMv7 or ARM64 processor</li>
                    <li>• 150 MB free storage</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-xl font-semibold mb-4 text-yellow-200">
                    <i className="fas fa-desktop mr-2"></i>
                    Desktop
                  </h3>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Windows 10, macOS 10.14, or Linux</li>
                    <li>• 4 GB RAM minimum</li>
                    <li>• 500 MB free storage</li>
                    <li>• Internet connection required</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center mt-16">
              <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Choose your platform and start experiencing the future of value-based messaging today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all">
                  <i className="fas fa-rocket mr-2"></i>
                  Try Web App Now
                </Link>
                <Link href="/support" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all">
                  <i className="fas fa-question-circle mr-2"></i>
                  Need Help?
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
