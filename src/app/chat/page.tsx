'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

type MessageType = 'text' | 'payment' | 'image';
type MessageStatus = 'sent' | 'delivered' | 'read' | 'sending';

interface User {
  id: string;
  name: string;
  username: string;
  isOnline: boolean;
  lastSeen?: string;
  avatar?: string;
}

interface Message {
  id: string;
  senderId: string;
  text: string;
  timestamp: Date;
  status: MessageStatus;
  type: MessageType;
  amount?: number;
  currency?: string;
  imageUrl?: string;
}

interface Chat {
  id: string;
  participant: User;
  lastMessage: string;
  timestamp: Date;
  unreadCount: number;
  messages: Message[];
}

// Mock data for demonstration
const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    username: 'johndo<PERSON>',
    isOnline: true,
    lastSeen: '2 min ago',
    avatar: 'JD'
  },
  {
    id: '2',
    name: '<PERSON>',
    username: 'j<PERSON><PERSON>',
    isOnline: false,
    lastSeen: '1 hour ago',
    avatar: 'JS'
  },
];

const mockMessages: Message[] = [
  {
    id: '1',
    senderId: '1',
    text: 'Hey there!',
    timestamp: new Date(Date.now() - 3600000),
    status: 'read',
    type: 'text',
  },
  {
    id: '2',
    senderId: 'current',
    text: 'Hi! How are you?',
    timestamp: new Date(Date.now() - 1800000),
    status: 'read',
    type: 'text',
  },
  {
    id: '3',
    senderId: '1',
    text: 'Can you send me the files?',
    timestamp: new Date(),
    status: 'read',
    type: 'text',
  },
];

const mockChats: Chat[] = [
  {
    id: '1',
    participant: mockUsers[0],
    lastMessage: 'Can you send me the files?',
    timestamp: new Date(),
    unreadCount: 2,
    messages: mockMessages,
  },
  {
    id: '2',
    participant: mockUsers[1],
    lastMessage: 'Meeting at 3 PM',
    timestamp: new Date(Date.now() - 86400000),
    unreadCount: 0,
    messages: [],
  },
];

export default function ChatPage() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chats, setChats] = useState<Chat[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Simulate loading user data
    const loadData = async () => {
      try {
        setIsLoading(true);
        const user: User = {
          id: 'current',
          name: 'Current User',
          username: 'currentuser',
          isOnline: true,
          avatar: 'CU'
        };
        setCurrentUser(user);
        setChats(mockChats);
        setCurrentChat(mockChats[0]);
        setMessages(mockMessages);
      } catch (error) {
        console.error('Error loading chat data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Auto-scroll to bottom of messages and mark messages as read
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
    
    // Mark messages as read when chat is opened
    if (currentChat) {
      const updatedChats = chats.map(chat => {
        if (chat.id === currentChat.id && chat.unreadCount > 0) {
          return { ...chat, unreadCount: 0 };
        }
        return chat;
      });
      setChats(updatedChats);
    }
  }, [messages, currentChat]);

  const handleSendMessage = (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!newMessage.trim() || !currentChat || !currentUser) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUser.id,
      text: newMessage,
      timestamp: new Date(),
      status: 'sending',
      type: 'text',
    };

    // Optimistic update
    const updatedMessages = [...messages, message];
    setMessages(updatedMessages);
    setNewMessage('');

    // Simulate message sending
    setTimeout(() => {
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === message.id 
            ? { ...msg, status: 'delivered' } 
            : msg
        )
      );
    }, 1000);

    // Update chat list
    const updatedChats = chats.map((chat) =>
      chat.id === currentChat.id
        ? {
            ...chat,
            lastMessage: newMessage,
            timestamp: new Date(),
            unreadCount: 0,
            messages: [...updatedMessages],
          }
        : chat
    );
    
    setChats(updatedChats);
    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);
  };

  const handleSendPayment = () => {
    if (!paymentAmount || !currentChat || !currentUser) return;

    const message: Message = {
      id: Date.now().toString(),
      senderId: currentUser.id,
      text: `Payment of $${paymentAmount}`,
      timestamp: new Date(),
      status: 'sent',
      type: 'payment',
      amount: parseFloat(paymentAmount),
      currency: 'USD',
    };

    const updatedMessages = [...messages, message];
    setMessages(updatedMessages);
    setShowPaymentModal(false);
    setPaymentAmount('');

    // Update last message in chats
    const updatedChats = chats.map((chat) =>
      chat.id === currentChat.id
        ? {
            ...chat,
            lastMessage: `Payment of $${paymentAmount}`,
            timestamp: new Date(),
            unreadCount: 0,
            messages: updatedMessages,
          }
        : chat
    );
    setChats(updatedChats);
    setCurrentChat(updatedChats.find(chat => chat.id === currentChat.id) || null);
  };

  const handleLogout = () => {
    // Clear user session and redirect to auth page
    router.push('/auth');
  };

  if (isLoading || !currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-purple-900 to-indigo-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-400 mx-auto mb-4"></div>
          <p className="text-white">Loading chat...</p>
        </div>
      </div>
    );
  }

  // Format time for messages
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date for message grouping
  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Get message status icon
  const getStatusIcon = (status: MessageStatus) => {
    switch (status) {
      case 'sending':
        return <span className="text-gray-400">🕒</span>;
      case 'sent':
        return <span className="text-gray-400">✓</span>;
      case 'delivered':
        return <span className="text-gray-400">✓✓</span>;
      case 'read':
        return <span className="text-blue-500">✓✓</span>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-full md:w-1/3 border-r border-gray-200 bg-white flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center bg-white sticky top-0 z-10">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-white font-bold">
              {currentUser.name.split(' ').map((n: string) => n[0]).join('')}
            </div>
            <div className="ml-3">
              <h2 className="font-semibold">{currentUser.name}</h2>
              <p className="text-xs text-gray-500">@{currentUser.username}</p>
            </div>
          </div>
          <button 
            onClick={handleLogout}
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100"
            title="Logout"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex flex-col">
          {currentChat ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-gray-200 flex items-center bg-white sticky top-0 z-10">
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-semibold">
                    {currentChat.participant.name.split(' ').map((n: string) => n[0]).join('')}
                  </div>
                  {currentChat.participant.isOnline && (
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                  )}
                </div>
                <div className="ml-3">
                  <h2 className="font-semibold">{currentChat.participant.name}</h2>
                  <p className="text-xs text-gray-500">
                    {currentChat.participant.isOnline 
                      ? 'Online' 
                      : `Last seen ${currentChat.participant.lastSeen}`}
                  </p>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
                {messages.length > 0 && (
                  <div className="text-center mb-4">
                    <span className="text-xs text-gray-500 bg-white px-3 py-1 rounded-full">
                      {formatDate(messages[0].timestamp)}
                    </span>
                  </div>
                )}
                
                <div className="space-y-4">
                  {messages.map((message, index) => {
                    const isCurrentUser = message.senderId === currentUser.id;
                    const showDate = index === 0 || 
                      new Date(message.timestamp).toDateString() !== 
                      new Date(messages[index - 1].timestamp).toDateString();
                    
                    return (
                      <div key={message.id} className="space-y-1">
                        {showDate && index !== 0 && (
                          <div className="text-center my-4">
                            <span className="text-xs text-gray-500 bg-white px-3 py-1 rounded-full">
                              {formatDate(message.timestamp)}
                            </span>
                          </div>
                        )}
                        
                        <div className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                            isCurrentUser
                              ? 'bg-yellow-400 rounded-tr-none'
                              : 'bg-white border border-gray-200 rounded-tl-none shadow-sm'
                          }`}>
                            {message.type === 'payment' && (
                              <div className="flex items-center mb-1">
                                <svg className="w-4 h-4 text-green-600 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                                </svg>
                                <span className="text-sm font-medium text-green-700">
                                  Payment: ${message.amount?.toFixed(2)}
                                </span>
                              </div>
                            )}
                            <p className={`text-gray-800 ${message.type === 'payment' ? 'text-sm' : ''}`}>
                              {message.text}
                            </p>
                            <div className="flex items-center justify-end mt-1 space-x-1">
                              <span className="text-xs text-gray-500">
                                {formatTime(message.timestamp)}
                              </span>
                              {isCurrentUser && (
                                <span className="ml-1">
                                  {getStatusIcon(message.status)}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="p-4 border-t border-gray-200 bg-white">
                <form onSubmit={handleSendMessage} className="flex items-center">
                  <div className="relative flex-1">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type a message..."
                      className="w-full py-3 px-4 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent pr-12"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-1">
                      <button
                        type="button"
                        className="text-gray-400 hover:text-gray-600 focus:outline-none"
                      >
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </button>
                      <button
                        type="button"
                        className="text-gray-400 hover:text-gray-600 focus:outline-none"
                      >
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <button
                    type="submit"
                    disabled={!newMessage.trim()}
                    className={`ml-2 p-2 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      newMessage.trim()
                        ? 'bg-yellow-400 hover:bg-yellow-500 focus:ring-yellow-400'
                        : 'bg-gray-300 cursor-not-allowed'
                    }`}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowPaymentModal(true)}
                    className="ml-2 p-2 rounded-full bg-green-500 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-400 focus:ring-offset-2"
                    title="Send Payment"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </button>
                </form>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center p-8">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-1">Select a chat</h3>
                <p className="text-gray-500">Choose a conversation to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Send Payment</h3>
                <button
                  onClick={() => {
                    setShowPaymentModal(false);
                    setPaymentAmount('');
                  }}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="mb-6">
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                  Amount (USD)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    name="amount"
                    id="amount"
                    value={paymentAmount}
                    onChange={(e) => setPaymentAmount(e.target.value)}
                    className="focus:ring-yellow-400 focus:border-yellow-400 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md py-3"
                    placeholder="0.00"
                    step="0.01"
                    min="0.01"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowPaymentModal(false);
                    setPaymentAmount('');
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSendPayment}
                  disabled={!paymentAmount || parseFloat(paymentAmount) <= 0}
                  className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-400 ${
                    !paymentAmount || parseFloat(paymentAmount) <= 0
                      ? 'bg-yellow-300 cursor-not-allowed'
                      : 'bg-yellow-500 hover:bg-yellow-600'
                  }`}
                >
                  Send Payment
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
