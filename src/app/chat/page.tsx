'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface User {
  id: string;
  uid: string;
  phoneNumber: string;
  name: string;
  username: string;
  displayName: string;
}

interface Chat {
  id: string;
  participants: string[];
  lastMessage: string;
  timestamp: Date;
}

interface Message {
  id: string;
  chatId: string;
  senderId: string;
  text: string;
  timestamp: Date;
  type: 'text' | 'payment';
  amount?: number;
}

export default function ChatPage() {
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [showSettings, setShowSettings] = useState(false);

  // Mock data
  const mockChats: Chat[] = [
    {
      id: 'chat1',
      participants: ['user1', 'user2'],
      lastMessage: 'Hey! Can you send me $50 for dinner?',
      timestamp: new Date()
    },
    {
      id: 'chat2',
      participants: ['user1', 'user3'],
      lastMessage: 'Thanks for the payment!',
      timestamp: new Date(Date.now() - 3600000)
    }
  ];

  const mockUsers = {
    user2: { id: 'user2', name: 'Alex Rivera', isOnline: true },
    user3: { id: 'user3', name: 'Sarah Chen', isOnline: false }
  };

  const mockMessages: Message[] = [
    {
      id: 'msg1',
      chatId: 'chat1',
      senderId: 'user2',
      text: 'Hey! Can you send me $50 for dinner? 🍕',
      timestamp: new Date(Date.now() - 1800000),
      type: 'text'
    },
    {
      id: 'msg2',
      chatId: 'chat1',
      senderId: 'user1',
      text: 'Sure! Sending now 💰',
      timestamp: new Date(Date.now() - 1200000),
      type: 'text'
    },
    {
      id: 'msg3',
      chatId: 'chat1',
      senderId: 'user1',
      text: 'Payment sent',
      timestamp: new Date(Date.now() - 1200000),
      type: 'payment',
      amount: 50
    }
  ];

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('boguani_user');
      router.push('/auth');
    }
  };

  const sendMessage = () => {
    if (!newMessage.trim() || !selectedChat) return;

    const message: Message = {
      id: 'msg' + Date.now(),
      chatId: selectedChat.id,
      senderId: currentUser?.id || 'user1',
      text: newMessage,
      timestamp: new Date(),
      type: 'text'
    };

    setMessages([...messages, message]);
    setNewMessage('');
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('boguani_user');
      if (userData) {
        setCurrentUser(JSON.parse(userData));
        setSelectedChat(mockChats[0]);
        setMessages(mockMessages);
      } else {
        router.push('/auth');
      }
    }
  }, [router]);

  if (!currentUser) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{
        background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)'
      }}>
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative h-screen w-full" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-1/4 bg-gradient-to-b from-purple-900/40 to-black/40 backdrop-blur-lg flex flex-col h-full border-r border-yellow-500/20">
            
            {/* App Header */}
            <div className="p-6 bg-gradient-to-r from-purple-900/80 to-black/80 backdrop-blur-lg text-white flex justify-between items-center border-b border-yellow-500/30">
              <h1 className="text-2xl font-bold flex items-center">
                <div className="w-8 h-8 mr-3 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-comment-dollar text-sm text-purple-900"></i>
                </div>
                <span className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">BoGuani</span>
              </h1>
              <div className="flex space-x-3">
                <button className="w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                  <i className="fas fa-plus text-yellow-400"></i>
                </button>
                <button 
                  className="w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300"
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <i className="fas fa-cog text-yellow-400"></i>
                </button>
              </div>
            </div>
            
            {/* User Profile */}
            <div className="p-6 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-lg border-b border-yellow-500/20">
              <div className="flex items-center">
                <div className="w-14 h-14 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-xl mr-4 shadow-lg">
                  {currentUser.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-lg text-yellow-200">{currentUser.name}</h3>
                  <p className="text-sm text-gray-300">@{currentUser.username}</p>
                  <p className="text-xs text-yellow-400 italic">&quot;Speak Gold. Share Value.&quot;</p>
                </div>
                <button 
                  className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center hover:bg-red-500/30 transition-all duration-300"
                  onClick={logout}
                >
                  <i className="fas fa-sign-out-alt text-red-400"></i>
                </button>
              </div>
            </div>

            {/* Chat List */}
            <div className="flex-1 overflow-y-auto">
              {mockChats.map((chat) => {
                const otherUserId = chat.participants.find(p => p !== currentUser.id);
                const otherUser = mockUsers[otherUserId as keyof typeof mockUsers];
                
                return (
                  <div
                    key={chat.id}
                    className={`p-4 border-b border-yellow-500/10 cursor-pointer hover:bg-yellow-400/10 transition-all ${
                      selectedChat?.id === chat.id ? 'bg-yellow-400/20' : ''
                    }`}
                    onClick={() => setSelectedChat(chat)}
                  >
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold mr-3">
                        {otherUser.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-white">{otherUser.name}</h3>
                        <p className="text-sm text-gray-400 truncate">{chat.lastMessage}</p>
                      </div>
                      <div className="text-right">
                        <span className={`w-2 h-2 ${otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full inline-block`}></span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Chat Area */}
          <div className="w-3/4 flex flex-col h-full bg-gradient-to-b from-purple-900/40 to-black/40 backdrop-blur-lg">
            {selectedChat ? (
              <>
                {/* Chat Header */}
                <div className="p-6 bg-gradient-to-r from-purple-900/80 to-black/80 backdrop-blur-lg border-b border-yellow-500/30 flex justify-between items-center">
                  <div className="flex items-center">
                    {(() => {
                      const otherUserId = selectedChat.participants.find(p => p !== currentUser.id);
                      const otherUser = mockUsers[otherUserId as keyof typeof mockUsers];
                      return (
                        <>
                          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-yellow-400 to-yellow-200 flex items-center justify-center text-purple-900 font-bold text-lg mr-4 shadow-lg">
                            {otherUser.name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <h2 className="font-bold text-xl text-yellow-200">{otherUser.name}</h2>
                            <p className="text-sm text-gray-300 flex items-center">
                              <span className={`w-2 h-2 ${otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full mr-2`}></span>
                              {otherUser.isOnline ? 'Online' : 'Offline'}
                            </p>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                  <div className="flex space-x-3">
                    <button className="w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                      <i className="fas fa-phone text-yellow-400"></i>
                    </button>
                    <button className="w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                      <i className="fas fa-video text-yellow-400"></i>
                    </button>
                    <button className="w-10 h-10 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                      <i className="fas fa-ellipsis-v text-yellow-400"></i>
                    </button>
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  {messages
                    .filter(msg => msg.chatId === selectedChat.id)
                    .map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.senderId === currentUser.id ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                            message.senderId === currentUser.id
                              ? 'bg-gradient-to-r from-yellow-400/30 to-yellow-200/30 text-white rounded-br-sm'
                              : 'bg-gradient-to-r from-purple-600/30 to-purple-800/30 text-white rounded-bl-sm'
                          }`}
                        >
                          {message.type === 'payment' ? (
                            <div className="text-center">
                              <div className="bg-yellow-400/20 p-3 rounded-lg">
                                <i className="fas fa-dollar-sign text-yellow-400 text-lg mb-2"></i>
                                <p className="text-yellow-200 font-semibold">${message.amount?.toFixed(2)} sent securely</p>
                                <p className="text-xs text-gray-400 mt-1">End-to-end encrypted</p>
                              </div>
                            </div>
                          ) : (
                            <p className="text-sm">{message.text}</p>
                          )}
                          <p className="text-xs text-gray-400 mt-1">
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>

                {/* Message Input */}
                <div className="p-6 bg-gradient-to-r from-purple-900/60 to-black/60 backdrop-blur-lg border-t border-yellow-500/20">
                  <div className="flex space-x-4">
                    <button className="w-12 h-12 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                      <i className="fas fa-plus text-yellow-400"></i>
                    </button>
                    <div className="flex-1 flex space-x-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type a message..."
                        className="flex-1 px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      />
                      <button className="w-12 h-12 rounded-full bg-yellow-400/20 flex items-center justify-center hover:bg-yellow-400/30 transition-all duration-300">
                        <i className="fas fa-dollar-sign text-yellow-400"></i>
                      </button>
                    </div>
                    <button 
                      onClick={sendMessage}
                      className="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-200 flex items-center justify-center hover:from-yellow-200 hover:to-yellow-400 transition-all duration-300"
                    >
                      <i className="fas fa-paper-plane text-purple-900"></i>
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-comment-dollar text-4xl text-purple-900"></i>
                  </div>
                  <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                    Welcome to BoGuani
                  </h2>
                  <p className="text-gray-400">Select a chat to start messaging</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
