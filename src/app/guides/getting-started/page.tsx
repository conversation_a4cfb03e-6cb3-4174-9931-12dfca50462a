'use client';

import Link from 'next/link';

export default function GettingStartedGuide() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .btn-hover {
          transition: all 0.3s ease;
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: rgba(31, 41, 55, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Header */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <Link href="/guides" className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors">
                <i className="fas fa-arrow-left mr-2"></i> Back to Guides
              </Link>
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-play-circle text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 gold-gradient">Getting Started with BoGuani</h1>
              <div className="flex items-center justify-center space-x-4 text-gray-400">
                <span className="bg-gradient-to-r from-green-600 to-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">Beginner</span>
                <span>•</span>
                <span>5 min read</span>
              </div>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="pb-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="glass-card rounded-2xl p-8 professional-shadow">
              <div className="prose prose-lg prose-invert max-w-none">
                <h2 className="text-3xl font-bold mb-6 gold-gradient">Welcome to BoGuani!</h2>
                <p className="text-gray-300 text-lg leading-relaxed mb-8">
                  BoGuani is the world&apos;s first value-based communication platform, inspired by ancient Taíno wisdom. 
                  This guide will help you get started with your account and send your first message.
                </p>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 1: Create Your Account</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>Visit the BoGuani web app or download our mobile app</li>
                    <li>Click &quot;Get Started&quot; or &quot;Sign Up&quot;</li>
                    <li>Enter your phone number</li>
                    <li>Verify your number with the SMS code we send</li>
                    <li>Set up your profile with your name and username</li>
                  </ol>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 2: Explore the Interface</h3>
                <p className="text-gray-300 mb-6">
                  Once you&apos;re logged in, you&apos;ll see the main chat interface. Here&apos;s what each section does:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Left Panel</h4>
                    <ul className="space-y-2 text-gray-300">
                      <li>• Your conversations list</li>
                      <li>• Search for contacts</li>
                      <li>• Settings and profile</li>
                    </ul>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Main Area</h4>
                    <ul className="space-y-2 text-gray-300">
                      <li>• Active conversation</li>
                      <li>• Message history</li>
                      <li>• Send messages and payments</li>
                    </ul>
                  </div>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 3: Send Your First Message</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>Click the &quot;+&quot; button to start a new conversation</li>
                    <li>Enter a phone number or search for a contact</li>
                    <li>Type your message in the text box</li>
                    <li>Press Enter or click the send button</li>
                  </ol>
                </div>

                <div className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 rounded-lg p-6 mb-8">
                  <h4 className="text-xl font-semibold mb-3 text-yellow-400">💡 Pro Tip</h4>
                  <p className="text-gray-300">
                    BoGuani automatically encrypts all your messages with end-to-end encryption. 
                    You&apos;ll see a lock icon next to encrypted conversations.
                  </p>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Next Steps</h3>
                <p className="text-gray-300 mb-6">
                  Now that you&apos;ve sent your first message, you&apos;re ready to explore more features:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link href="/guides/first-payment" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Send Your First Payment</h4>
                    <p className="text-gray-400 text-sm">Learn how to send money securely</p>
                  </Link>
                  <Link href="/guides/security" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Understanding Security</h4>
                    <p className="text-gray-400 text-sm">Learn about our encryption features</p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center">
              <Link href="/" className="flex items-center justify-center mb-4">
                <div className="text-yellow-400 text-2xl mr-2">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-xl gold-gradient">BoGuani</span>
              </Link>
              <p className="text-gray-400 mb-4">Messenger of Value</p>
              <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
