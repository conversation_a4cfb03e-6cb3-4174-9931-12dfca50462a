'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function GuidesPage() {
  const guides = [
    {
      title: 'Getting Started with BoGuani',
      description: 'Learn the basics of setting up your account and sending your first message',
      icon: 'fas fa-play-circle',
      category: 'Beginner',
      readTime: '5 min'
    },
    {
      title: 'Sending Your First Payment',
      description: 'Step-by-step guide to sending money securely through BoGuani',
      icon: 'fas fa-dollar-sign',
      category: 'Payments',
      readTime: '3 min'
    },
    {
      title: 'Understanding End-to-End Encryption',
      description: 'Learn how BoGuani keeps your messages and payments secure',
      icon: 'fas fa-shield-alt',
      category: 'Security',
      readTime: '7 min'
    },
    {
      title: 'Managing Group Chats and Payments',
      description: 'Create groups, split bills, and manage shared expenses',
      icon: 'fas fa-users',
      category: 'Advanced',
      readTime: '8 min'
    },
    {
      title: 'Setting Up Two-Factor Authentication',
      description: 'Add an extra layer of security to your BoGuani account',
      icon: 'fas fa-lock',
      category: 'Security',
      readTime: '4 min'
    },
    {
      title: 'Troubleshooting Common Issues',
      description: 'Solutions to frequently encountered problems',
      icon: 'fas fa-wrench',
      category: 'Support',
      readTime: '6 min'
    }
  ];

  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
              <Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-book text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                User Guides
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Step-by-step tutorials to help you master BoGuani and get the most out of your messaging experience.
              </p>
            </div>

            {/* Guides Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {guides.map((guide, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 cursor-pointer group"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                      <i className={`${guide.icon} text-purple-900`}></i>
                    </div>
                    <div className="text-right">
                      <span className="text-xs bg-yellow-400/20 text-yellow-400 px-2 py-1 rounded-full">
                        {guide.category}
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold mb-3 text-yellow-200 group-hover:text-yellow-100 transition-colors">
                    {guide.title}
                  </h3>
                  
                  <p className="text-gray-300 text-sm leading-relaxed mb-4">
                    {guide.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-gray-400 flex items-center">
                      <i className="fas fa-clock mr-1"></i>
                      {guide.readTime}
                    </span>
                    <span className="text-yellow-400 text-sm font-semibold group-hover:text-yellow-200 transition-colors">
                      Read Guide →
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Quick Start Section */}
            <div className="mt-16">
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 text-center"
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <h2 className="text-3xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  Quick Start
                </h2>
                <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
                  New to BoGuani? Get up and running in just a few minutes with our quick start guide.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                      <span className="text-purple-900 font-bold text-xl">1</span>
                    </div>
                    <h3 className="font-semibold text-yellow-200 mb-2">Sign Up</h3>
                    <p className="text-gray-300 text-sm">Create your account with just your phone number</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                      <span className="text-purple-900 font-bold text-xl">2</span>
                    </div>
                    <h3 className="font-semibold text-yellow-200 mb-2">Verify</h3>
                    <p className="text-gray-300 text-sm">Confirm your phone number with the SMS code</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                      <span className="text-purple-900 font-bold text-xl">3</span>
                    </div>
                    <h3 className="font-semibold text-yellow-200 mb-2">Start Messaging</h3>
                    <p className="text-gray-300 text-sm">Begin secure conversations and value transfers</p>
                  </div>
                </div>
                
                <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all inline-block">
                  <i className="fas fa-rocket mr-2"></i>
                  Get Started Now
                </Link>
              </motion.div>
            </div>

            {/* Need More Help */}
            <div className="text-center mt-16">
              <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Need More Help?
              </h2>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/support" className="border-2 border-yellow-400 text-yellow-400 px-6 py-3 rounded-full font-semibold hover:bg-yellow-400 hover:text-purple-900 transition-all">
                  <i className="fas fa-question-circle mr-2"></i>
                  Visit Support Center
                </Link>
                <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-3 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                  <i className="fas fa-envelope mr-2"></i>
                  Contact Support
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
