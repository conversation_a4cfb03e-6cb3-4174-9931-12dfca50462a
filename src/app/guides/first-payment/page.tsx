'use client';

import Link from 'next/link';

export default function FirstPaymentGuide() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: rgba(31, 41, 55, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Header */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <Link href="/guides" className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors">
                <i className="fas fa-arrow-left mr-2"></i> Back to Guides
              </Link>
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-dollar-sign text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 gold-gradient">Sending Your First Payment</h1>
              <div className="flex items-center justify-center space-x-4 text-gray-400">
                <span className="bg-gradient-to-r from-green-600 to-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">Payments</span>
                <span>•</span>
                <span>3 min read</span>
              </div>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="pb-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="glass-card rounded-2xl p-8 professional-shadow">
              <div className="prose prose-lg prose-invert max-w-none">
                <h2 className="text-3xl font-bold mb-6 gold-gradient">Send Money Securely with BoGuani</h2>
                <p className="text-gray-300 text-lg leading-relaxed mb-8">
                  BoGuani makes sending money as easy as sending a message. This guide will walk you through 
                  your first secure payment transaction.
                </p>

                <div className="bg-gradient-to-r from-red-500/20 to-red-400/20 border border-red-400/30 rounded-lg p-6 mb-8">
                  <h4 className="text-xl font-semibold mb-3 text-red-400">⚠️ Before You Start</h4>
                  <p className="text-gray-300">
                    Make sure you have completed identity verification and linked a bank account or payment method 
                    to your BoGuani account.
                  </p>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 1: Open a Conversation</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>Open BoGuani and select an existing conversation</li>
                    <li>Or start a new conversation with the person you want to pay</li>
                    <li>Make sure the conversation shows the lock icon (encrypted)</li>
                  </ol>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 2: Initiate Payment</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>Look for the dollar sign ($) button in the message input area</li>
                    <li>Click the $ button to open the payment interface</li>
                    <li>Enter the amount you want to send</li>
                    <li>Add an optional note describing the payment</li>
                  </ol>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 3: Review and Confirm</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>Review the payment details carefully</li>
                    <li>Check the recipient&apos;s information</li>
                    <li>Verify the amount and any fees</li>
                    <li>Click &quot;Send Payment&quot; to confirm</li>
                  </ol>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Step 4: Authentication</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <ol className="list-decimal list-inside space-y-3 text-gray-300">
                    <li>You may be asked to authenticate with biometrics or PIN</li>
                    <li>Complete the authentication process</li>
                    <li>Wait for the payment confirmation</li>
                  </ol>
                </div>

                <div className="bg-gradient-to-r from-green-500/20 to-green-400/20 border border-green-400/30 rounded-lg p-6 mb-8">
                  <h4 className="text-xl font-semibold mb-3 text-green-400">✅ Payment Sent!</h4>
                  <p className="text-gray-300">
                    Your payment has been sent securely. Both you and the recipient will receive confirmation 
                    messages, and the transaction will appear in your payment history.
                  </p>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Security Features</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">End-to-End Encryption</h4>
                    <p className="text-gray-300">All payment data is encrypted from your device to the recipient&apos;s device.</p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Bank-Grade Security</h4>
                    <p className="text-gray-300">We use the same security standards as major financial institutions.</p>
                  </div>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Next Steps</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link href="/guides/group-payments" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Group Payments</h4>
                    <p className="text-gray-400 text-sm">Learn about splitting bills and group expenses</p>
                  </Link>
                  <Link href="/guides/security" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Security Features</h4>
                    <p className="text-gray-400 text-sm">Understand how we protect your money</p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center">
              <Link href="/" className="flex items-center justify-center mb-4">
                <div className="text-yellow-400 text-2xl mr-2">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-xl gold-gradient">BoGuani</span>
              </Link>
              <p className="text-gray-400 mb-4">Messenger of Value</p>
              <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
