'use client';

import Link from 'next/link';

export default function SecurityGuide() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: rgba(31, 41, 55, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Header */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="text-center mb-16">
              <Link href="/guides" className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors">
                <i className="fas fa-arrow-left mr-2"></i> Back to Guides
              </Link>
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-shield-alt text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 gold-gradient">Understanding End-to-End Encryption</h1>
              <div className="flex items-center justify-center space-x-4 text-gray-400">
                <span className="bg-gradient-to-r from-blue-600 to-blue-500 text-white px-3 py-1 rounded-full text-sm font-bold">Security</span>
                <span>•</span>
                <span>7 min read</span>
              </div>
            </div>
          </div>
        </section>

        {/* Content */}
        <section className="pb-20 px-6">
          <div className="container mx-auto max-w-4xl">
            <div className="glass-card rounded-2xl p-8 professional-shadow">
              <div className="prose prose-lg prose-invert max-w-none">
                <h2 className="text-3xl font-bold mb-6 gold-gradient">Your Privacy is Sacred</h2>
                <p className="text-gray-300 text-lg leading-relaxed mb-8">
                  At BoGuani, we believe your conversations and financial transactions should be as private as 
                  ancient sacred communications. Learn how our end-to-end encryption protects you.
                </p>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">What is End-to-End Encryption?</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <p className="text-gray-300 mb-4">
                    End-to-end encryption means that only you and the person you&apos;re communicating with can read 
                    your messages. Not even BoGuani can access your message content.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-green-500 rounded-full flex items-center justify-center">
                        <i className="fas fa-mobile-alt text-white"></i>
                      </div>
                      <p className="text-sm text-gray-400">Your Device</p>
                      <p className="text-xs text-green-400">Encrypted</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i className="fas fa-server text-white"></i>
                      </div>
                      <p className="text-sm text-gray-400">BoGuani Servers</p>
                      <p className="text-xs text-yellow-400">Cannot Read</p>
                    </div>
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto mb-3 bg-green-500 rounded-full flex items-center justify-center">
                        <i className="fas fa-mobile-alt text-white"></i>
                      </div>
                      <p className="text-sm text-gray-400">Recipient&apos;s Device</p>
                      <p className="text-xs text-green-400">Decrypted</p>
                    </div>
                  </div>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">How We Protect Your Messages</h3>
                <div className="space-y-6 mb-8">
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Signal Protocol</h4>
                    <p className="text-gray-300">
                      We use the Signal Protocol, the same encryption used by WhatsApp and Signal, 
                      ensuring military-grade security for all your communications.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Perfect Forward Secrecy</h4>
                    <p className="text-gray-300">
                      Each message uses a unique encryption key. Even if one key is compromised, 
                      your other messages remain secure.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <h4 className="text-xl font-semibold mb-3 text-yellow-400">Zero Knowledge Architecture</h4>
                    <p className="text-gray-300">
                      BoGuani servers never have access to your encryption keys or message content. 
                      We literally cannot read your messages.
                    </p>
                  </div>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Payment Security</h3>
                <div className="bg-gray-800/50 rounded-lg p-6 mb-8">
                  <p className="text-gray-300 mb-4">
                    Your financial transactions receive the same level of protection as your messages:
                  </p>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Payment details are encrypted end-to-end</li>
                    <li>• Bank information is tokenized and never stored in plain text</li>
                    <li>• All transactions use secure banking protocols</li>
                    <li>• Multi-factor authentication for sensitive operations</li>
                  </ul>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Verification Indicators</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <div className="flex items-center mb-3">
                      <i className="fas fa-lock text-green-400 mr-3"></i>
                      <h4 className="text-lg font-semibold text-green-400">Encrypted</h4>
                    </div>
                    <p className="text-gray-300 text-sm">
                      This icon appears when your conversation is fully encrypted and secure.
                    </p>
                  </div>
                  <div className="bg-gray-800/50 rounded-lg p-6">
                    <div className="flex items-center mb-3">
                      <i className="fas fa-check-circle text-blue-400 mr-3"></i>
                      <h4 className="text-lg font-semibold text-blue-400">Verified</h4>
                    </div>
                    <p className="text-gray-300 text-sm">
                      This icon shows when a contact&apos;s identity has been verified.
                    </p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 border border-yellow-400/30 rounded-lg p-6 mb-8">
                  <h4 className="text-xl font-semibold mb-3 text-yellow-400">💡 Best Practices</h4>
                  <ul className="space-y-2 text-gray-300">
                    <li>• Always verify the lock icon before sending sensitive information</li>
                    <li>• Use strong device passwords and biometric authentication</li>
                    <li>• Keep your BoGuani app updated to the latest version</li>
                    <li>• Never share your account credentials with anyone</li>
                  </ul>
                </div>

                <h3 className="text-2xl font-bold mb-4 text-yellow-400">Questions About Security?</h3>
                <p className="text-gray-300 mb-6">
                  We&apos;re committed to transparency about our security practices. If you have questions 
                  about how we protect your data, don&apos;t hesitate to reach out.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link href="/support" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Contact Support</h4>
                    <p className="text-gray-400 text-sm">Get help with security questions</p>
                  </Link>
                  <Link href="/security" className="bg-gray-800/50 hover:bg-gray-700/50 rounded-lg p-4 transition-colors">
                    <h4 className="text-lg font-semibold mb-2 text-yellow-400">Security Center</h4>
                    <p className="text-gray-400 text-sm">Learn more about our security practices</p>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center">
              <Link href="/" className="flex items-center justify-center mb-4">
                <div className="text-yellow-400 text-2xl mr-2">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-xl gold-gradient">BoGuani</span>
              </Link>
              <p className="text-gray-400 mb-4">Messenger of Value</p>
              <p className="text-gray-400 text-sm">© 2024 BoGuani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
