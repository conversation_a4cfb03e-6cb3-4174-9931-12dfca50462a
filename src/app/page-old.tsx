'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="text-white min-h-screen" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="min-h-screen" style={{
        backgroundImage: `
          radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%),
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-10" style={{
          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 100%)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(212, 175, 55, 0.2)'
        }}>
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <div className="text-3xl mr-3" style={{ color: '#D4AF37' }}>
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl" style={{
                background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                color: 'transparent'
              }}>
                BoGuani
              </span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Features</a>
              <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>About</a>
              <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Download</a>
              <Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Privacy</Link>
            </div>
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-white focus:outline-none p-2"
              >
                <i className="fas fa-bars text-xl"></i>
              </button>
            </div>
          </div>
          {/* Mobile menu */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-opacity-20" style={{
              background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)',
              borderColor: '#D4AF37'
            }}>
              <div className="container mx-auto px-6 py-4 flex flex-col space-y-4">
                <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>About</a>
                <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Download</a>
                <Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Privacy</Link>
              </div>
            </div>
          )}
        </nav>
        {/* Navigation */}
        <nav className="fixed w-full z-10" style={{
          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 100%)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(212, 175, 55, 0.2)'
        }}>
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <div className="text-3xl mr-3" style={{ color: '#D4AF37' }}>
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl" style={{
                background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                color: 'transparent'
              }}>
                BoGuani
              </span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Features</a>
              <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>About</a>
              <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Download</a>
              <Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Privacy</Link>
            </div>
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-white focus:outline-none p-2"
              >
                <i className="fas fa-bars text-xl"></i>
              </button>
            </div>
          </div>
          {/* Mobile menu */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-opacity-20" style={{
              background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)',
              borderColor: '#D4AF37'
            }}>
              <div className="container mx-auto px-6 py-4 flex flex-col space-y-4">
                <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>About</a>
                <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Download</a>
                <Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium" style={{ color: '#D4AF37' }}>Privacy</Link>
              </div>
            </div>
          )}
        </nav>

        {/* Hero Section */}
        <header className="pt-32 pb-24 px-6 min-h-screen flex items-center" style={{
          background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)'
        }}>
          <div className="container mx-auto flex flex-col md:flex-row items-center relative">
            {/* Floating background elements */}
            <div className="absolute top-10 left-10 w-32 h-32 opacity-5 rounded-full blur-3xl animate-pulse" style={{ background: '#D4AF37' }}></div>
            <div className="absolute bottom-20 right-20 w-40 h-40 opacity-3 rounded-full blur-3xl animate-pulse" style={{ background: '#D4AF37', animationDelay: '2s' }}></div>

            <div className="md:w-1/2 mb-12 md:mb-0 relative z-10">
              <div className="mb-6">
                <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                  <span style={{
                    background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    color: 'transparent',
                    filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))'
                  }}>BoGuani</span>
                </h1>
                <h2 className="text-3xl md:text-4xl font-semibold mb-4 text-gray-100">Messenger of Value</h2>
                <p className="text-2xl mb-8 italic font-medium" style={{ color: '#F2D675' }}>Where Words Carry Worth</p>
              </div>

              <div className="p-6 rounded-2xl mb-8" style={{
                background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(212, 175, 55, 0.2)'
              }}>
                <p className="mb-6 text-gray-200 text-lg leading-relaxed">
                  Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
                  Send messages that matter, share moments that count, and transfer value instantly -
                  all protected by sacred-level encryption.
                </p>
                <div className="text-center">
                  <p className="text-2xl font-bold" style={{
                    background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    color: 'transparent'
                  }}>"Speak Gold. Share Value."</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-4 justify-center md:justify-start">
                <Link href="/auth" className="px-8 py-4 rounded-full font-bold text-lg transition-all flex items-center shadow-lg relative overflow-hidden" style={{
                  background: 'linear-gradient(to right, #D4AF37, #F2D675)',
                  color: '#2D1B4E'
                }}>
                  <i className="fas fa-globe mr-3"></i> Open BoGuani Web Version
                </Link>
                <a href="#features" className="px-8 py-4 rounded-full font-bold text-lg transition-all flex items-center" style={{
                  border: '2px solid transparent',
                  background: 'linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box, linear-gradient(90deg, #D4AF37, #F2D675) border-box',
                  color: '#D4AF37'
                }}>
                  <i className="fas fa-play mr-3"></i> <span>Watch Demo</span>
                </a>
              </div>
            <div className="md:w-1/2 flex justify-center relative z-10">
              <div className="relative w-80 h-96">
                {/* App mockup */}
                <div className="absolute inset-0 rounded-3xl shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-500" style={{
                  background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: '2px solid transparent',
                  backgroundImage: 'linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)), linear-gradient(90deg, #D4AF37, #F2D675)',
                  backgroundOrigin: 'border-box',
                  backgroundClip: 'content-box, border-box'
                }}>
                  <div className="p-4 border-b" style={{
                    background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)',
                    borderColor: 'rgba(212, 175, 55, 0.4)'
                  }}>
                    <div className="flex justify-between items-center">
                      <div className="text-xl" style={{ color: '#D4AF37' }}>
                        <i className="fas fa-comment-dollar"></i>
                      </div>
                      <p className="font-bold text-lg" style={{
                        background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
                        WebkitBackgroundClip: 'text',
                        backgroundClip: 'text',
                        color: 'transparent'
                      }}>BoGuani</p>
                      <div style={{ color: '#D4AF37' }}>
                        <i className="fas fa-ellipsis-v"></i>
                      </div>
                    </div>
                  </div>
                  <div className="p-4 h-full" style={{
                    background: 'linear-gradient(to bottom, transparent, rgba(45, 27, 78, 0.2))'
                  }}>
                    <div className="flex flex-col h-full space-y-3">
                      <div className="p-3 rounded-xl self-start max-w-[75%]" style={{
                        background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(78, 58, 112, 0.3)'
                      }}>
                        <p className="text-sm text-gray-200">Hey! Can you send me 20 for dinner tonight?</p>
                      </div>
                      <div className="p-3 rounded-xl self-end max-w-[75%]" style={{
                        background: 'linear-gradient(to right, rgba(212, 175, 55, 0.2), rgba(242, 214, 117, 0.2))',
                        border: '1px solid rgba(212, 175, 55, 0.4)'
                      }}>
                        <p className="text-sm text-gray-100">Sure! Sending it now with a special message.</p>
                      </div>
                      <div className="p-3 rounded-xl self-end max-w-[75%]" style={{
                        background: 'linear-gradient(to right, rgba(212, 175, 55, 0.3), rgba(242, 214, 117, 0.3))',
                        border: '1px solid rgba(212, 175, 55, 0.5)'
                      }}>
                        <div className="flex items-center">
                          <div className="mr-2" style={{ color: '#D4AF37' }}>
                            <i className="fas fa-coins"></i>
                          </div>
                          <p className="text-sm font-semibold text-gray-100">$20.00 sent - Enjoy dinner!</p>
                        </div>
                      </div>
                      <div className="p-3 rounded-xl self-start max-w-[75%]" style={{
                        background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(78, 58, 112, 0.3)'
                      }}>
                        <p className="text-sm text-gray-200">Thanks! Value received. 🙏</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Enhanced decorative elements */}
                <div className="absolute -bottom-8 -right-8 w-48 h-48 opacity-10 rounded-full blur-3xl animate-pulse" style={{
                  background: 'linear-gradient(to right, #D4AF37, #F2D675)'
                }}></div>
                <div className="absolute -top-8 -left-8 w-24 h-24 opacity-15 rounded-full blur-2xl animate-pulse" style={{
                  background: 'linear-gradient(to right, #D4AF37, #F2D675)',
                  animationDelay: '1s'
                }}></div>
                <div className="absolute top-1/2 -right-4 w-16 h-16 opacity-20 rounded-full blur-xl animate-bounce" style={{
                  background: '#D4AF37',
                  animationDelay: '3s'
                }}></div>
              </div>
            </div>
          </div>
        </header>
      </div>

      {/* Features Section */}
      <section id="features" className="py-20 relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'
      }}>
        {/* Background decorative elements */}
        <div className="absolute top-0 left-1/4 w-64 h-64 opacity-5 rounded-full blur-3xl" style={{ background: '#D4AF37' }}></div>
        <div className="absolute bottom-0 right-1/4 w-48 h-48 opacity-3 rounded-full blur-3xl" style={{ background: '#D4AF37' }}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{
              background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              color: 'transparent'
            }}>Key Features</h2>
            <div className="w-24 h-1 mx-auto mb-4" style={{
              background: 'linear-gradient(to right, #D4AF37, #F2D675)'
            }}></div>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">Experience the future of value-based communication</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="p-8 rounded-2xl relative overflow-hidden group transition-all duration-400" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',
              backdropFilter: 'blur(15px)',
              border: '1px solid rgba(212, 175, 55, 0.3)'
            }}>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500" style={{
                background: 'linear-gradient(to bottom right, #D4AF37, #F2D675)'
              }}></div>
              <div className="relative z-10">
                <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}>
                  <i className="fas fa-lock"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4" style={{ color: '#F2D675' }}>256-bit End-to-End Encryption</h3>
                <p className="text-gray-300 leading-relaxed">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>
              </div>
            </div>

            <div className="p-8 rounded-2xl relative overflow-hidden group transition-all duration-400" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',
              backdropFilter: 'blur(15px)',
              border: '1px solid rgba(212, 175, 55, 0.3)'
            }}>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500" style={{
                background: 'linear-gradient(to bottom right, #D4AF37, #F2D675)'
              }}></div>
              <div className="relative z-10">
                <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}>
                  <i className="fas fa-bolt"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4" style={{ color: '#F2D675' }}>Instant Money Transfers</h3>
                <p className="text-gray-300 leading-relaxed">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
              </div>
            </div>

            <div className="p-8 rounded-2xl relative overflow-hidden group transition-all duration-400" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.6) 0%, rgba(78, 58, 112, 0.4) 50%, rgba(45, 27, 78, 0.6) 100%)',
              backdropFilter: 'blur(15px)',
              border: '1px solid rgba(212, 175, 55, 0.3)'
            }}>
              <div className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-500" style={{
                background: 'linear-gradient(to bottom right, #D4AF37, #F2D675)'
              }}></div>
              <div className="relative z-10">
                <div className="text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}>
                  <i className="fas fa-headset"></i>
                </div>
                <h3 className="text-2xl font-bold mb-4" style={{ color: '#F2D675' }}>24/7 Global Support</h3>
                <p className="text-gray-300 leading-relaxed">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Download Section */}
      <section id="download" className="py-20 relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)'
      }}>
        {/* Background decorative elements */}
        <div className="absolute top-1/2 left-0 w-72 h-72 opacity-5 rounded-full blur-3xl transform -translate-y-1/2" style={{ background: '#D4AF37' }}></div>
        <div className="absolute top-1/2 right-0 w-72 h-72 opacity-5 rounded-full blur-3xl transform -translate-y-1/2" style={{ background: '#D4AF37' }}></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{
              background: 'linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              color: 'transparent'
            }}>Get BoGuani Now</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-6">Experience the revolution in value-based messaging. Available on all major platforms.</p>
            <div className="w-24 h-1 mx-auto" style={{
              background: 'linear-gradient(to right, #D4AF37, #F2D675)'
            }}></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            <Link href="/downloads" className="px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(212, 175, 55, 0.2)'
            }}>
              <i className="fab fa-apple text-5xl mb-4 group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}></i>
              <div>
                <p className="text-sm text-gray-400 mb-1">Download for</p>
                <p className="font-bold text-lg" style={{ color: '#F2D675' }}>iOS</p>
                <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
              </div>
            </Link>

            <Link href="/downloads" className="px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(212, 175, 55, 0.2)'
            }}>
              <i className="fab fa-android text-5xl mb-4 group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}></i>
              <div>
                <p className="text-sm text-gray-400 mb-1">Download for</p>
                <p className="font-bold text-lg" style={{ color: '#F2D675' }}>Android</p>
                <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
              </div>
            </Link>

            <Link href="/downloads" className="px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden" style={{
              background: 'linear-gradient(135deg, rgba(61, 42, 95, 0.4) 0%, rgba(78, 58, 112, 0.3) 50%, rgba(45, 27, 78, 0.4) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(212, 175, 55, 0.2)'
            }}>
              <i className="fas fa-desktop text-5xl mb-4 group-hover:scale-110 transition-transform duration-300" style={{ color: '#D4AF37' }}></i>
              <div>
                <p className="text-sm text-gray-400 mb-1">Download for</p>
                <p className="font-bold text-lg" style={{ color: '#F2D675' }}>Desktop</p>
                <p className="text-xs text-gray-500 mt-2">Windows, Mac, Linux</p>
              </div>
            </Link>

            <Link href="/auth" className="px-6 py-8 rounded-2xl flex flex-col items-center text-center transition-all duration-300 group relative overflow-hidden" style={{
              background: 'linear-gradient(to right, #D4AF37, #F2D675)',
              color: '#2D1B4E'
            }}>
              <i className="fas fa-globe text-5xl mb-4 group-hover:scale-110 transition-transform duration-300"></i>
              <div>
                <p className="text-sm mb-1 opacity-80">Try it now</p>
                <p className="font-bold text-lg">Web App</p>
                <p className="text-xs mt-2 opacity-80">Available Now</p>
              </div>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
