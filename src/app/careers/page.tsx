'use client';

import Link from 'next/link';

export default function CareersPage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .btn-hover {
          transition: all 0.3s ease;
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: rgba(31, 41, 55, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-6 pt-20">
          <div className="w-full max-w-6xl">
            <div className="text-center mb-16">
              <h1 className="text-5xl lg:text-6xl font-bold mb-6 gold-gradient">Join Our Team</h1>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Help us build the future of value-based communication. Join a team that&apos;s revolutionizing how people connect and share value.
              </p>
            </div>
          </div>
        </section>

        {/* Company Values */}
        <section className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Our Values</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                The principles that guide everything we do at BoGuani
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="glass-card p-8 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-heart text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Sacred Trust</h3>
                <p className="text-gray-300">We protect our users&apos; privacy and security with the same reverence ancient cultures held for sacred communication.</p>
              </div>

              <div className="glass-card p-8 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-lightbulb text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Innovation</h3>
                <p className="text-gray-300">We constantly push boundaries to create meaningful connections between communication and value exchange.</p>
              </div>

              <div className="glass-card p-8 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-users text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Community</h3>
                <p className="text-gray-300">We believe in building inclusive teams that reflect the diverse communities we serve worldwide.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Open Positions */}
        <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Open Positions</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Join our mission to revolutionize communication and value exchange
              </p>
            </div>

            <div className="max-w-4xl mx-auto space-y-6">
              <div className="glass-card rounded-2xl p-8 hover:scale-105 transition-all professional-shadow">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold mb-4 gold-gradient">Senior Full-Stack Developer</h3>
                    <p className="text-gray-300 mb-6 leading-relaxed">Help build our core messaging and payment infrastructure using React, Node.js, and blockchain technologies.</p>
                    <div className="flex flex-wrap gap-3">
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">React</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Node.js</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">TypeScript</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">WebRTC</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-300 font-medium">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>

              <div className="glass-card rounded-2xl p-8 hover:scale-105 transition-all professional-shadow">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold mb-4 gold-gradient">Security Engineer</h3>
                    <p className="text-gray-300 mb-6 leading-relaxed">Lead our security initiatives, implement end-to-end encryption, and ensure the highest standards of user privacy.</p>
                    <div className="flex flex-wrap gap-3">
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Cryptography</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Security</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Penetration Testing</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-300 font-medium">San Francisco</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>

              <div className="glass-card rounded-2xl p-8 hover:scale-105 transition-all professional-shadow">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold mb-4 gold-gradient">Product Designer</h3>
                    <p className="text-gray-300 mb-6 leading-relaxed">Design intuitive user experiences that make complex financial communications feel natural and beautiful.</p>
                    <div className="flex flex-wrap gap-3">
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">UI/UX</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Figma</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Mobile Design</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-300 font-medium">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>

              <div className="glass-card rounded-2xl p-8 hover:scale-105 transition-all professional-shadow">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-2xl font-bold mb-4 gold-gradient">DevOps Engineer</h3>
                    <p className="text-gray-300 mb-6 leading-relaxed">Scale our infrastructure to handle millions of secure messages and transactions worldwide.</p>
                    <div className="flex flex-wrap gap-3">
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">AWS</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Kubernetes</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Docker</span>
                      <span className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 text-yellow-400 px-4 py-2 rounded-full text-sm font-medium">Terraform</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-gray-300 font-medium">Remote</p>
                    <p className="text-gray-400 text-sm">Full-time</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits */}
        <section className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Why Work With Us</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Join a team that values innovation, growth, and meaningful impact
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="glass-card p-6 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-home text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Remote First</h3>
                <p className="text-gray-300">Work from anywhere in the world</p>
              </div>

              <div className="glass-card p-6 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-heart text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Health & Wellness</h3>
                <p className="text-gray-300">Comprehensive health coverage</p>
              </div>

              <div className="glass-card p-6 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-graduation-cap text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Learning Budget</h3>
                <p className="text-gray-300">$2,000 annual learning allowance</p>
              </div>

              <div className="glass-card p-6 rounded-2xl text-center professional-shadow">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                  <i className="fas fa-chart-line text-2xl text-purple-900"></i>
                </div>
                <h3 className="text-xl font-bold mb-4 gold-gradient">Equity Package</h3>
                <p className="text-gray-300">Share in our success</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Ready to Join the Revolution?</h2>
            <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">Don&apos;t see a position that fits? We&apos;re always looking for exceptional talent.</p>

            <div className="flex flex-wrap justify-center gap-6">
              <Link href="/contact" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover">
                <i className="fas fa-envelope mr-2"></i> Send Us Your Resume
              </Link>
              <Link href="/support" className="bg-transparent border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all btn-hover">
                <i className="fas fa-question-circle mr-2"></i> Have Questions?
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
              {/* Company Info */}
              <div>
                <Link href="/" className="flex items-center mb-4">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mb-4">Messenger of Value</p>
                <p className="text-gray-400 text-sm leading-relaxed">
                  Where ancient wisdom meets modern technology. Experience the future of value-based communication.
                </p>
              </div>

              {/* Product Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Product</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
                  <li><Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link></li>
                  <li><Link href="/pricing" className="hover:text-yellow-400 transition-colors">Pricing</Link></li>
                  <li><Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link></li>
                </ul>
              </div>

              {/* Company Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Company</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
                  <li><Link href="/careers" className="hover:text-yellow-400 transition-colors">Careers</Link></li>
                  <li><Link href="/blog" className="hover:text-yellow-400 transition-colors">Blog</Link></li>
                  <li><a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a></li>
                </ul>
              </div>

              {/* Support Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Support</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/support" className="hover:text-yellow-400 transition-colors">Help Center</Link></li>
                  <li><Link href="/guides" className="hover:text-yellow-400 transition-colors">Guides</Link></li>
                  <li><Link href="/api-docs" className="hover:text-yellow-400 transition-colors">API Docs</Link></li>
                  <li><Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link></li>
                  <li><Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 BoGuani. All rights reserved.
              </p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
