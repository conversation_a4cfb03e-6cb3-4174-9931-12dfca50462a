'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function SecurityPage() {
  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      <div className="relative min-h-screen" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Navigation */}
        <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
          <div className="container mx-auto px-6 py-4 flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="text-yellow-500 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  BoGuani
                </span>
              </Link>
            </div>
            <div className="flex space-x-6">
              <Link href="/" className="hover:text-yellow-400 transition-colors">Home</Link>
              <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link>
              <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link>
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-4 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
                Open Web App
              </Link>
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <div className="pt-24 pb-16 px-6">
          <div className="container mx-auto max-w-6xl">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                <i className="fas fa-shield-alt text-3xl text-purple-900"></i>
              </div>
              <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Security & Trust
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Your security is our top priority. Learn about the advanced measures we take to protect your messages, payments, and personal information.
              </p>
            </div>

            {/* Security Features */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {[
                {
                  icon: 'fas fa-lock',
                  title: 'End-to-End Encryption',
                  description: 'All messages and payments are encrypted on your device using military-grade AES-256 encryption. Only you and your recipient can read your conversations.'
                },
                {
                  icon: 'fas fa-key',
                  title: 'Zero-Knowledge Architecture',
                  description: 'We cannot access your message content or transaction details. Your private keys never leave your device, ensuring complete privacy.'
                },
                {
                  icon: 'fas fa-shield-virus',
                  title: 'Advanced Threat Protection',
                  description: 'Real-time monitoring and AI-powered threat detection protect against malware, phishing, and other security threats.'
                },
                {
                  icon: 'fas fa-user-shield',
                  title: 'Identity Verification',
                  description: 'Multi-factor authentication and biometric verification ensure only you can access your account and authorize transactions.'
                },
                {
                  icon: 'fas fa-server',
                  title: 'Secure Infrastructure',
                  description: 'Our servers are hosted in SOC 2 compliant data centers with 24/7 monitoring, redundancy, and physical security controls.'
                },
                {
                  icon: 'fas fa-certificate',
                  title: 'Compliance & Audits',
                  description: 'Regular security audits by independent firms and compliance with international standards including PCI DSS and GDPR.'
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-6 rounded-2xl border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mb-6">
                    <i className={`${feature.icon} text-2xl text-purple-900`}></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 text-yellow-200">{feature.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Certifications */}
            <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 mb-16">
              <h2 className="text-3xl font-bold mb-8 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent text-center">
                Security Certifications
              </h2>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {[
                  { name: 'SOC 2 Type II', icon: 'fas fa-certificate' },
                  { name: 'PCI DSS Level 1', icon: 'fas fa-credit-card' },
                  { name: 'ISO 27001', icon: 'fas fa-award' },
                  { name: 'GDPR Compliant', icon: 'fas fa-balance-scale' }
                ].map((cert, index) => (
                  <div key={index} className="text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                      <i className={`${cert.icon} text-2xl text-purple-900`}></i>
                    </div>
                    <h3 className="font-semibold text-yellow-200">{cert.name}</h3>
                  </div>
                ))}
              </div>
            </div>

            {/* Security Best Practices */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20">
                <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  How We Protect You
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <i className="fas fa-check-circle text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Automatic Security Updates</h3>
                      <p className="text-gray-300 text-sm">Critical security patches are deployed automatically to keep you protected.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-check-circle text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Fraud Detection</h3>
                      <p className="text-gray-300 text-sm">AI-powered systems monitor for suspicious activity and unauthorized access attempts.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-check-circle text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Secure Key Management</h3>
                      <p className="text-gray-300 text-sm">Encryption keys are generated and stored using hardware security modules (HSMs).</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-check-circle text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Regular Backups</h3>
                      <p className="text-gray-300 text-sm">Encrypted backups ensure your data is safe and recoverable in any scenario.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20">
                <h2 className="text-2xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                  How You Can Stay Safe
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <i className="fas fa-lightbulb text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Enable Two-Factor Authentication</h3>
                      <p className="text-gray-300 text-sm">Add an extra layer of security to your account with 2FA.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-lightbulb text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Keep Your App Updated</h3>
                      <p className="text-gray-300 text-sm">Always use the latest version to benefit from security improvements.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-lightbulb text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Verify Contacts</h3>
                      <p className="text-gray-300 text-sm">Always verify the identity of new contacts before sending payments.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <i className="fas fa-lightbulb text-yellow-400 mr-3 mt-1"></i>
                    <div>
                      <h3 className="font-semibold text-yellow-200">Report Suspicious Activity</h3>
                      <p className="text-gray-300 text-sm">Contact our security team immediately if you notice anything unusual.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Security Contact */}
            <div className="text-center">
              <div className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 max-w-2xl mx-auto">
                <h3 className="text-2xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">Security Concerns?</h3>
                <p className="text-gray-300 mb-6">If you discover a security vulnerability or have concerns about your account security, please contact our security team immediately.</p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a href="mailto:<EMAIL>" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all">
                    <i className="fas fa-shield-alt mr-2"></i>
                    Report Security Issue
                  </a>
                  <Link href="/contact" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all">
                    <i className="fas fa-envelope mr-2"></i>
                    General Contact
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
