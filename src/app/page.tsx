'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function HomePage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
          font-family: 'Montserrat', sans-serif;
          min-height: 100vh;
        }

        .hero-gradient {
          background: linear-gradient(135deg,
            rgba(30, 30, 36, 0.95) 0%,
            rgba(45, 27, 78, 0.9) 25%,
            rgba(61, 42, 95, 0.85) 50%,
            rgba(78, 58, 112, 0.8) 75%,
            rgba(45, 27, 78, 0.9) 100%);
        }

        .section-gradient {
          background: linear-gradient(135deg,
            rgba(30, 30, 36, 0.8) 0%,
            rgba(45, 27, 78, 0.7) 50%,
            rgba(61, 42, 95, 0.6) 100%);
        }

        .card-gradient {
          background: linear-gradient(135deg,
            rgba(61, 42, 95, 0.4) 0%,
            rgba(78, 58, 112, 0.3) 50%,
            rgba(45, 27, 78, 0.4) 100%);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(212, 175, 55, 0.2);
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
          transition: all 0.4s ease;
          background: linear-gradient(135deg,
            rgba(61, 42, 95, 0.6) 0%,
            rgba(78, 58, 112, 0.4) 50%,
            rgba(45, 27, 78, 0.6) 100%);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .feature-card:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px -10px rgba(212, 175, 55, 0.3);
          background: linear-gradient(135deg,
            rgba(61, 42, 95, 0.8) 0%,
            rgba(78, 58, 112, 0.6) 50%,
            rgba(45, 27, 78, 0.8) 100%);
        }

        .btn-hover {
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .btn-hover:hover {
          transform: translateY(-3px);
          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
        }

        .btn-hover::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        .btn-hover:hover::before {
          left: 100%;
        }

        .hero-pattern {
          background-image:
            radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .nav-gradient {
          background: linear-gradient(135deg,
            rgba(30, 30, 36, 0.95) 0%,
            rgba(45, 27, 78, 0.9) 100%);
          backdrop-filter: blur(20px);
          border-bottom: 1px solid rgba(212, 175, 55, 0.2);
        }
      `}</style>

      <div className="text-white min-h-screen">
        <div className="hero-pattern min-h-screen">
          {/* Navigation */}
          <nav className="nav-gradient fixed w-full z-10">
            <div className="container mx-auto px-6 py-4 flex justify-between items-center">
              <div className="flex items-center">
                <div className="text-yellow-400 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl gold-gradient">BoGuani</span>
              </div>
              <div className="hidden md:flex space-x-8">
                <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium">Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium">About</a>
                <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium">Download</a>
                <a href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium">Privacy</a>
              </div>
              <div className="md:hidden">
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="text-white focus:outline-none p-2"
                >
                  <i className="fas fa-bars text-xl"></i>
                </button>
              </div>
            </div>
            {/* Mobile menu */}
            {mobileMenuOpen && (
              <div className="md:hidden section-gradient border-t border-yellow-400 border-opacity-20">
                <div className="container mx-auto px-6 py-4 flex flex-col space-y-4">
                  <a href="#features" className="hover:text-yellow-400 transition-all duration-300 font-medium">Features</a>
                  <a href="#about" className="hover:text-yellow-400 transition-all duration-300 font-medium">About</a>
                  <a href="#download" className="hover:text-yellow-400 transition-all duration-300 font-medium">Download</a>
                  <a href="/privacy" className="hover:text-yellow-400 transition-all duration-300 font-medium">Privacy</a>
                </div>
              </div>
            )}
          </nav>

          {/* Hero Section */}
          <header className="hero-gradient pt-32 pb-24 px-6 min-h-screen flex items-center">
            <div className="container mx-auto flex flex-col md:flex-row items-center relative">
              {/* Floating background elements */}
              <div className="absolute top-10 left-10 w-32 h-32 bg-yellow-400 opacity-5 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-20 right-20 w-40 h-40 bg-yellow-400 opacity-3 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>

              <div className="md:w-1/2 mb-12 md:mb-0 relative z-10">
                <div className="mb-6">
                  <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                    <span className="gold-gradient drop-shadow-lg">BoGuani</span>
                  </h1>
                  <h2 className="text-3xl md:text-4xl font-semibold mb-4 text-gray-100">Messenger of Value</h2>
                  <p className="text-2xl mb-8 text-yellow-200 italic font-medium">Where Words Carry Worth</p>
                </div>

                <div className="card-gradient p-6 rounded-2xl mb-8">
                  <p className="mb-6 text-gray-200 text-lg leading-relaxed">
                    Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
                    Send messages that matter, share moments that count, and transfer value instantly -
                    all protected by sacred-level encryption.
                  </p>
                  <div className="text-center">
                    <p className="text-2xl font-bold gold-gradient">"Speak Gold. Share Value."</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-4 justify-center md:justify-start">
                  <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-gray-800 px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center shadow-lg">
                    <i className="fas fa-globe mr-3"></i> Open BoGuani Web Version
                  </Link>
                  <a href="#features" className="gold-border bg-transparent px-8 py-4 rounded-full font-bold text-lg transition-all btn-hover flex items-center">
                    <i className="fas fa-play mr-3 text-yellow-400"></i> <span className="text-yellow-400">Watch Demo</span>
                  </a>
                </div>
              </div>

              <div className="md:w-1/2 flex justify-center relative z-10">
                <div className="relative w-80 h-96">
                  {/* App mockup */}
                  <div className="absolute inset-0 card-gradient rounded-3xl gold-border shadow-2xl overflow-hidden transform hover:scale-105 transition-all duration-500">
                    <div className="section-gradient p-4 border-b border-yellow-400 border-opacity-40">
                      <div className="flex justify-between items-center">
                        <div className="text-yellow-400 text-xl">
                          <i className="fas fa-comment-dollar"></i>
                        </div>
                        <p className="font-bold text-lg gold-gradient">BoGuani</p>
                        <div className="text-yellow-400">
                          <i className="fas fa-ellipsis-v"></i>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 h-full bg-gradient-to-b from-transparent to-gray-800 to-opacity-20">
                      <div className="flex flex-col h-full space-y-3">
                        <div className="card-gradient p-3 rounded-xl self-start max-w-[75%] border border-purple-400 border-opacity-30">
                          <p className="text-sm text-gray-200">Hey! Can you send me 20 for dinner tonight?</p>
                        </div>
                        <div className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-opacity-20 p-3 rounded-xl self-end max-w-[75%] border border-yellow-400 border-opacity-40">
                          <p className="text-sm text-gray-100">Sure! Sending it now with a special message.</p>
                        </div>
                        <div className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-opacity-30 p-3 rounded-xl self-end max-w-[75%] border border-yellow-400 border-opacity-50">
                          <div className="flex items-center">
                            <div className="text-yellow-400 mr-2">
                              <i className="fas fa-coins"></i>
                            </div>
                            <p className="text-sm font-semibold text-gray-100">$20.00 sent - Enjoy dinner!</p>
                          </div>
                        </div>
                        <div className="card-gradient p-3 rounded-xl self-start max-w-[75%] border border-purple-400 border-opacity-30">
                          <p className="text-sm text-gray-200">Thanks! Value received. 🙏</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Enhanced decorative elements */}
                  <div className="absolute -bottom-8 -right-8 w-48 h-48 bg-gradient-to-r from-yellow-400 to-yellow-200 opacity-10 rounded-full blur-3xl animate-pulse"></div>
                  <div className="absolute -top-8 -left-8 w-24 h-24 bg-gradient-to-r from-yellow-400 to-yellow-200 opacity-15 rounded-full blur-2xl animate-pulse" style={{animationDelay: '1s'}}></div>
                  <div className="absolute top-1/2 -right-4 w-16 h-16 bg-yellow-400 opacity-20 rounded-full blur-xl animate-bounce" style={{animationDelay: '3s'}}></div>
                </div>
              </div>
            </div>
          </header>
        </div>

        {/* Features Section */}
        <section id="features" className="py-20 section-gradient relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 left-1/4 w-64 h-64 bg-yellow-400 opacity-5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-48 h-48 bg-yellow-400 opacity-3 rounded-full blur-3xl"></div>

          <div className="container mx-auto px-6 relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Key Features</h2>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-yellow-200 mx-auto mb-4"></div>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">Experience the future of value-based communication</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="feature-card p-8 rounded-2xl relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-lock"></i>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-yellow-200">256-bit End-to-End Encryption</h3>
                  <p className="text-gray-300 leading-relaxed">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>
                </div>
              </div>

              <div className="feature-card p-8 rounded-2xl relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-bolt"></i>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-yellow-200">Instant Money Transfers</h3>
                  <p className="text-gray-300 leading-relaxed">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
                </div>
              </div>

              <div className="feature-card p-8 rounded-2xl relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-200 opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="text-yellow-400 text-5xl mb-6 transform group-hover:scale-110 transition-transform duration-300">
                    <i className="fas fa-headset"></i>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 text-yellow-200">24/7 Global Support</h3>
                  <p className="text-gray-300 leading-relaxed">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Download Section */}
        <section id="download" className="py-20 hero-gradient relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-1/2 left-0 w-72 h-72 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-y-1/2"></div>
          <div className="absolute top-1/2 right-0 w-72 h-72 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-y-1/2"></div>

          <div className="container mx-auto px-6 relative z-10">
            <div className="text-center mb-20">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Get BoGuani Now</h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-6">Experience the revolution in value-based messaging. Available on all major platforms.</p>
              <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-yellow-200 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
              <a href="/downloads/ios" className="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                <i className="fab fa-apple text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300"></i>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Download for</p>
                  <p className="font-bold text-lg text-yellow-200">iOS</p>
                  <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
                </div>
              </a>

              <a href="/downloads/android" className="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                <i className="fab fa-android text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300"></i>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Download for</p>
                  <p className="font-bold text-lg text-yellow-200">Android</p>
                  <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
                </div>
              </a>

              <a href="/downloads/windows" className="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                <i className="fas fa-desktop text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300"></i>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Download for</p>
                  <p className="font-bold text-lg text-yellow-200">Windows</p>
                  <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
                </div>
              </a>

              <a href="/downloads/mac" className="card-gradient hover:bg-opacity-80 transition-all duration-300 px-6 py-8 rounded-2xl flex flex-col items-center text-center btn-hover group">
                <i className="fas fa-laptop text-5xl mb-4 text-yellow-400 group-hover:scale-110 transition-transform duration-300"></i>
                <div>
                  <p className="text-sm text-gray-400 mb-1">Download for</p>
                  <p className="font-bold text-lg text-yellow-200">macOS</p>
                  <p className="text-xs text-gray-500 mt-2">Coming Soon</p>
                </div>
              </a>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="section-gradient py-12 border-t border-yellow-400 border-opacity-20">
          <div className="container mx-auto px-6">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="text-yellow-400 text-2xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-xl gold-gradient">BoGuani</span>
              </div>
              <p className="text-gray-400 mb-4">Messenger of Value - Where Words Carry Worth</p>
              <p className="text-gray-500 text-sm">© 2024 BoGuani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
