'use client';

import { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <div className="min-h-screen text-white" style={{
      background: 'linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%)',
      fontFamily: 'Montserrat, sans-serif'
    }}>
      {/* Navigation */}
      <nav className="fixed w-full z-50 bg-gradient-to-r from-black/80 to-purple-900/80 backdrop-blur-md border-b border-yellow-500/20">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <div className="text-yellow-500 text-3xl mr-3">
              <i className="fas fa-comment-dollar"></i>
            </div>
            <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
              BoGuani
            </span>
          </div>
          
          {/* Desktop Menu */}
          <div className="hidden md:flex space-x-8">
            <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
            <Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link>
            <Link href="/support" className="hover:text-yellow-400 transition-colors">Support</Link>
            <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-2 rounded-full font-semibold hover:from-yellow-200 hover:to-yellow-400 transition-all">
              Open Web App
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden text-yellow-400"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-gradient-to-r from-purple-900/95 to-black/95 backdrop-blur-md border-t border-yellow-500/20">
            <div className="px-6 py-4 space-y-4">
              <a href="#features" className="block hover:text-yellow-400 transition-colors">Features</a>
              <Link href="/downloads" className="block hover:text-yellow-400 transition-colors">Downloads</Link>
              <Link href="/support" className="block hover:text-yellow-400 transition-colors">Support</Link>
              <Link href="/auth" className="block bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-2 rounded-full font-semibold text-center">
                Open Web App
              </Link>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.95) 0%, rgba(45, 27, 78, 0.9) 25%, rgba(61, 42, 95, 0.85) 50%, rgba(78, 58, 112, 0.8) 75%, rgba(45, 27, 78, 0.9) 100%)',
        backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%)'
      }}>
        {/* Background decorative elements */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-yellow-400 opacity-10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-yellow-400 opacity-5 rounded-full blur-3xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-yellow-400 opacity-8 rounded-full blur-2xl animate-pulse" style={{animationDelay: '4s'}}></div>

        <div className="container mx-auto px-6 py-20 flex flex-col lg:flex-row items-center">
          {/* Left Content */}
          <motion.div 
            className="lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              <span className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                BoGuani
              </span>
              <br />
              <span className="text-white">Messenger of Value</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
              Where Words Carry Worth - Experience the future of value-based communication with end-to-end encryption and instant transfers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all transform hover:scale-105 hover:shadow-2xl">
                <i className="fas fa-rocket mr-2"></i>
                Start Messaging
              </Link>
              <Link href="/downloads" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all transform hover:scale-105">
                <i className="fas fa-download mr-2"></i>
                Download App
              </Link>
            </div>
          </motion.div>

          {/* Right Content - App Mockup */}
          <motion.div 
            className="lg:w-1/2 flex justify-center"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="relative">
              <div className="w-80 h-96 bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg rounded-3xl border border-yellow-500/20 p-6 shadow-2xl">
                {/* Mock Chat Interface */}
                <div className="flex items-center mb-6 pb-4 border-b border-yellow-500/20">
                  <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-3">
                    <i className="fas fa-user text-purple-900"></i>
                  </div>
                  <div>
                    <h3 className="font-semibold text-white">Alex Rivera</h3>
                    <p className="text-sm text-gray-400">Online</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-yellow-400/20 to-yellow-200/20 p-3 rounded-2xl rounded-bl-sm">
                    <p className="text-white text-sm">Hey! Can you send me $50 for dinner? 🍕</p>
                  </div>
                  <div className="bg-gradient-to-r from-purple-600/30 to-purple-800/30 p-3 rounded-2xl rounded-br-sm ml-8">
                    <p className="text-white text-sm">Sure! Sending now 💰</p>
                    <div className="mt-2 p-2 bg-yellow-400/20 rounded-lg">
                      <p className="text-yellow-200 text-xs font-semibold">💸 $50.00 sent securely</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 relative" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'
      }}>
        <div className="container mx-auto px-6">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Revolutionary Features
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Experience the perfect blend of secure messaging and instant value transfer
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: 'fas fa-shield-alt',
                title: 'End-to-End Encryption',
                description: 'Military-grade encryption ensures your messages and transactions remain completely private and secure.'
              },
              {
                icon: 'fas fa-bolt',
                title: 'Instant Transfers',
                description: 'Send money as easily as sending a text message. Lightning-fast transfers with real-time notifications.'
              },
              {
                icon: 'fas fa-users',
                title: 'Group Payments',
                description: 'Split bills, share expenses, and manage group finances seamlessly within your chat conversations.'
              },
              {
                icon: 'fas fa-mobile-alt',
                title: 'Cross-Platform',
                description: 'Available on all devices - iOS, Android, and Web. Your conversations sync perfectly everywhere.'
              },
              {
                icon: 'fas fa-chart-line',
                title: 'Smart Analytics',
                description: 'Track your spending, analyze patterns, and make informed financial decisions with built-in insights.'
              },
              {
                icon: 'fas fa-globe',
                title: 'Global Reach',
                description: 'Send messages and money worldwide with support for multiple currencies and international transfers.'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl border border-yellow-500/20 hover:border-yellow-500/40 transition-all duration-300 hover:transform hover:scale-105"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mb-6">
                  <i className={`${feature.icon} text-2xl text-purple-900`}></i>
                </div>
                <h3 className="text-xl font-bold mb-4 text-white">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'
      }}>
        <div className="absolute top-0 left-1/2 w-96 h-96 bg-yellow-400 opacity-5 rounded-full blur-3xl transform -translate-x-1/2"></div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">
                Ready to Transform
              </span>
              <br />
              <span className="text-white">Your Communication?</span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of users who are already experiencing the future of value-based messaging.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/auth" className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all transform hover:scale-105 hover:shadow-2xl">
                <i className="fas fa-rocket mr-2"></i>
                Get Started Now
              </Link>
              <Link href="/downloads" className="border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all transform hover:scale-105">
                <i className="fas fa-download mr-2"></i>
                Download Apps
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 relative overflow-hidden" style={{
        background: 'linear-gradient(135deg, rgba(30, 30, 36, 0.8) 0%, rgba(45, 27, 78, 0.7) 50%, rgba(61, 42, 95, 0.6) 100%)'
      }}>
        <div className="absolute top-0 left-1/2 w-96 h-96 bg-yellow-400 opacity-3 rounded-full blur-3xl transform -translate-x-1/2"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-start mb-12">
            <div className="mb-8 md:mb-0">
              <div className="flex items-center mb-4">
                <div className="text-yellow-400 text-3xl mr-3">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl bg-gradient-to-r from-yellow-400 to-yellow-200 bg-clip-text text-transparent">BoGuani</span>
              </div>
              <p className="text-gray-300 text-lg mb-4">Messenger of Value</p>
              <p className="text-gray-400 max-w-sm">Where Words Carry Worth - Experience the future of value-based communication.</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div>
                <h3 className="font-bold mb-4 text-yellow-200 text-lg">Product</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><a href="#features" className="hover:text-yellow-400 transition-all duration-300">Features</a></li>
                  <li><Link href="/downloads" className="hover:text-yellow-400 transition-all duration-300">Downloads</Link></li>
                  <li><Link href="/auth" className="hover:text-yellow-400 transition-all duration-300">Web App</Link></li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold mb-4 text-yellow-200 text-lg">Company</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><a href="#about" className="hover:text-yellow-400 transition-all duration-300">About</a></li>
                  <li><Link href="/contact" className="hover:text-yellow-400 transition-all duration-300">Contact</Link></li>
                  <li><Link href="/support" className="hover:text-yellow-400 transition-all duration-300">Support</Link></li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold mb-4 text-yellow-200 text-lg">Resources</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><Link href="/help" className="hover:text-yellow-400 transition-all duration-300">Help Center</Link></li>
                  <li><Link href="/guides" className="hover:text-yellow-400 transition-all duration-300">Guides</Link></li>
                  <li><Link href="/api" className="hover:text-yellow-400 transition-all duration-300">API Docs</Link></li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold mb-4 text-yellow-200 text-lg">Legal</h3>
                <ul className="space-y-3 text-gray-400">
                  <li><Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300">Privacy</Link></li>
                  <li><Link href="/terms" className="hover:text-yellow-400 transition-all duration-300">Terms</Link></li>
                  <li><Link href="/security" className="hover:text-yellow-400 transition-all duration-300">Security</Link></li>
                </ul>
              </div>
            </div>
          </div>

          <div className="border-t border-yellow-400 border-opacity-20 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 mb-4 md:mb-0">© 2024 BoGuani. All rights reserved.</p>
            <div className="flex space-x-8 text-gray-400">
              <Link href="/privacy" className="hover:text-yellow-400 transition-all duration-300">Privacy Policy</Link>
              <Link href="/terms" className="hover:text-yellow-400 transition-all duration-300">Terms of Service</Link>
              <Link href="/cookies" className="hover:text-yellow-400 transition-all duration-300">Cookie Policy</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
