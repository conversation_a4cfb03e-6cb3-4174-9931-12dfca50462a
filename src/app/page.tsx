'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';

export default function HomePage() {
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern">
        {/* Hero Section */}
        <div className="min-h-screen flex items-center justify-center px-6">
          <div className="w-full max-w-6xl">

            {/* Header */}
            <div className="text-center mb-16">
              <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300">
                <i className="fas fa-comment-dollar text-5xl text-purple-900"></i>
              </div>
              <h1 className="text-6xl md:text-7xl font-bold mb-6 gold-gradient">
                BoGuani
              </h1>
              <p className="text-2xl text-gray-300 mb-4">Messenger of Value</p>
              <p className="text-xl text-gray-400 italic mb-8">&quot;Speak Gold. Share Value.&quot;</p>
              <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
                Experience the future of value-based communication with end-to-end encryption,
                instant money transfers, and crystal-clear voice & video calls.
              </p>
            </div>

            {/* Main Action Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">

              {/* Open Web Version Card */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl gold-border shadow-2xl feature-card"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-globe text-2xl text-purple-900"></i>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 gold-gradient">Open Web Version</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Start messaging instantly in your browser. No downloads required.
                  </p>
                  <Link
                    href="/auth"
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover inline-block"
                  >
                    <i className="fas fa-rocket mr-2"></i>
                    Launch BoGuani
                  </Link>
                </div>
              </motion.div>

              {/* Download App Card */}
              <motion.div
                className="bg-gradient-to-br from-purple-900/40 to-black/40 backdrop-blur-lg p-8 rounded-2xl gold-border shadow-2xl feature-card"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-download text-2xl text-purple-900"></i>
                  </div>
                  <h3 className="text-2xl font-bold mb-4 gold-gradient">Download Apps</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Get the full experience on iOS, Android, or Desktop.
                  </p>
                  <Link
                    href="/downloads"
                    className="w-full bg-transparent border-2 border-yellow-400 text-yellow-400 px-6 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all btn-hover inline-block"
                  >
                    <i className="fas fa-mobile-alt mr-2"></i>
                    Choose Platform
                  </Link>
                </div>
              </motion.div>
            </div>

            {/* Feature Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
              <motion.div
                className="bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-shield-alt text-lg text-purple-900"></i>
                  </div>
                  <h4 className="text-lg font-semibold mb-2 gold-gradient">End-to-End Encryption</h4>
                  <p className="text-gray-400 text-sm">Military-grade security for all your messages</p>
                </div>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-dollar-sign text-lg text-purple-900"></i>
                  </div>
                  <h4 className="text-lg font-semibold mb-2 gold-gradient">Instant Money Transfers</h4>
                  <p className="text-gray-400 text-sm">Send money as easily as sending a message</p>
                </div>
              </motion.div>

              <motion.div
                className="bg-gradient-to-br from-purple-900/30 to-black/30 backdrop-blur-lg p-6 rounded-xl border border-yellow-400/20 feature-card"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-video text-lg text-purple-900"></i>
                  </div>
                  <h4 className="text-lg font-semibold mb-2 gold-gradient">HD Voice & Video</h4>
                  <p className="text-gray-400 text-sm">Crystal-clear calls with WebRTC technology</p>
                </div>
              </motion.div>
            </div>

            {/* Quick Links */}
            <div className="text-center">
              <div className="flex flex-wrap justify-center gap-4">
                <Link href="/security" className="text-yellow-400 hover:text-yellow-200 transition-colors text-sm">
                  <i className="fas fa-lock mr-1"></i> Security
                </Link>
                <Link href="/pricing" className="text-yellow-400 hover:text-yellow-200 transition-colors text-sm">
                  <i className="fas fa-tag mr-1"></i> Pricing
                </Link>
                <Link href="/support" className="text-yellow-400 hover:text-yellow-200 transition-colors text-sm">
                  <i className="fas fa-question-circle mr-1"></i> Support
                </Link>
                <Link href="/blog" className="text-yellow-400 hover:text-yellow-200 transition-colors text-sm">
                  <i className="fas fa-newspaper mr-1"></i> Blog
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
