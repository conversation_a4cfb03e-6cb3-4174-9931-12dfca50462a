'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { useState } from 'react';

export default function HomePage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

        body {
          background: linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }

        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .professional-shadow {
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .glass-card {
          background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.8) 100%);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(75, 85, 99, 0.3);
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Navigation */}
        <nav className="glass-card fixed w-full z-50 professional-shadow">
          <div className="container mx-auto px-6 py-3 flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <div className="text-yellow-400 text-3xl mr-2">
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl gold-gradient">BoGuani</span>
            </Link>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
              <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
              <a href="#how-it-works" className="hover:text-yellow-400 transition-colors">How It Works</a>
              <a href="#pricing" className="hover:text-yellow-400 transition-colors">Pricing</a>
              <a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a>
              <Link href="/auth" className="bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors">
                Get Started
              </Link>
            </div>
            <button
              className="md:hidden text-yellow-400"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden bg-gray-900 bg-opacity-95 backdrop-blur-md">
              <div className="container mx-auto px-6 py-4 flex flex-col space-y-4">
                <a href="#features" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>About</a>
                <a href="#how-it-works" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>How It Works</a>
                <a href="#pricing" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Pricing</a>
                <a href="#contact" className="hover:text-yellow-400 transition-colors" onClick={() => setIsMenuOpen(false)}>Contact</a>
                <Link href="/auth" className="bg-yellow-400 text-purple-900 px-4 py-2 rounded-full font-semibold hover:bg-yellow-300 transition-colors text-center">
                  Get Started
                </Link>
              </div>
            </div>
          )}
        </nav>

        {/* Hero Section */}
        <section className="min-h-screen flex items-center justify-center px-6 pt-20">
          <div className="w-full max-w-6xl">
            <div className="text-center mb-16">
              <motion.div
                className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center shadow-2xl"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <i className="fas fa-comment-dollar text-5xl text-purple-900"></i>
              </motion.div>
              <h1 className="text-6xl md:text-7xl font-bold mb-6 gold-gradient">
                BoGuani
              </h1>
              <p className="text-2xl text-gray-300 mb-4">Messenger of Value</p>
              <p className="text-xl text-gray-400 italic mb-8">&quot;Speak Gold. Share Value.&quot;</p>
              <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed mb-12">
                The world&apos;s first value-based communication platform. Send messages, transfer money,
                and make calls with military-grade encryption and Taíno-inspired wisdom.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link
                  href="/auth"
                  className="bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-8 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover"
                >
                  <i className="fas fa-rocket mr-2"></i>
                  Start Messaging
                </Link>
                <Link
                  href="/downloads"
                  className="bg-transparent border-2 border-yellow-400 text-yellow-400 px-8 py-4 rounded-full font-bold text-lg hover:bg-yellow-400 hover:text-purple-900 transition-all btn-hover"
                >
                  <i className="fas fa-download mr-2"></i>
                  Download App
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* What is BoGuani Section */}
        <section id="about" className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">What is BoGuani?</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
                BoGuani is the world&apos;s first value-based communication platform, inspired by ancient Taíno wisdom
                where messages carried both meaning and worth. We&apos;ve reimagined this concept for the digital age.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl font-bold mb-6 text-yellow-400">The Ancient Wisdom</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  The name &quot;BoGuani&quot; comes from the ancient Taíno civilization, where communication and value
                  exchange were intrinsically linked. In their culture, every message carried weight, meaning,
                  and often tangible value.
                </p>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  We&apos;ve brought this sacred tradition into the modern world, creating a platform where your
                  words truly carry worth - literally and figuratively.
                </p>
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4">
                    <i className="fas fa-leaf text-purple-900 text-xl"></i>
                  </div>
                  <div>
                    <h4 className="font-semibold text-yellow-400">Sacred Communication</h4>
                    <p className="text-gray-400 text-sm">Every message is treated with reverence and security</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4">
                    <i className="fas fa-coins text-purple-900 text-xl"></i>
                  </div>
                  <div>
                    <h4 className="font-semibold text-yellow-400">Value Exchange</h4>
                    <p className="text-gray-400 text-sm">Send money as naturally as sending words</p>
                  </div>
                </div>
              </div>

              <div className="glass-card p-8 rounded-2xl professional-shadow">
                <div className="text-center mb-6">
                  <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-comment-dollar text-3xl text-purple-900"></i>
                  </div>
                  <h4 className="text-2xl font-bold gold-gradient mb-2">Modern Innovation</h4>
                  <p className="text-gray-300">Ancient wisdom meets cutting-edge technology</p>
                </div>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">End-to-end encryption</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Instant money transfers</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">HD voice & video calls</span>
                  </div>
                  <div className="flex items-center">
                    <i className="fas fa-check text-yellow-400 mr-3"></i>
                    <span className="text-gray-300">Cross-platform compatibility</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Powerful Features</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Everything you need for secure communication and value exchange in one platform
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-shield-alt text-2xl text-purple-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Military-Grade Security</h3>
                  <p className="text-gray-300 leading-relaxed">
                    256-bit AES encryption ensures your messages and transactions are completely secure and private.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                    <i className="fas fa-bolt text-2xl text-gray-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Instant Transfers</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Send money instantly to anyone, anywhere. As simple as sending a text message.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                    <i className="fas fa-video text-2xl text-gray-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">HD Voice & Video</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Crystal-clear voice and video calls powered by advanced WebRTC technology.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                    <i className="fas fa-users text-2xl text-gray-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Group Payments</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Split bills, share expenses, and manage group finances effortlessly.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center professional-shadow">
                    <i className="fas fa-globe text-2xl text-gray-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">Cross-Platform</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Available on iOS, Android, Desktop, and Web. Your conversations sync everywhere.
                  </p>
                </div>
              </motion.div>

              <motion.div
                className="glass-card p-8 rounded-2xl feature-card professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center">
                    <i className="fas fa-clock text-2xl text-purple-900"></i>
                  </div>
                  <h3 className="text-xl font-bold mb-4 gold-gradient">24/7 Support</h3>
                  <p className="text-gray-300 leading-relaxed">
                    Round-the-clock customer support to help you with any questions or issues.
                  </p>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section id="how-it-works" className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">How It Works</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Get started with BoGuani in three simple steps
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl">
                  1
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Sign Up</h3>
                <p className="text-gray-300 leading-relaxed">
                  Create your account with just your phone number. We'll verify it with a secure SMS code.
                </p>
              </motion.div>

              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl">
                  2
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Connect</h3>
                <p className="text-gray-300 leading-relaxed">
                  Add your contacts and start secure conversations. Import from your phone or invite friends.
                </p>
              </motion.div>

              <motion.div
                className="text-center"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center text-purple-900 font-bold text-2xl">
                  3
                </div>
                <h3 className="text-2xl font-bold mb-4 gold-gradient">Share Value</h3>
                <p className="text-gray-300 leading-relaxed">
                  Send messages, make calls, and transfer money - all with the same level of security and ease.
                </p>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-20 bg-gradient-to-br from-gray-800/50 to-gray-900/50 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Simple Pricing</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Choose the plan that works best for you. All plans include end-to-end encryption.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Free Plan */}
              <motion.div
                className="glass-card p-8 rounded-2xl professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold mb-4">Free</h3>
                  <div className="text-4xl font-bold mb-6 gold-gradient">$0</div>
                  <p className="text-gray-300 mb-8">Perfect for personal use</p>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>Unlimited messaging</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>Voice & video calls</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>$100/month transfers</span>
                    </li>
                  </ul>

                  <Link href="/auth" className="w-full bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block">
                    Get Started
                  </Link>
                </div>
              </motion.div>

              {/* Pro Plan */}
              <motion.div
                className="bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl p-8 text-gray-900 relative"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                  Most Popular
                </div>
                <div className="text-center">
                  <h3 className="text-2xl font-bold mb-4">Pro</h3>
                  <div className="text-4xl font-bold mb-6">$9.99</div>
                  <p className="text-gray-700 mb-8">For power users</p>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center">
                      <i className="fas fa-check text-purple-600 mr-3"></i>
                      <span>Everything in Free</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-purple-600 mr-3"></i>
                      <span>$5,000/month transfers</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-purple-600 mr-3"></i>
                      <span>Priority support</span>
                    </li>
                  </ul>

                  <Link href="/auth" className="w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-full font-semibold transition-all inline-block">
                    Start Free Trial
                  </Link>
                </div>
              </motion.div>

              {/* Business Plan */}
              <motion.div
                className="glass-card p-8 rounded-2xl professional-shadow"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <div className="text-center">
                  <h3 className="text-2xl font-bold mb-4">Business</h3>
                  <div className="text-4xl font-bold mb-6 gold-gradient">$29.99</div>
                  <p className="text-gray-300 mb-8">For teams and organizations</p>

                  <ul className="space-y-4 mb-8 text-left">
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>Everything in Pro</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>Unlimited transfers</span>
                    </li>
                    <li className="flex items-center">
                      <i className="fas fa-check text-yellow-400 mr-3"></i>
                      <span>Admin controls</span>
                    </li>
                  </ul>

                  <Link href="/contact" className="w-full gold-border bg-transparent px-6 py-3 rounded-full font-semibold transition-all hover:scale-105 inline-block">
                    <span className="text-yellow-400">Contact Sales</span>
                  </Link>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section id="contact" className="py-20 glass-card border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold mb-6 gold-gradient">Get In Touch</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mb-8"></div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* Contact Info */}
              <div>
                <h3 className="text-2xl font-bold mb-8 gold-gradient">Contact Information</h3>

                <div className="space-y-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4">
                      <i className="fas fa-envelope text-purple-900"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-yellow-400">Email</h4>
                      <p className="text-gray-300"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4">
                      <i className="fas fa-phone text-purple-900"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-yellow-400">Phone</h4>
                      <p className="text-gray-300">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-200 rounded-full flex items-center justify-center mr-4">
                      <i className="fas fa-map-marker-alt text-purple-900"></i>
                    </div>
                    <div>
                      <h4 className="font-semibold text-yellow-400">Address</h4>
                      <p className="text-gray-300">123 Innovation Street<br />San Francisco, CA 94105</p>
                    </div>
                  </div>
                </div>

                <div className="mt-8">
                  <h4 className="text-xl font-semibold mb-4 text-yellow-400">Follow Us</h4>
                  <div className="flex space-x-4">
                    <a href="https://twitter.com/boguani" target="_blank" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors">
                      <i className="fab fa-twitter text-yellow-400"></i>
                    </a>
                    <a href="https://facebook.com/boguani" target="_blank" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors">
                      <i className="fab fa-facebook-f text-yellow-400"></i>
                    </a>
                    <a href="https://instagram.com/boguani" target="_blank" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors">
                      <i className="fab fa-instagram text-yellow-400"></i>
                    </a>
                    <a href="https://linkedin.com/company/boguani" target="_blank" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-700 transition-colors">
                      <i className="fab fa-linkedin-in text-yellow-400"></i>
                    </a>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="glass-card p-8 rounded-2xl professional-shadow">
                <h3 className="text-2xl font-bold mb-6 gold-gradient">Send us a Message</h3>

                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">First Name</label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <label className="block text-gray-300 text-sm font-medium mb-2">Last Name</label>
                      <input
                        type="text"
                        className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">Email</label>
                    <input
                      type="email"
                      className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">Subject</label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                      placeholder="How can we help?"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm font-medium mb-2">Message</label>
                    <textarea
                      rows={5}
                      className="w-full px-4 py-3 bg-purple-900/50 border border-yellow-400/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 focus:ring-1 focus:ring-yellow-400"
                      placeholder="Tell us about your project or question..."
                    ></textarea>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-200 text-purple-900 px-6 py-4 rounded-full font-bold text-lg hover:from-yellow-200 hover:to-yellow-400 transition-all btn-hover"
                  >
                    <i className="fas fa-paper-plane mr-2"></i>
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="glass-card py-12 border-t border-gray-600/30">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
              {/* Company Info */}
              <div>
                <Link href="/" className="flex items-center mb-4">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </Link>
                <p className="text-gray-400 mb-4">Messenger of Value</p>
                <p className="text-gray-400 text-sm leading-relaxed">
                  Where ancient wisdom meets modern technology. Experience the future of value-based communication.
                </p>
              </div>

              {/* Product Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Product</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
                  <li><Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link></li>
                  <li><Link href="/pricing" className="hover:text-yellow-400 transition-colors">Pricing</Link></li>
                  <li><Link href="/downloads" className="hover:text-yellow-400 transition-colors">Downloads</Link></li>
                </ul>
              </div>

              {/* Company Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Company</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
                  <li><Link href="/careers" className="hover:text-yellow-400 transition-colors">Careers</Link></li>
                  <li><Link href="/blog" className="hover:text-yellow-400 transition-colors">Blog</Link></li>
                  <li><a href="#contact" className="hover:text-yellow-400 transition-colors">Contact</a></li>
                </ul>
              </div>

              {/* Support Links */}
              <div>
                <h3 className="font-semibold mb-4 text-yellow-400">Support</h3>
                <ul className="space-y-2 text-gray-400">
                  <li><Link href="/support" className="hover:text-yellow-400 transition-colors">Help Center</Link></li>
                  <li><Link href="/guides" className="hover:text-yellow-400 transition-colors">Guides</Link></li>
                  <li><Link href="/api-docs" className="hover:text-yellow-400 transition-colors">API Docs</Link></li>
                  <li><Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</Link></li>
                  <li><Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms</Link></li>
                </ul>
              </div>
            </div>

            <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">
                © 2024 BoGuani. All rights reserved.
              </p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <Link href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</Link>
                <Link href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</Link>
                <Link href="/security" className="hover:text-yellow-400 transition-colors">Security</Link>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
