'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function HomePage() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }

        .gold-gradient {
          background: linear-gradient(90deg, #fbbf24 0%, #fde047 50%, #fbbf24 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #fbbf24, #fde047) border-box;
        }

        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }

        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(251, 191, 36, 0.3);
        }

        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23fbbf24' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      <div className="text-white min-h-screen">
        <div className="hero-pattern">
          {/* Navigation */}
          <nav className="bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10">
            <div className="container mx-auto px-6 py-3 flex justify-between items-center">
              <div className="flex items-center">
                <div className="text-yellow-400 text-3xl mr-2">
                  <i className="fas fa-comment-dollar"></i>
                </div>
                <span className="font-bold text-2xl gold-gradient">BoGuani</span>
              </div>
              <div className="hidden md:flex space-x-8">
                <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
                <a href="#download" className="hover:text-yellow-400 transition-colors">Download</a>
                <a href="#support" className="hover:text-yellow-400 transition-colors">Support</a>
              </div>
              <div className="md:hidden">
                <button
                  onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                  className="text-white focus:outline-none"
                >
                  <i className="fas fa-bars"></i>
                </button>
              </div>
            </div>
            {/* Mobile menu */}
            {mobileMenuOpen && (
              <div className="md:hidden bg-gray-800">
                <div className="container mx-auto px-6 py-3 flex flex-col space-y-4">
                  <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
                  <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
                  <a href="#download" className="hover:text-yellow-400 transition-colors">Download</a>
                  <a href="#support" className="hover:text-yellow-400 transition-colors">Support</a>
                </div>
              </div>
            )}
          </nav>

          {/* Hero Section */}
          <header className="pt-28 pb-20 px-6">
            <div className="container mx-auto flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-10 md:mb-0">
                <h1 className="text-4xl md:text-5xl font-bold mb-4">
                  <span className="gold-gradient">BoGuani</span>
                </h1>
                <h2 className="text-2xl md:text-3xl font-semibold mb-2">Messenger of Value</h2>
                <p className="text-xl mb-6 text-gray-300 italic">Where Words Carry Worth</p>
                <p className="mb-8 text-gray-300 max-w-lg">
                  Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange.
                  Send messages that matter, share moments that count, and transfer value instantly -
                  all protected by sacred-level encryption.
                </p>
                <div className="mb-8">
                  <p className="text-xl font-semibold gold-gradient">"Speak Gold. Share Value."</p>
                </div>
                <div className="flex flex-wrap gap-4">
                  <Link href="/auth" className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i className="fas fa-globe mr-2"></i> Open BoGuani Web Version
                  </Link>
                  <a href="#features" className="gold-border bg-transparent px-6 py-3 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i className="fas fa-play mr-2 text-yellow-400"></i> <span className="text-yellow-400">Watch Demo</span>
                  </a>
                </div>
              </div>

              <div className="md:w-1/2 flex justify-center">
                <div className="relative w-80 h-80">
                  {/* App mockup */}
                  <div className="absolute inset-0 bg-purple-800 rounded-3xl gold-border shadow-2xl overflow-hidden">
                    <div className="bg-purple-900 p-4 border-b border-yellow-400 border-opacity-30">
                      <div className="flex justify-between items-center">
                        <div className="text-yellow-400 text-xl">
                          <i className="fas fa-comment-dollar"></i>
                        </div>
                        <p className="font-semibold">BoGuani</p>
                        <div className="text-yellow-400">
                          <i className="fas fa-ellipsis-v"></i>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 h-full">
                      <div className="flex flex-col h-full">
                        <div className="bg-purple-600 bg-opacity-30 p-3 rounded-lg mb-3 self-start max-w-[70%]">
                          <p className="text-sm">Hey! Can you send me 20 for dinner tonight?</p>
                        </div>
                        <div className="bg-yellow-400 bg-opacity-20 p-3 rounded-lg mb-3 self-end max-w-[70%]">
                          <p className="text-sm">Sure! Sending it now with a special message.</p>
                        </div>
                        <div className="bg-yellow-400 bg-opacity-20 p-3 rounded-lg mb-3 self-end max-w-[70%]">
                          <div className="flex items-center">
                            <div className="text-yellow-400 mr-2">
                              <i className="fas fa-coins"></i>
                            </div>
                            <p className="text-sm">20.00 sent - Enjoy dinner!</p>
                          </div>
                        </div>
                        <div className="bg-purple-600 bg-opacity-30 p-3 rounded-lg self-start max-w-[70%]">
                          <p className="text-sm">Thanks! Value received. 🙏</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Decorative elements */}
                  <div className="absolute -bottom-5 -right-5 w-40 h-40 bg-yellow-400 opacity-10 rounded-full blur-xl"></div>
                  <div className="absolute -top-5 -left-5 w-20 h-20 bg-yellow-400 opacity-10 rounded-full blur-md"></div>
                </div>
              </div>
            </div>
          </header>
        </div>

        {/* Features Section */}
        <section id="features" className="py-16 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Key Features</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="feature-card bg-purple-800 p-6 rounded-xl gold-border">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-lock"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">256-bit End-to-End Encryption</h3>
                <p className="text-gray-300">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>
              </div>

              <div className="feature-card bg-purple-800 p-6 rounded-xl gold-border">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-bolt"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">Instant Money Transfers</h3>
                <p className="text-gray-300">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
              </div>

              <div className="feature-card bg-purple-800 p-6 rounded-xl gold-border">
                <div className="text-yellow-400 text-4xl mb-4">
                  <i className="fas fa-headset"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">24/7 Global Support</h3>
                <p className="text-gray-300">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Download Section */}
        <section id="download" className="py-16 bg-gradient-to-b from-gray-900 to-purple-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Get BoGuani Now</h2>
              <p className="text-gray-300 max-w-2xl mx-auto">Experience the revolution in value-based messaging. Available on all major platforms.</p>
              <div className="w-20 h-1 bg-yellow-400 mx-auto mt-4"></div>
            </div>

            <div className="flex flex-wrap justify-center gap-6">
              <a href="#" className="bg-purple-800 hover:bg-purple-600 transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                <i className="fab fa-apple text-3xl mr-4 text-yellow-400"></i>
                <div>
                  <p className="text-xs">Download for</p>
                  <p className="font-semibold">iOS</p>
                </div>
              </a>

              <a href="#" className="bg-purple-800 hover:bg-purple-600 transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                <i className="fab fa-android text-3xl mr-4 text-yellow-400"></i>
                <div>
                  <p className="text-xs">Download for</p>
                  <p className="font-semibold">Android</p>
                </div>
              </a>

              <a href="#" className="bg-purple-800 hover:bg-purple-600 transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                <i className="fas fa-desktop text-3xl mr-4 text-yellow-400"></i>
                <div>
                  <p className="text-xs">Download</p>
                  <p className="font-semibold">Desktop App</p>
                </div>
              </a>

              <Link href="/auth" className="bg-purple-800 hover:bg-purple-600 transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                <i className="fas fa-globe text-3xl mr-4 text-yellow-400"></i>
                <div>
                  <p className="text-xs">Open</p>
                  <p className="font-semibold">Web Version</p>
                </div>
              </Link>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-16 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-10 md:mb-0">
                <div className="relative">
                  <svg className="w-full max-w-md mx-auto" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="#fbbf24" fillOpacity="0.2" d="M45.7,-51.9C59.9,-41.5,72.3,-27.7,76.3,-11.5C80.3,4.7,75.9,23.4,65.1,36.6C54.3,49.8,37.1,57.5,19.3,63.3C1.6,69.1,-16.8,73,-32.5,67.5C-48.2,62,-61.3,47.1,-68.3,29.8C-75.3,12.5,-76.2,-7.2,-69.8,-23.6C-63.3,-40,-49.5,-53.1,-34.7,-63C-19.9,-72.9,-3.9,-79.7,9.9,-76.8C23.8,-73.9,31.5,-62.3,45.7,-51.9Z" transform="translate(100 100)" />
                    <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fill="#fbbf24" fontSize="24" fontWeight="bold">Taíno Wisdom</text>
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-yellow-400 text-6xl opacity-20">
                      <i className="fas fa-comment-dollar"></i>
                    </div>
                  </div>
                </div>
              </div>
              <div className="md:w-1/2 md:pl-10">
                <h2 className="text-3xl font-bold mb-6">Our Story</h2>
                <div className="w-20 h-1 bg-yellow-400 mb-6"></div>
                <p className="mb-4 text-gray-300">
                  BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. The name "BoGuani" combines elements from their language representing both "message" and "worth."
                </p>
                <p className="mb-6 text-gray-300">
                  We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.
                </p>
                <div className="flex items-center">
                  <div className="h-px bg-yellow-400 flex-grow"></div>
                  <p className="px-4 text-yellow-400 italic">"Speak Gold. Share Value."</p>
                  <div className="h-px bg-yellow-400 flex-grow"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-16 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">What Our Users Say</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300">BoGuani has completely changed how I think about messaging. Being able to send value along with my words makes every conversation more meaningful.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">M</div>
                  <div className="ml-3">
                    <p className="font-semibold">Maria L.</p>
                    <p className="text-xs text-gray-400">Entrepreneur</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300">The security features are impressive. I feel confident sending money through BoGuani knowing that my transactions are protected by top-tier encryption.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">J</div>
                  <div className="ml-3">
                    <p className="font-semibold">James T.</p>
                    <p className="text-xs text-gray-400">Security Analyst</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300">I use BoGuani daily for both personal and business communications. The ability to instantly transfer value has streamlined so many of my interactions.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">S</div>
                  <div className="ml-3">
                    <p className="font-semibold">Sarah K.</p>
                    <p className="text-xs text-gray-400">Digital Nomad</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-b from-purple-900 to-gray-900">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Communications?</h2>
            <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Join thousands of users who are already experiencing the power of value-based messaging with BoGuani.</p>

            <div className="flex flex-wrap justify-center gap-6">
              <Link href="/auth" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-globe mr-2"></i> Open Web Version
              </Link>
              <a href="#download" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-download mr-2 text-yellow-400"></i> <span className="text-yellow-400">Download App</span>
              </a>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 py-10">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </div>
                <p className="text-gray-400 mt-2">Messenger of Value</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-6 md:mb-0">
                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Product</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Security</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Pricing</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Company</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Careers</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Blog</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Resources</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Help Center</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Guides</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">API Docs</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Legal</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Terms</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Security</a></li>
                  </ul>
                </div>
              </div>

              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-twitter text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-facebook-f text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-instagram text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-linkedin-in text-yellow-400"></i>
                </a>
              </div>
            </div>

            <div className="border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">© 2024 BoGuani. All rights reserved.</p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <a href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-yellow-400 transition-colors">Terms of Service</a>
                <a href="#" className="hover:text-yellow-400 transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
