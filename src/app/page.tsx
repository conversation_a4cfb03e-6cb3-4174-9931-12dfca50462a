'use client';

import Link from 'next/link';
import { useState } from 'react';
import Image from 'next/image';

export default function Home() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
          background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
          font-family: 'Montserrat', sans-serif;
        }
        
        .gold-gradient {
          background: linear-gradient(90deg, #fbbf24 0%, #fde047 50%, #fbbf24 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }
        
        .gold-border {
          border: 2px solid transparent;
          background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                      linear-gradient(90deg, #fbbf24, #fde047) border-box;
        }
        
        .feature-card {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }
        
        .btn-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(251, 191, 36, 0.3);
        }
        
        .hero-pattern {
          background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23fbbf24' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
      `}</style>

      <div className="text-white min-h-screen hero-pattern">
        {/* Navigation */}
        <nav className="bg-gray-900 bg-opacity-80 backdrop-blur-md fixed w-full z-10">
          <div className="container mx-auto px-6 py-3 flex justify-between items-center">
            <div className="flex items-center">
              <div className="text-yellow-400 text-3xl mr-2">
                <i className="fas fa-comment-dollar"></i>
              </div>
              <span className="font-bold text-2xl gold-gradient">BoGuani</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
              <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
              <a href="#download" className="hover:text-yellow-400 transition-colors">Download</a>
              <a href="#support" className="hover:text-yellow-400 transition-colors">Support</a>
            </div>
            <div className="md:hidden">
              <button 
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-white focus:outline-none"
              >
                <i className="fas fa-bars"></i>
              </button>
            </div>
          </div>
          {/* Mobile menu */}
          {isMenuOpen && (
            <div className="md:hidden bg-gray-700">
              <div className="container mx-auto px-6 py-3 flex flex-col space-y-4">
                <a href="#features" className="hover:text-yellow-400 transition-colors">Features</a>
                <a href="#about" className="hover:text-yellow-400 transition-colors">About</a>
                <a href="#download" className="hover:text-yellow-400 transition-colors">Download</a>
                <a href="#support" className="hover:text-yellow-400 transition-colors">Support</a>
              </div>
            </div>
          )}
        </nav>

        {/* Hero Section */}
        <section className="pt-24 pb-16">
          <div className="container mx-auto px-6">
            <div className="flex flex-col lg:flex-row items-center min-h-screen">
              <div className="lg:w-1/2 lg:pr-12">
                <h1 className="text-5xl lg:text-7xl font-bold mb-6 leading-tight">
                  <span className="gold-gradient">BoGuani</span>
                </h1>
                <h2 className="text-2xl lg:text-3xl font-semibold mb-4">Messenger of Value</h2>
                <h3 className="text-xl lg:text-2xl text-gray-300 mb-6">Where Words Carry Worth</h3>
                <p className="text-lg text-gray-400 mb-8 max-w-lg leading-relaxed">
                  Inspired by ancient Taíno wisdom, BoGuani transforms communication into something more meaningful. Send messages that matter, share moments that count, and transfer value instantly - all protected by sacred-level encryption.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <Link href="/auth" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover text-center inline-flex items-center justify-center">
                    <i className="fas fa-globe mr-2"></i> Open Web Version
                  </Link>
                  <a href="#download" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover text-center inline-flex items-center justify-center">
                    <i className="fas fa-play mr-2 text-yellow-400"></i> <span className="text-yellow-400">Watch Demo</span>
                  </a>
                </div>
                <p className="text-yellow-400 italic font-medium text-lg">"Speak Gold. Share Value."</p>
              </div>
              
              <div className="lg:w-1/2 flex justify-center lg:justify-end mt-12 lg:mt-0">
                <div className="relative">
                  <div className="w-80 h-[600px] bg-gradient-to-b from-purple-800 to-purple-900 rounded-3xl shadow-2xl border border-yellow-400 border-opacity-30 overflow-hidden">
                    {/* Phone mockup content */}
                    <div className="p-6 h-full flex flex-col">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 text-sm font-bold">J</div>
                          <span className="ml-3 font-semibold">Joel</span>
                        </div>
                        <div className="text-yellow-400 text-xl">
                          <i className="fas fa-video"></i>
                        </div>
                      </div>
                      
                      <div className="flex-1 space-y-4 overflow-y-auto">
                        <div className="bg-purple-700 bg-opacity-60 p-4 rounded-2xl max-w-xs">
                          <p className="text-sm">Hey! Can you send me $50 for lunch today?</p>
                        </div>
                        <div className="bg-yellow-400 text-gray-900 p-4 rounded-2xl max-w-xs ml-auto">
                          <p className="text-sm font-medium">Sure! Sending now 💰</p>
                          <div className="mt-3 p-3 bg-yellow-300 rounded-xl text-xs font-bold text-center">
                            💸 $50.00 sent
                          </div>
                        </div>
                        <div className="bg-purple-700 bg-opacity-60 p-4 rounded-2xl max-w-xs">
                          <p className="text-sm">Thanks! Value received ✨</p>
                        </div>
                      </div>
                      
                      <div className="mt-4 p-3 bg-purple-700 bg-opacity-40 rounded-xl text-center">
                        <p className="text-xs text-yellow-400 font-medium">End-to-End Encrypted</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Floating elements */}
                  <div className="absolute -top-6 -right-6 w-20 h-20 bg-yellow-400 rounded-full flex items-center justify-center text-gray-900 text-3xl shadow-lg animate-pulse">
                    <i className="fas fa-dollar-sign"></i>
                  </div>
                  <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center text-yellow-400 text-xl shadow-lg animate-bounce">
                    <i className="fas fa-shield-alt"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Key Features */}
        <section id="features" className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Key Features</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-purple-800 p-8 rounded-xl feature-card text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-shield-alt"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">256-bit End-to-End Encryption</h3>
                <p className="text-gray-300">Your messages and transactions are protected with sacred-level security. No one can access your communications.</p>
              </div>
              
              <div className="bg-purple-800 p-8 rounded-xl feature-card text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-bolt"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">Instant Money Transfers</h3>
                <p className="text-gray-300">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
              </div>
              
              <div className="bg-purple-800 p-8 rounded-xl feature-card text-center">
                <div className="text-yellow-400 text-5xl mb-6">
                  <i className="fas fa-headset"></i>
                </div>
                <h3 className="text-xl font-semibold mb-4">24/7 Global Support</h3>
                <p className="text-gray-300">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Download Section */}
        <section id="download" className="py-20 bg-gray-900">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl font-bold mb-8">Get BoGuani Now</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4">Android</h3>
                <p className="text-gray-300 mb-6">Download BoGuani from Google Play Store</p>
                <a href="#" className="btn-primary inline-flex items-center">
                  <i className="fas fa-android mr-2"></i>
                  <span>Get it on Google Play</span>
                </a>
              </div>
              <div className="bg-purple-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4">iOS</h3>
                <p className="text-gray-300 mb-6">Download BoGuani from App Store</p>
                <a href="#" className="btn-primary inline-flex items-center">
                  <i className="fab fa-apple mr-2"></i>
                  <span>Download on the App Store</span>
                </a>
              </div>
            </div>
            <h2 className="text-3xl font-bold mb-4">Get BoGuani Now</h2>
            <p className="text-gray-300 mb-12 max-w-2xl mx-auto">
              Experience the revolution in value-based messaging. Available on all major platforms.
            </p>

            <div className="flex flex-wrap justify-center gap-6">
              <a href="#" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fab fa-apple mr-2"></i> Download for iOS
              </a>
              <a href="#" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fab fa-android mr-2"></i> Download for Android
              </a>
              <a href="#" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-desktop mr-2 text-yellow-400"></i> <span className="text-yellow-400">Desktop App</span>
              </a>
              <Link href="/auth" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-globe mr-2 text-yellow-400"></i> <span className="text-yellow-400">Web Version</span>
              </Link>
            </div>
          </div>
        </section>

        {/* About Section */}
        <section id="about" className="py-20 bg-purple-900">
          <div className="container mx-auto px-6">
            <div className="flex flex-col lg:flex-row items-center">
              <div className="lg:w-1/2 mb-12 lg:mb-0">
                <div className="relative">
                  <svg className="w-full max-w-md mx-auto" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="#fbbf24" fillOpacity="0.2" d="M45.7,-51.9C59.9,-41.5,72.3,-27.7,76.3,-11.5C80.3,4.7,75.9,23.4,65.1,36.6C54.3,49.8,37.1,57.5,19.3,63.3C1.6,69.1,-16.8,73,-32.5,67.5C-48.2,62,-61.3,47.1,-68.3,29.8C-75.3,12.5,-76.2,-7.2,-69.8,-23.6C-63.3,-40,-49.5,-53.1,-34.7,-63C-19.9,-72.9,-3.9,-79.7,9.9,-76.8C23.8,-73.9,31.5,-62.3,45.7,-51.9Z" transform="translate(100 100)" />
                    <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fill="#fbbf24" fontSize="20" fontWeight="bold">Taíno Wisdom</text>
                  </svg>
                </div>
              </div>
              <div className="lg:w-1/2 lg:pl-12">
                <h2 className="text-3xl font-bold mb-6">Our Story</h2>
                <div className="w-20 h-1 bg-yellow-400 mb-6"></div>
                <p className="mb-4 text-gray-300 leading-relaxed">
                  BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. The name "BoGuani" combines elements from their language representing both "message" and "worth."
                </p>
                <p className="mb-6 text-gray-300 leading-relaxed">
                  We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.
                </p>
                <div className="flex items-center">
                  <div className="h-px bg-yellow-400 flex-grow"></div>
                  <p className="px-4 text-yellow-400 italic font-medium">"Speak Gold. Share Value."</p>
                  <div className="h-px bg-yellow-400 flex-grow"></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="py-20 bg-gray-900">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">What Our Users Say</h2>
              <div className="w-20 h-1 bg-yellow-400 mx-auto"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300 leading-relaxed">BoGuani has completely changed how I think about messaging. Being able to send value along with my words makes every conversation more meaningful.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">M</div>
                  <div className="ml-3">
                    <p className="font-semibold">Maria L.</p>
                    <p className="text-xs text-gray-400">Entrepreneur</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300 leading-relaxed">The security features are impressive. I feel confident sending money through BoGuani knowing that my transactions are protected by top-tier encryption.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">J</div>
                  <div className="ml-3">
                    <p className="font-semibold">James T.</p>
                    <p className="text-xs text-gray-400">Security Analyst</p>
                  </div>
                </div>
              </div>

              <div className="bg-purple-800 p-6 rounded-xl relative">
                <div className="text-yellow-400 text-4xl absolute -top-5 -left-2">
                  <i className="fas fa-quote-left"></i>
                </div>
                <p className="mt-4 mb-6 text-gray-300 leading-relaxed">I use BoGuani daily for both personal and business communications. The ability to instantly transfer value has streamlined so many of my interactions.</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-yellow-400 flex items-center justify-center text-gray-900 font-bold">S</div>
                  <div className="ml-3">
                    <p className="font-semibold">Sarah K.</p>
                    <p className="text-xs text-gray-400">Digital Nomad</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-b from-purple-900 to-gray-900">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Communications?</h2>
            <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Join thousands of users who are already experiencing the power of value-based messaging with BoGuani.</p>

            <div className="flex flex-wrap justify-center gap-6">
              <Link href="/auth" className="bg-yellow-400 text-gray-900 px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-globe mr-2"></i> Open Web Version
              </Link>
              <a href="#download" className="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                <i className="fas fa-download mr-2 text-yellow-400"></i> <span className="text-yellow-400">Download App</span>
              </a>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-gray-900 py-12">
          <div className="container mx-auto px-6">
            <div className="flex flex-col md:flex-row justify-between items-center mb-8">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center">
                  <div className="text-yellow-400 text-2xl mr-2">
                    <i className="fas fa-comment-dollar"></i>
                  </div>
                  <span className="font-bold text-xl gold-gradient">BoGuani</span>
                </div>
                <p className="text-gray-400 mt-2">Messenger of Value</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-6 md:mb-0">
                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Product</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="#features" className="hover:text-yellow-400 transition-colors">Features</a></li>
                    <li><a href="/security" className="hover:text-yellow-400 transition-colors">Security</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Pricing</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Company</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="#about" className="hover:text-yellow-400 transition-colors">About</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Careers</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">Blog</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Resources</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="/support" className="hover:text-yellow-400 transition-colors">Help Center</a></li>
                    <li><a href="/guides" className="hover:text-yellow-400 transition-colors">Guides</a></li>
                    <li><a href="#" className="hover:text-yellow-400 transition-colors">API Docs</a></li>
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold mb-3 text-yellow-400">Legal</h3>
                  <ul className="space-y-2 text-gray-400">
                    <li><a href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy</a></li>
                    <li><a href="/terms" className="hover:text-yellow-400 transition-colors">Terms</a></li>
                    <li><a href="/security" className="hover:text-yellow-400 transition-colors">Security</a></li>
                  </ul>
                </div>
              </div>

              <div className="flex space-x-4">
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-twitter text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-facebook-f text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-instagram text-yellow-400"></i>
                </a>
                <a href="#" className="w-10 h-10 rounded-full bg-purple-800 flex items-center justify-center hover:bg-purple-600 transition-colors">
                  <i className="fab fa-linkedin-in text-yellow-400"></i>
                </a>
              </div>
            </div>

            <div className="border-t border-gray-700 pt-6 flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 text-sm mb-4 md:mb-0">© 2024 BoGuani. All rights reserved.</p>
              <div className="flex space-x-6 text-gray-400 text-sm">
                <a href="/privacy" className="hover:text-yellow-400 transition-colors">Privacy Policy</a>
                <a href="/terms" className="hover:text-yellow-400 transition-colors">Terms of Service</a>
                <a href="#" className="hover:text-yellow-400 transition-colors">Cookie Policy</a>
              </div>
            </div>
          </div>
        </footer>
        {/* Footer */}
        <footer className="bg-gray-800 py-12">
          <div className="container mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-xl font-semibold mb-4">BoGuani</h3>
                <p className="text-gray-400">Messenger of Value</p>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">Company</h3>
                <ul className="space-y-2">
                  <li><a href="#about" className="text-gray-400 hover:text-yellow-400">About Us</a></li>
                  <li><a href="#features" className="text-gray-400 hover:text-yellow-400">Features</a></li>
                  <li><a href="#download" className="text-gray-400 hover:text-yellow-400">Download</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">Support</h3>
                <ul className="space-y-2">
                  <li><a href="#support" className="text-gray-400 hover:text-yellow-400">Help Center</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-yellow-400">Contact Us</a></li>
                  <li><a href="#" className="text-gray-400 hover:text-yellow-400">Terms of Service</a></li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-4">Social</h3>
                <div className="flex space-x-4">
                  <a href="#" className="text-gray-400 hover:text-yellow-400">
                    <i className="fab fa-twitter"></i>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-yellow-400">
                    <i className="fab fa-facebook"></i>
                  </a>
                  <a href="#" className="text-gray-400 hover:text-yellow-400">
                    <i className="fab fa-instagram"></i>
                  </a>
                </div>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
              <p>&copy; {new Date().getFullYear()} BoGuani. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
