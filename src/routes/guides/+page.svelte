<script lang="ts">
  const guides = [
    {
      title: 'Getting Started with BoGuani',
      description: 'Learn the basics of setting up your account and sending your first message',
      duration: '5 min read',
      difficulty: 'Beginner',
      category: 'Getting Started',
      icon: 'fas fa-play-circle',
      steps: [
        'Download and install BoGuani',
        'Create your account with phone verification',
        'Set up your profile',
        'Send your first message',
        'Explore security features'
      ]
    },
    {
      title: 'Sending Your First Payment',
      description: 'Step-by-step guide to securely sending money through BoGuani',
      duration: '8 min read',
      difficulty: 'Beginner',
      category: 'Payments',
      icon: 'fas fa-credit-card',
      steps: [
        'Connect your bank account',
        'Verify your identity',
        'Select a contact to pay',
        'Enter payment amount',
        'Add a message and send'
      ]
    },
    {
      title: 'Understanding End-to-End Encryption',
      description: 'Learn how BoGuani protects your messages and transactions',
      duration: '10 min read',
      difficulty: 'Intermediate',
      category: 'Security',
      icon: 'fas fa-shield-alt',
      steps: [
        'What is end-to-end encryption?',
        'How your messages are protected',
        'Key generation and storage',
        'Verifying contact security',
        'Best security practices'
      ]
    },
    {
      title: 'Managing Group Chats and Payments',
      description: 'Create groups, manage members, and split payments easily',
      duration: '12 min read',
      difficulty: 'Intermediate',
      category: 'Advanced Features',
      icon: 'fas fa-users',
      steps: [
        'Creating a group chat',
        'Adding and removing members',
        'Group payment features',
        'Splitting bills and expenses',
        'Group security settings'
      ]
    },
    {
      title: 'Advanced Security Settings',
      description: 'Configure advanced security features for maximum protection',
      duration: '15 min read',
      difficulty: 'Advanced',
      category: 'Security',
      icon: 'fas fa-lock',
      steps: [
        'Two-factor authentication setup',
        'Biometric authentication',
        'Session management',
        'Device security',
        'Emergency access recovery'
      ]
    },
    {
      title: 'API Integration Guide',
      description: 'Integrate BoGuani messaging and payments into your applications',
      duration: '20 min read',
      difficulty: 'Advanced',
      category: 'Development',
      icon: 'fas fa-code',
      steps: [
        'Getting API credentials',
        'Authentication methods',
        'Sending messages via API',
        'Processing payments',
        'Webhook configuration'
      ]
    }
  ];

  let selectedCategory = 'All';
  const categories = ['All', 'Getting Started', 'Payments', 'Security', 'Advanced Features', 'Development'];

  $: filteredGuides = selectedCategory === 'All' 
    ? guides 
    : guides.filter(guide => guide.category === selectedCategory);

  function getDifficultyColor(difficulty: string) {
    switch (difficulty) {
      case 'Beginner': return 'text-green-400';
      case 'Intermediate': return 'text-yellow-400';
      case 'Advanced': return 'text-red-400';
      default: return 'text-gray-400';
    }
  }
</script>

<svelte:head>
  <title>Guides - BoGuani</title>
  <meta name="description" content="Learn how to use BoGuani with our comprehensive guides and tutorials. From getting started to advanced features." />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white">
  <!-- Navigation -->
  <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <div class="text-[#D4AF37] text-3xl mr-3">
            <i class="fas fa-comment-dollar"></i>
          </div>
          <span class="font-bold text-2xl bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </a>
      </div>
      <div class="flex space-x-6">
        <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
        <a href="/help" class="hover:text-[#D4AF37] transition-colors">Help</a>
        <a href="/support" class="hover:text-[#D4AF37] transition-colors">Support</a>
        <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="pt-24 pb-16 px-6">
    <div class="container mx-auto max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
          <i class="fas fa-book-open text-3xl text-[#2D1B4E]"></i>
        </div>
        <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Guides & Tutorials
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto">
          Master BoGuani with our step-by-step guides. From basic messaging to advanced security features, we've got you covered.
        </p>
      </div>

      <!-- Category Filter -->
      <div class="flex justify-center mb-12">
        <div class="flex flex-wrap gap-3">
          {#each categories as category}
            <button
              on:click={() => selectedCategory = category}
              class="px-6 py-3 rounded-full font-semibold transition-all {selectedCategory === category 
                ? 'bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E]' 
                : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37] border border-[#D4AF37] border-opacity-30'}"
            >
              {category}
            </button>
          {/each}
        </div>
      </div>

      <!-- Guides Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {#each filteredGuides as guide}
          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg rounded-2xl border border-[#D4AF37] border-opacity-30 overflow-hidden hover:border-opacity-50 transition-all group">
            <!-- Guide Header -->
            <div class="p-6 border-b border-[#D4AF37] border-opacity-20">
              <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
                  <i class="{guide.icon} text-[#2D1B4E] text-lg"></i>
                </div>
                <div class="text-right">
                  <span class="text-xs {getDifficultyColor(guide.difficulty)} font-semibold">{guide.difficulty}</span>
                  <p class="text-xs text-gray-400">{guide.duration}</p>
                </div>
              </div>
              <h3 class="text-xl font-bold text-[#F2D675] mb-2 group-hover:text-[#D4AF37] transition-colors">{guide.title}</h3>
              <p class="text-gray-300 text-sm leading-relaxed">{guide.description}</p>
            </div>

            <!-- Guide Steps -->
            <div class="p-6">
              <h4 class="text-sm font-semibold text-[#D4AF37] mb-4 uppercase tracking-wide">What You'll Learn</h4>
              <ul class="space-y-2">
                {#each guide.steps as step, index}
                  <li class="flex items-start text-sm text-gray-300">
                    <span class="w-5 h-5 bg-[#D4AF37] bg-opacity-20 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span class="text-[#D4AF37] text-xs font-bold">{index + 1}</span>
                    </span>
                    <span>{step}</span>
                  </li>
                {/each}
              </ul>
            </div>

            <!-- Guide Footer -->
            <div class="p-6 pt-0">
              <button class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] py-3 rounded-full font-semibold hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
                <i class="fas fa-book-reader mr-2"></i>
                Read Guide
              </button>
            </div>
          </div>
        {/each}
      </div>

      <!-- Call to Action -->
      <div class="text-center mt-16">
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 max-w-2xl mx-auto">
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">Ready to Get Started?</h3>
          <p class="text-gray-300 mb-6">Join thousands of users who are already experiencing the power of value-based messaging.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/auth" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
              <i class="fas fa-rocket mr-2"></i>
              Start Using BoGuani
            </a>
            <a href="/downloads" class="border-2 border-[#D4AF37] text-[#D4AF37] px-8 py-4 rounded-full font-bold text-lg hover:bg-[#D4AF37] hover:text-[#2D1B4E] transition-all">
              <i class="fas fa-download mr-2"></i>
              Download Apps
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
