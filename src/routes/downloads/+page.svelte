<script lang="ts">
  import { page } from '$app/stores';
  import { onMount } from 'svelte';

  let platform = '';

  onMount(() => {
    const urlParams = new URLSearchParams(window.location.search);
    platform = urlParams.get('platform') || 'all';
  });
</script>

<svelte:head>
  <title>Download BoGuani - Messenger of Value</title>
  <meta name="description" content="Download BoGuani for iOS, Android, Desktop, or use our Web Version. Experience secure messaging with instant value transfer." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white">
  <!-- Navigation -->
  <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <div class="text-[#D4AF37] text-3xl mr-3">
            <i class="fas fa-comment-dollar"></i>
          </div>
          <span class="font-bold text-2xl bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </a>
      </div>
      <div class="flex space-x-6">
        <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
        <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="pt-24 pb-16 px-6">
    <div class="container mx-auto max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Download BoGuani
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Choose your platform and start experiencing the future of value-based messaging
        </p>
      </div>

      <!-- Download Options -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
        <!-- iOS -->
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center">
          <i class="fab fa-apple text-6xl text-[#D4AF37] mb-6"></i>
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">iOS App</h3>
          <p class="text-gray-300 mb-6">iPhone & iPad</p>
          <button class="w-full bg-gray-600 text-gray-400 px-6 py-3 rounded-full font-semibold cursor-not-allowed">
            Coming Soon
          </button>
          <p class="text-xs text-gray-500 mt-2">Available Q2 2024</p>
        </div>

        <!-- Android -->
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center">
          <i class="fab fa-android text-6xl text-[#D4AF37] mb-6"></i>
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">Android App</h3>
          <p class="text-gray-300 mb-6">Android 8.0+</p>
          <button class="w-full bg-gray-600 text-gray-400 px-6 py-3 rounded-full font-semibold cursor-not-allowed">
            Coming Soon
          </button>
          <p class="text-xs text-gray-500 mt-2">Available Q2 2024</p>
        </div>

        <!-- Desktop -->
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center">
          <i class="fas fa-desktop text-6xl text-[#D4AF37] mb-6"></i>
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">Desktop App</h3>
          <p class="text-gray-300 mb-6">Windows, Mac, Linux</p>
          <button class="w-full bg-gray-600 text-gray-400 px-6 py-3 rounded-full font-semibold cursor-not-allowed">
            Coming Soon
          </button>
          <p class="text-xs text-gray-500 mt-2">Available Q3 2024</p>
        </div>

        <!-- Web Version -->
        <div class="bg-gradient-to-br from-[#D4AF37] to-[#F2D675] p-8 rounded-2xl text-center">
          <i class="fas fa-globe text-6xl text-[#2D1B4E] mb-6"></i>
          <h3 class="text-2xl font-bold mb-4 text-[#2D1B4E]">Web Version</h3>
          <p class="text-[#2D1B4E] opacity-80 mb-6">Available Now</p>
          <a href="/auth" class="block w-full bg-[#2D1B4E] text-[#D4AF37] px-6 py-3 rounded-full font-semibold hover:bg-[#3D2A5F] transition-colors">
            Open Web App
          </a>
          <p class="text-xs text-[#2D1B4E] opacity-70 mt-2">No download required</p>
        </div>
      </div>

      <!-- Features Preview -->
      <div class="text-center">
        <h2 class="text-3xl font-bold mb-8 text-[#F2D675]">What You'll Get</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="text-4xl mb-4 text-[#D4AF37]">🔒</div>
            <h3 class="text-xl font-semibold mb-2">End-to-End Encryption</h3>
            <p class="text-gray-400">Military-grade security for all your messages and transactions</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4 text-[#D4AF37]">⚡</div>
            <h3 class="text-xl font-semibold mb-2">Instant Transfers</h3>
            <p class="text-gray-400">Send money as easily as sending a text message</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4 text-[#D4AF37]">🌍</div>
            <h3 class="text-xl font-semibold mb-2">Global Reach</h3>
            <p class="text-gray-400">Connect and transfer value worldwide</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
