<script lang="ts">
  import { goto } from '$app/navigation';
  
  function goBack() {
    goto('/downloads');
  }
</script>

<svelte:head>
  <title>BoGuani Desktop App - Coming Soon</title>
  <meta name="description" content="BoGuani Desktop app for Windows, Mac, and Linux is coming soon. Experience BoGuani on your computer." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white flex items-center justify-center px-6">
  <div class="max-w-2xl mx-auto text-center">
    <!-- Desktop Icon -->
    <div class="mb-8">
      <i class="fas fa-desktop text-8xl text-[#D4AF37] mb-6"></i>
    </div>
    
    <!-- Content -->
    <h1 class="text-5xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
      BoGuani Desktop
    </h1>
    
    <p class="text-2xl text-gray-300 mb-8">Coming Soon for Windows, Mac & Linux</p>
    
    <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 mb-8">
      <h2 class="text-2xl font-bold mb-4 text-[#F2D675]">What to Expect</h2>
      <ul class="text-left space-y-3 text-gray-300">
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Full-featured desktop experience with keyboard shortcuts
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Multi-window support for enhanced productivity
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Native desktop notifications and system tray integration
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          File sharing and drag-and-drop support
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Cross-platform compatibility (Windows, macOS, Linux)
        </li>
      </ul>
    </div>
    
    <div class="mb-8">
      <p class="text-lg text-gray-400 mb-4">Expected Release: Q3 2024</p>
      <p class="text-sm text-gray-500">We're working hard to bring BoGuani to desktop. Stay tuned for updates!</p>
    </div>
    
    <!-- Platform Icons -->
    <div class="flex justify-center space-x-8 mb-8">
      <div class="text-center">
        <i class="fab fa-windows text-3xl text-[#D4AF37] mb-2"></i>
        <p class="text-sm text-gray-400">Windows 10+</p>
      </div>
      <div class="text-center">
        <i class="fab fa-apple text-3xl text-[#D4AF37] mb-2"></i>
        <p class="text-sm text-gray-400">macOS 11+</p>
      </div>
      <div class="text-center">
        <i class="fab fa-linux text-3xl text-[#D4AF37] mb-2"></i>
        <p class="text-sm text-gray-400">Ubuntu 20+</p>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <a href="/auth" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
        Try Web Version Now
      </a>
      <button on:click={goBack} class="border-2 border-[#D4AF37] text-[#D4AF37] px-8 py-4 rounded-full font-bold text-lg hover:bg-[#D4AF37] hover:text-[#2D1B4E] transition-all">
        View All Downloads
      </button>
    </div>
  </div>
</div>
