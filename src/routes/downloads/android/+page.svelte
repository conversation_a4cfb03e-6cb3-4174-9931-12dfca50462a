<script lang="ts">
  import { goto } from '$app/navigation';
  
  function goBack() {
    goto('/downloads');
  }
</script>

<svelte:head>
  <title>BoGuani for Android - Coming Soon</title>
  <meta name="description" content="BoGuani Android app is coming soon. Be the first to know when it's available on Google Play Store." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white flex items-center justify-center px-6">
  <div class="max-w-2xl mx-auto text-center">
    <!-- Android Icon -->
    <div class="mb-8">
      <i class="fab fa-android text-8xl text-[#D4AF37] mb-6"></i>
    </div>
    
    <!-- Content -->
    <h1 class="text-5xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
      BoGuani for Android
    </h1>
    
    <p class="text-2xl text-gray-300 mb-8">Coming Soon to Google Play Store</p>
    
    <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 mb-8">
      <h2 class="text-2xl font-bold mb-4 text-[#F2D675]">What to Expect</h2>
      <ul class="text-left space-y-3 text-gray-300">
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Native Android experience with Material Design
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Fingerprint and biometric authentication support
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Rich push notifications and quick replies
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Integration with Android Contacts and Google Pay
        </li>
        <li class="flex items-center">
          <i class="fas fa-check text-[#D4AF37] mr-3"></i>
          Adaptive themes and Android 12+ features
        </li>
      </ul>
    </div>
    
    <div class="mb-8">
      <p class="text-lg text-gray-400 mb-4">Expected Release: Q2 2024</p>
      <p class="text-sm text-gray-500">We're working hard to bring BoGuani to Android. Stay tuned for updates!</p>
    </div>
    
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <a href="/auth" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
        Try Web Version Now
      </a>
      <button on:click={goBack} class="border-2 border-[#D4AF37] text-[#D4AF37] px-8 py-4 rounded-full font-bold text-lg hover:bg-[#D4AF37] hover:text-[#2D1B4E] transition-all">
        View All Downloads
      </button>
    </div>
  </div>
</div>
