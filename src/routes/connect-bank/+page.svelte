<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';
  import { plaid } from '$lib/plaid';
  import { getAuth } from 'firebase/auth';
  import { db } from '$lib/firebase';
  import { doc, setDoc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';

  let loading = true;
  let linkToken = '';
  let error: string | null = null;
  let success = '';
  let plaidLoaded = false;
  
  // Check if user is authenticated
  onMount(async () => {
    if (!browser) return;
    
    try {
      const auth = getAuth();
      if (!auth.currentUser) {
        goto('/auth');
        return;
      }
      
      // Initialize Plaid Link
      await initializePlaid();
      plaidLoaded = true;
    } catch (err) {
      console.error('Error initializing bank connection:', err);
      error = 'Failed to initialize bank connection';
    } finally {
      loading = false;
    }
  });
  
  async function initializePlaid() {
    try {
      const auth = getAuth();
      if (!auth.currentUser) throw new Error('User not authenticated');
      
      // In a real app, call your backend to create a link token
      // For now, we'll use a mock token or implement the client-side flow
      linkToken = 'development-mock-link-token';
      
      // Load Plaid script
      await loadPlaidScript();
    } catch (err) {
      console.error('Plaid initialization error:', err);
      throw err;
    }
  }
  
  function loadPlaidScript() {
    return new Promise((resolve, reject) => {
      if (window.Plaid) {
        resolve(true);
        return;
      }
      
      const script = document.createElement('script');
      script.src = 'https://cdn.plaid.com/link/v2/stable/link-initialize.js';
      script.onload = () => resolve(true);
      script.onerror = (err) => {
        console.error('Failed to load Plaid script', err);
        reject(new Error('Failed to load Plaid'));
      };
      document.head.appendChild(script);
    });
  }
  
  async function openPlaid() {
    if (!window.Plaid) {
      error = 'Plaid failed to load. Please refresh and try again.';
      return;
    }
    
    try {
      const handler = window.Plaid.create({
        token: linkToken,
        onSuccess: async (publicToken: string, metadata: any) => {
          // In a real app, send public token to your backend
          // For now, we'll simulate a successful connection
          await handleBankConnected(metadata);
        },
        onExit: (err: any, metadata: any) => {
          if (err) {
            console.error('Plaid exit error:', err);
            error = 'Failed to connect bank. Please try again.';
          }
        },
        onEvent: (eventName: string, metadata: any) => {
          console.log('Plaid event:', eventName, metadata);
        },
      });
      
      handler.open();
    } catch (err) {
      console.error('Plaid error:', err);
      error = 'Failed to open Plaid. Please try again.';
    }
  }
  
  async function handleBankConnected(metadata: any) {
    try {
      const auth = getAuth();
      if (!auth.currentUser) throw new Error('User not authenticated');
      
      // In a real app, you would send the public token to your backend
      // and then update Firestore with the bank connection details
      const userId = auth.currentUser.uid;
      const userRef = doc(db, 'users', userId);
      
      // Create or update user document with bank account
      await setDoc(userRef, {
        bankAccounts: arrayUnion({
          id: `bank_${Date.now()}`,
          name: metadata.accounts[0]?.name || 'Bank Account',
          mask: metadata.accounts[0]?.mask || '0000',
          type: 'checking',
          isConnected: true,
          connectedAt: new Date().toISOString(),
          institution: metadata.institution?.name || 'Bank',
          // In a real app, you would store the access token and item ID
          // but they should be handled server-side
        })
      }, { merge: true });
      
      success = 'Bank account connected successfully!';
      // Redirect back to settings after a short delay
      setTimeout(() => {
        goto('/settings');
      }, 2000);
      
    } catch (err) {
      console.error('Error saving bank connection:', err);
      error = 'Failed to save bank connection. Please try again.';
    }
  }
  
  function handleBack() {
    history.back();
  }
</script>

<svelte:head>
  <title>Connect Bank - BoGuani</title>
</svelte:head>

<div class="min-h-screen bg-gradient-main text-gray-200 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md mx-auto">
    <div class="glass-effect rounded-2xl p-8">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">
          Connect Your Bank
        </h1>
        <p class="text-gray-400">Securely link your bank account to send and receive money</p>
      </div>
      
      {#if loading}
        <div class="text-center py-8">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-purple-500 border-t-gold-500"></div>
          <p class="mt-4 text-gray-400">Loading secure connection...</p>
        </div>
      {:else if error}
        <div class="bg-red-900/30 border border-red-800/50 rounded-lg p-4 mb-6">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <span class="text-red-300">{error}</span>
          </div>
          <button 
            on:click={() => error = null} 
            class="mt-2 text-sm text-red-300 hover:text-red-100"
          >
            Try Again
          </button>
        </div>
      {:else if success}
        <div class="bg-green-900/30 border border-green-800/50 rounded-lg p-4 mb-6">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <span class="text-green-300">{success}</span>
          </div>
        </div>
      {/if}
      
      <div class="space-y-6">
        <div class="bg-purple-900/20 border border-purple-800/50 rounded-xl p-6 text-center">
          <div class="bg-purple-800/30 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3.9 2.7c.5.3 1.1.3 1.6 0L16 7m-9.5 9h9.5m-9.5 0a5.002 5.002 0 01-3.9-6.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-white mb-2">Connect Your Bank Securely</h3>
          <p class="text-sm text-gray-400 mb-4">
            We use Plaid to securely connect to your bank. Your login credentials are never stored by BoGuani.
          </p>
          
          <button
            on:click={openPlaid}
            disabled={!plaidLoaded}
            class="w-full py-3 px-6 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-white font-medium rounded-lg transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if !plaidLoaded}
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            {:else}
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z"/>
              </svg>
              Connect with Plaid
            {/if}
          </button>
          
          <div class="mt-4 flex items-center justify-center space-x-2">
            <span class="text-xs text-gray-500">Secured by</span>
            <img src="/plaid-logo.png" alt="Plaid" class="h-4 opacity-70" />
          </div>
        </div>
        
        <div class="text-center">
          <button
            on:click={handleBack}
            class="text-sm text-gray-400 hover:text-white transition-colors flex items-center justify-center mx-auto"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Back to Settings
          </button>
        </div>
      </div>
    </div>
    
    <div class="mt-6 text-center">
      <p class="text-xs text-gray-500">
        By connecting your bank, you agree to our
        <a href="/terms" class="text-gold-400 hover:underline">Terms of Service</a> and
        <a href="/privacy" class="text-gold-400 hover:underline">Privacy Policy</a>.
      </p>
    </div>
  </div>
</div>
