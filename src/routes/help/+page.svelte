<script lang="ts">
  let searchQuery = '';
  
  const helpCategories = [
    {
      title: 'Getting Started',
      icon: 'fas fa-rocket',
      articles: [
        { title: 'How to create an account', url: '#' },
        { title: 'Setting up your profile', url: '#' },
        { title: 'Verifying your phone number', url: '#' },
        { title: 'First steps with BoGuani', url: '#' }
      ]
    },
    {
      title: 'Messaging',
      icon: 'fas fa-comments',
      articles: [
        { title: 'Sending your first message', url: '#' },
        { title: 'Creating group chats', url: '#' },
        { title: 'Message encryption explained', url: '#' },
        { title: 'Sharing files and media', url: '#' }
      ]
    },
    {
      title: 'Payments',
      icon: 'fas fa-credit-card',
      articles: [
        { title: 'How to send money', url: '#' },
        { title: 'Connecting your bank account', url: '#' },
        { title: 'Payment security features', url: '#' },
        { title: 'Transaction limits and fees', url: '#' }
      ]
    },
    {
      title: 'Security',
      icon: 'fas fa-shield-alt',
      articles: [
        { title: 'Understanding end-to-end encryption', url: '#' },
        { title: 'Setting up two-factor authentication', url: '#' },
        { title: 'Reporting suspicious activity', url: '#' },
        { title: 'Account security best practices', url: '#' }
      ]
    },
    {
      title: 'Troubleshooting',
      icon: 'fas fa-tools',
      articles: [
        { title: 'App not loading or crashing', url: '#' },
        { title: 'Messages not sending', url: '#' },
        { title: 'Payment issues', url: '#' },
        { title: 'Account access problems', url: '#' }
      ]
    },
    {
      title: 'Account Management',
      icon: 'fas fa-user-cog',
      articles: [
        { title: 'Updating your profile', url: '#' },
        { title: 'Changing your phone number', url: '#' },
        { title: 'Privacy settings', url: '#' },
        { title: 'Deleting your account', url: '#' }
      ]
    }
  ];

  $: filteredCategories = helpCategories.map(category => ({
    ...category,
    articles: category.articles.filter(article => 
      searchQuery === '' || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      category.title.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })).filter(category => category.articles.length > 0);
</script>

<svelte:head>
  <title>Help Center - BoGuani</title>
  <meta name="description" content="Find help and answers to your questions about using BoGuani. Browse our comprehensive help center for guides and tutorials." />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white">
  <!-- Navigation -->
  <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <div class="text-[#D4AF37] text-3xl mr-3">
            <i class="fas fa-comment-dollar"></i>
          </div>
          <span class="font-bold text-2xl bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </a>
      </div>
      <div class="flex space-x-6">
        <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
        <a href="/support" class="hover:text-[#D4AF37] transition-colors">Support</a>
        <a href="/contact" class="hover:text-[#D4AF37] transition-colors">Contact</a>
        <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="pt-24 pb-16 px-6">
    <div class="container mx-auto max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
          <i class="fas fa-question-circle text-3xl text-[#2D1B4E]"></i>
        </div>
        <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Help Center
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Find answers to your questions and learn how to get the most out of BoGuani
        </p>
      </div>

      <!-- Search Bar -->
      <div class="max-w-2xl mx-auto mb-16">
        <div class="relative">
          <input
            type="text"
            bind:value={searchQuery}
            placeholder="Search for help articles..."
            class="w-full px-6 py-4 pl-14 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37] text-lg"
          />
          <i class="fas fa-search absolute left-5 top-1/2 transform -translate-y-1/2 text-[#D4AF37] text-xl"></i>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
        <a href="/contact" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all group">
          <i class="fas fa-envelope text-4xl text-[#D4AF37] mb-4 group-hover:scale-110 transition-transform"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">Contact Support</h3>
          <p class="text-gray-300">Get personalized help from our team</p>
        </a>

        <a href="/support" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all group">
          <i class="fas fa-comments text-4xl text-[#D4AF37] mb-4 group-hover:scale-110 transition-transform"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">FAQ</h3>
          <p class="text-gray-300">Browse frequently asked questions</p>
        </a>

        <a href="/auth" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all group">
          <i class="fas fa-rocket text-4xl text-[#D4AF37] mb-4 group-hover:scale-110 transition-transform"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">Get Started</h3>
          <p class="text-gray-300">Start using BoGuani right now</p>
        </a>
      </div>

      <!-- Help Categories -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {#each filteredCategories as category}
          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30">
            <div class="flex items-center mb-6">
              <div class="w-12 h-12 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center mr-4">
                <i class="{category.icon} text-[#2D1B4E] text-lg"></i>
              </div>
              <h3 class="text-xl font-bold text-[#F2D675]">{category.title}</h3>
            </div>
            
            <div class="space-y-3">
              {#each category.articles as article}
                <a href={article.url} class="block text-gray-300 hover:text-[#D4AF37] transition-colors py-2 border-b border-gray-600 border-opacity-30 last:border-b-0">
                  <div class="flex items-center">
                    <i class="fas fa-chevron-right text-[#D4AF37] text-xs mr-3"></i>
                    <span>{article.title}</span>
                  </div>
                </a>
              {/each}
            </div>
          </div>
        {/each}
      </div>

      {#if filteredCategories.length === 0}
        <div class="text-center py-16">
          <i class="fas fa-search text-6xl text-gray-500 mb-6"></i>
          <h3 class="text-2xl font-bold text-gray-400 mb-4">No results found</h3>
          <p class="text-gray-500 mb-8">Try adjusting your search terms or browse our categories above.</p>
          <button on:click={() => searchQuery = ''} class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-3 rounded-full font-semibold hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
            Clear Search
          </button>
        </div>
      {/if}

      <!-- Still Need Help -->
      <div class="text-center mt-16">
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 max-w-2xl mx-auto">
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">Still Need Help?</h3>
          <p class="text-gray-300 mb-6">Can't find what you're looking for? Our support team is here to help you 24/7.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="/contact" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-3 rounded-full font-semibold hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
              Contact Support
            </a>
            <a href="/support" class="border-2 border-[#D4AF37] text-[#D4AF37] px-6 py-3 rounded-full font-semibold hover:bg-[#D4AF37] hover:text-[#2D1B4E] transition-all">
              Browse FAQ
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
