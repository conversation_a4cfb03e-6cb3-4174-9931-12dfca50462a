<script lang="ts">
  let name = '';
  let email = '';
  let subject = '';
  let message = '';
  let isSubmitting = false;
  let isSubmitted = false;

  async function handleSubmit() {
    isSubmitting = true;
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    isSubmitting = false;
    isSubmitted = true;
    
    // Reset form after 3 seconds
    setTimeout(() => {
      isSubmitted = false;
      name = '';
      email = '';
      subject = '';
      message = '';
    }, 3000);
  }
</script>

<svelte:head>
  <title>Contact Us - BoGuani</title>
  <meta name="description" content="Get in touch with the BoGuani team. We're here to help with any questions about our secure messaging platform." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white">
  <!-- Navigation -->
  <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <div class="text-[#D4AF37] text-3xl mr-3">
            <i class="fas fa-comment-dollar"></i>
          </div>
          <span class="font-bold text-2xl bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </a>
      </div>
      <div class="flex space-x-6">
        <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
        <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="pt-24 pb-16 px-6">
    <div class="container mx-auto max-w-4xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Contact Us
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Have questions about BoGuani? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- Contact Form -->
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30">
          <h2 class="text-2xl font-bold mb-6 text-[#F2D675]">Send us a Message</h2>
          
          {#if isSubmitted}
            <div class="text-center py-8">
              <i class="fas fa-check-circle text-6xl text-[#D4AF37] mb-4"></i>
              <h3 class="text-2xl font-bold mb-2 text-[#F2D675]">Message Sent!</h3>
              <p class="text-gray-300">Thank you for contacting us. We'll get back to you soon.</p>
            </div>
          {:else}
            <form on:submit|preventDefault={handleSubmit} class="space-y-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Name</label>
                <input
                  type="text"
                  id="name"
                  bind:value={name}
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                  placeholder="Your full name"
                />
              </div>
              
              <div>
                <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <input
                  type="email"
                  id="email"
                  bind:value={email}
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                <input
                  type="text"
                  id="subject"
                  bind:value={subject}
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                  placeholder="What's this about?"
                />
              </div>
              
              <div>
                <label for="message" class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                <textarea
                  id="message"
                  bind:value={message}
                  required
                  rows="5"
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37] resize-none"
                  placeholder="Tell us more about your inquiry..."
                ></textarea>
              </div>
              
              <button
                type="submit"
                disabled={isSubmitting}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if isSubmitting}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Sending...
                {:else}
                  <i class="fas fa-paper-plane mr-2"></i>
                  Send Message
                {/if}
              </button>
            </form>
          {/if}
        </div>

        <!-- Contact Information -->
        <div class="space-y-8">
          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30">
            <div class="flex items-center mb-4">
              <i class="fas fa-envelope text-2xl text-[#D4AF37] mr-4"></i>
              <div>
                <h3 class="text-lg font-bold text-[#F2D675]">Email</h3>
                <p class="text-gray-300"><EMAIL></p>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30">
            <div class="flex items-center mb-4">
              <i class="fas fa-clock text-2xl text-[#D4AF37] mr-4"></i>
              <div>
                <h3 class="text-lg font-bold text-[#F2D675]">Response Time</h3>
                <p class="text-gray-300">Within 24 hours</p>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30">
            <div class="flex items-center mb-4">
              <i class="fas fa-headset text-2xl text-[#D4AF37] mr-4"></i>
              <div>
                <h3 class="text-lg font-bold text-[#F2D675]">Support</h3>
                <p class="text-gray-300">24/7 Global Support</p>
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30">
            <h3 class="text-lg font-bold text-[#F2D675] mb-4">Follow Us</h3>
            <div class="flex space-x-4">
              <a href="https://twitter.com/boguani" class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all">
                <i class="fab fa-twitter text-[#D4AF37]"></i>
              </a>
              <a href="https://facebook.com/boguani" class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all">
                <i class="fab fa-facebook-f text-[#D4AF37]"></i>
              </a>
              <a href="https://instagram.com/boguani" class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all">
                <i class="fab fa-instagram text-[#D4AF37]"></i>
              </a>
              <a href="https://linkedin.com/company/boguani" class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all">
                <i class="fab fa-linkedin-in text-[#D4AF37]"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
