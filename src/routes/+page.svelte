<script lang="ts">
  import { goto } from '$app/navigation';
  import Hero from '$lib/components/Hero.svelte';
  import Features from '$lib/components/Features.svelte';
  import Security from '$lib/components/Security.svelte';
  import Pricing from '$lib/components/Pricing.svelte';
  import Footer from '$lib/components/Footer.svelte';

  function openWebChat() {
    goto('/auth');
  }
</script>

<!-- Home Page -->
<div class="min-h-screen bg-gradient-main">
  <Hero on:openWebChat={openWebChat} />
  <Features />
  <Security />
  <Pricing />
  <Footer />
</div>
