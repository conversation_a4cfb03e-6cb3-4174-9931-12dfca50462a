<script lang="ts">
  import { goto } from '$app/navigation';
  import Hero from '$lib/components/Hero.svelte';
  import Features from '$lib/components/Features.svelte';
  import EpicStats from '$lib/components/EpicStats.svelte';
  import EpicTestimonials from '$lib/components/EpicTestimonials.svelte';
  import Demo from '$lib/components/Demo.svelte';
  import Download from '$lib/components/Download.svelte';
  import Security from '$lib/components/Security.svelte';
  import Pricing from '$lib/components/Pricing.svelte';
  import Footer from '$lib/components/Footer.svelte';

  function openWebChat() {
    goto('/auth');
  }
</script>

<svelte:head>
  <title>BoGuani - Messenger of Value | Sacred Communication & Value Exchange</title>
  <meta name="description" content="BoGuani transforms communication into value exchange. Inspired by ancient Taíno wisdom, experience secure messaging with instant money transfers. Speak Gold. Share Value." />
  <meta name="keywords" content="<PERSON><PERSON><PERSON><PERSON>, messenger of value, secure messaging, money transfer, Taíno wisdom, encrypted chat, value exchange" />
  <meta property="og:title" content="BoGuani - Messenger of Value" />
  <meta property="og:description" content="Where ancient wisdom meets modern communication. Send messages that matter, share moments that count, and transfer value instantly." />
  <meta property="og:type" content="website" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="BoGuani - Messenger of Value" />
  <meta name="twitter:description" content="Sacred communication platform where every message carries meaning and worth." />
</svelte:head>

<!-- Home Page -->
<div class="min-h-screen bg-gradient-main">
  <Hero on:openWebChat={openWebChat} />
  <Features />
  <EpicStats />
  <EpicTestimonials />
  <Demo />
  <Download />
  <Security />
  <Pricing />
  <Footer />
</div>
