<script lang="ts">
  import { onMount } from 'svelte';

  onMount(() => {
    // Redirect to the static home page
    window.location.href = '/home.html';
  });
</script>

<svelte:head>
  <title>BoGuani - Messenger of Value</title>
  <meta name="description" content="Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange. Send messages that matter, share moments that count, and transfer value instantly - all protected by sacred-level encryption." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#2E003E] via-[#4A1A5C] to-[#2E003E] text-white flex items-center justify-center">
  <div class="text-center">
    <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-[#D4AF37] mx-auto mb-4"></div>
    <p class="text-xl text-gray-300">Redirecting to BoGuani homepage...</p>
  </div>
</div>