<script lang="ts">
  import { onMount } from 'svelte';
  import ChatBubble from '$lib/components/ChatBubble.svelte';
  import MessageInput from '$lib/components/MessageInput.svelte';
  import BankTransferModal from '$lib/components/BankTransferModal.svelte';
  import { websocket, type Message, type Chat } from '$lib/websocket.js';
  import type { BankAccount } from '$lib/plaid.js';

  // Mock data for development
  let currentUser = {
    id: 'user1',
    name: 'You',
    handle: 'you',
    avatar: ''
  };

  let selectedChat: Chat | null = null;
  let messages: Message[] = [
    {
      id: 'msg1',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Hey there! How\'s it going?',
      type: 'text',
      timestamp: new Date(Date.now() - 300000),
      status: 'read'
    },
    {
      id: 'msg2',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Hi! I\'m good, thanks. Just working on some projects.',
      type: 'text',
      timestamp: new Date(Date.now() - 240000),
      status: 'read'
    },
    {
      id: 'msg3',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Cool! Hey, can you send me $20 for lunch? I forgot my wallet.',
      type: 'text',
      timestamp: new Date(Date.now() - 180000),
      status: 'read'
    },
    {
      id: 'msg4',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Sure, no problem. I\'ll send it right away.',
      type: 'text',
      timestamp: new Date(Date.now() - 120000),
      status: 'read'
    },
    {
      id: 'msg5',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Payment sent',
      type: 'payment',
      timestamp: new Date(Date.now() - 60000),
      status: 'read',
      paymentData: {
        amount: 20.00,
        status: 'completed',
        transactionId: 'txn_123'
      }
    },
    {
      id: 'msg6',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Thanks a lot! Got it. 👍',
      type: 'text',
      timestamp: new Date(Date.now() - 30000),
      status: 'read'
    }
  ];

  let chats: Chat[] = [
    {
      id: 'chat1',
      participants: ['user1', 'user2'],
      lastMessage: messages[messages.length - 1],
      unreadCount: 0,
      isTyping: false,
      typingUsers: []
    },
    {
      id: 'chat2',
      participants: ['user1', 'user3'],
      lastMessage: {
        id: 'msg_old',
        chatId: 'chat2',
        senderId: 'user3',
        recipientId: 'user1',
        content: 'Thanks for the payment!',
        type: 'text',
        timestamp: new Date(Date.now() - ********),
        status: 'read'
      },
      unreadCount: 0,
      isTyping: false,
      typingUsers: []
    }
  ];

  let users = [
    { id: 'user2', name: 'John Doe', handle: 'johndoe', isOnline: true },
    { id: 'user3', name: 'Alice Smith', handle: 'alicesmith', isOnline: false },
    { id: 'user4', name: 'Robert Johnson', handle: 'robertj', isOnline: false },
    { id: 'user5', name: 'Emma Wilson', handle: 'emmaw', isOnline: false }
  ];

  let bankAccounts: BankAccount[] = [
    {
      id: 'acc1',
      name: 'Chase Checking',
      mask: '4567',
      type: 'depository',
      subtype: 'checking',
      balance: 1245.67,
      isConnected: true
    }
  ];

  let showPaymentModal = false;
  let showPlaidModal = false;
  let searchQuery = '';
  let isTyping = false;

  // Set default selected chat
  selectedChat = chats[0];

  function selectChat(chat: Chat) {
    selectedChat = chat;
    // Mark messages as read
    messages.forEach(msg => {
      if (msg.chatId === chat.id && msg.senderId !== currentUser.id) {
        msg.status = 'read';
      }
    });
  }

  function handleSendMessage(event: CustomEvent<{ content: string; type: 'text' | 'payment' }>) {
    if (!selectedChat) return;

    const { content, type } = event.detail;

    if (type === 'payment') {
      // Extract amount from message
      const amountMatch = content.match(/\$(\d+(?:\.\d{2})?)/);
      if (amountMatch) {
        const amount = parseFloat(amountMatch[1]);
        showPaymentModal = true;
        // You would set the default amount here
      }
    } else {
      const newMessage: Message = {
        id: `msg_${Date.now()}`,
        chatId: selectedChat.id,
        senderId: currentUser.id,
        recipientId: selectedChat.participants.find(p => p !== currentUser.id) || '',
        content,
        type,
        timestamp: new Date(),
        status: 'sent'
      };

      messages = [...messages, newMessage];

      // Update chat's last message
      selectedChat.lastMessage = newMessage;
      chats = chats.map(c => c.id === selectedChat?.id ? selectedChat : c);

      // Scroll to bottom
      setTimeout(() => {
        const messagesArea = document.getElementById('messagesArea');
        if (messagesArea) {
          messagesArea.scrollTop = messagesArea.scrollHeight;
        }
      }, 100);
    }
  }

  function handleTyping(event: CustomEvent<{ isTyping: boolean }>) {
    isTyping = event.detail.isTyping;
    // In a real app, you'd send this to the WebSocket server
  }

  function handleTransfer(event: CustomEvent<{ amount: number; accountId: string; note: string }>) {
    if (!selectedChat) return;

    const { amount, note } = event.detail;

    const paymentMessage: Message = {
      id: `msg_${Date.now()}`,
      chatId: selectedChat.id,
      senderId: currentUser.id,
      recipientId: selectedChat.participants.find(p => p !== currentUser.id) || '',
      content: `Payment sent: $${amount.toFixed(2)}${note ? ` - ${note}` : ''}`,
      type: 'payment',
      timestamp: new Date(),
      status: 'sent',
      paymentData: {
        amount,
        status: 'pending'
      }
    };

    messages = [...messages, paymentMessage];

    // Simulate payment processing
    setTimeout(() => {
      paymentMessage.paymentData!.status = 'completed';
      paymentMessage.status = 'delivered';
      messages = [...messages];
    }, 2000);

    showPaymentModal = false;

    // Update chat's last message
    selectedChat.lastMessage = paymentMessage;
    chats = chats.map(c => c.id === selectedChat?.id ? selectedChat : c);
  }

  function getCurrentUser(userId: string) {
    return users.find(u => u.id === userId) || { name: 'Unknown', handle: 'unknown' };
  }

  function formatTime(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();

    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < ********) return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
    if (diff < 604800000) return date.toLocaleDateString('en-US', { weekday: 'short' });
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  $: filteredChats = chats.filter(chat => {
    if (!searchQuery) return true;
    const otherUserId = chat.participants.find(p => p !== currentUser.id);
    const otherUser = getCurrentUser(otherUserId || '');
    return otherUser.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           otherUser.handle.toLowerCase().includes(searchQuery.toLowerCase());
  });

  $: chatMessages = messages.filter(msg => msg.chatId === selectedChat?.id);

  onMount(() => {
    // Scroll to bottom on mount
    setTimeout(() => {
      const messagesArea = document.getElementById('messagesArea');
      if (messagesArea) {
        messagesArea.scrollTop = messagesArea.scrollHeight;
      }
    }, 100);
  });
</script>

<div class="flex h-screen bg-dark-dark text-gray-200">
  <!-- Sidebar -->
  <div
    class="w-1/4 glass-effect flex flex-col h-full border-r border-gray-700 animate-slide-up"
  >
    <!-- App Header -->
    <div class="p-4 bg-primary-dark bg-opacity-90 text-white flex justify-between items-center border-b border-gray-700">
      <h1 class="text-xl font-semibold flex items-center">
        <span class="text-gold-light mr-2">Nexus</span>Pay
      </h1>
      <div class="flex space-x-3">
        <button class="focus:outline-none hover:text-gold-light transition-colors duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>
        <button class="focus:outline-none hover:text-gold-light transition-colors duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
          </svg>
        </button>
      </div>
    </div>

    <!-- Search -->
    <div class="p-3 bg-dark-DEFAULT bg-opacity-70">
      <div class="relative">
        <input
          type="text"
          bind:value={searchQuery}
          placeholder="Search or start new chat"
          class="w-full py-2 px-4 bg-dark-light bg-opacity-50 rounded-full pl-10 focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400 transition-all duration-200"
        />
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
    </div>

    <!-- Chat List -->
    <div class="flex-1 overflow-y-auto scrollbar-hide">
      {#each filteredChats as chat, index}
        {@const otherUserId = chat.participants.find(p => p !== currentUser.id)}
        {@const otherUser = getCurrentUser(otherUserId || '')}
        <button
          class="w-full flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer transition-colors duration-200 {selectedChat?.id === chat.id ? 'bg-dark-light bg-opacity-30' : ''} animate-fade-in"
          on:click={() => selectChat(chat)}
        >
          <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
            {otherUser.name.charAt(0).toUpperCase()}
          </div>
          <div class="flex-1 text-left">
            <div class="flex justify-between items-center">
              <h3 class="font-semibold">{otherUser.name}</h3>
              <span class="text-xs text-gray-400">
                {chat.lastMessage ? formatTime(chat.lastMessage.timestamp) : ''}
              </span>
            </div>
            <p class="text-sm text-gray-400 truncate">
              {chat.lastMessage?.type === 'payment' ?
                `💰 ${chat.lastMessage.content}` :
                chat.lastMessage?.content || 'No messages yet'
              }
            </p>
          </div>
          {#if chat.unreadCount > 0}
            <div class="w-5 h-5 bg-primary rounded-full flex items-center justify-center text-xs text-white">
              {chat.unreadCount}
            </div>
          {/if}
        </button>
      {/each}
    </div>

    <!-- Banking Section -->
    <div class="p-4 glass-effect border-t border-gray-700">
      <h2 class="text-lg font-semibold mb-2 flex items-center">
        <span class="text-gold-light">Banking</span>
        <span class="ml-1 text-xs bg-primary-light px-1.5 py-0.5 rounded-full">Premium</span>
      </h2>
      <div class="flex justify-between items-center">
        <div>
          <p class="text-sm opacity-70">Balance</p>
          <p class="font-bold text-lg text-gold-light">
            ${bankAccounts.length > 0 ? bankAccounts[0].balance.toFixed(2) : '0.00'}
          </p>
        </div>
        <button
          class="bg-primary bg-opacity-80 hover:bg-primary-dark text-white px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center"
          on:click={() => showPlaidModal = true}
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          {bankAccounts.length > 0 ? 'Manage' : 'Connect Bank'}
        </button>
      </div>
    </div>
  </div>

  <!-- Chat Area -->
  <div class="w-3/4 flex flex-col h-full">
    {#if selectedChat}
      {@const otherUserId = selectedChat.participants.find(p => p !== currentUser.id)}
      {@const otherUser = getCurrentUser(otherUserId || '')}

      <!-- Chat Header -->
      <div
        class="p-3 glass-effect border-b border-gray-700 flex justify-between items-center animate-slide-up"
      >
        <div class="flex items-center">
          <div class="w-10 h-10 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
            {otherUser.name.charAt(0).toUpperCase()}
          </div>
          <div>
            <h2 class="font-semibold">{otherUser.name}</h2>
            <p class="text-xs text-gray-400 flex items-center">
              <span class="w-2 h-2 {otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full mr-1.5"></span>
              {otherUser.isOnline ? 'Online' : 'Offline'}
            </p>
          </div>
        </div>
        <div class="flex space-x-4">
          <button class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
          </button>
          <button class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
          <button
            class="focus:outline-none text-gold-light hover:text-gold transition-colors duration-200"
            on:click={() => showPaymentModal = true}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </button>
          <button class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Messages Area -->
      <div
        id="messagesArea"
        class="flex-1 p-4 overflow-y-auto bg-gradient-to-b from-dark-DEFAULT to-dark-dark scrollbar-hide animate-fade-in"
      >
        <!-- Day Divider -->
        <div class="flex justify-center mb-4">
          <span class="bg-dark-light bg-opacity-50 text-gray-300 text-xs px-3 py-1 rounded-full">Today</span>
        </div>

        <!-- Messages -->
        {#each chatMessages as message}
          <ChatBubble
            {message}
            isOwn={message.senderId === currentUser.id}
            senderName={message.senderId === currentUser.id ? currentUser.name : otherUser.name}
          />
        {/each}

        <!-- Typing Indicator -->
        {#if isTyping}
          <div
            class="flex mb-4 animate-fade-in"
          >
            <div class="chat-bubble received p-3 shadow-lg">
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
              </div>
            </div>
          </div>
        {/if}
      </div>

      <!-- Message Input -->
      <MessageInput
        on:send={handleSendMessage}
        on:typing={handleTyping}
        placeholder="Type a message"
      />
    {:else}
      <!-- No Chat Selected -->
      <div class="flex-1 flex items-center justify-center bg-gradient-to-b from-dark-DEFAULT to-dark-dark">
        <div class="text-center">
          <div class="w-24 h-24 mx-auto mb-4 rounded-full purple-gradient flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h3 class="text-xl font-semibold text-white mb-2">Welcome to NexusPay</h3>
          <p class="text-gray-400">Select a chat to start messaging and sending money</p>
        </div>
      </div>
    {/if}
  </div>
</div>

<!-- Bank Transfer Modal -->
{#if selectedChat}
  {@const otherUserId = selectedChat.participants.find(p => p !== currentUser.id)}
  {@const otherUser = getCurrentUser(otherUserId || '')}

  <BankTransferModal
    isOpen={showPaymentModal}
    recipientName={otherUser.name}
    recipientHandle={otherUser.handle}
    accounts={bankAccounts}
    on:close={() => showPaymentModal = false}
    on:transfer={handleTransfer}
  />
{/if}

<!-- Plaid Connection Modal -->
{#if showPlaidModal}
  <div
    class="modal-backdrop fixed inset-0 flex items-center justify-center z-50 animate-fade-in"
    on:click={() => showPlaidModal = false}
  >
    <div
      class="glass-effect rounded-xl shadow-2xl w-full max-w-md mx-4 border border-gray-700 animate-bounce-in"
      on:click|stopPropagation
    >
      <div class="p-5 border-b border-gray-700">
        <div class="flex justify-between items-center">
          <h3 class="text-xl font-semibold text-white flex items-center">
            <span class="text-gold-light mr-2">Connect</span> Your Bank
          </h3>
          <button
            class="text-gray-400 hover:text-white focus:outline-none transition-colors duration-200"
            on:click={() => showPlaidModal = false}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      <div class="p-5">
        <p class="text-gray-300 mb-4">Connect your bank account securely with Plaid to enable money transfers within chats.</p>

        <div class="space-y-3 mb-5">
          <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition-colors duration-200 border border-gray-700">
            <div class="w-10 h-10 bg-primary-light bg-opacity-30 rounded-full flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
            </div>
            <div>
              <h4 class="font-medium text-white">Chase</h4>
              <p class="text-xs text-gray-400">Personal Banking</p>
            </div>
          </div>

          <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition-colors duration-200 border border-gray-700">
            <div class="w-10 h-10 bg-red-900 bg-opacity-30 rounded-full flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
            </div>
            <div>
              <h4 class="font-medium text-white">Bank of America</h4>
              <p class="text-xs text-gray-400">Personal Banking</p>
            </div>
          </div>

          <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition-colors duration-200 border border-gray-700">
            <div class="w-10 h-10 bg-primary-dark bg-opacity-30 rounded-full flex items-center justify-center mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
            </div>
            <div>
              <h4 class="font-medium text-white">Wells Fargo</h4>
              <p class="text-xs text-gray-400">Personal Banking</p>
            </div>
          </div>
        </div>

        <div class="text-center">
          <button class="text-primary-light hover:text-primary hover:underline focus:outline-none transition-colors duration-200">
            Search for more banks
          </button>
        </div>
      </div>
      <div class="p-4 bg-dark-DEFAULT bg-opacity-70 rounded-b-xl border-t border-gray-700">
        <div class="flex justify-between items-center">
          <p class="text-xs text-gray-400">Secured by Plaid</p>
          <button
            class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg focus:outline-none transition-colors duration-200"
            on:click={() => {
              showPlaidModal = false;
              // In a real app, this would trigger Plaid Link
              console.log('Connecting to Plaid...');
            }}
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  </div>
{/if}
