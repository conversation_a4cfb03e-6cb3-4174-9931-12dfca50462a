<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import EnhancedChatBubble from '$lib/components/EnhancedChatBubble.svelte';
  import MessageInput from '$lib/components/MessageInput.svelte';
  import BankTransferModal from '$lib/components/BankTransferModal.svelte';
  import TainoIcons from '$lib/components/TainoIcons.svelte';
  import CallModal from '$lib/components/CallModal.svelte';
  import IncomingCallModal from '$lib/components/IncomingCallModal.svelte';
  import CallButtons from '$lib/components/CallButtons.svelte';
  import PaymentModal from '$lib/components/PaymentModal.svelte';
  import ContactImportModal from '$lib/components/ContactImportModal.svelte';
  import ManageButton from '$lib/components/ManageButton.svelte';

  // Import new services
  import { messagingService, chatRooms, messages, currentUser, activeChat, activeChatMessages } from '$lib/stores/messaging';
  import { plaidService, bankAccounts } from '$lib/services/plaid';

  // Import types from services
  import type { Message as MessageType, ChatRoom, Contact } from '$lib/stores/messaging';
  import type { BankAccount } from '$lib/services/plaid';
  
  // Re-export and extend types for local use
  type LocalMessage = Message;
  
  // Define Chat type that extends ChatRoom with additional properties
  interface Chat extends ChatRoom {
    isTyping: boolean;
    typingUsers: string[];
  }
  
  // Define a complete BankAccount type that matches our needs
  interface BankAccount {
    id: string;
    name: string;
    institutionName: string;
    accountNumber?: string;
    routingNumber?: string;
    type: 'checking' | 'savings' | 'credit' | 'other';
    subtype?: string;
    mask?: string;
    balance: number;
    currency: string;
    isConnected: boolean;
    isDefault: boolean;
    plaidAccountId?: string;
    plaidAccessToken?: string;
  }

  // Define local types
  interface Message extends Omit<MessageType, 'paymentData'> {
    paymentData?: {
      amount: number;
      status: 'pending' | 'completed' | 'failed';
      transactionId?: string;
    };
  }
  
  // Helper function to safely access store values
  function getStoreValue<T>(store: { subscribe: (run: (value: T) => void) => () => void }): T {
    let value: T;
    store.subscribe(v => value = v)();
    return value!;
  }
  
  // Process bank accounts to ensure they have all required fields
  function processBankAccounts(accounts: any[]): BankAccount[] {
    if (!Array.isArray(accounts)) return [];
    
    return accounts
      .filter(acc => acc && typeof acc === 'object')
      .map(acc => ({
        id: acc.id || '',
        name: acc.name || 'Unnamed Account',
        institutionName: acc.institutionName || 'Unknown Bank',
        accountNumber: acc.accountNumber || '',
        routingNumber: acc.routingNumber || '',
        type: (acc.type || 'checking') as 'checking' | 'savings' | 'credit' | 'other',
        subtype: acc.subtype || '',
        mask: acc.mask || '****',
        balance: typeof acc.balance === 'number' ? acc.balance : 0,
        currency: acc.currency || 'USD',
        isConnected: Boolean(acc.isConnected),
        isDefault: Boolean(acc.isDefault),
        plaidAccountId: acc.plaidAccountId || '',
        plaidAccessToken: acc.plaidAccessToken || ''
      }));
  }
  
  // Process and store bank accounts
  $: {
    bankAccountsList = processBankAccounts($bankAccounts);
  }

  // State variables
  let selectedChat: Chat | null = null;
  let currentChatMessages: Message[] = [];
  
  // Store subscriptions at the top level with proper typing
  $: currentChatMessages = $activeChatMessages as unknown as LocalMessage[];
  
  // Get current user from store with proper typing
  $: currentUserData = $currentUser;
  
  // Bank accounts list is now handled by the processBankAccounts function
  let bankAccountsList: BankAccount[] = [];
  
  // Get active chat participants
  $: activeChatParticipants = (typeof $activeChat === 'object' && $activeChat?.participants) || [];
  
  // Find other user in the active chat
  $: otherUser = Array.isArray(activeChatParticipants)
    ? activeChatParticipants.find((p: Contact) => p.id !== currentUserData?.id) || null
    : null;
  let showSettings = false;
  let showPaymentModal = false;
  let showEnhancedPaymentModal = false;
  let showContactImportModal = false;
  let showPlaidModal = false;
  let searchQuery = '';
  let isTyping = false;

  // Settings state
  let showEditProfile = false;
  let showChangePassword = false;
  let showActiveSessions = false;
  let showStorageUsage = false;
  let showExportData = false;
  let showAddBankAccount = false;

  // Settings toggles
  let readReceipts = true;
  let lastSeen = false;
  let profilePhoto = true;
  let messageNotifications = true;
  let paymentNotifications = true;
  let soundEnabled = true;
  let darkMode = true;
  let autoDownloadMedia = false;

  // Profile editing
  let editingName = '';
  let editingHandle = '';

  // Password change
  let currentPassword = '';
  let newPassword = '';
  let confirmPassword = '';

  // Bank account form
  let newBankAccountName = '';
  let newBankAccountNumber = '';
  let newBankRoutingNumber = '';

  // Initialize with store data
  let chats: Chat[] = [];
  let users: Contact[] = [];
  
  // Initialize from stores at the top level with proper typing
  $: {
    const rooms = $chatRooms as unknown as ChatRoom[];
    chats = rooms.map(room => ({
      ...room,
      isTyping: room.participants.some((p: Contact) => p.isTyping),
      typingUsers: room.participants
        .filter((p: Contact) => p.isTyping)
        .map((p: Contact) => p.id)
    }));
    
    users = Array.from(
      new Map(
        rooms.flatMap((room: ChatRoom) => 
          room.participants.map((p: Contact) => [p.id, p] as const)
        )
      ).values()
    );
    
    // Set first chat as selected if none selected
    if (rooms.length > 0 && !selectedChat) {
      selectChat(rooms[0].id);
    }
  }
  

  // Function to select a chat
  function selectChat(chat: Chat | string) {
    const chatId = typeof chat === 'string' ? chat : chat.id;
    const foundChat = chats.find((c: Chat) => c.id === chatId);
    if (foundChat) {
      selectedChat = foundChat;
      // Mark messages as read when selecting a chat
      if (currentUser) {
        messagingService.markMessagesAsRead(chatId, currentUser.id);
      }
    }
  }

  async function handleSendMessage(event: CustomEvent<{ content: string; type: 'text' | 'payment' }>) {
    if (!selectedChat || !currentUser) return;
    
    const { content, type } = event.detail;
    const message: Message = {
      id: generateId(),
      chatId: selectedChat.id,
      senderId: currentUser.id,
      content,
      type,
      timestamp: new Date().toISOString(),
      status: 'sending',
      isRead: false
    };
    
    // Add to local state immediately for optimistic UI
    messagingService.addMessage(message);
    
    try {
      // Send message via WebSocket or HTTP
      await messagingService.sendMessage(selectedChat.id, content, type);
      
      // Update message status
      messagingService.updateMessage(selectedChat.id, message.id, { status: 'sent' });
    } catch (error) {
      console.error('Failed to send message:', error);
      messagingService.updateMessage(selectedChat.id, message.id, { status: 'failed' });
      // Show error to user
      alert('Failed to send message. Please try again.');
    }
  }

  function handleTyping(event: CustomEvent<{ isTyping: boolean }>) {
    isTyping = event.detail.isTyping;
    // In a real app, you'd send this to the WebSocket server
  }

  // Handle manage button actions
  function handleManageAction(event: CustomEvent<{ action: string; data?: any }>) {
    const { action, data } = event.detail;

    switch (action) {
      case 'importContacts':
        showContactImportModal = true;
        break;
      case 'newChat':
        console.log('Creating new chat...');
        break;
      case 'createGroup':
        console.log('Creating group chat...');
        break;
      case 'connectBank':
        plaidService.openPlaidLink();
        break;
      case 'manageBanking':
        showSettings = true;
        break;
      case 'transactionHistory':
        showSettings = true;
        break;
      case 'settings':
        showSettings = true;
        break;
      case 'sendMoney':
        showEnhancedPaymentModal = true;
        break;
      case 'requestMoney':
        console.log('Requesting money...');
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  // Handle enhanced payment modal
  function handleEnhancedPayment(event: CustomEvent<any>) {
    const { type, amount, recipients, note } = event.detail;
    console.log('💰 Enhanced payment:', event.detail);

    const paymentContent = type === 'group'
      ? `Split payment: $${amount} among ${recipients.length} people`
      : `Payment sent: $${amount}`;

    if (selectedChat) {
      messagingService.sendMessage(selectedChat.id, paymentContent, 'payment');
    }
  }

  // Handle contact import results
  function handleContactImport(event: CustomEvent<{ chatId: string; contact: any }>) {
    const { chatId, contact } = event.detail;
    console.log('📱 New chat created:', chatId, contact);
  }

  async function handleTransfer(event: CustomEvent<{ amount: number; accountId: string; note: string }>) {
    if (!selectedChat || !$currentUser) return;

    const { amount, note, accountId } = event.detail;

    try {
      // Get recipient phone number (in a real app, you'd get this from the chat participants)
      const recipientPhone = selectedChat.participants.find(p => p !== $currentUser.id) || '';

      // Send payment using Plaid service
      const transaction = await plaidService.sendMoney(recipientPhone, amount, note, accountId);

      // Send payment message through messaging service
      const paymentContent = `Payment sent: $${amount.toFixed(2)}${note ? ` - ${note}` : ''}`;
      await messagingService.sendMessage(selectedChat.id, paymentContent, 'payment');

      console.log('💰 Payment sent successfully:', transaction);
      showPaymentModal = false;

    } catch (error) {
      console.error('Payment failed:', error);
      alert('Payment failed. Please try again.');
    }
  }

  function getCurrentUser(userId: string): Contact {
    const user = users.find(user => user.id === userId);
    if (!user) {
      return {
        id: userId,
        name: 'Unknown User',
        phoneNumber: '',
        isOnline: false
      };
    }
    return user;
  }

  // Format time as HH:MM AM/PM
  function formatTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
      console.error('Error formatting time:', e);
      return '';
    }
  }

  // Format relative time (e.g., '2m ago', 'Today', 'Yesterday')
  function formatRelativeTime(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return ''; // Handle invalid dates
      
      const now = new Date();
      const diff = now.getTime() - date.getTime();

      if (diff < 60000) return 'now';
      if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
      if (diff < 86400000) return 'Today';
      if (diff < 172800000) return 'Yesterday';
      if (diff < 604800000) return date.toLocaleDateString('en-US', { weekday: 'short' });
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    } catch (e) {
      console.error('Error formatting relative time:', e);
      return '';
    }
  }

  function logout() {
    localStorage.removeItem('boguani_token');
    localStorage.removeItem('boguani_user');
    localStorage.removeItem('boguani_login_time');
    console.log('👋 User logged out');
    goto('/');
  }

  $: filteredChats = chats.filter((chat: Chat) => {
    if (!searchQuery) return true;
    
    // Find the other participant in the chat
    const otherParticipant = chat.participants
      .map((p: string | Contact) => typeof p === 'string' ? p : p.id)
      .find((pId: string) => pId !== currentUserStore?.id);
    
    if (!otherParticipant) return false;
    
    // Find the user details
    const otherUser = users.find((u: Contact) => u.id === otherParticipant);
    if (!otherUser) return false;
    
    // Check for matches in name or handle
    const searchTerm = searchQuery.toLowerCase();
    const nameMatch = otherUser.name?.toLowerCase().includes(searchTerm) || false;
    const handleMatch = otherUser.handle?.toLowerCase().includes(searchTerm) || false;
    
    return nameMatch || handleMatch;
  });

  // Use the new messaging store for chat messages
  $: chatMessages = $activeChatMessages;

  // Settings Functions
  function openEditProfile() {
    if (!currentUserData) {
      // Handle case when user is not logged in
      goto('/login');
      return;
    }
    editingName = currentUserData.name || '';
    editingHandle = currentUserData.handle || '';
    showEditProfile = true;
  }

  function saveProfile() {
    if (!editingName.trim() || !editingHandle.trim()) {
      alert('Please fill in all fields');
      return;
    }

    if (!/^[a-zA-Z0-9_]{3,20}$/.test(editingHandle)) {
      alert('Handle must be 3-20 characters, letters, numbers, and underscores only');
      return;
    }

    // Update current user
    if (currentUserData) {
      currentUserData.name = editingName;
      currentUserData.handle = editingHandle;
      localStorage.setItem('boguani_user', JSON.stringify(currentUser));
    }

    showEditProfile = false;
    alert('Profile updated successfully!');
  }

  function openChangePassword() {
    currentPassword = '';
    newPassword = '';
    confirmPassword = '';
    showChangePassword = true;
  }

  function savePassword() {
    if (!currentPassword || !newPassword || !confirmPassword) {
      alert('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      alert('New passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      alert('Password must be at least 8 characters long');
      return;
    }

    // In a real app, you'd verify current password and update
    showChangePassword = false;
    alert('Password changed successfully!');
  }

  function openActiveSessions() {
    showActiveSessions = true;
  }

  function openStorageUsage() {
    showStorageUsage = true;
  }

  function exportData() {
    showExportData = true;
    // Simulate data export
    setTimeout(() => {
      if (!currentUserData) {
        // Handle case when user is not logged in
        goto('/login');
        return;
      }
      const data = {
        user: currentUserData,
        messages: $activeChatMessages,
        chats: chats,
        exportDate: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `boguani-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showExportData = false;
      alert('Data exported successfully!');
    }, 2000);
  }

  function openAddBankAccount() {
    newBankAccountName = '';
    newBankAccountNumber = '';
    newBankRoutingNumber = '';
    showAddBankAccount = true;
  }

  function saveBankAccount() {
    if (!newBankAccountName || !newBankAccountNumber || !newBankRoutingNumber) {
      alert('Please fill in all fields');
      return;
    }

    const newAccount: BankAccount = {
      id: `acc_${Date.now()}`,
      name: newBankAccountName,
      mask: newBankAccountNumber.slice(-4),
      type: 'checking',
      accountType: 'checking',
      balance: 0,
      isConnected: true,
      isVerified: false
    };

    bankAccounts.update(accounts => [...accounts, newAccount]);
    showAddBankAccount = false;
    alert('Bank account added successfully!');
  }

  function removeBankAccount(accountId: string) {
    if (confirm('Are you sure you want to remove this bank account?')) {
      bankAccounts.update(accounts => accounts.filter(acc => acc.id !== accountId));
      alert('Bank account removed successfully!');
    }
  }

  function toggleSetting(setting: string) {
    switch (setting) {
      case 'readReceipts':
        readReceipts = !readReceipts;
        break;
      case 'lastSeen':
        lastSeen = !lastSeen;
        break;
      case 'profilePhoto':
        profilePhoto = !profilePhoto;
        break;
      case 'messageNotifications':
        messageNotifications = !messageNotifications;
        break;
      case 'paymentNotifications':
        paymentNotifications = !paymentNotifications;
        break;
      case 'soundEnabled':
        soundEnabled = !soundEnabled;
        break;
      case 'darkMode':
        darkMode = !darkMode;
        break;
      case 'autoDownloadMedia':
        autoDownloadMedia = !autoDownloadMedia;
        break;
    }

    // Save settings to localStorage
    const settings = {
      readReceipts,
      lastSeen,
      profilePhoto,
      messageNotifications,
      paymentNotifications,
      soundEnabled,
      darkMode,
      autoDownloadMedia
    };
    localStorage.setItem('boguani_settings', JSON.stringify(settings));
  }

  // Call and Video Functions
  function startVoiceCall() {
    if (!selectedChat) return;
    const otherUserId = selectedChat.participants.find(p => p !== $currentUser?.id);
    const otherUser = getCurrentUser(otherUserId || '');
    alert(`📞 Starting voice call with ${otherUser.name}...\n\nThis would integrate with WebRTC for real calls!`);
  }

  function startVideoCall() {
    if (!selectedChat) return;
    const otherUserId = selectedChat.participants.find(p => p !== $currentUser?.id);
    const otherUser = getCurrentUser(otherUserId || '');
    alert(`📹 Starting video call with ${otherUser.name}...\n\nThis would integrate with WebRTC for real video calls!`);
  }

  function openPlaidManagement() {
    showPlaidModal = true;
  }


  onMount(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('boguani_token');
    const userStr = localStorage.getItem('boguani_user');

    if (!token || !userStr) {
      goto('/auth');
      return;
    }

    try {
      const userData = JSON.parse(userStr);
      userData.id = userData.phoneNumber; // Use phone number as ID
      currentUser.set(userData);
    } catch (err) {
      goto('/auth');
      return;
    }

    // Scroll to bottom on mount
    setTimeout(() => {
      const messagesArea = document.getElementById('messagesArea');
      if (messagesArea) {
        messagesArea.scrollTop = messagesArea.scrollHeight;
      }
    }, 100);
  });

  onMount(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('boguani_token');
    const userStr = localStorage.getItem('boguani_user');
    const loginTime = localStorage.getItem('boguani_login_time');

    // Check if login is still valid (30 days)
    const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
    const isLoginExpired = loginTime && (Date.now() - parseInt(loginTime)) > thirtyDaysInMs;

    if (!token || !userStr || isLoginExpired) {
      // Clear expired data
      localStorage.removeItem('boguani_token');
      localStorage.removeItem('boguani_user');
      localStorage.removeItem('boguani_login_time');
      goto('/auth');
      return;
    }

    try {
      const userData = JSON.parse(userStr);
      userData.id = userData.phoneNumber; // Use phone number as ID
      currentUser.set(userData);
      console.log('✅ User auto-logged in:', userData.name);

      // Initialize demo data
      initializeDemoData();
    } catch (err) {
      localStorage.removeItem('boguani_token');
      localStorage.removeItem('boguani_user');
      localStorage.removeItem('boguani_login_time');
      goto('/auth');
      return;
    }
    
    // Scroll to bottom on mount
    setTimeout(() => {
      const messagesArea = document.getElementById('messagesArea');
      if (messagesArea) {
        messagesArea.scrollTop = messagesArea.scrollHeight;
      }
    }, 100);
  });

  // Initialize demo data for testing
  function initializeDemoData() {
    // Import demo contacts
    messagingService.importPhoneContacts();

    // Create a demo chat room
    setTimeout(async () => {
      const demoContact = {
        id: 'demo_contact_1',
        name: 'Maria Rodriguez',
        phoneNumber: '+1234567891',
        isBoGuaniUser: true,
        isOnline: true
      };

      try {
        const chatId = await messagingService.createChatRoom([demoContact], 'direct');

        // Add some demo messages
        await messagingService.sendMessage(chatId, 'Hey! How are you doing?', 'text');

        setTimeout(() => {
          // Simulate received message
          const receivedMessage = {
            id: `msg_${Date.now()}_received`,
            chatId,
            senderId: demoContact.id,
            senderName: demoContact.name,
            senderPhone: demoContact.phoneNumber,
            content: 'Hi! I\'m good, thanks! Just testing out BoGuani 🎉',
            type: 'text' as const,
            timestamp: new Date().toISOString(),
            status: 'delivered' as const
          };

          // Add to messages store
          messages.update(msgs => ({
            ...msgs,
            [chatId]: [...(msgs[chatId] || []), receivedMessage]
          }));
        }, 2000);
      } catch (error) {
        console.error('Failed to initialize demo data:', error);
      }
    }, 1000);
  }
</script>

<svelte:head>
  <title>Chat - BoGuani</title>
  <meta name="description" content="BoGuani Chat - Secure messaging with instant value transfer" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
      font-family: 'Montserrat', sans-serif;
      min-height: 100vh;
    }

    .hero-gradient {
      background: linear-gradient(135deg,
        rgba(30, 30, 36, 0.95) 0%,
        rgba(45, 27, 78, 0.9) 25%,
        rgba(61, 42, 95, 0.85) 50%,
        rgba(78, 58, 112, 0.8) 75%,
        rgba(45, 27, 78, 0.9) 100%);
    }

    .section-gradient {
      background: linear-gradient(135deg,
        rgba(30, 30, 36, 0.8) 0%,
        rgba(45, 27, 78, 0.7) 50%,
        rgba(61, 42, 95, 0.6) 100%);
    }

    .card-gradient {
      background: linear-gradient(135deg,
        rgba(61, 42, 95, 0.4) 0%,
        rgba(78, 58, 112, 0.3) 50%,
        rgba(45, 27, 78, 0.4) 100%);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(212, 175, 55, 0.2);
    }

    .gold-gradient {
      background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }

    .btn-hover {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn-hover:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
    }

    .hero-pattern {
      background-image:
        radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%);
    }
  </style>
</svelte:head>

{#if $currentUser}
  <div class="hero-pattern flex h-screen text-white">
    <div class="hero-gradient flex h-screen w-full">
      <!-- Sidebar -->
      <div class="w-1/4 card-gradient flex flex-col h-full border-r border-[#D4AF37] border-opacity-20">
      <!-- App Header -->
      <div class="p-6 bg-gradient-to-r from-[#3D2A5F] to-[#4E3A70] bg-opacity-80 backdrop-blur-lg text-white flex justify-between items-center border-b border-[#D4AF37] border-opacity-30">
        <h1 class="text-2xl font-bold flex items-center">
          <div class="w-8 h-8 mr-3 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
            <i class="fas fa-comment-dollar text-sm text-[#2D1B4E]"></i>
          </div>
          <span class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </h1>
        <div class="flex space-x-3">
          <button
            class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
            aria-label="New chat"
          >
            <i class="fas fa-plus text-[#D4AF37]"></i>
          </button>
          <button
            class="w-10 h-10 rounded-full bg-[#D4AF37] bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
            on:click={() => showSettings = !showSettings}
            aria-label="Settings"
          >
            <i class="fas fa-cog text-[#D4AF37]"></i>
          </button>
        </div>
      </div>

      <!-- User Profile -->
      <div class="p-6 bg-gradient-to-r from-[#2D1B4E] to-[#3D2A5F] bg-opacity-60 backdrop-blur-lg border-b border-[#D4AF37] border-opacity-20">
        <div class="flex items-center">
          <div class="w-14 h-14 rounded-full bg-gradient-to-br from-[#D4AF37] to-[#F2D675] flex items-center justify-center text-[#2D1B4E] font-bold text-xl mr-4 shadow-lg">
            {$currentUser?.name?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div class="flex-1">
            <h3 class="font-bold text-lg text-[#F2D675]">{$currentUser?.name || 'User'}</h3>
            <p class="text-sm text-gray-300">@{$currentUser?.phoneNumber || 'user'}</p>
            <p class="text-xs text-[#D4AF37] italic">"Speak Gold. Share Value."</p>
          </div>
          <button
            class="w-10 h-10 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all duration-300"
            on:click={logout}
            aria-label="Logout"
          >
            <i class="fas fa-sign-out-alt text-red-400"></i>
          </button>
        </div>
      </div>
      
      <!-- Search -->
      <div class="p-3 bg-dark-DEFAULT bg-opacity-70">
        <div class="relative">
          <input 
            type="text" 
            bind:value={searchQuery}
            placeholder="Search or start new chat" 
            class="w-full py-2 px-4 bg-dark-light bg-opacity-50 rounded-full pl-10 focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400 transition-all duration-200"
          />
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      
      <!-- Chat List -->
      <div class="flex-1 overflow-y-auto scrollbar-hide">
        {#each filteredChats as chat}
          {@const otherUserId = chat.participants.find(p => p !== $currentUser.id)}
          {@const otherUser = getCurrentUser(otherUserId || '')}
          <button
            class="w-full flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer transition-colors duration-200 {selectedChat?.id === chat.id ? 'bg-dark-light bg-opacity-30' : ''} animate-fade-in"
            on:click={() => selectChat(chat)}
          >
            <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
              {otherUser.name.charAt(0).toUpperCase()}
            </div>
            <div class="flex-1 text-left">
              <div class="flex justify-between items-center">
                <h3 class="font-semibold">{otherUser.name}</h3>
                <span class="text-xs text-gray-400">
                  {chat.lastMessage ? formatTime(chat.lastMessage.timestamp) : ''}
                </span>
              </div>
              <p class="text-sm text-gray-400 truncate">
                {chat.lastMessage?.type === 'payment' ? 
                  `💰 ${chat.lastMessage.content}` : 
                  chat.lastMessage?.content || 'No messages yet'
                }
              </p>
            </div>
            {#if chat.unreadCount > 0}
              <div class="w-5 h-5 bg-primary rounded-full flex items-center justify-center text-xs text-white">
                {chat.unreadCount}
              </div>
            {/if}
          </button>
        {/each}
      </div>
      
      <!-- Banking Section -->
      <div class="p-4 glass-effect border-t border-gray-700">
        <h2 class="text-lg font-semibold mb-2 flex items-center">
          <span class="text-gold-light">Banking</span>
          <span class="ml-1 text-xs bg-primary-light px-1.5 py-0.5 rounded-full">Premium</span>
        </h2>
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm opacity-70">Balance</p>
            <p class="font-bold text-lg text-gold-light">
              ${$bankAccounts.length > 0 ? $bankAccounts[0].balance.toFixed(2) : '0.00'}
            </p>
          </div>
          <button 
            class="bg-primary bg-opacity-80 hover:bg-primary-dark text-white px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center"
            on:click={() => showPlaidModal = true}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {$bankAccounts.length > 0 ? 'Manage' : 'Connect Bank'}
          </button>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="w-3/4 flex flex-col h-full bg-gradient-to-b from-[#2D1B4E] to-[#1E1E24] bg-opacity-40 backdrop-blur-lg relative z-10">
      {#if selectedChat}
        {@const otherUserId = selectedChat.participants.find(p => p !== $currentUser?.id)}
        {@const otherUser = getCurrentUser(otherUserId || '')}

        <!-- Chat Header -->
        <div class="p-6 bg-gradient-to-r from-[#3D2A5F] to-[#4E3A70] bg-opacity-80 backdrop-blur-lg border-b border-[#D4AF37] border-opacity-30 flex justify-between items-center">
          <div class="flex items-center">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-[#D4AF37] to-[#F2D675] flex items-center justify-center text-[#2D1B4E] font-bold text-lg mr-4 shadow-lg">
              {otherUser.name.charAt(0).toUpperCase()}
            </div>
            <div>
              <h2 class="font-bold text-xl text-[#F2D675]">{otherUser.name}</h2>
              <p class="text-sm text-gray-300 flex items-center">
                <span class="w-2 h-2 {otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full mr-2"></span>
                {otherUser.isOnline ? 'Online' : 'Offline'}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <!-- Enhanced Call Buttons -->
            <CallButtons
              participant={{
                id: otherUser.id,
                name: otherUser.name,
                phoneNumber: otherUser.handle
              }}
              size="sm"
            />
            <button
              aria-label="Start audio call"
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
              on:click={startVoiceCall}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <span class="sr-only">Start audio call</span>
            </button>
            <button
              aria-label="Start video call"
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
              on:click={startVideoCall}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <span class="sr-only">Start video call</span>
            </button>
            <button
              aria-label="Send money"
              class="focus:outline-none text-gold-light hover:text-gold transition-colors duration-200"
              on:click={() => showPaymentModal = true}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="sr-only">Send money</span>
            </button>
            <button
              aria-label="Chat settings"
              aria-expanded={showSettings}
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
              on:click={() => showSettings = !showSettings}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span class="sr-only">Chat settings</span>
            </button>
          </div>
        </div>

        <!-- Messages Area -->
        <div
          id="messagesArea"
          class="flex-1 p-4 overflow-y-auto bg-gradient-to-b from-dark-DEFAULT to-dark-dark scrollbar-hide animate-fade-in relative"
        >
          <!-- Taíno Background Elements -->
          <div class="absolute inset-0 pointer-events-none overflow-hidden">
            <div class="absolute top-10 left-10">
              <TainoIcons icon="wave" size="xl" opacity={0.03} color="#D4AF37" />
            </div>
            <div class="absolute top-32 right-16">
              <TainoIcons icon="sun" size="lg" opacity={0.04} color="#B8860B" />
            </div>
            <div class="absolute bottom-20 left-20">
              <TainoIcons icon="spiral" size="md" opacity={0.05} color="#D4AF37" />
            </div>
            <div class="absolute top-1/2 right-8">
              <TainoIcons icon="feather" size="lg" opacity={0.03} color="#B8860B" />
            </div>
            <div class="absolute bottom-40 right-32">
              <TainoIcons icon="bird" size="md" opacity={0.04} color="#D4AF37" />
            </div>
          </div>

          <!-- Day Divider -->
          <div class="flex justify-center mb-4 relative z-10">
            <span class="bg-dark-light bg-opacity-50 text-gray-300 text-xs px-3 py-1 rounded-full">Today</span>
          </div>

          <!-- Messages -->
          {#each currentChatMessages as message}
            <EnhancedChatBubble
              {message}
              isOwn={message.senderId === $currentUser?.id}
              showAvatar={true}
            />
          {/each}

          <!-- Typing Indicator -->
          {#if isTyping}
            <div class="flex mb-4 animate-fade-in">
              <div class="chat-bubble received p-3 shadow-lg">
                <div class="flex space-x-1">
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
              </div>
            </div>
          {/if}
        </div>

        <!-- Message Input -->
        <MessageInput
          on:send={handleSendMessage}
          on:typing={handleTyping}
          on:payment={() => showEnhancedPaymentModal = true}
          placeholder="Type a message"
        />
      {:else}
        <!-- No Chat Selected -->
        <div class="flex-1 flex items-center justify-center bg-gradient-to-b from-dark-DEFAULT to-dark-dark">
          <div class="text-center">
            <div class="w-24 h-24 mx-auto mb-4 rounded-full purple-gradient flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2 font-serif">Welcome to BoGuani</h3>
            <p class="text-gray-400">Select a chat to start exchanging value through sacred communication</p>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Bank Transfer Modal -->
  {#if selectedChat}
    {@const otherUserId = selectedChat.participants.find(p => p !== $currentUser?.id)}
    {@const otherUser = getCurrentUser(otherUserId || '')}

    <BankTransferModal
      isOpen={showPaymentModal}
      recipientName={otherUser.name}
      recipientHandle={otherUser.handle}
      accounts={$bankAccounts}
      on:close={() => showPaymentModal = false}
      on:transfer={handleTransfer}
    />
  {/if}

  <!-- Settings Panel -->
  {#if showSettings}
    <div class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto animate-bounce-in border border-purple-900/50 shadow-2xl shadow-purple-900/20">
        <!-- Settings Header -->
        <div class="p-6 border-b border-purple-900/50 flex justify-between items-center bg-gradient-to-r from-[#2E003E] to-[#3A004E]">
          <h2 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500">Settings</h2>
          <button
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 p-1 rounded-full hover:bg-purple-900/50"
            on:click={() => showSettings = false}
            aria-label="Close settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span class="sr-only">Close settings</span>
          </button>
        </div>

        <!-- Settings Content -->
        <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Profile Settings -->
          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">Profile</h3>

            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <div class="flex items-center mb-6">
                <div class="w-20 h-20 rounded-full bg-gradient-to-br from-purple-600 to-[#2E003E] flex items-center justify-center text-3xl font-bold text-gold-300 border-2 border-gold-500/30 shadow-lg">
                  {$currentUser?.name?.charAt(0).toUpperCase() || 'U'}
                </div>
                <div class="ml-4">
                  <h4 class="font-bold text-lg text-white">{$currentUser?.name || 'User'}</h4>
                  <p class="text-gold-300 font-mono">@{$currentUser?.phoneNumber || 'user'}</p>
                </div>
              </div>
              <button
                class="w-full py-2.5 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-gold/30 flex items-center justify-center gap-2"
                on:click={openEditProfile}
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Profile
              </button>
            </div>

            <!-- Privacy Settings -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                Privacy & Security
              </h4>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Read receipts</span>
                    <span class="text-gray-400 text-sm">Let others see when you've read their messages</span>
                  </div>
                  <button
                    type="button"
                    role="switch"
                    aria-checked={readReceipts}
                    aria-label="Toggle read receipts"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 {readReceipts ? 'bg-gold-500' : 'bg-gray-600'}"
                    on:click={() => toggleSetting('readReceipts')}
                  >
                    <span class="sr-only">Toggle read receipts</span>
                    <span
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out {readReceipts ? 'translate-x-5' : 'translate-x-0'}"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center pt-3 border-t border-purple-900/30">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Last seen</span>
                    <span class="text-gray-400 text-sm">Show when you were last active</span>
                  </div>
                  <button
                    type="button"
                    role="switch"
                    aria-checked={lastSeen}
                    aria-label="Toggle last seen"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 {lastSeen ? 'bg-gold-500' : 'bg-gray-600'}"
                    on:click={() => toggleSetting('lastSeen')}
                  >
                    <span class="sr-only">Toggle last seen</span>
                    <span
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out {lastSeen ? 'translate-x-5' : 'translate-x-0'}"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center pt-3 border-t border-purple-900/30">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Profile visibility</span>
                    <span class="text-gray-400 text-sm">Make your profile visible to others</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle profile visibility"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-gradient-to-r from-gold-500 to-gold-600"
                  >
                    <span class="sr-only">Toggle profile visibility</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-5"
                    ></span>
                  </button>
                </div>
              </div>
            </div>

            <!-- Notifications -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                Notifications
              </h4>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Message notifications</span>
                    <span class="text-gray-400 text-sm">Get notified about new messages</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle message notifications"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-purple-900/50"
                  >
                    <span class="sr-only">Toggle message notifications</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-5"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Payment notifications</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle payment notifications"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle payment notifications</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Sound</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle sound notifications"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle sound notifications</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Security & Banking -->
          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">
              Security & Banking
            </h3>

            <!-- Security -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Security
              </h4>
              <div class="space-y-3">
                <button 
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Two-factor authentication, enabled"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Two-Factor Authentication</p>
                      <p class="text-gold-300 text-sm mt-0.5">Extra layer of security for your account</p>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Active
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </button>
                
                <button
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Change password"
                  on:click={openChangePassword}
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Password</p>
                      <p class="text-gray-400 text-sm mt-0.5">Last changed 3 months ago</p>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
                
                <button
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Login activity"
                  on:click={openActiveSessions}
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Login Activity</p>
                      <p class="text-gray-400 text-sm mt-0.5">View recent account activity</p>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              </div>
            </div>

            <!-- Banking -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <div class="flex justify-between items-center mb-4">
                <h4 class="font-semibold text-white flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3.9 2.7c.5.3 1.1.3 1.6 0L16 7m-9.5 9h9.5m-9.5 0a5.002 5.002 0 01-3.9-6.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Banking & Payments
                </h4>
                <button 
                  class="text-xs font-medium text-gold-400 hover:text-gold-300 flex items-center gap-1 transition-colors"
                  aria-label="View all transactions"
                >
                  View All
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
              
              <div class="space-y-3">
                {#each $bankAccounts as account}
                  <div class="group relative p-4 bg-gradient-to-r from-purple-900/30 to-purple-800/10 rounded-xl border border-purple-900/50 hover:border-gold-500/30 transition-colors">
                    <div class="flex justify-between items-start">
                      <div class="flex items-start gap-3">
                        <div class="mt-0.5 p-2 bg-purple-900/50 rounded-lg border border-purple-800/50">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                          </svg>
                        </div>
                        <div>
                          <h5 class="font-medium text-white">{account.name}</h5>
                          <p class="text-sm text-gray-300 font-mono">•••• {account.id.slice(-4)}</p>
                          <p class="mt-1 text-xs text-gray-400">Primary Account</p>
                        </div>
                      </div>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-900/30 text-green-400">
                        <svg class="-ml-0.5 mr-1 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Connected
                      </span>
                      <div class="absolute top-3 right-3">
                        <button 
                          class="p-1 text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
                          aria-label="More options"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                {/each}
                
                <button
                  class="w-full mt-2 py-3 px-4 bg-gradient-to-r from-purple-900/30 to-purple-800/10 hover:from-purple-900/40 hover:to-purple-800/20 text-gold-400 font-medium rounded-xl border-2 border-dashed border-purple-800/50 hover:border-gold-500/30 transition-all duration-200 flex items-center justify-center gap-2 group"
                  aria-label="Add bank account"
                  on:click={openAddBankAccount}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400 group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Link Bank Account</span>
                </button>
                
                <div class="pt-2 flex justify-between items-center text-xs text-gray-400">
                  <span>Secured with bank-level encryption</span>
                  <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Verified</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- App Settings -->
            <div class="glass-effect-light rounded-lg p-4">
              <h4 class="font-semibold text-white mb-3">App Settings</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Dark mode</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle dark mode"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle dark mode</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Auto-download media</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="false"
                    class="w-12 h-6 bg-gray-600 rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
                  >
                    <span class="sr-only">Toggle last seen</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <button
                  class="w-full text-left p-3 hover:bg-dark-light hover:bg-opacity-30 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  aria-label="View storage usage"
                  on:click={openStorageUsage}
                >
                  <span class="text-gray-300">Storage usage</span>
                </button>
                <button
                  class="w-full text-left p-3 hover:bg-dark-light hover:bg-opacity-30 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  aria-label="Export data"
                  on:click={exportData}
                >
                  <span class="text-gray-300">Export data</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Footer -->
        <div class="p-6 border-t border-gray-700">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-400">
              BoGuani v1.0.0 - Messenger of Value
            </div>
            <div class="flex space-x-4">
              <button class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Help & Support
              </button>
              <button class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </button>
              <button class="text-red-400 hover:text-red-300 text-sm transition-colors duration-200" on:click={logout}>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}

  <!-- Edit Profile Modal -->
  {#if showEditProfile}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-md mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Edit Profile</h3>
        <div class="space-y-4">
          <div>
            <label for="edit-name" class="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
            <input
              id="edit-name"
              type="text"
              bind:value={editingName}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Enter your name"
            />
          </div>
          <div>
            <label for="edit-handle" class="block text-gray-300 text-sm font-medium mb-2">Username</label>
            <div class="relative">
              <span class="absolute left-4 top-3 text-gray-400">@</span>
              <input
                id="edit-handle"
                type="text"
                bind:value={editingHandle}
                class="w-full pl-8 pr-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
                placeholder="username"
              />
            </div>
          </div>
        </div>
        <div class="flex space-x-3 mt-6">
          <button
            class="flex-1 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
            on:click={() => showEditProfile = false}
          >
            Cancel
          </button>
          <button
            class="flex-1 py-2 bg-gold hover:bg-gold-dark text-dark-dark font-semibold rounded-lg transition-colors duration-200"
            on:click={saveProfile}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Change Password Modal -->
  {#if showChangePassword}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-md mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Change Password</h3>
        <div class="space-y-4">
          <div>
            <label for="current-password" class="block text-gray-300 text-sm font-medium mb-2">Current Password</label>
            <input
              id="current-password"
              type="password"
              bind:value={currentPassword}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Enter current password"
            />
          </div>
          <div>
            <label for="new-password" class="block text-gray-300 text-sm font-medium mb-2">New Password</label>
            <input
              id="new-password"
              type="password"
              bind:value={newPassword}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Enter new password"
            />
          </div>
          <div>
            <label for="confirm-password" class="block text-gray-300 text-sm font-medium mb-2">Confirm New Password</label>
            <input
              id="confirm-password"
              type="password"
              bind:value={confirmPassword}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Confirm new password"
            />
          </div>
        </div>
        <div class="flex space-x-3 mt-6">
          <button
            class="flex-1 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
            on:click={() => showChangePassword = false}
          >
            Cancel
          </button>
          <button
            class="flex-1 py-2 bg-gold hover:bg-gold-dark text-dark-dark font-semibold rounded-lg transition-colors duration-200"
            on:click={savePassword}
          >
            Change Password
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Active Sessions Modal -->
  {#if showActiveSessions}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-2xl mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Active Sessions</h3>
        <div class="space-y-3">
          <div class="p-4 bg-dark-light bg-opacity-30 rounded-lg border border-green-500">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-white font-medium">Current Session</p>
                <p class="text-gray-400 text-sm">MacBook Pro • Chrome • San Francisco, CA</p>
                <p class="text-gray-400 text-xs">Active now</p>
              </div>
              <span class="text-green-400 text-sm">Current</span>
            </div>
          </div>
          <div class="p-4 bg-dark-light bg-opacity-30 rounded-lg">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-white font-medium">iPhone 15 Pro</p>
                <p class="text-gray-400 text-sm">Safari • San Francisco, CA</p>
                <p class="text-gray-400 text-xs">2 hours ago</p>
              </div>
              <button class="text-red-400 hover:text-red-300 text-sm">Revoke</button>
            </div>
          </div>
        </div>
        <div class="flex justify-end mt-6">
          <button
            class="py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
            on:click={() => showActiveSessions = false}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Storage Usage Modal -->
  {#if showStorageUsage}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-md mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Storage Usage</h3>
        <div class="space-y-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-gold mb-2">2.4 GB</div>
            <div class="text-gray-400">of 15 GB used</div>
          </div>
          <div class="w-full bg-gray-700 rounded-full h-2">
            <div class="bg-gold h-2 rounded-full" style="width: 16%"></div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-300">Messages</span>
              <span class="text-white">1.2 GB</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-300">Media</span>
              <span class="text-white">800 MB</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-300">Documents</span>
              <span class="text-white">400 MB</span>
            </div>
          </div>
        </div>
        <div class="flex justify-end mt-6">
          <button
            class="py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
            on:click={() => showStorageUsage = false}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  {/if}

  <!-- Export Data Modal -->
  {#if showExportData}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-md mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Export Data</h3>
        <div class="text-center">
          <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gold bg-opacity-20 flex items-center justify-center">
            <svg class="animate-spin h-8 w-8 text-gold" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <p class="text-white mb-2">Preparing your data export...</p>
          <p class="text-gray-400 text-sm">This may take a few moments</p>
        </div>
      </div>
    </div>
  {/if}

  <!-- Add Bank Account Modal -->
  {#if showAddBankAccount}
    <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-md mx-4 p-6">
        <h3 class="text-xl font-bold text-white mb-4">Add Bank Account</h3>
        <div class="space-y-4">
          <div>
            <label for="account-name" class="block text-gray-300 text-sm font-medium mb-2">Account Name</label>
            <input
              id="account-name"
              type="text"
              bind:value={newBankAccountName}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="e.g., Chase Checking"
            />
          </div>
          <div>
            <label for="account-number" class="block text-gray-300 text-sm font-medium mb-2">Account Number</label>
            <input
              id="account-number"
              type="text"
              bind:value={newBankAccountNumber}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Account number"
            />
          </div>
          <div>
            <label for="routing-number" class="block text-gray-300 text-sm font-medium mb-2">Routing Number</label>
            <input
              id="routing-number"
              type="text"
              bind:value={newBankRoutingNumber}
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white"
              placeholder="Routing number"
            />
          </div>
        </div>
        <div class="flex space-x-3 mt-6">
          <button
            class="flex-1 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200"
            on:click={() => showAddBankAccount = false}
          >
            Cancel
          </button>
          <button
            class="flex-1 py-2 bg-gold hover:bg-gold-dark text-dark-dark font-semibold rounded-lg transition-colors duration-200"
            on:click={saveBankAccount}
          >
            Add Account
          </button>
        </div>
      </div>
    </div>
  {/if}
    </div>
{/if}

<!-- Enhanced Modals -->
<PaymentModal
  isOpen={showEnhancedPaymentModal}
  recipientName={otherUser?.name || ''}
  recipientPhone={otherUser?.handle || ''}
  isGroupChat={false}
  on:close={() => showEnhancedPaymentModal = false}
  on:payment={handleEnhancedPayment}
/>

<ContactImportModal
  isOpen={showContactImportModal}
  on:close={() => showContactImportModal = false}
  on:chatCreated={handleContactImport}
/>

<!-- Floating Manage Button -->
<div class="fixed bottom-6 right-6 z-40">
  <ManageButton on:action={handleManageAction} />
</div>

<!-- Call Modals -->
<CallModal />
<IncomingCallModal />
