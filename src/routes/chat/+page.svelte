<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import ChatBubble from '$lib/components/ChatBubble.svelte';
  import MessageInput from '$lib/components/MessageInput.svelte';
  import BankTransferModal from '$lib/components/BankTransferModal.svelte';
  import { websocket, type Message, type Chat } from '$lib/websocket.js';
  import type { BankAccount } from '$lib/plaid.js';
  
  // Current user from localStorage
  let currentUser: any = null;
  let selectedChat: Chat | null = null;
  let showSettings = false;
  
  // Mock data for development
  let messages: Message[] = [
    {
      id: 'msg1',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Hey there! How\'s it going?',
      type: 'text',
      timestamp: new Date(Date.now() - 300000),
      status: 'read'
    },
    {
      id: 'msg2',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Hi! I\'m good, thanks. Just working on some projects.',
      type: 'text',
      timestamp: new Date(Date.now() - 240000),
      status: 'read'
    },
    {
      id: 'msg3',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Cool! Hey, can you send me $20 for lunch? I forgot my wallet.',
      type: 'text',
      timestamp: new Date(Date.now() - 180000),
      status: 'read'
    },
    {
      id: 'msg4',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Sure, no problem. I\'ll send it right away.',
      type: 'text',
      timestamp: new Date(Date.now() - 120000),
      status: 'read'
    },
    {
      id: 'msg5',
      chatId: 'chat1',
      senderId: 'user1',
      recipientId: 'user2',
      content: 'Payment sent',
      type: 'payment',
      timestamp: new Date(Date.now() - 60000),
      status: 'read',
      paymentData: {
        amount: 20.00,
        status: 'completed',
        transactionId: 'txn_123'
      }
    },
    {
      id: 'msg6',
      chatId: 'chat1',
      senderId: 'user2',
      recipientId: 'user1',
      content: 'Thanks a lot! Got it. 👍',
      type: 'text',
      timestamp: new Date(Date.now() - 30000),
      status: 'read'
    }
  ];
  
  let chats: Chat[] = [
    {
      id: 'chat1',
      participants: ['user1', 'user2'],
      lastMessage: messages[messages.length - 1],
      unreadCount: 0,
      isTyping: false,
      typingUsers: []
    },
    {
      id: 'chat2',
      participants: ['user1', 'user3'],
      lastMessage: {
        id: 'msg_old',
        chatId: 'chat2',
        senderId: 'user3',
        recipientId: 'user1',
        content: 'Thanks for the payment!',
        type: 'text',
        timestamp: new Date(Date.now() - ********),
        status: 'read'
      },
      unreadCount: 0,
      isTyping: false,
      typingUsers: []
    }
  ];
  
  let users = [
    { id: 'user2', name: 'John Doe', handle: 'johndoe', isOnline: true },
    { id: 'user3', name: 'Alice Smith', handle: 'alicesmith', isOnline: false },
    { id: 'user4', name: 'Robert Johnson', handle: 'robertj', isOnline: false },
    { id: 'user5', name: 'Emma Wilson', handle: 'emmaw', isOnline: false }
  ];
  
  let bankAccounts: BankAccount[] = [
    {
      id: 'acc1',
      name: 'Chase Checking',
      mask: '4567',
      type: 'depository',
      subtype: 'checking',
      balance: 1245.67,
      isConnected: true
    }
  ];
  
  let showPaymentModal = false;
  let showPlaidModal = false;
  let searchQuery = '';
  let isTyping = false;
  
  // Set default selected chat
  selectedChat = chats[0];
  
  function selectChat(chat: Chat) {
    selectedChat = chat;
    // Mark messages as read
    messages.forEach(msg => {
      if (msg.chatId === chat.id && msg.senderId !== currentUser?.id) {
        msg.status = 'read';
      }
    });
  }
  
  function handleSendMessage(event: CustomEvent<{ content: string; type: 'text' | 'payment' }>) {
    if (!selectedChat || !currentUser) return;
    
    const { content, type } = event.detail;
    
    if (type === 'payment') {
      // Extract amount from message
      const amountMatch = content.match(/\$(\d+(?:\.\d{2})?)/);
      if (amountMatch) {
        const amount = parseFloat(amountMatch[1]);
        showPaymentModal = true;
        // You would set the default amount here
      }
    } else {
      const newMessage: Message = {
        id: `msg_${Date.now()}`,
        chatId: selectedChat.id,
        senderId: currentUser.id,
        recipientId: selectedChat.participants.find(p => p !== currentUser.id) || '',
        content,
        type,
        timestamp: new Date(),
        status: 'sent'
      };
      
      messages = [...messages, newMessage];
      
      // Update chat's last message
      selectedChat.lastMessage = newMessage;
      chats = chats.map(c => c.id === selectedChat?.id ? selectedChat : c);
      
      // Scroll to bottom
      setTimeout(() => {
        const messagesArea = document.getElementById('messagesArea');
        if (messagesArea) {
          messagesArea.scrollTop = messagesArea.scrollHeight;
        }
      }, 100);
    }
  }
  
  function handleTyping(event: CustomEvent<{ isTyping: boolean }>) {
    isTyping = event.detail.isTyping;
    // In a real app, you'd send this to the WebSocket server
  }
  
  function handleTransfer(event: CustomEvent<{ amount: number; accountId: string; note: string }>) {
    if (!selectedChat || !currentUser) return;
    
    const { amount, note } = event.detail;
    
    const paymentMessage: Message = {
      id: `msg_${Date.now()}`,
      chatId: selectedChat.id,
      senderId: currentUser.id,
      recipientId: selectedChat.participants.find(p => p !== currentUser.id) || '',
      content: `Payment sent: $${amount.toFixed(2)}${note ? ` - ${note}` : ''}`,
      type: 'payment',
      timestamp: new Date(),
      status: 'sent',
      paymentData: {
        amount,
        status: 'pending'
      }
    };
    
    messages = [...messages, paymentMessage];
    
    // Simulate payment processing
    setTimeout(() => {
      paymentMessage.paymentData!.status = 'completed';
      paymentMessage.status = 'delivered';
      messages = [...messages];
    }, 2000);
    
    showPaymentModal = false;
    
    // Update chat's last message
    selectedChat.lastMessage = paymentMessage;
    chats = chats.map(c => c.id === selectedChat?.id ? selectedChat : c);
  }
  
  function getCurrentUser(userId: string) {
    return users.find(u => u.id === userId) || { 
      id: 'unknown',
      name: 'Unknown', 
      handle: 'unknown',
      isOnline: false 
    };
  }
  
  function formatTime(date: Date): string {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < ********) return date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
    if (diff < 604800000) return date.toLocaleDateString('en-US', { weekday: 'short' });
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
  
  function logout() {
    localStorage.removeItem('nexuspay_token');
    localStorage.removeItem('nexuspay_user');
    goto('/');
  }
  
  $: filteredChats = chats.filter(chat => {
    if (!searchQuery) return true;
    const otherUserId = chat.participants.find(p => p !== currentUser?.id);
    const otherUser = getCurrentUser(otherUserId || '');
    return otherUser.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           otherUser.handle.toLowerCase().includes(searchQuery.toLowerCase());
  });
  
  $: chatMessages = messages.filter(msg => msg.chatId === selectedChat?.id);
  
  onMount(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('nexuspay_token');
    const userStr = localStorage.getItem('nexuspay_user');
    
    if (!token || !userStr) {
      goto('/auth');
      return;
    }
    
    try {
      currentUser = JSON.parse(userStr);
      currentUser.id = 'user1'; // Set for mock data compatibility
    } catch (err) {
      goto('/auth');
      return;
    }
    
    // Scroll to bottom on mount
    setTimeout(() => {
      const messagesArea = document.getElementById('messagesArea');
      if (messagesArea) {
        messagesArea.scrollTop = messagesArea.scrollHeight;
      }
    }, 100);
  });
</script>

<svelte:head>
  <title>Chat - BoGuani</title>
</svelte:head>

{#if currentUser}
  <div class="flex h-screen bg-gradient-to-br from-[#1a0a2a] to-[#2E003E] text-gray-200">
    <!-- Sidebar -->
    <div class="w-1/4 glass-effect flex flex-col h-full border-r border-purple-900/50 animate-slide-up">
      <!-- App Header -->
      <div class="p-4 bg-gradient-to-r from-[#2E003E] to-[#46005E] text-white flex justify-between items-center border-b border-purple-900/50">
        <h1 class="text-2xl font-bold flex items-center">
          <span class="bg-clip-text text-transparent bg-gradient-to-r from-gold-400 to-gold-600">BoGuani</span>
        </h1>
        <div class="flex space-x-3">
          <button 
            class="focus:outline-none hover:text-gold-light transition-colors duration-200"
            aria-label="New chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <button 
            class="focus:outline-none hover:text-gold-light transition-colors duration-200"
            on:click={() => showSettings = !showSettings}
            aria-label="Settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- User Profile -->
      <div class="p-4 bg-dark-DEFAULT bg-opacity-70 border-b border-gray-700">
        <div class="flex items-center">
          <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
            {currentUser.name.charAt(0).toUpperCase()}
          </div>
          <div class="flex-1">
            <h3 class="font-semibold text-white">{currentUser.name}</h3>
            <p class="text-xs text-gray-400">@{currentUser.handle}</p>
          </div>
          <button 
            class="text-gray-400 hover:text-red-400 transition-colors duration-200"
            on:click={logout}
            aria-label="Logout"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Search -->
      <div class="p-3 bg-dark-DEFAULT bg-opacity-70">
        <div class="relative">
          <input 
            type="text" 
            bind:value={searchQuery}
            placeholder="Search or start new chat" 
            class="w-full py-2 px-4 bg-dark-light bg-opacity-50 rounded-full pl-10 focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400 transition-all duration-200"
          />
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>
      
      <!-- Chat List -->
      <div class="flex-1 overflow-y-auto scrollbar-hide">
        {#each filteredChats as chat, index}
          {@const otherUserId = chat.participants.find(p => p !== currentUser.id)}
          {@const otherUser = getCurrentUser(otherUserId || '')}
          <button
            class="w-full flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer transition-colors duration-200 {selectedChat?.id === chat.id ? 'bg-dark-light bg-opacity-30' : ''} animate-fade-in"
            on:click={() => selectChat(chat)}
          >
            <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
              {otherUser.name.charAt(0).toUpperCase()}
            </div>
            <div class="flex-1 text-left">
              <div class="flex justify-between items-center">
                <h3 class="font-semibold">{otherUser.name}</h3>
                <span class="text-xs text-gray-400">
                  {chat.lastMessage ? formatTime(chat.lastMessage.timestamp) : ''}
                </span>
              </div>
              <p class="text-sm text-gray-400 truncate">
                {chat.lastMessage?.type === 'payment' ? 
                  `💰 ${chat.lastMessage.content}` : 
                  chat.lastMessage?.content || 'No messages yet'
                }
              </p>
            </div>
            {#if chat.unreadCount > 0}
              <div class="w-5 h-5 bg-primary rounded-full flex items-center justify-center text-xs text-white">
                {chat.unreadCount}
              </div>
            {/if}
          </button>
        {/each}
      </div>
      
      <!-- Banking Section -->
      <div class="p-4 glass-effect border-t border-gray-700">
        <h2 class="text-lg font-semibold mb-2 flex items-center">
          <span class="text-gold-light">Banking</span>
          <span class="ml-1 text-xs bg-primary-light px-1.5 py-0.5 rounded-full">Premium</span>
        </h2>
        <div class="flex justify-between items-center">
          <div>
            <p class="text-sm opacity-70">Balance</p>
            <p class="font-bold text-lg text-gold-light">
              ${bankAccounts.length > 0 ? bankAccounts[0].balance.toFixed(2) : '0.00'}
            </p>
          </div>
          <button 
            class="bg-primary bg-opacity-80 hover:bg-primary-dark text-white px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center"
            on:click={() => showPlaidModal = true}
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {bankAccounts.length > 0 ? 'Manage' : 'Connect Bank'}
          </button>
        </div>
      </div>
    </div>

    <!-- Chat Area -->
    <div class="w-3/4 flex flex-col h-full">
      {#if selectedChat}
        {@const otherUserId = selectedChat.participants.find(p => p !== currentUser.id)}
        {@const otherUser = getCurrentUser(otherUserId || '')}

        <!-- Chat Header -->
        <div class="p-3 glass-effect border-b border-gray-700 flex justify-between items-center animate-slide-up">
          <div class="flex items-center">
            <div class="w-10 h-10 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">
              {otherUser.name.charAt(0).toUpperCase()}
            </div>
            <div>
              <h2 class="font-semibold">{otherUser.name}</h2>
              <p class="text-xs text-gray-400 flex items-center">
                <span class="w-2 h-2 {otherUser.isOnline ? 'bg-green-400' : 'bg-gray-400'} rounded-full mr-1.5"></span>
                {otherUser.isOnline ? 'Online' : 'Offline'}
              </p>
            </div>
          </div>
          <div class="flex space-x-4">
            <button 
              aria-label="Start audio call" 
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              <span class="sr-only">Start audio call</span>
            </button>
            <button 
              aria-label="Start video call" 
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <span class="sr-only">Start video call</span>
            </button>
            <button
              aria-label="Send money"
              class="focus:outline-none text-gold-light hover:text-gold transition-colors duration-200"
              on:click={() => showPaymentModal = true}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="sr-only">Send money</span>
            </button>
            <button
              aria-label="Chat settings"
              aria-expanded={showSettings}
              class="focus:outline-none text-gray-400 hover:text-white transition-colors duration-200"
              on:click={() => showSettings = !showSettings}
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span class="sr-only">Chat settings</span>
            </button>
          </div>
        </div>

        <!-- Messages Area -->
        <div
          id="messagesArea"
          class="flex-1 p-4 overflow-y-auto bg-gradient-to-b from-dark-DEFAULT to-dark-dark scrollbar-hide animate-fade-in"
        >
          <!-- Day Divider -->
          <div class="flex justify-center mb-4">
            <span class="bg-dark-light bg-opacity-50 text-gray-300 text-xs px-3 py-1 rounded-full">Today</span>
          </div>

          <!-- Messages -->
          {#each chatMessages as message}
            <ChatBubble
              {message}
              isOwn={message.senderId === currentUser.id}
              senderName={message.senderId === currentUser.id ? currentUser.name : otherUser.name}
            />
          {/each}

          <!-- Typing Indicator -->
          {#if isTyping}
            <div class="flex mb-4 animate-fade-in">
              <div class="chat-bubble received p-3 shadow-lg">
                <div class="flex space-x-1">
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                  <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
              </div>
            </div>
          {/if}
        </div>

        <!-- Message Input -->
        <MessageInput
          on:send={handleSendMessage}
          on:typing={handleTyping}
          placeholder="Type a message"
        />
      {:else}
        <!-- No Chat Selected -->
        <div class="flex-1 flex items-center justify-center bg-gradient-to-b from-dark-DEFAULT to-dark-dark">
          <div class="text-center">
            <div class="w-24 h-24 mx-auto mb-4 rounded-full purple-gradient flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">Welcome to NexusPay</h3>
            <p class="text-gray-400">Select a chat to start messaging and sending money</p>
          </div>
        </div>
      {/if}
    </div>
  </div>

  <!-- Bank Transfer Modal -->
  {#if selectedChat}
    {@const otherUserId = selectedChat.participants.find(p => p !== currentUser.id)}
    {@const otherUser = getCurrentUser(otherUserId || '')}

    <BankTransferModal
      isOpen={showPaymentModal}
      recipientName={otherUser.name}
      recipientHandle={otherUser.handle}
      accounts={bankAccounts}
      on:close={() => showPaymentModal = false}
      on:transfer={handleTransfer}
    />
  {/if}

  <!-- Settings Panel -->
  {#if showSettings}
    <div class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center animate-fade-in">
      <div class="glass-effect rounded-2xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto animate-bounce-in border border-purple-900/50 shadow-2xl shadow-purple-900/20">
        <!-- Settings Header -->
        <div class="p-6 border-b border-purple-900/50 flex justify-between items-center bg-gradient-to-r from-[#2E003E] to-[#3A004E]">
          <h2 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500">Settings</h2>
          <button
            class="text-gray-300 hover:text-gold-400 transition-colors duration-200 p-1 rounded-full hover:bg-purple-900/50"
            on:click={() => showSettings = false}
            aria-label="Close settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
            <span class="sr-only">Close settings</span>
          </button>
        </div>

        <!-- Settings Content -->
        <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Profile Settings -->
          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">Profile</h3>

            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <div class="flex items-center mb-6">
                <div class="w-20 h-20 rounded-full bg-gradient-to-br from-purple-600 to-[#2E003E] flex items-center justify-center text-3xl font-bold text-gold-300 border-2 border-gold-500/30 shadow-lg">
                  {currentUser.name.charAt(0).toUpperCase()}
                </div>
                <div class="ml-4">
                  <h4 class="font-bold text-lg text-white">{currentUser.name}</h4>
                  <p class="text-gold-300 font-mono">@{currentUser.handle}</p>
                </div>
              </div>
              <button class="w-full py-2.5 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-400 hover:to-gold-500 text-white font-medium rounded-lg transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-gold/30 flex items-center justify-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit Profile
              </button>
            </div>

            <!-- Privacy Settings -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                Privacy & Security
              </h4>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Read receipts</span>
                    <span class="text-gray-400 text-sm">Let others see when you've read their messages</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle read receipts"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-purple-900/50"
                  >
                    <span class="sr-only">Toggle read receipts</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-5"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center pt-3 border-t border-purple-900/30">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Last seen</span>
                    <span class="text-gray-400 text-sm">Show when you were last active</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="false"
                    aria-label="Toggle last seen"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-gray-700/50"
                  >
                    <span class="sr-only">Toggle last seen</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-0"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center pt-3 border-t border-purple-900/30">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Profile visibility</span>
                    <span class="text-gray-400 text-sm">Make your profile visible to others</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle profile visibility"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-gradient-to-r from-gold-500 to-gold-600"
                  >
                    <span class="sr-only">Toggle profile visibility</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-5"
                    ></span>
                  </button>
                </div>
              </div>
            </div>

            <!-- Notifications -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                Notifications
              </h4>
              <div class="space-y-4">
                <div class="flex justify-between items-center">
                  <div class="space-y-0.5">
                    <span class="text-white font-medium block">Message notifications</span>
                    <span class="text-gray-400 text-sm">Get notified about new messages</span>
                  </div>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle message notifications"
                    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-purple-900/50"
                  >
                    <span class="sr-only">Toggle message notifications</span>
                    <span 
                      aria-hidden="true"
                      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out translate-x-5"
                    ></span>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Payment notifications</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle payment notifications"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle payment notifications</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Sound</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle sound notifications"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle sound notifications</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Security & Banking -->
          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">
              Security & Banking
            </h3>

            <!-- Security -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <h4 class="font-semibold text-white mb-4 flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Security
              </h4>
              <div class="space-y-3">
                <button 
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Two-factor authentication, enabled"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Two-Factor Authentication</p>
                      <p class="text-gold-300 text-sm mt-0.5">Extra layer of security for your account</p>
                    </div>
                    <div class="flex items-center">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400">
                        <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Active
                      </span>
                      <svg xmlns="http://www.w3.org/2000/svg" class="ml-2 h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </button>
                
                <button 
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Change password"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Password</p>
                      <p class="text-gray-400 text-sm mt-0.5">Last changed 3 months ago</p>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
                
                <button 
                  class="w-full text-left p-4 hover:bg-purple-900/20 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500/50 focus:ring-offset-2 focus:ring-offset-[#2E003E] group"
                  aria-label="Login activity"
                >
                  <div class="flex justify-between items-center">
                    <div>
                      <p class="text-white font-medium">Login Activity</p>
                      <p class="text-gray-400 text-sm mt-0.5">View recent account activity</p>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 group-hover:text-gold-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </button>
              </div>
            </div>

            <!-- Banking -->
            <div class="glass-effect rounded-xl p-5 border border-purple-900/30">
              <div class="flex justify-between items-center mb-4">
                <h4 class="font-semibold text-white flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3.9 2.7c.5.3 1.1.3 1.6 0L16 7m-9.5 9h9.5m-9.5 0a5.002 5.002 0 01-3.9-6.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Banking & Payments
                </h4>
                <button 
                  class="text-xs font-medium text-gold-400 hover:text-gold-300 flex items-center gap-1 transition-colors"
                  aria-label="View all transactions"
                >
                  View All
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
              
              <div class="space-y-3">
                {#each bankAccounts as account}
                  <div class="group relative p-4 bg-gradient-to-r from-purple-900/30 to-purple-800/10 rounded-xl border border-purple-900/50 hover:border-gold-500/30 transition-colors">
                    <div class="flex justify-between items-start">
                      <div class="flex items-start gap-3">
                        <div class="mt-0.5 p-2 bg-purple-900/50 rounded-lg border border-purple-800/50">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                          </svg>
                        </div>
                        <div>
                          <h5 class="font-medium text-white">{account.name}</h5>
                          <p class="text-sm text-gray-300 font-mono">•••• {account.mask}</p>
                          <p class="mt-1 text-xs text-gray-400">Primary Account</p>
                        </div>
                      </div>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-900/30 text-green-400">
                        <svg class="-ml-0.5 mr-1 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                          <circle cx="4" cy="4" r="3" />
                        </svg>
                        Connected
                      </span>
                    </div>
                    <div class="mt-3 pt-3 border-t border-purple-900/50 flex justify-between items-center">
                      <div class="text-xs text-gray-400">Balance</div>
                      <div class="text-right">
                        <p class="text-sm font-medium text-white">$5,432.10</p>
                        <p class="text-xs text-gray-400">Available</p>
                      </div>
                    </div>
                    <button 
                      class="absolute top-3 right-3 p-1 text-gray-400 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
                      aria-label="More options"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                      </svg>
                    </button>
                  </div>
                {/each}
                
                <button 
                  class="w-full mt-2 py-3 px-4 bg-gradient-to-r from-purple-900/30 to-purple-800/10 hover:from-purple-900/40 hover:to-purple-800/20 text-gold-400 font-medium rounded-xl border-2 border-dashed border-purple-800/50 hover:border-gold-500/30 transition-all duration-200 flex items-center justify-center gap-2 group"
                  aria-label="Add bank account"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400 group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span>Link Bank Account</span>
                </button>
                
                <div class="pt-2 flex justify-between items-center text-xs text-gray-400">
                  <span>Secured with bank-level encryption</span>
                  <div class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Verified</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- App Settings -->
            <div class="glass-effect-light rounded-lg p-4">
              <h4 class="font-semibold text-white mb-3">App Settings</h4>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Dark mode</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="true"
                    aria-label="Toggle dark mode"
                    class="w-12 h-6 bg-primary rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <span class="sr-only">Toggle dark mode</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-300">Auto-download media</span>
                  <button 
                    type="button"
                    role="switch"
                    aria-checked="false"
                    class="w-12 h-6 bg-gray-600 rounded-full relative focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
                  >
                    <span class="sr-only">Toggle last seen</span>
                    <div class="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 transition-transform duration-200 translate-x-0"></div>
                  </button>
                </div>
                <button 
                  class="w-full text-left p-3 hover:bg-dark-light hover:bg-opacity-30 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  aria-label="View storage usage"
                >
                  <span class="text-gray-300">Storage usage</span>
                </button>
                <button 
                  class="w-full text-left p-3 hover:bg-dark-light hover:bg-opacity-30 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  aria-label="Export data"
                >
                  <span class="text-gray-300">Export data</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Settings Footer -->
        <div class="p-6 border-t border-gray-700">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-400">
              NexusPay v1.0.0
            </div>
            <div class="flex space-x-4">
              <button class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Help & Support
              </button>
              <button class="text-gray-400 hover:text-white text-sm transition-colors duration-200">
                Privacy Policy
              </button>
              <button class="text-red-400 hover:text-red-300 text-sm transition-colors duration-200" on:click={logout}>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/if}
{/if}
