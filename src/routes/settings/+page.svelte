<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { getAuth, signOut } from 'firebase/auth';
  import { db } from '$lib/firebase';
  import { doc, getDoc, updateDoc, arrayRemove } from 'firebase/firestore';
  
  const auth = getAuth();
  let currentUser = $state<any>(null);
  let loading = $state(true);
  let bankAccounts = $state<any[]>([]);
  let showDisconnectConfirm = $state<string | null>(null);
  let error = $state<string | null>(null);
  
  onMount(async () => {
    if (!auth.currentUser) {
      goto('/auth');
      return;
    }
    
    try {
      currentUser = auth.currentUser;
      await loadUserData();
    } catch (err) {
      console.error('Error loading settings:', err);
      error = 'Failed to load settings';
    } finally {
      loading = false;
    }
  });
  
  async function loadUserData() {
    if (!auth.currentUser) return;
    
    const userDoc = await getDoc(doc(db, 'users', auth.currentUser.uid));
    if (userDoc.exists()) {
      const data = userDoc.data();
      bankAccounts = data.bankAccounts || [];
    }
  }
  
  function handleConnectBank() {
    goto('/connect-bank');
  }
  
  async function handleDisconnectBank(accountId: string) {
    if (!auth.currentUser) return;
    
    try {
      await updateDoc(doc(db, 'users', auth.currentUser.uid), {
        bankAccounts: arrayRemove(bankAccounts.find(acc => acc.id === accountId))
      });
      
      // Reload data
      await loadUserData();
      showDisconnectConfirm = null;
    } catch (err) {
      console.error('Error disconnecting bank:', err);
      error = 'Failed to disconnect bank account';
    }
  }
  
  async function handleSignOut() {
    try {
      await signOut(auth);
      goto('/auth');
    } catch (err) {
      console.error('Error signing out:', err);
      error = 'Failed to sign out';
    }
  }
  
  function maskAccountNumber(mask: string) {
    return `••••${mask}`;
  }
  
  function getBankIcon(bankName: string) {
    // Simple bank icon mapping - you can expand this
    const bankIcons: Record<string, string> = {
      'chase': '🏦',
      'bank of america': '🏛️',
      'wells fargo': '🏛️',
      'citi': '🏦',
      'capital one': '💳',
    };
    
    const lowerName = bankName.toLowerCase();
    for (const [key, icon] of Object.entries(bankIcons)) {
      if (lowerName.includes(key)) return icon;
    }
    
    return '🏦';
  }
</script>

<svelte:head>
  <title>Settings - BoGuani</title>
</svelte:head>

<div class="min-h-screen bg-gradient-main text-gray-200">
  <!-- Header -->
  <header class="bg-gradient-to-r from-purple-900 to-purple-800 p-4 shadow-lg">
    <div class="container mx-auto flex items-center">
      <button 
        on:click={() => history.back()}
        class="text-gold-400 hover:text-white mr-4"
        aria-label="Back"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <h1 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500">
        Settings
      </h1>
    </div>
  </header>
  
  <main class="container mx-auto p-4 max-w-3xl">
    {#if loading}
      <div class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-purple-500 border-t-gold-500"></div>
        <p class="mt-4 text-gray-400">Loading settings...</p>
      </div>
    {:else if error}
      <div class="bg-red-900/30 border border-red-800/50 rounded-lg p-4 mb-6">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span class="text-red-300">{error}</span>
        </div>
      </div>
    {:else}
      <!-- Profile Section -->
      <div class="glass-effect rounded-2xl p-6 mb-6">
        <h2 class="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          Profile
        </h2>
        
        <div class="space-y-4">
          <div class="flex items-center">
            <div class="w-16 h-16 rounded-full bg-gradient-to-br from-purple-600 to-purple-800 flex items-center justify-center text-2xl font-bold text-white">
              {currentUser?.displayName?.[0]?.toUpperCase() || 'U'}
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-white">{currentUser?.displayName || 'User'}</h3>
              <p class="text-sm text-gray-400">{currentUser?.email || currentUser?.phoneNumber || 'No contact info'}</p>
            </div>
          </div>
          
          <div class="pt-4 border-t border-gray-700">
            <button 
              on:click={handleSignOut}
              class="w-full py-2 px-4 bg-red-900/30 hover:bg-red-900/50 text-red-400 font-medium rounded-lg border border-red-800/50 hover:border-red-700/50 transition-colors flex items-center justify-center gap-2"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Sign Out
            </button>
          </div>
        </div>
      </div>
      
      <!-- Bank Accounts Section -->
      <div class="glass-effect rounded-2xl p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-white flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3.9 2.7c.5.3 1.1.3 1.6 0L16 7m-9.5 9h9.5m-9.5 0a5.002 5.002 0 01-3.9-6.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Bank Accounts
          </h2>
          <button 
            on:click={handleConnectBank}
            class="text-sm bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-white font-medium py-1.5 px-3 rounded-lg flex items-center gap-1 transition-all duration-200"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Bank
          </button>
        </div>
        
        {#if bankAccounts.length === 0}
          <div class="text-center py-8">
            <div class="bg-purple-900/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3.9 2.7c.5.3 1.1.3 1.6 0L16 7m-9.5 9h9.5m-9.5 0a5.002 5.002 0 01-3.9-6.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-300 mb-1">No bank accounts</h3>
            <p class="text-sm text-gray-500 mb-4">Connect your bank account to send and receive money</p>
            <button 
              on:click={handleConnectBank}
              class="px-4 py-2 bg-gradient-to-r from-gold-500 to-gold-600 hover:from-gold-600 hover:to-gold-700 text-white font-medium rounded-lg transition-all duration-200 inline-flex items-center gap-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Connect Bank
            </button>
          </div>
        {:else}
          <div class="space-y-3">
            {#each bankAccounts as account}
              <div class="relative group">
                <div class="bg-gradient-to-r from-purple-900/20 to-purple-800/10 rounded-xl p-4 border border-purple-800/30 hover:border-gold-500/30 transition-colors">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-10 h-10 rounded-full bg-purple-800/50 flex items-center justify-center text-xl mr-3">
                        {getBankIcon(account.institution || '')}
                      </div>
                      <div>
                        <h4 class="font-medium text-white">{account.name} {maskAccountNumber(account.mask)}</h4>
                        <p class="text-xs text-gray-400">{account.institution || 'Bank'} • {account.type}</p>
                      </div>
                    </div>
                    <div class="text-right">
                      <div class="text-xs px-2 py-1 bg-green-900/30 text-green-400 rounded-full inline-flex items-center">
                        <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                        Connected
                      </div>
                    </div>
                  </div>
                  
                  <div class="mt-3 pt-3 border-t border-gray-700 flex justify-end">
                    <button 
                      on:click={() => showDisconnectConfirm = account.id}
                      class="text-xs text-red-400 hover:text-red-300 transition-colors flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Disconnect
                    </button>
                  </div>
                </div>
                
                {#if showDisconnectConfirm === account.id}
                  <div class="absolute inset-0 bg-black/80 backdrop-blur-sm rounded-xl flex items-center justify-center p-4 z-10">
                    <div class="bg-gray-800 rounded-lg p-4 max-w-xs w-full border border-gray-700">
                      <h4 class="font-medium text-white mb-2">Disconnect Bank Account?</h4>
                      <p class="text-sm text-gray-400 mb-4">Are you sure you want to disconnect this bank account? You'll need to reconnect it to use it again.</p>
                      <div class="flex justify-end space-x-2">
                        <button 
                          on:click={() => showDisconnectConfirm = null}
                          class="px-3 py-1.5 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors"
                        >
                          Cancel
                        </button>
                        <button 
                          on:click={() => handleDisconnectBank(account.id)}
                          class="px-3 py-1.5 text-sm bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                          Disconnect
                        </button>
                      </div>
                    </div>
                  </div>
                {/if}
              </div>
            {/each}
          </div>
        {/if}
      </div>
      
      <!-- App Settings -->
      <div class="glass-effect rounded-2xl p-6 mb-6">
        <h2 class="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          App Settings
        </h2>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between py-2">
            <div>
              <h3 class="font-medium text-white">Dark Mode</h3>
              <p class="text-sm text-gray-400">Switch between light and dark theme</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" value="" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between py-2">
            <div>
              <h3 class="font-medium text-white">Notifications</h3>
              <p class="text-sm text-gray-400">Get notified about transactions</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" value="" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between py-2">
            <div>
              <h3 class="font-medium text-white">Biometric Login</h3>
              <p class="text-sm text-gray-400">Use fingerprint or face ID to log in</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" value="" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-700 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gold-500"></div>
            </label>
          </div>
        </div>
      </div>
      
      <!-- Legal Links -->
      <div class="glass-effect rounded-2xl p-6">
        <h2 class="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gold-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Legal
        </h2>
        
        <div class="space-y-3">
          <a 
            href="/terms" 
            class="flex items-center justify-between py-2.5 text-gray-300 hover:text-white transition-colors border-b border-gray-700"
          >
            <span>Terms of Service</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </a>
          
          <a 
            href="/privacy" 
            class="flex items-center justify-between py-2.5 text-gray-300 hover:text-white transition-colors border-b border-gray-700"
          >
            <span>Privacy Policy</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </a>
          
          <div class="pt-2">
            <p class="text-xs text-gray-500">
              BoGuani v1.0.0<br>
              © {new Date().getFullYear()} BoGuani. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    {/if}
  </main>
</div>
