<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { browser } from '$app/environment';

  onMount(() => {
    if (browser) {
      document.title = 'Terms of Service - BoGuani';
    }
  });
</script>

<svelte:head>
  <title>Terms of Service - BoGuani</title>
</svelte:head>

<div class="min-h-screen bg-gradient-main text-gray-200 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-4xl mx-auto">
    <div class="glass-effect rounded-2xl p-8">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">
          Terms of Service
        </h1>
        <p class="text-gray-400">Last updated: June 28, 2025</p>
      </div>
      
      <div class="prose prose-invert max-w-none">
        <!-- Your terms of service content here -->
        <p class="text-gray-300 mb-6">
          Welcome to BoGuani. By using our service, you agree to these terms. Please read them carefully.
        </p>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">1. Account Terms</h2>
        <p class="text-gray-300 mb-4">
          You are responsible for maintaining the security of your account and for all activities that occur under your account.
        </p>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">2. Privacy</h2>
        <p class="text-gray-300 mb-4">
          Your privacy is important to us. Please review our Privacy Policy to understand how we collect and use your information.
        </p>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">3. Payments</h2>
        <p class="text-gray-300 mb-4">
          All payments are processed securely through our payment partners. You agree to pay all fees associated with your use of the service.
        </p>
        
        <div class="mt-12 text-center">
          <button 
            on:click={() => history.back()} 
            class="px-6 py-2 bg-gradient-to-r from-purple-700 to-purple-900 hover:from-purple-600 hover:to-purple-800 text-white font-medium rounded-lg transition-all duration-200 border border-purple-700 hover:border-gold-500/50"
          >
            Back to App
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
