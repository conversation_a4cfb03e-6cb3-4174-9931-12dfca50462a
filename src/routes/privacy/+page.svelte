<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  onMount(() => {
    if (browser) {
      document.title = 'Privacy Policy - BoGuani';
    }
  });
</script>

<svelte:head>
  <title>Privacy Policy - BoGuani</title>
</svelte:head>

<div class="min-h-screen bg-gradient-main text-gray-200 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-4xl mx-auto">
    <div class="glass-effect rounded-2xl p-8">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gold-300 to-gold-500 mb-4">
          Privacy Policy
        </h1>
        <p class="text-gray-400">Last updated: June 28, 2025</p>
      </div>
      
      <div class="prose prose-invert max-w-none">
        <p class="text-gray-300 mb-6">
          At BoGuani, we take your privacy seriously. This Privacy Policy explains how we collect, use, and protect your information.
        </p>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">1. Information We Collect</h2>
        <p class="text-gray-300 mb-4">
          We collect information you provide when you create an account, use our services, or communicate with us. This may include:
        </p>
        <ul class="list-disc pl-6 text-gray-300 mb-4 space-y-2">
          <li>Contact information (name, email, phone number)</li>
          <li>Account credentials</li>
          <li>Payment information (processed securely by our payment partners)</li>
          <li>Usage data and analytics</li>
        </ul>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">2. How We Use Your Information</h2>
        <p class="text-gray-300 mb-4">
          We use your information to:
        </p>
        <ul class="list-disc pl-6 text-gray-300 mb-4 space-y-2">
          <li>Provide and maintain our services</li>
          <li>Process transactions</li>
          <li>Improve user experience</li>
          <li>Communicate with you</li>
          <li>Ensure security and prevent fraud</li>
        </ul>
        
        <h2 class="text-xl font-semibold text-gold-400 mt-8 mb-4">3. Data Security</h2>
        <p class="text-gray-300 mb-4">
          We implement appropriate security measures to protect your information. However, no method of transmission over the internet is 100% secure.
        </p>
        
        <div class="mt-12 text-center">
          <button 
            on:click={() => history.back()} 
            class="px-6 py-2 bg-gradient-to-r from-purple-700 to-purple-900 hover:from-purple-600 hover:to-purple-800 text-white font-medium rounded-lg transition-all duration-200 border border-purple-700 hover:border-gold-500/50"
          >
            Back to App
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
