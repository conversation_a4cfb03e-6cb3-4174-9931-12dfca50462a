<script>
  import PhoneAuth from '$lib/components/PhoneAuth.svelte';
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  
  // Redirect if already authenticated
  import { auth } from '$lib/firebase';
  import { onAuthStateChanged } from 'firebase/auth';
  import { goto } from '$app/navigation';
  
  onMount(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        // User is signed in, redirect to dashboard
        goto('/dashboard');
      }
    });
    
    return () => unsubscribe();
  });
</script>

<svelte:head>
  <title>Phone Verification | ChatPay</title>
  <meta name="description" content="Verify your phone number to access your ChatPay account" />
</svelte:head>

<main class="min-h-screen bg-gradient-to-br from-gray-900 to-purple-900 flex items-center justify-center p-4">
  <div class="w-full max-w-md">
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-white mb-2">Welcome to ChatPay</h1>
      <p class="text-gray-300">Secure messaging and payments</p>
    </div>
    
    <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border border-gray-700/50">
      <PhoneAuth />
    </div>
    
    <p class="mt-6 text-center text-sm text-gray-400">
      Need help? <a href="/support" class="text-yellow-400 hover:underline">Contact Support</a>
    </p>
  </div>
</main>
