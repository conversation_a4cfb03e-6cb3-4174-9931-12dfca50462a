<script lang="ts">
  import PhoneAuth from '$lib/components/PhoneAuth.svelte';
  
  // This page is specifically for testing phone authentication
  // It includes the PhoneAuth component with additional debugging info
</script>

<div class="min-h-screen bg-gray-900 text-white p-4">
  <div class="max-w-md mx-auto bg-gray-800 rounded-lg shadow-lg p-8">
    <h1 class="text-2xl font-bold mb-6 text-center text-yellow-400">Phone Authentication Test</h1>
    <p class="text-sm text-gray-400 mb-6 text-center">
      This is a test page for phone authentication. Use it to verify SMS delivery.
    </p>
    
    <div class="bg-gray-700 p-4 rounded-lg mb-6">
      <h2 class="text-lg font-semibold mb-2">Test Instructions:</h2>
      <ol class="list-decimal list-inside space-y-2 text-sm">
        <li>Enter your phone number (with country code, e.g., +14155551234)</li>
        <li>Click "Send Verification Code"</li>
        <li>Check your phone for the SMS with the verification code</li>
        <li>Enter the code to verify your phone number</li>
      </ol>
    </div>
    
    <!-- Phone Auth Component -->
    <PhoneAuth />
    
    <div class="mt-8 pt-4 border-t border-gray-700">
      <h3 class="text-sm font-semibold text-gray-400 mb-2">Debug Info:</h3>
      <p class="text-xs text-gray-500">
        Check browser console for detailed logs. Make sure:
      </p>
      <ul class="list-disc list-inside text-xs text-gray-500 space-y-1 mt-2">
        <li>Billing is enabled in Firebase Console</li>
        <li>Phone Authentication is enabled in Firebase Console</li>
        <li>Your phone number is in E.164 format (+[country code][number])</li>
      </ul>
    </div>
  </div>
</div>
