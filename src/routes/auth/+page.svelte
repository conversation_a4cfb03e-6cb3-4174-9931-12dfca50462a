<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';

  let step: 'phone' | 'otp' | 'profile' = 'phone';
  let countryCode = '+1';
  let phoneNumber = '';
  let otpCode = '';
  let name = '';
  let username = '';
  let error = '';
  let loading = false;
  let success = false;

  const countryCodes = [
    { code: '+1', country: 'United States', flag: '🇺🇸' },
    { code: '+1', country: 'Canada', flag: '🇨🇦' },
    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' },
    { code: '+91', country: 'India', flag: '🇮🇳' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
    { code: '+61', country: 'Australia', flag: '🇦🇺' }
  ];

  async function sendOTP() {
    if (!phoneNumber.trim()) {
      error = 'Please enter your phone number';
      return;
    }

    loading = true;
    error = '';

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      step = 'otp';
      loading = false;
    } catch (err) {
      error = 'Failed to send verification code. Please try again.';
      loading = false;
    }
  }

  async function verifyOTP() {
    if (!otpCode.trim() || otpCode.length !== 6) {
      error = 'Please enter a valid 6-digit code';
      return;
    }

    loading = true;
    error = '';

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      step = 'profile';
      loading = false;
    } catch (err) {
      error = 'Invalid verification code. Please try again.';
      loading = false;
    }
  }

  async function completeProfile() {
    if (!name.trim() || !username.trim()) {
      error = 'Please fill in all fields';
      return;
    }

    loading = true;
    error = '';

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (browser) {
        localStorage.setItem('boguani_user', JSON.stringify({
          id: 'user1',
          uid: 'user-' + Date.now(),
          phoneNumber: countryCode + phoneNumber,
          name: name,
          username: username,
          displayName: name
        }));
      }
      
      loading = false;
      success = true;
      
      setTimeout(() => {
        goto('/chat');
      }, 1500);
    } catch (err) {
      error = 'Failed to create profile. Please try again.';
      loading = false;
    }
  }

  function goBack() {
    if (step === 'otp') {
      step = 'phone';
    } else if (step === 'profile') {
      step = 'otp';
    }
    error = '';
  }

  onMount(() => {
    if (browser) {
      const userData = localStorage.getItem('boguani_user');
      if (userData) {
        goto('/chat');
      }
    }
  });
</script>

<svelte:head>
  <title>Sign In - BoGuani</title>
  <meta name="description" content="Sign in to BoGuani - Secure messaging and value transfer platform" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
      font-family: 'Montserrat', sans-serif;
      min-height: 100vh;
    }
    
    .hero-gradient {
      background: linear-gradient(135deg, 
        rgba(30, 30, 36, 0.95) 0%, 
        rgba(45, 27, 78, 0.9) 25%, 
        rgba(61, 42, 95, 0.85) 50%, 
        rgba(78, 58, 112, 0.8) 75%, 
        rgba(45, 27, 78, 0.9) 100%);
    }
    
    .card-gradient {
      background: linear-gradient(135deg, 
        rgba(61, 42, 95, 0.4) 0%, 
        rgba(78, 58, 112, 0.3) 50%, 
        rgba(45, 27, 78, 0.4) 100%);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(212, 175, 55, 0.2);
    }
    
    .gold-gradient {
      background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    
    .btn-hover {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .btn-hover:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
    }
    
    .hero-pattern {
      background-image: 
        radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%);
    }
    
    .gold-border {
      border: 2px solid transparent;
      background: linear-gradient(rgba(45, 27, 78, 0.8), rgba(45, 27, 78, 0.8)) padding-box,
                  linear-gradient(90deg, #D4AF37, #F2D675) border-box;
    }
  </style>
</svelte:head>

<div class="hero-pattern min-h-screen text-white">
  <div class="hero-gradient min-h-screen flex items-center justify-center px-6">
    <div class="w-full max-w-md">
      <!-- Header -->
      <div class="text-center mb-12">
        <a href="/" class="inline-block" aria-label="Go to BoGuani homepage">
          <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center shadow-2xl btn-hover">
            <i class="fas fa-comment-dollar text-3xl text-[#2D1B4E]"></i>
          </div>
        </a>
        <h1 class="text-4xl md:text-5xl font-bold mb-4 gold-gradient">
          Welcome to BoGuani
        </h1>
        <p class="text-xl text-gray-300 mb-2">Messenger of Value</p>
        <p class="text-gray-400 italic">"Speak Gold. Share Value."</p>
      </div>

      <!-- Auth Card -->
      <div class="card-gradient p-8 rounded-2xl shadow-2xl">
        
        {#if success}
          <!-- Success State -->
          <div class="text-center py-8">
            <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
              <i class="fas fa-check text-2xl text-[#2D1B4E]"></i>
            </div>
            <h2 class="text-2xl font-bold mb-4 gold-gradient">Welcome to BoGuani!</h2>
            <p class="text-gray-300 mb-6">Your account has been created successfully.</p>
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#D4AF37] mx-auto"></div>
            <p class="text-sm text-gray-400 mt-4">Redirecting to chat...</p>
          </div>
        {:else}
          <!-- Step Indicator -->
          <div class="flex justify-center mb-8">
            <div class="flex space-x-4">
              <div class="w-3 h-3 rounded-full {step === 'phone' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
              <div class="w-3 h-3 rounded-full {step === 'otp' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
              <div class="w-3 h-3 rounded-full {step === 'profile' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
            </div>
          </div>

          <!-- Error Message -->
          {#if error}
            <div class="bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                <p class="text-red-200 text-sm">{error}</p>
              </div>
            </div>
          {/if}

          <!-- Phone Step -->
          {#if step === 'phone'}
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-2 gold-gradient">Enter Your Phone</h2>
              <p class="text-gray-400 text-sm">We'll send you a verification code</p>
            </div>

            <form on:submit|preventDefault={sendOTP} class="space-y-6">
              <div>
                <label for="countryCode" class="block text-gray-300 text-sm font-medium mb-2">Country</label>
                <select
                  id="countryCode"
                  bind:value={countryCode}
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                >
                  {#each countryCodes as country}
                    <option value={country.code}>
                      {country.flag} {country.code} ({country.country})
                    </option>
                  {/each}
                </select>
              </div>

              <div>
                <label for="phone" class="block text-gray-300 text-sm font-medium mb-2">Phone Number</label>
                <div class="flex space-x-2">
                  <div class="w-20 px-3 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-center text-gray-300">
                    {countryCode}
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    bind:value={phoneNumber}
                    placeholder="1234567890"
                    required
                    class="flex-1 px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg btn-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Sending Code...
                {:else}
                  <i class="fas fa-paper-plane mr-2"></i>
                  Send Verification Code
                {/if}
              </button>
            </form>
          {/if}

          <!-- OTP Step -->
          {#if step === 'otp'}
            <div class="mb-6">
              <button on:click={goBack} class="text-[#D4AF37] hover:text-[#F2D675] transition-colors mb-4">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
              </button>
              <h2 class="text-2xl font-bold mb-2 gold-gradient">Enter Verification Code</h2>
              <p class="text-gray-400 text-sm">We sent a 6-digit code to {countryCode}{phoneNumber}</p>
            </div>

            <form on:submit|preventDefault={verifyOTP} class="space-y-6">
              <div>
                <label for="otp" class="block text-gray-300 text-sm font-medium mb-2">Verification Code</label>
                <input
                  type="text"
                  id="otp"
                  bind:value={otpCode}
                  placeholder="123456"
                  maxlength="6"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg btn-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Verifying...
                {:else}
                  <i class="fas fa-check mr-2"></i>
                  Verify Code
                {/if}
              </button>
            </form>
          {/if}

          <!-- Profile Step -->
          {#if step === 'profile'}
            <div class="mb-6">
              <button on:click={goBack} class="text-[#D4AF37] hover:text-[#F2D675] transition-colors mb-4">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
              </button>
              <h2 class="text-2xl font-bold mb-2 gold-gradient">Create Your Profile</h2>
              <p class="text-gray-400 text-sm">Tell us a bit about yourself</p>
            </div>

            <form on:submit|preventDefault={completeProfile} class="space-y-6">
              <div>
                <label for="name" class="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
                <input
                  type="text"
                  id="name"
                  bind:value={name}
                  placeholder="John Doe"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <div>
                <label for="username" class="block text-gray-300 text-sm font-medium mb-2">Username</label>
                <input
                  type="text"
                  id="username"
                  bind:value={username}
                  placeholder="johndoe"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg btn-hover disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Creating Profile...
                {:else}
                  <i class="fas fa-user-plus mr-2"></i>
                  Complete Setup
                {/if}
              </button>
            </form>
          {/if}
        {/if}
      </div>

      <!-- Footer -->
      <div class="text-center mt-8">
        <p class="text-gray-400 text-sm">
          By continuing, you agree to our 
          <a href="/terms" class="text-[#D4AF37] hover:text-[#F2D675] transition-colors">Terms</a> and 
          <a href="/privacy" class="text-[#D4AF37] hover:text-[#F2D675] transition-colors">Privacy Policy</a>
        </p>
      </div>
    </div>
  </div>
</div>
