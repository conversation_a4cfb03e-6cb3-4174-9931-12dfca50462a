<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { dev } from '$app/environment';
  import { browser } from '$app/environment';
  
  let step: 'phone' | 'otp' | 'profile' = 'phone';
  let countryCode = '+1';
  
  // Development mode auto-login
  async function handleDevLogin() {
    if (!browser) return;
    
    try {
      // Create a mock user object that matches the chat page's expected format
      const mockUser = {
        id: 'user1', // Must match the mock data in chat page
        uid: 'dev-user-123',
        email: '<EMAIL>',
        displayName: 'Developer',
        phoneNumber: '+15551234567',
        emailVerified: true,
        name: 'Developer',
        handle: 'dev'
      };
      
      // Store user in localStorage with the key expected by the chat page
      localStorage.setItem('nexuspay_user', JSON.stringify(mockUser));
      
      // Add a mock token
      localStorage.setItem('nexuspay_token', 'dev-mock-token-12345');
      
      // Redirect to chat
      goto('/chat');
    } catch (err) {
      console.error('Dev login error:', err);
      error = 'Development login failed: ' + (err as Error).message;
    }
  }

  let phoneNumber = '';
  let otp = '';
  let name = '';
  let handle = '';
  let isLoading = false;
  let error = '';
  let countdown = 0;
  let countdownInterval: NodeJS.Timeout;
  let developmentOtp = ''; // For development testing

  // Popular country codes
  const countryCodes = [
    { code: '+1', country: 'US/CA', flag: '🇺🇸' },
    { code: '+44', country: 'UK', flag: '🇬🇧' },
    { code: '+34', country: 'Spain', flag: '🇪🇸' },
    { code: '+52', country: 'Mexico', flag: '🇲🇽' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+39', country: 'Italy', flag: '🇮🇹' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' }
  ];
  
  // Format phone number as user types
  function formatPhoneNumber(value: string) {
    const cleaned = value.replace(/\D/g, '');
    if (countryCode === '+1') {
      // US/Canada format
      const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
      if (match) {
        return `(${match[1]}) ${match[2]}-${match[3]}`;
      }
    }
    return value;
  }

  function handlePhoneInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const formatted = formatPhoneNumber(target.value);
    phoneNumber = formatted;
  }

  // Get full phone number
  $: fullPhoneNumber = countryCode + phoneNumber.replace(/\D/g, '');
  
  async function sendOTP() {
    if (!phoneNumber) {
      error = 'Please enter your phone number';
      return;
    }

    isLoading = true;
    error = '';

    try {
      const response = await fetch('/api/auth/send-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ phone: fullPhoneNumber })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        step = 'otp';
        startCountdown();
        // For development, show the OTP
        if (data.developmentOtp) {
          developmentOtp = data.developmentOtp;
        }
      } else {
        error = data.error || 'Failed to send OTP';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      isLoading = false;
    }
  }
  
  function startCountdown() {
    countdown = 60;
    countdownInterval = setInterval(() => {
      countdown--;
      if (countdown <= 0) {
        clearInterval(countdownInterval);
      }
    }, 1000);
  }
  
  async function verifyOTP() {
    if (!otp || otp.length !== 6) {
      error = 'Please enter the 6-digit code';
      return;
    }
    
    isLoading = true;
    error = '';
    
    try {
      const response = await fetch('/api/auth/verify-otp', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          phone: fullPhoneNumber,
          otp,
          userData: name && handle ? { name, handle } : null
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        if (data.token) {
          localStorage.setItem('boguani_token', data.token);
        }
        if (data.user) {
          localStorage.setItem('boguani_user', JSON.stringify(data.user));
          localStorage.setItem('boguani_login_time', Date.now().toString());
          goto('/chat');
        } else {
          step = 'profile';
        }
      } else {
        if (data.error === 'User not found. Please provide user data for registration.') {
          if (data.token) {
            localStorage.setItem('boguani_token', data.token);
          }
          step = 'profile';
        } else {
          error = data.error || 'Invalid code';
        }
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      isLoading = false;
    }
  }
  
  async function createProfile() {
    if (!name || !handle) {
      error = 'Please fill in all fields';
      return;
    }
    
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(handle)) {
      error = 'Handle must be 3-20 characters, letters, numbers, and underscores only';
      return;
    }
    try {
      // Use the JWT token from localStorage for authentication
      const token = localStorage.getItem('boguani_token');
      if (!token) {
        error = 'Session expired. Please verify your phone again.';
        isLoading = false;
        step = 'phone';
        return;
      }
      const response = await fetch('/api/auth/complete-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          name,
          handle
        })
      });
      const data = await response.json();
      if (response.ok) {
        localStorage.setItem('boguani_user', JSON.stringify(data.user));
        localStorage.setItem('boguani_login_time', Date.now().toString());
        goto('/terms');
      } else {
        error = data.error || 'Failed to create profile';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      isLoading = false;
    }
  }
  
  function goBack() {
    if (step === 'otp') {
      step = 'phone';
      clearInterval(countdownInterval);
    } else if (step === 'profile') {
      step = 'otp';
    }
    error = '';
  }
  
  onMount(() => {
    return () => {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
    };
  });
</script>

<svelte:head>
  <title>Login - BoGuani</title>
</svelte:head>

<div class="min-h-screen bg-gradient-main flex items-center justify-center p-6">
  <!-- Background Effects -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute top-20 left-10 w-20 h-20 bg-gold bg-opacity-20 rounded-full animate-bounce"></div>
    <div class="absolute top-40 right-20 w-16 h-16 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
    <div class="absolute bottom-40 left-20 w-12 h-12 bg-gold-light bg-opacity-30 rounded-full animate-bounce" style="animation-delay: 1s"></div>
    <div class="absolute bottom-20 right-10 w-24 h-24 bg-primary-light bg-opacity-20 rounded-full animate-pulse" style="animation-delay: 2s"></div>
  </div>
  
  <!-- Auth Card -->
  <div class="relative z-10 w-full max-w-md">
    <div class="glass-effect rounded-3xl p-8 border border-white border-opacity-20">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gold bg-opacity-20 backdrop-blur-md border border-gold border-opacity-30 mb-6">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gold" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h1 class="text-3xl font-bold text-white mb-2 font-serif">
          Welcome to <span class="text-gold">BoGuani</span>
        </h1>
        <p class="text-gray-300">
          {#if step === 'phone'}
            Enter your phone number to get started
          {:else if step === 'otp'}
            Enter the verification code sent to {fullPhoneNumber}
          {:else}
            Create your profile to complete setup
          {/if}
        </p>
      </div>
      
      <!-- Error Message -->
      {#if error}
        <div class="bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-3 mb-6">
          <p class="text-red-200 text-sm">{error}</p>
        </div>
      {/if}
      
      <!-- Phone Step -->
      {#if step === 'phone'}
        <form on:submit|preventDefault={sendOTP} class="space-y-6">
          <div>
            <label for="phone" class="block text-gray-300 text-sm font-medium mb-2">
              Phone Number
            </label>

            <!-- Country Code Selection -->
            <div class="mb-3">
              <label for="countryCode" class="block text-gray-400 text-xs mb-1">Country</label>
              <select
                id="countryCode"
                bind:value={countryCode}
                class="w-full px-3 py-2 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white text-sm"
              >
                {#each countryCodes as country}
                  <option value={country.code}>
                    {country.flag} {country.code} ({country.country})
                  </option>
                {/each}
              </select>
            </div>

            <!-- Phone Number Input -->
            <div class="flex space-x-2">
              <div class="w-20">
                <input
                  type="text"
                  value={countryCode}
                  disabled
                  class="w-full px-3 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white text-center"
                />
              </div>
              <input
                id="phone"
                type="tel"
                bind:value={phoneNumber}
                on:input={handlePhoneInput}
                placeholder="(*************"
                class="flex-1 px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white placeholder-gray-400"
                required
                disabled={isLoading}
              />
            </div>

            <!-- Full Number Preview -->
            <p class="text-xs text-gray-400 mt-2">
              Full number: {fullPhoneNumber}
            </p>
            <p class="text-xs text-gold mt-1">
              📱 SMS will be sent to this number for verification
            </p>
          </div>
          
          <button
            type="submit"
            disabled={isLoading || !phoneNumber}
            class="w-full py-3 bg-gold hover:bg-gold-dark text-dark-dark font-bold rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {#if isLoading}
              <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending Code...
            {:else}
              Send Verification Code
            {/if}
          </button>
        </form>
      {/if}
      
      <!-- OTP Step -->
      {#if step === 'otp'}
        <form on:submit|preventDefault={verifyOTP} class="space-y-6">
          <div>
            <label for="otp" class="block text-gray-300 text-sm font-medium mb-2">
              Verification Code
            </label>
            <input
              id="otp"
              type="text"
              bind:value={otp}
              placeholder="123456"
              maxlength="6"
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white placeholder-gray-400 text-center text-2xl tracking-widest"
              required
              disabled={isLoading}
            />

            <!-- Development Helper -->
            {#if developmentOtp}
              <div class="mt-3 p-3 bg-gold bg-opacity-20 border border-gold rounded-lg">
                <p class="text-gold text-sm font-medium">🔧 Development Mode</p>
                <p class="text-white text-lg font-mono">{developmentOtp}</p>
                <p class="text-gold text-xs mt-1">Use this code above ↑</p>
              </div>
            {/if}
          </div>
          
          <button
            type="submit"
            disabled={isLoading || otp.length !== 6}
            class="w-full py-3 bg-gold hover:bg-gold-dark text-dark-dark font-bold rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {#if isLoading}
              <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Verifying...
            {:else}
              Verify Code
            {/if}
          </button>
          
          <div class="text-center">
            {#if countdown > 0}
              <p class="text-gray-400 text-sm">Resend code in {countdown}s</p>
            {:else}
              <button
                type="button"
                on:click={sendOTP}
                class="text-gold hover:text-gold-light text-sm font-medium"
              >
                Resend Code
              </button>
            {/if}
          </div>
          
          <button
            type="button"
            on:click={goBack}
            class="w-full py-2 text-gray-400 hover:text-white text-sm"
          >
            ← Change Phone Number
          </button>
        </form>
      {/if}
      
      <!-- Profile Step -->
      {#if step === 'profile'}
        <form on:submit|preventDefault={createProfile} class="space-y-6">
          <div>
            <label for="name" class="block text-gray-300 text-sm font-medium mb-2">
              Full Name
            </label>
            <input
              id="name"
              type="text"
              bind:value={name}
              placeholder="John Doe"
              class="w-full px-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white placeholder-gray-400"
              required
              disabled={isLoading}
            />
          </div>
          
          <div>
            <label for="handle" class="block text-gray-300 text-sm font-medium mb-2">
              Username
            </label>
            <div class="relative">
              <span class="absolute left-4 top-3 text-gray-400">@</span>
              <input
                id="handle"
                type="text"
                bind:value={handle}
                placeholder="johndoe"
                class="w-full pl-8 pr-4 py-3 bg-dark-light bg-opacity-50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent text-white placeholder-gray-400"
                required
                disabled={isLoading}
              />
            </div>
            <p class="text-gray-400 text-xs mt-1">3-20 characters, letters, numbers, and underscores only</p>
          </div>
          
          <button
            type="submit"
            disabled={isLoading || !name || !handle}
            class="w-full py-3 bg-gold hover:bg-gold-dark text-dark-dark font-bold rounded-lg transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {#if isLoading}
              <svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Profile...
            {:else}
              Complete Setup
            {/if}
          </button>
          
          <button
            type="button"
            on:click={goBack}
            class="w-full py-2 text-gray-400 hover:text-white text-sm"
          >
            ← Back
          </button>
        </form>
      {/if}
      
      <!-- Development Login Button (only shows in dev mode) -->
      {#if dev}
        <div class="mt-8 pt-6 border-t border-gray-700">
          <p class="text-center text-gray-400 text-sm mb-4">Development Tools</p>
          <button
            type="button"
            on:click={handleDevLogin}
            class="w-full py-3 px-4 bg-gray-800 hover:bg-gray-700 text-red-400 font-medium rounded-xl border border-red-900/50 hover:border-red-500/50 transition-all duration-200 flex items-center justify-center gap-2"
            aria-label="Development login (bypasses phone verification)"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Development Mode
          </button>
          <p class="mt-2 text-xs text-center text-gray-500">This button is only visible in development</p>
        </div>
      {/if}

      <!-- Footer -->
      <div class="mt-8 text-center">
        <p class="text-gray-400 text-xs">
          By continuing, you agree to our
          <button class="text-gold hover:text-gold-light underline" on:click={() => goto('/terms')}>Terms of Service</button>
          and
          <button class="text-gold hover:text-gold-light underline">Privacy Policy</button>
        </p>
      </div>
    </div>
  </div>
</div>
