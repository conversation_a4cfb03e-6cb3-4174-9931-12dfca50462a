<script lang="ts">
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { dev } from '$app/environment';
  import { browser } from '$app/environment';

  let step: 'phone' | 'otp' | 'profile' = 'phone';
  let countryCode = '+1';
  let phoneNumber = '';
  let otpCode = '';
  let name = '';
  let username = '';
  let error = '';
  let loading = false;
  let success = false;

  const countryCodes = [
    { code: '+1', country: 'United States', flag: '🇺🇸' },
    { code: '+1', country: 'Canada', flag: '🇨🇦' },
    { code: '+44', country: 'United Kingdom', flag: '🇬🇧' },
    { code: '+33', country: 'France', flag: '🇫🇷' },
    { code: '+49', country: 'Germany', flag: '🇩🇪' },
    { code: '+81', country: 'Japan', flag: '🇯🇵' },
    { code: '+86', country: 'China', flag: '🇨🇳' },
    { code: '+91', country: 'India', flag: '🇮🇳' },
    { code: '+55', country: 'Brazil', flag: '🇧🇷' },
    { code: '+61', country: 'Australia', flag: '🇦🇺' }
  ];

  // Development mode auto-login
  async function handleDevLogin() {
    if (!dev) return;
    
    loading = true;
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Store mock user data
    if (browser) {
      localStorage.setItem('boguani_user', JSON.stringify({
        id: 'user1',
        uid: 'dev-user-123',
        email: '<EMAIL>',
        displayName: 'Developer',
        phoneNumber: '+1234567890',
        name: 'Developer User',
        username: 'developer'
      }));
    }
    
    loading = false;
    success = true;
    
    setTimeout(() => {
      goto('/chat');
    }, 1500);
  }

  async function sendOTP() {
    if (!phoneNumber.trim()) {
      error = 'Please enter your phone number';
      return;
    }

    loading = true;
    error = '';

    try {
      // Simulate OTP sending
      await new Promise(resolve => setTimeout(resolve, 2000));
      step = 'otp';
      loading = false;
    } catch (err) {
      error = 'Failed to send verification code. Please try again.';
      loading = false;
    }
  }

  async function verifyOTP() {
    if (!otpCode.trim() || otpCode.length !== 6) {
      error = 'Please enter a valid 6-digit code';
      return;
    }

    loading = true;
    error = '';

    try {
      // Simulate OTP verification
      await new Promise(resolve => setTimeout(resolve, 1500));
      step = 'profile';
      loading = false;
    } catch (err) {
      error = 'Invalid verification code. Please try again.';
      loading = false;
    }
  }

  async function completeProfile() {
    if (!name.trim() || !username.trim()) {
      error = 'Please fill in all fields';
      return;
    }

    loading = true;
    error = '';

    try {
      // Simulate profile creation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Store user data
      if (browser) {
        localStorage.setItem('boguani_user', JSON.stringify({
          id: 'user1',
          uid: 'user-' + Date.now(),
          phoneNumber: countryCode + phoneNumber,
          name: name,
          username: username,
          displayName: name
        }));
      }
      
      loading = false;
      success = true;
      
      setTimeout(() => {
        goto('/chat');
      }, 1500);
    } catch (err) {
      error = 'Failed to create profile. Please try again.';
      loading = false;
    }
  }

  function goBack() {
    if (step === 'otp') {
      step = 'phone';
    } else if (step === 'profile') {
      step = 'otp';
    }
    error = '';
  }

  onMount(() => {
    // Check if user is already logged in
    if (browser) {
      const userData = localStorage.getItem('boguani_user');
      if (userData) {
        goto('/chat');
      }
    }
  });
</script>

<svelte:head>
  <title>Sign In - BoGuani</title>
  <meta name="description" content="Sign in to BoGuani - Secure messaging and value transfer platform" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute top-20 left-20 w-32 h-32 bg-[#D4AF37] opacity-5 rounded-full blur-3xl animate-pulse"></div>
  <div class="absolute bottom-20 right-20 w-40 h-40 bg-[#D4AF37] opacity-3 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
  <div class="absolute top-1/2 left-1/4 w-24 h-24 bg-[#D4AF37] opacity-4 rounded-full blur-2xl animate-pulse" style="animation-delay: 4s;"></div>
  <div class="absolute bottom-1/3 right-1/3 w-16 h-16 bg-[#D4AF37] opacity-6 rounded-full blur-xl animate-bounce" style="animation-delay: 6s;"></div>
  
  <!-- Background pattern -->
  <div class="absolute inset-0 opacity-5" style="background-image: radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%), radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%);"></div>

  <div class="relative z-10 min-h-screen flex items-center justify-center p-6">
    <div class="w-full max-w-md">
      <!-- Header -->
      <div class="text-center mb-12">
        <a href="/" class="inline-block">
          <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center shadow-2xl hover:scale-105 transition-transform duration-300">
            <i class="fas fa-comment-dollar text-3xl text-[#2D1B4E]"></i>
          </div>
        </a>
        <h1 class="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Welcome to BoGuani
        </h1>
        <p class="text-xl text-gray-300 mb-2">Messenger of Value</p>
        <p class="text-gray-400 italic">"Speak Gold. Share Value."</p>
      </div>

      <!-- Auth Card -->
      <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 shadow-2xl">
        
        {#if success}
          <!-- Success State -->
          <div class="text-center py-8">
            <div class="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
              <i class="fas fa-check text-2xl text-[#2D1B4E]"></i>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-[#F2D675]">Welcome to BoGuani!</h2>
            <p class="text-gray-300 mb-6">Your account has been created successfully.</p>
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#D4AF37] mx-auto"></div>
            <p class="text-sm text-gray-400 mt-4">Redirecting to chat...</p>
          </div>
        {:else}
          <!-- Step Indicator -->
          <div class="flex justify-center mb-8">
            <div class="flex space-x-4">
              <div class="w-3 h-3 rounded-full {step === 'phone' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
              <div class="w-3 h-3 rounded-full {step === 'otp' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
              <div class="w-3 h-3 rounded-full {step === 'profile' ? 'bg-[#D4AF37]' : 'bg-gray-600'}"></div>
            </div>
          </div>

          <!-- Error Message -->
          {#if error}
            <div class="bg-red-900 bg-opacity-50 border border-red-500 rounded-lg p-4 mb-6">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-400 mr-3"></i>
                <p class="text-red-200 text-sm">{error}</p>
              </div>
            </div>
          {/if}

          <!-- Development Mode Button -->
          {#if dev}
            <div class="mb-6">
              <button
                on:click={handleDevLogin}
                disabled={loading}
                class="w-full bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-full font-semibold hover:from-green-700 hover:to-green-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Signing in...
                {:else}
                  <i class="fas fa-code mr-2"></i>
                  Dev Mode - Quick Login
                {/if}
              </button>
              <div class="flex items-center my-4">
                <div class="flex-1 border-t border-gray-600"></div>
                <span class="px-4 text-gray-400 text-sm">or</span>
                <div class="flex-1 border-t border-gray-600"></div>
              </div>
            </div>
          {/if}

          <!-- Phone Step -->
          {#if step === 'phone'}
            <div class="mb-6">
              <h2 class="text-2xl font-bold mb-2 text-[#F2D675]">Enter Your Phone</h2>
              <p class="text-gray-400 text-sm">We'll send you a verification code</p>
            </div>

            <form on:submit|preventDefault={sendOTP} class="space-y-6">
              <div>
                <label for="countryCode" class="block text-gray-300 text-sm font-medium mb-2">Country</label>
                <select
                  id="countryCode"
                  bind:value={countryCode}
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                >
                  {#each countryCodes as country}
                    <option value={country.code}>
                      {country.flag} {country.code} ({country.country})
                    </option>
                  {/each}
                </select>
              </div>

              <div>
                <label for="phone" class="block text-gray-300 text-sm font-medium mb-2">Phone Number</label>
                <div class="flex space-x-2">
                  <div class="w-20 px-3 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-center text-gray-300">
                    {countryCode}
                  </div>
                  <input
                    type="tel"
                    id="phone"
                    bind:value={phoneNumber}
                    placeholder="1234567890"
                    required
                    class="flex-1 px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Sending Code...
                {:else}
                  <i class="fas fa-paper-plane mr-2"></i>
                  Send Verification Code
                {/if}
              </button>
            </form>
          {/if}

          <!-- OTP Step -->
          {#if step === 'otp'}
            <div class="mb-6">
              <button on:click={goBack} class="text-[#D4AF37] hover:text-[#F2D675] transition-colors mb-4">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
              </button>
              <h2 class="text-2xl font-bold mb-2 text-[#F2D675]">Enter Verification Code</h2>
              <p class="text-gray-400 text-sm">We sent a 6-digit code to {countryCode}{phoneNumber}</p>
            </div>

            <form on:submit|preventDefault={verifyOTP} class="space-y-6">
              <div>
                <label for="otp" class="block text-gray-300 text-sm font-medium mb-2">Verification Code</label>
                <input
                  type="text"
                  id="otp"
                  bind:value={otpCode}
                  placeholder="123456"
                  maxlength="6"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white text-center text-2xl tracking-widest placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Verifying...
                {:else}
                  <i class="fas fa-check mr-2"></i>
                  Verify Code
                {/if}
              </button>
            </form>
          {/if}

          <!-- Profile Step -->
          {#if step === 'profile'}
            <div class="mb-6">
              <button on:click={goBack} class="text-[#D4AF37] hover:text-[#F2D675] transition-colors mb-4">
                <i class="fas fa-arrow-left mr-2"></i>
                Back
              </button>
              <h2 class="text-2xl font-bold mb-2 text-[#F2D675]">Create Your Profile</h2>
              <p class="text-gray-400 text-sm">Tell us a bit about yourself</p>
            </div>

            <form on:submit|preventDefault={completeProfile} class="space-y-6">
              <div>
                <label for="name" class="block text-gray-300 text-sm font-medium mb-2">Full Name</label>
                <input
                  type="text"
                  id="name"
                  bind:value={name}
                  placeholder="John Doe"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <div>
                <label for="username" class="block text-gray-300 text-sm font-medium mb-2">Username</label>
                <input
                  type="text"
                  id="username"
                  bind:value={username}
                  placeholder="johndoe"
                  required
                  class="w-full px-4 py-3 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
                />
              </div>

              <button
                type="submit"
                disabled={loading}
                class="w-full bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-6 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {#if loading}
                  <i class="fas fa-spinner fa-spin mr-2"></i>
                  Creating Profile...
                {:else}
                  <i class="fas fa-user-plus mr-2"></i>
                  Complete Setup
                {/if}
              </button>
            </form>
          {/if}
        {/if}
      </div>

      <!-- Footer -->
      <div class="text-center mt-8">
        <p class="text-gray-400 text-sm">
          By continuing, you agree to our 
          <a href="/terms" class="text-[#D4AF37] hover:text-[#F2D675] transition-colors">Terms</a> and 
          <a href="/privacy" class="text-[#D4AF37] hover:text-[#F2D675] transition-colors">Privacy Policy</a>
        </p>
      </div>
    </div>
  </div>
</div>
