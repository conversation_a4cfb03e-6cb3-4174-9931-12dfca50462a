<script lang="ts">
  let searchQuery = '';
  let selectedCategory = 'all';
  
  const faqs = [
    {
      category: 'general',
      question: 'What is BoGuani?',
      answer: 'BoGuani is a secure messaging platform that allows you to send messages and transfer value instantly. Inspired by ancient Taíno wisdom, we combine communication with value exchange.'
    },
    {
      category: 'security',
      question: 'How secure is BoGuani?',
      answer: 'BoGuani uses 256-bit end-to-end encryption to protect all your messages and transactions. Your data is secured with military-grade encryption that ensures only you and your recipient can access your communications.'
    },
    {
      category: 'payments',
      question: 'How do money transfers work?',
      answer: 'You can send money instantly to anyone in your contacts through our secure payment system. Simply select the amount, add a message, and send. The recipient will receive both your message and the payment instantly.'
    },
    {
      category: 'general',
      question: 'Is BoGuani free to use?',
      answer: 'Yes, BoGuani is free to download and use for messaging. We charge a small fee for money transfers to cover processing costs and maintain our secure infrastructure.'
    },
    {
      category: 'technical',
      question: 'What devices does BoGuani support?',
      answer: 'BoGuani is available as a web application now, with mobile apps for iOS and Android coming in Q2 2024, and desktop applications for Windows, Mac, and Linux coming in Q3 2024.'
    },
    {
      category: 'security',
      question: 'Can BoGuani see my messages?',
      answer: 'No, we cannot see your messages. All communications are end-to-end encrypted, meaning only you and your recipient have the keys to decrypt and read your messages.'
    }
  ];

  $: filteredFaqs = faqs.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });
</script>

<svelte:head>
  <title>Support - BoGuani</title>
  <meta name="description" content="Get help with BoGuani. Find answers to frequently asked questions and learn how to use our secure messaging platform." />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-[#1E1E24] via-[#2D1B4E] to-[#4A1A5C] text-white">
  <!-- Navigation -->
  <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
    <div class="container mx-auto px-6 py-4 flex justify-between items-center">
      <div class="flex items-center">
        <a href="/" class="flex items-center">
          <div class="text-[#D4AF37] text-3xl mr-3">
            <i class="fas fa-comment-dollar"></i>
          </div>
          <span class="font-bold text-2xl bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">BoGuani</span>
        </a>
      </div>
      <div class="flex space-x-6">
        <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
        <a href="/contact" class="hover:text-[#D4AF37] transition-colors">Contact</a>
        <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="pt-24 pb-16 px-6">
    <div class="container mx-auto max-w-6xl">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-[#D4AF37] to-[#F2D675] bg-clip-text text-transparent">
          Support Center
        </h1>
        <p class="text-xl text-gray-300 max-w-2xl mx-auto">
          Find answers to your questions and get help with BoGuani
        </p>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
        <a href="/contact" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all">
          <i class="fas fa-envelope text-4xl text-[#D4AF37] mb-4"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">Contact Support</h3>
          <p class="text-gray-300">Get personalized help from our team</p>
        </a>

        <a href="/auth" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all">
          <i class="fas fa-rocket text-4xl text-[#D4AF37] mb-4"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">Get Started</h3>
          <p class="text-gray-300">Start using BoGuani right now</p>
        </a>

        <a href="/downloads" class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-6 rounded-2xl border border-[#D4AF37] border-opacity-30 text-center hover:border-opacity-50 transition-all">
          <i class="fas fa-download text-4xl text-[#D4AF37] mb-4"></i>
          <h3 class="text-xl font-bold mb-2 text-[#F2D675]">Download Apps</h3>
          <p class="text-gray-300">Get BoGuani on your device</p>
        </a>
      </div>

      <!-- Search and Filter -->
      <div class="mb-12">
        <div class="max-w-2xl mx-auto mb-8">
          <div class="relative">
            <input
              type="text"
              bind:value={searchQuery}
              placeholder="Search for help..."
              class="w-full px-6 py-4 pl-12 bg-[#2D1B4E] bg-opacity-50 border border-[#D4AF37] border-opacity-30 rounded-full text-white placeholder-gray-400 focus:outline-none focus:border-[#D4AF37] focus:ring-1 focus:ring-[#D4AF37]"
            />
            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#D4AF37]"></i>
          </div>
        </div>

        <div class="flex justify-center space-x-4 flex-wrap">
          <button
            on:click={() => selectedCategory = 'all'}
            class="px-6 py-2 rounded-full font-semibold transition-all {selectedCategory === 'all' ? 'bg-[#D4AF37] text-[#2D1B4E]' : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37]'}"
          >
            All
          </button>
          <button
            on:click={() => selectedCategory = 'general'}
            class="px-6 py-2 rounded-full font-semibold transition-all {selectedCategory === 'general' ? 'bg-[#D4AF37] text-[#2D1B4E]' : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37]'}"
          >
            General
          </button>
          <button
            on:click={() => selectedCategory = 'security'}
            class="px-6 py-2 rounded-full font-semibold transition-all {selectedCategory === 'security' ? 'bg-[#D4AF37] text-[#2D1B4E]' : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37]'}"
          >
            Security
          </button>
          <button
            on:click={() => selectedCategory = 'payments'}
            class="px-6 py-2 rounded-full font-semibold transition-all {selectedCategory === 'payments' ? 'bg-[#D4AF37] text-[#2D1B4E]' : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37]'}"
          >
            Payments
          </button>
          <button
            on:click={() => selectedCategory = 'technical'}
            class="px-6 py-2 rounded-full font-semibold transition-all {selectedCategory === 'technical' ? 'bg-[#D4AF37] text-[#2D1B4E]' : 'bg-[#2D1B4E] bg-opacity-50 text-gray-300 hover:text-[#D4AF37]'}"
          >
            Technical
          </button>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold mb-8 text-center text-[#F2D675]">Frequently Asked Questions</h2>
        
        {#if filteredFaqs.length === 0}
          <div class="text-center py-12">
            <i class="fas fa-search text-6xl text-gray-500 mb-4"></i>
            <p class="text-xl text-gray-400">No results found. Try adjusting your search or category filter.</p>
          </div>
        {:else}
          <div class="space-y-6">
            {#each filteredFaqs as faq}
              <details class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg rounded-2xl border border-[#D4AF37] border-opacity-30 overflow-hidden">
                <summary class="p-6 cursor-pointer hover:bg-opacity-80 transition-all">
                  <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-[#F2D675] pr-4">{faq.question}</h3>
                    <i class="fas fa-chevron-down text-[#D4AF37] transform transition-transform duration-200"></i>
                  </div>
                </summary>
                <div class="px-6 pb-6">
                  <p class="text-gray-300 leading-relaxed">{faq.answer}</p>
                </div>
              </details>
            {/each}
          </div>
        {/if}
      </div>

      <!-- Still Need Help -->
      <div class="text-center mt-16">
        <div class="bg-gradient-to-br from-[#3D2A5F] to-[#2D1B4E] bg-opacity-60 backdrop-blur-lg p-8 rounded-2xl border border-[#D4AF37] border-opacity-30 max-w-2xl mx-auto">
          <h3 class="text-2xl font-bold mb-4 text-[#F2D675]">Still Need Help?</h3>
          <p class="text-gray-300 mb-6">Can't find what you're looking for? Our support team is here to help.</p>
          <a href="/contact" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all inline-block">
            Contact Support
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  details[open] summary i {
    transform: rotate(180deg);
  }
</style>
