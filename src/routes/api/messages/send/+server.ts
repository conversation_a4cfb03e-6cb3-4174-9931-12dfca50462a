import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { env } from '$env/dynamic/private';
import jwt from 'jsonwebtoken';

// In-memory message storage (use database in production)
const messages: { [chatId: string]: any[] } = {};
const connectedUsers = new Map<string, WebSocket>();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    let userId: string;
    
    try {
      const decoded = jwt.verify(token, env.JWT_SECRET || 'boguani-secret-key') as any;
      userId = decoded.userId;
    } catch (error) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const message = await request.json();
    
    // Validate message
    if (!message.chatId || !message.content || !message.type) {
      return json({ error: 'Invalid message format' }, { status: 400 });
    }

    // Add timestamp and status
    message.timestamp = new Date().toISOString();
    message.status = 'sent';
    message.id = message.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Store message
    if (!messages[message.chatId]) {
      messages[message.chatId] = [];
    }
    messages[message.chatId].push(message);

    // Broadcast to connected users in the chat
    // In a real app, you'd get chat participants from database
    broadcastToChat(message.chatId, {
      type: 'message',
      message
    });

    // Simulate SMS sending for real phone numbers
    if (message.type === 'text') {
      await sendSMSNotification(message);
    }

    return json({ 
      success: true, 
      messageId: message.id,
      status: 'sent'
    });

  } catch (error) {
    console.error('Send message error:', error);
    return json({ error: 'Failed to send message' }, { status: 500 });
  }
};

function broadcastToChat(chatId: string, data: any) {
  // In a real app, you'd get chat participants from database
  // For now, broadcast to all connected users
  connectedUsers.forEach((ws, userId) => {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(data));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        connectedUsers.delete(userId);
      }
    }
  });
}

async function sendSMSNotification(message: any) {
  // This would integrate with Twilio or Firebase to send actual SMS
  // For now, just log the notification
  console.log(`📱 SMS notification would be sent for message: ${message.content}`);
  
  // In production, you'd:
  // 1. Get recipient phone numbers from chat participants
  // 2. Check if they have the app installed
  // 3. Send SMS notification if they don't have the app or are offline
  
  try {
    // Example Twilio integration (commented out for demo)
    /*
    const twilioClient = twilio(env.TWILIO_ACCOUNT_SID, env.TWILIO_AUTH_TOKEN);
    
    await twilioClient.messages.create({
      body: `New message from ${message.senderName}: ${message.content}`,
      from: env.TWILIO_PHONE_NUMBER,
      to: recipientPhoneNumber
    });
    */
  } catch (error) {
    console.error('SMS notification failed:', error);
  }
}

// GET endpoint to retrieve messages for a chat
export const GET: RequestHandler = async ({ url, request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    
    try {
      jwt.verify(token, env.JWT_SECRET || 'boguani-secret-key');
    } catch (error) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const chatId = url.searchParams.get('chatId');
    if (!chatId) {
      return json({ error: 'Chat ID required' }, { status: 400 });
    }

    const chatMessages = messages[chatId] || [];
    
    // Sort by timestamp
    chatMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    return json({
      success: true,
      messages: chatMessages
    });

  } catch (error) {
    console.error('Get messages error:', error);
    return json({ error: 'Failed to get messages' }, { status: 500 });
  }
};
