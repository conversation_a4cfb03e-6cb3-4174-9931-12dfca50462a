import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import jwt from 'jsonwebtoken';

// For development, we'll use the same in-memory store
// In production, you'd use your database
const otpStore = new Map<string, { otp: string; expiresAt: Date }>();
const userStore = new Map<string, any>();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone, otp, userData } = await request.json();

    if (!phone || !otp) {
      return json({ error: 'Phone number and OTP are required' }, { status: 400 });
    }

    // Verify OTP
    const storedOtp = otpStore.get(phone);
    if (!storedOtp) {
      return json({ error: 'OTP not found or expired' }, { status: 400 });
    }

    if (storedOtp.expiresAt < new Date()) {
      otpStore.delete(phone);
      return json({ error: 'OTP has expired' }, { status: 400 });
    }

    if (storedOtp.otp !== otp) {
      return json({ error: 'Invalid OTP' }, { status: 400 });
    }

    // OTP is valid, remove it
    otpStore.delete(phone);

    // Check if user exists
    let user = userStore.get(phone);

    if (!user && userData) {
      // Create new user
      if (!userData.handle || !userData.name) {
        return json({ error: 'Handle and name are required for new users' }, { status: 400 });
      }

      // Validate handle format
      const handleRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!handleRegex.test(userData.handle)) {
        return json({ error: 'Invalid handle format' }, { status: 400 });
      }

      user = {
        id: `user_${Date.now()}`,
        phone,
        handle: userData.handle,
        name: userData.name,
        avatar: userData.avatar || null,
        createdAt: new Date(),
        lastSeen: new Date(),
        isOnline: true
      };

      // Store user (in production, save to database)
      userStore.set(phone, user);
      console.log('✅ Created new BoGuani user:', user);
    } else if (!user) {
      return json({ error: 'User not found. Please provide user data for registration.' }, { status: 404 });
    }

    // Create JWT token
    const token = jwt.sign(
      { userId: user.id, phone: user.phone },
      process.env.JWT_SECRET || 'boguani-secret-key',
      { expiresIn: '30d' }
    );

    return json({
      success: true,
      user: {
        id: user.id,
        phone: user.phone,
        handle: user.handle,
        name: user.name,
        avatar: user.avatar
      },
      token,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    });

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
};
