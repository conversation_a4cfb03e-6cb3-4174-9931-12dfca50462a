import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { auth } from '$lib/auth.js';
import { encryption } from '$lib/encryption.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone, otp, userData } = await request.json();

    if (!phone || !otp) {
      return json({ error: 'Phone number and OTP are required' }, { status: 400 });
    }

    const formattedPhone = auth.formatPhoneNumber(phone);

    // Verify OTP
    if (!auth.verifyOTP(formattedPhone, otp)) {
      return json({ error: 'Invalid or expired OTP' }, { status: 400 });
    }

    // Check if user exists
    // In a real app, you'd query the database here
    let user = null; // await getUserByPhone(formattedPhone);

    if (!user && userData) {
      // Create new user
      if (!userData.handle || !userData.name) {
        return json({ error: 'Handle and name are required for new users' }, { status: 400 });
      }

      if (!auth.isValidHandle(userData.handle)) {
        return json({ error: 'Invalid handle format' }, { status: 400 });
      }

      // Generate encryption keys for the user
      const keyPair = await encryption.generateKeyPair();

      user = {
        id: `user_${Date.now()}`,
        phone: formattedPhone,
        handle: userData.handle,
        name: userData.name,
        avatar: userData.avatar || null,
        publicKey: keyPair.publicKey,
        privateKey: keyPair.privateKey,
        createdAt: new Date(),
        lastSeen: new Date(),
        isOnline: true
      };

      // In a real app, you'd save the user to the database here
      console.log('Created new user:', user);
    } else if (!user) {
      return json({ error: 'User not found. Please provide user data for registration.' }, { status: 404 });
    }

    // Create session
    const session = auth.createSession(user.id);

    return json({
      success: true,
      user: {
        id: user.id,
        phone: user.phone,
        handle: user.handle,
        name: user.name,
        avatar: user.avatar,
        publicKey: user.publicKey
      },
      token: session.token,
      expiresAt: session.expiresAt
    });

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
};
