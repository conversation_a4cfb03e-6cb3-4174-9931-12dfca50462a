import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { cert, getApps, initializeApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { env } from '$env/dynamic/private';
import jwt from 'jsonwebtoken';
import twilio from 'twilio';

// 🔥 Initialize Firebase Admin SDK (only once)
if (!getApps().length) {
  try {
    const firebaseAdminKey = env.FIREBASE_ADMIN_KEY_JSON;
    if (firebaseAdminKey) {
      initializeApp({
        credential: cert(JSON.parse(firebaseAdminKey))
      });
      console.log('🔥 Firebase Admin SDK initialized successfully');
    } else {
      console.warn('⚠️ FIREBASE_ADMIN_KEY_JSON not found in environment');
    }
  } catch (error) {
    console.error('❌ Firebase Admin SDK initialization failed:', error);
  }
}

// Get Firebase Admin services
const auth = getAuth();
const db = getFirestore();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { idToken, name, username } = await request.json();

    if (!idToken) {
      return json({ error: 'Firebase ID Token is required' }, { status: 400 });
    }

    if (!name || !username) {
      return json({ error: 'Name and username are required' }, { status: 400 });
    }

    // 🔐 Verify Firebase ID Token (server-side security)
    let decodedToken;
    try {
      decodedToken = await auth.verifyIdToken(idToken);
      console.log(`🔥 Firebase ID Token verified for user: ${decodedToken.uid}`);
      console.log(`📱 Phone number: ${decodedToken.phone_number}`);
    } catch (error) {
      console.error('❌ Firebase ID Token verification failed:', error);
      return json({ error: 'Invalid authentication token' }, { status: 401 });
    }

    const phoneNumber = decodedToken.phone_number;
    if (!phoneNumber) {
      return json({ error: 'No phone number found in authentication token' }, { status: 400 });
    }

    // Validate username format
    const handleRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!handleRegex.test(username)) {
      return json({ error: 'Username must be 3-20 characters, letters, numbers, and underscores only' }, { status: 400 });
    }

    // ✅ Save user to Firestore
    try {
      const userData = {
        phoneNumber,
        name,
        username,
        uid: decodedToken.uid,
        createdAt: new Date().toISOString(),
        lastSeen: new Date().toISOString(),
        isOnline: true
      };

      await db.collection('users').doc(phoneNumber).set(userData);
      console.log(`✅ User saved to Firestore: ${phoneNumber}`);

      // Create JWT token for app sessions
      const token = jwt.sign(
        { userId: decodedToken.uid, phone: phoneNumber },
        env.JWT_SECRET || 'boguani-secret-key',
        { expiresIn: '30d' }
      );

      return json({
        success: true,
        user: {
          id: decodedToken.uid,
          phone: phoneNumber,
          handle: username,
          name: name,
          avatar: null
        },
        token,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      });

    } catch (firestoreError) {
      console.error('❌ Firestore save failed:', firestoreError);
      return json({ error: 'Failed to save user data' }, { status: 500 });
    }

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
};
