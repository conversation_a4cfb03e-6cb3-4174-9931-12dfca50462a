import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { cert, getApps, initializeApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { env } from '$env/dynamic/private';
import jwt from 'jsonwebtoken';
import twilio from 'twilio';

// 🔥 Initialize Firebase Admin SDK (only once)
if (!getApps().length) {
  try {
    const firebaseAdminKey = env.FIREBASE_ADMIN_KEY_JSON;
    if (firebaseAdminKey) {
      initializeApp({
        credential: cert(JSON.parse(firebaseAdminKey))
      });
      console.log('🔥 Firebase Admin SDK initialized successfully');
    } else {
      console.warn('⚠️ FIREBASE_ADMIN_KEY_JSON not found in environment');
    }
  } catch (error) {
    console.error('❌ Firebase Admin SDK initialization failed:', error);
  }
}

// Get Firebase Admin services
const auth = getAuth();
const db = getFirestore();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone, otp, name, username } = await request.json();

    if (!phone || !otp) {
      return json({ error: 'Phone number and OTP are required' }, { status: 400 });
    }

    if (!name || !username) {
      return json({ error: 'Name and username are required' }, { status: 400 });
    }

    // 🔐 Verify OTP with Twilio first
    const TWILIO_ACCOUNT_SID = env.TWILIO_ACCOUNT_SID || '**********************************';
    const TWILIO_AUTH_TOKEN = env.TWILIO_AUTH_TOKEN || '09f5e32c69ee3131b553a3caee157bd8';
    const TWILIO_VERIFY_SERVICE_SID = env.TWILIO_VERIFY_SERVICE_SID || 'VA6038d83be5f3524536dca194a96512a7';

    const twilioClient = TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN
      ? twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
      : null;

    if (!twilioClient || !TWILIO_VERIFY_SERVICE_SID) {
      return json({ error: 'SMS verification service not configured' }, { status: 500 });
    }

    try {
      const verificationCheck = await twilioClient.verify.v2
        .services(TWILIO_VERIFY_SERVICE_SID)
        .verificationChecks
        .create({
          to: phone,
          code: otp
        });

      if (verificationCheck.status !== 'approved') {
        return json({ error: 'Invalid verification code' }, { status: 400 });
      }

      console.log(`✅ Twilio OTP verified for ${phone}`);
    } catch (twilioError) {
      console.error('Twilio verification error:', twilioError);
      return json({ error: 'Verification failed' }, { status: 400 });
    }

    // Validate username format
    const handleRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!handleRegex.test(username)) {
      return json({ error: 'Username must be 3-20 characters, letters, numbers, and underscores only' }, { status: 400 });
    }

    // ✅ Save user to Firestore (if Firebase Admin is configured)
    const userId = `user_${Date.now()}`;

    try {
      // Try to save to Firestore if Firebase Admin is configured
      if (getApps().length > 0) {
        const userData = {
          phoneNumber: phone,
          name,
          username,
          uid: userId,
          createdAt: new Date().toISOString(),
          lastSeen: new Date().toISOString(),
          isOnline: true
        };

        await db.collection('users').doc(phone).set(userData);
        console.log(`✅ User saved to Firestore: ${phone}`);
      } else {
        console.log(`⚠️ Firebase Admin not configured, skipping Firestore save`);
      }

      // Create JWT token for app sessions
      const token = jwt.sign(
        { userId, phone },
        env.JWT_SECRET || 'boguani-secret-key',
        { expiresIn: '30d' }
      );

      return json({
        success: true,
        user: {
          id: userId,
          phone: phone,
          handle: username,
          name: name,
          avatar: null
        },
        token,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      });

    } catch (firestoreError) {
      console.error('❌ Firestore save failed:', firestoreError);

      // Still return success even if Firestore fails
      const token = jwt.sign(
        { userId, phone },
        env.JWT_SECRET || 'boguani-secret-key',
        { expiresIn: '30d' }
      );

      return json({
        success: true,
        user: {
          id: userId,
          phone: phone,
          handle: username,
          name: name,
          avatar: null
        },
        token,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
      });
    }

  } catch (error) {
    console.error('Error verifying OTP:', error);
    return json({ error: 'Failed to verify OTP' }, { status: 500 });
  }
};
