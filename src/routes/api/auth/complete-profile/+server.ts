import { json } from '@sveltejs/kit';
import type { <PERSON>questH<PERSON><PERSON> } from './$types';
import jwt from 'jsonwebtoken';
import { JWT_SECRET } from '$env/static/private';

// Use the same in-memory userStore as auth endpoints
import { userStore } from '../verify-otp/+server';

export const POST: RequestHandler = async ({ request, locals }) => {
  let token = '';
  // Accept token from Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    token = authHeader.substring(7);
  } else {
    return json({ error: 'Missing or invalid authorization.' }, { status: 401 });
  }

  let payload: any;
  try {
    payload = jwt.verify(token, JWT_SECRET);
  } catch (err) {
    return json({ error: 'Invalid or expired token.' }, { status: 401 });
  }

  const { phone } = payload;
  if (!phone) {
    return json({ error: 'Invalid token payload.' }, { status: 400 });
  }

  const body = await request.json();
  const { name, handle } = body;

  if (!name || !handle) {
    return json({ error: 'Name and handle are required.' }, { status: 400 });
  }

  // Check for handle uniqueness
  for (const user of userStore.values()) {
    if (user.handle === handle) {
      return json({ error: 'Handle already taken.' }, { status: 400 });
    }
  }

  // Update or create user profile
  let user = userStore.get(phone);
  if (!user) {
    // Should not happen, but create if missing
    user = { phone };
  }
  user.name = name;
  user.handle = handle;
  userStore.set(phone, user);

  return json({ user });
};
