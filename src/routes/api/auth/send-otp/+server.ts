import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import twilio from 'twilio';
import { env } from '$env/dynamic/private';

// For development, we'll use a simple in-memory store
// In production, you'd use your database
const otpStore = new Map<string, { otp: string; expiresAt: Date }>();

// Twilio Verify Service Configuration
const TWILIO_ACCOUNT_SID = env.TWILIO_ACCOUNT_SID || '**********************************';
const TWILIO_AUTH_TOKEN = env.TWILIO_AUTH_TOKEN || '09f5e32c69ee3131b553a3caee157bd8';
const TWILIO_VERIFY_SERVICE_SID = env.TWILIO_VERIFY_SERVICE_SID || 'VA6038d83be5f3524536dca194a96512a7';

// Initialize Twilio client
console.log('🔧 Twilio Verify Environment Check:');
console.log('TWILIO_ACCOUNT_SID:', TWILIO_ACCOUNT_SID ? 'SET' : 'NOT SET');
console.log('TWILIO_AUTH_TOKEN:', TWILIO_AUTH_TOKEN ? 'SET' : 'NOT SET');
console.log('TWILIO_VERIFY_SERVICE_SID:', TWILIO_VERIFY_SERVICE_SID ? 'SET' : 'NOT SET');

const twilioClient = TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN
  ? twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
  : null;

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone } = await request.json();

    if (!phone) {
      return json({ error: 'Phone number is required' }, { status: 400 });
    }

    // Basic phone validation
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(phone)) {
      return json({ error: 'Invalid phone number format. Please include country code.' }, { status: 400 });
    }

    // Always clear any existing OTP for this phone before generating a new one
    otpStore.delete(phone);

    // Send OTP using Twilio Verify
    if (twilioClient && TWILIO_VERIFY_SERVICE_SID) {
      try {
        const verification = await twilioClient.verify.v2
          .services(TWILIO_VERIFY_SERVICE_SID)
          .verifications
          .create({
            to: phone,
            channel: 'sms'
          });

        // Store a placeholder for fallback even if Twilio succeeds
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
        otpStore.set(phone, { otp, expiresAt });

        console.log(` Twilio Verify SMS sent successfully to ${phone}`);
        console.log(` Verification SID: ${verification.sid}`);
        console.log(` Status: ${verification.status}`);
        console.log(` Fallback OTP for dev/manual verification: ${otp}`);

        return json({
          success: true,
          message: 'Verification code sent successfully',
          phone,
          verificationSid: verification.sid,
          status: verification.status,
          developmentOtp: otp // for dev/test
        });

      } catch (twilioError) {
        console.error('Twilio Verify Error:', twilioError);

        // Fallback to manual OTP generation
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
        otpStore.set(phone, { otp, expiresAt });

        console.log(`🔐 Fallback - BoGuani OTP for ${phone}: ${otp}`);

        return json({
          success: true,
          message: 'OTP sent successfully (fallback mode)',
          phone,
          developmentOtp: otp,
          error: 'Twilio Verify failed, using fallback'
        });
      }
    } else {
      // Development fallback - generate manual OTP
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000);
      otpStore.set(phone, { otp, expiresAt });

      console.log(`🔐 BoGuani OTP for ${phone}: ${otp}`);
      console.log(`📱 Configure Twilio Verify to send real SMS`);

      return json({
        success: true,
        message: 'OTP generated (development mode)',
        phone,
        developmentOtp: otp
      });
    }

  } catch (error) {
    console.error('Error sending OTP:', error);
    return json({ error: 'Failed to send OTP' }, { status: 500 });
  }
};
