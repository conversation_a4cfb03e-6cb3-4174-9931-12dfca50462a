import { json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';

// For development, we'll use a simple in-memory store
// In production, you'd use your database
const otpStore = new Map<string, { otp: string; expiresAt: Date }>();

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone } = await request.json();

    if (!phone) {
      return json({ error: 'Phone number is required' }, { status: 400 });
    }

    // Basic phone validation
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(phone)) {
      return json({ error: 'Invalid phone number format. Please include country code.' }, { status: 400 });
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store OTP (in production, use database)
    otpStore.set(phone, { otp, expiresAt });

    // For development, always log the OTP
    console.log(`🔐 BoGuani OTP for ${phone}: ${otp}`);
    console.log(`📱 This would be sent via SMS in production`);

    // In production with real Twilio:
    /*
    if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
      const twilio = require('twilio');
      const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);

      await client.messages.create({
        body: `Your BoGuani verification code is: ${otp}. This code expires in 10 minutes.`,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone
      });
    }
    */

    return json({
      success: true,
      message: 'OTP sent successfully',
      phone,
      // For development only - remove in production
      developmentOtp: otp
    });

  } catch (error) {
    console.error('Error sending OTP:', error);
    return json({ error: 'Failed to send OTP' }, { status: 500 });
  }
};
