import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import twilio from 'twilio';

// For development, we'll use a simple in-memory store
// In production, you'd use your database
const otpStore = new Map<string, { otp: string; expiresAt: Date }>();

// Initialize Twilio client
console.log('🔧 Twilio Environment Check:');
console.log('TWILIO_ACCOUNT_SID:', process.env.TWILIO_ACCOUNT_SID ? 'SET' : 'NOT SET');
console.log('TWILIO_AUTH_TOKEN:', process.env.TWILIO_AUTH_TOKEN ? 'SET' : 'NOT SET');
console.log('TWILIO_PHONE_NUMBER:', process.env.TWILIO_PHONE_NUMBER || 'NOT SET');

const twilioClient = process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN
  ? twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN)
  : null;

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone } = await request.json();

    if (!phone) {
      return json({ error: 'Phone number is required' }, { status: 400 });
    }

    // Basic phone validation
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(phone)) {
      return json({ error: 'Invalid phone number format. Please include country code.' }, { status: 400 });
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Store OTP (in production, use database)
    otpStore.set(phone, { otp, expiresAt });

    // Send real SMS if Twilio is configured
    if (twilioClient && process.env.TWILIO_PHONE_NUMBER) {
      try {
        await twilioClient.messages.create({
          body: `Your BoGuani verification code is: ${otp}. This code expires in 10 minutes. - Messenger of Value`,
          from: process.env.TWILIO_PHONE_NUMBER,
          to: phone
        });
        console.log(`📱 SMS sent successfully to ${phone}`);
      } catch (twilioError) {
        console.error('Twilio SMS Error:', twilioError);
        // Don't fail the request if SMS fails, just log it
        console.log(`🔐 Fallback - BoGuani OTP for ${phone}: ${otp}`);
      }
    } else {
      // Development fallback - log the OTP
      console.log(`🔐 BoGuani OTP for ${phone}: ${otp}`);
      console.log(`📱 Configure Twilio to send real SMS`);
    }

    return json({
      success: true,
      message: 'OTP sent successfully',
      phone,
      // Only include OTP in development mode when Twilio is not configured
      ...((!twilioClient || !process.env.TWILIO_PHONE_NUMBER) && { developmentOtp: otp })
    });

  } catch (error) {
    console.error('Error sending OTP:', error);
    return json({ error: 'Failed to send OTP' }, { status: 500 });
  }
};
