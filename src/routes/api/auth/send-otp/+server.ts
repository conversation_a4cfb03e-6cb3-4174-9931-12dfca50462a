import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import twilio from 'twilio';
import { env } from '$env/dynamic/private';

// Twilio Verify Service Configuration
const TWILIO_ACCOUNT_SID = env.TWILIO_ACCOUNT_SID || '**********************************';
const TWILIO_AUTH_TOKEN = env.TWILIO_AUTH_TOKEN || '09f5e32c69ee3131b553a3caee157bd8';
const TWILIO_VERIFY_SERVICE_SID = env.TWILIO_VERIFY_SERVICE_SID || 'VA6038d83be5f3524536dca194a96512a7';

// Initialize Twilio client
const twilioClient = TWILIO_ACCOUNT_SID && TWILIO_AUTH_TOKEN
  ? twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
  : null;

console.log('🔧 Twilio Verify Configuration:');
console.log('TWILIO_ACCOUNT_SID:', TWILIO_ACCOUNT_SID ? 'SET' : 'NOT SET');
console.log('TWILIO_AUTH_TOKEN:', TWILIO_AUTH_TOKEN ? 'SET' : 'NOT SET');
console.log('TWILIO_VERIFY_SERVICE_SID:', TWILIO_VERIFY_SERVICE_SID ? 'SET' : 'NOT SET');

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone } = await request.json();

    if (!phone) {
      return json({ error: 'Phone number is required' }, { status: 400 });
    }

    // Basic phone validation
    const phoneRegex = /^\+\d{10,15}$/;
    if (!phoneRegex.test(phone)) {
      return json({ error: 'Invalid phone number format. Please include country code.' }, { status: 400 });
    }

    // Send OTP using Twilio Verify
    if (twilioClient && TWILIO_VERIFY_SERVICE_SID) {
      try {
        const verification = await twilioClient.verify.v2
          .services(TWILIO_VERIFY_SERVICE_SID)
          .verifications
          .create({
            to: phone,
            channel: 'sms'
          });

        console.log(`📱 Twilio Verify SMS sent successfully to ${phone}`);
        console.log(`🔐 Verification SID: ${verification.sid}`);

        return json({
          success: true,
          message: 'Verification code sent successfully',
          phone,
          verificationSid: verification.sid,
          status: verification.status
        });

      } catch (twilioError) {
        console.error('Twilio Verify Error:', twilioError);
        return json({ error: 'Failed to send verification code' }, { status: 500 });
      }
    } else {
      console.error('❌ Twilio not configured properly');
      return json({ error: 'SMS service not configured' }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in send-otp:', error);
    return json({ error: 'Failed to process request' }, { status: 500 });
  }
};
