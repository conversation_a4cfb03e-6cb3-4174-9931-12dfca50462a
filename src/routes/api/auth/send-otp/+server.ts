import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { auth } from '$lib/auth.js';
import twilio from 'twilio';

const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

export const POST: RequestHandler = async ({ request }) => {
  try {
    const { phone } = await request.json();

    if (!phone) {
      return json({ error: 'Phone number is required' }, { status: 400 });
    }

    if (!auth.isValidPhoneNumber(phone)) {
      return json({ error: 'Invalid phone number format' }, { status: 400 });
    }

    const formattedPhone = auth.formatPhoneNumber(phone);
    const otp = auth.generateOTP();

    // Store OTP
    auth.storeOTP(formattedPhone, otp);

    // Send SMS via Twilio
    if (process.env.NODE_ENV === 'production') {
      await twilioClient.messages.create({
        body: `Your NexusPay verification code is: ${otp}`,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: formattedPhone
      });
    } else {
      // In development, log the OTP instead of sending SMS
      console.log(`OTP for ${formattedPhone}: ${otp}`);
    }

    return json({ 
      success: true, 
      message: 'OTP sent successfully',
      phone: formattedPhone 
    });

  } catch (error) {
    console.error('Error sending OTP:', error);
    return json({ error: 'Failed to send OTP' }, { status: 500 });
  }
};
