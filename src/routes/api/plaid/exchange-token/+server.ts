import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { plaid } from '$lib/plaid.js';
import { auth } from '$lib/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const decoded = auth.verifyToken(token);
    
    if (!decoded) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { publicToken } = await request.json();

    if (!publicToken) {
      return json({ error: 'Public token is required' }, { status: 400 });
    }

    const encryptedAccessToken = await plaid.exchangePublicToken(publicToken);
    
    // In a real app, you'd save the encrypted access token to the database
    // associated with the user ID
    console.log('Encrypted access token for user', decoded.userId, ':', encryptedAccessToken);

    // Get account information
    const accounts = await plaid.getAccounts(encryptedAccessToken);

    return json({ 
      success: true, 
      accounts,
      message: 'Bank account connected successfully' 
    });

  } catch (error) {
    console.error('Error exchanging public token:', error);
    return json({ error: 'Failed to connect bank account' }, { status: 500 });
  }
};
