import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { plaid } from '$lib/plaid.js';
import { auth } from '$lib/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    let userId = 'demo_user';

    // For demo purposes, make auth optional
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const decoded = auth.verifyToken(token);
        if (decoded) {
          userId = decoded.userId;
        }
      } catch (error) {
        console.log('Auth failed, using demo user');
      }
    }

    const { publicToken, metadata } = await request.json();

    if (!publicToken) {
      return json({ error: 'Public token is required' }, { status: 400 });
    }

    // For demo, return mock account data
    const accounts = [
      {
        account_id: `acc_${Date.now()}_1`,
        name: 'Demo Checking',
        type: 'depository',
        subtype: 'checking',
        mask: '1234',
        balance: 2500.00
      },
      {
        account_id: `acc_${Date.now()}_2`,
        name: 'Demo Savings',
        type: 'depository',
        subtype: 'savings',
        mask: '5678',
        balance: 10000.00
      }
    ];

    return json({
      success: true,
      accounts,
      accessToken: `access-demo-${userId}-${Date.now()}`,
      institutionName: metadata?.institution?.name || 'Demo Bank',
      message: 'Bank account connected successfully'
    });

  } catch (error) {
    console.error('Error exchanging public token:', error);
    return json({ error: 'Failed to connect bank account' }, { status: 500 });
  }
};
