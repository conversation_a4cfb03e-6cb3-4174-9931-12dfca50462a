import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { plaid } from '$lib/plaid.js';
import { auth } from '$lib/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    const decoded = auth.verifyToken(token);
    
    if (!decoded) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const linkToken = await plaid.createLinkToken(decoded.userId);

    return json({ linkToken });

  } catch (error) {
    console.error('Error creating link token:', error);
    return json({ error: 'Failed to create link token' }, { status: 500 });
  }
};
