import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { plaid } from '$lib/plaid.js';
import { auth } from '$lib/auth.js';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    let userId = 'demo_user';

    // For demo purposes, make auth optional
    if (authHeader?.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      try {
        const decoded = auth.verifyToken(token);
        if (decoded) {
          userId = decoded.userId;
        }
      } catch (error) {
        console.log('Auth failed, using demo user');
      }
    }

    // For demo, return a mock link token
    const linkToken = `link-sandbox-${userId}-${Date.now()}`;

    return json({ linkToken });

  } catch (error) {
    console.error('Error creating link token:', error);
    return json({ error: 'Failed to create link token' }, { status: 500 });
  }
};
