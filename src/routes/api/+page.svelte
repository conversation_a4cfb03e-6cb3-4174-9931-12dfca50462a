<script lang="ts">
  // API documentation content
</script>

<svelte:head>
  <title>API Documentation - BoGuani</title>
  <meta name="description" content="Integrate BoGuani's messaging and payment features into your applications with our comprehensive API documentation." />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous">
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 50%, #4A1A5C 100%);
      font-family: 'Montserrat', sans-serif;
      min-height: 100vh;
    }
    
    .hero-gradient {
      background: linear-gradient(135deg, 
        rgba(30, 30, 36, 0.95) 0%, 
        rgba(45, 27, 78, 0.9) 25%, 
        rgba(61, 42, 95, 0.85) 50%, 
        rgba(78, 58, 112, 0.8) 75%, 
        rgba(45, 27, 78, 0.9) 100%);
    }
    
    .card-gradient {
      background: linear-gradient(135deg, 
        rgba(61, 42, 95, 0.4) 0%, 
        rgba(78, 58, 112, 0.3) 50%, 
        rgba(45, 27, 78, 0.4) 100%);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(212, 175, 55, 0.2);
    }
    
    .gold-gradient {
      background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
    
    .hero-pattern {
      background-image: 
        radial-gradient(circle at 20% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 175, 55, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(212, 175, 55, 0.06) 0%, transparent 50%);
    }
  </style>
</svelte:head>

<div class="hero-pattern min-h-screen text-white">
  <div class="hero-gradient min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gradient-to-r from-[#1E1E24] to-[#2D1B4E] bg-opacity-95 backdrop-blur-md fixed w-full z-10 border-b border-[#D4AF37] border-opacity-20">
      <div class="container mx-auto px-6 py-4 flex justify-between items-center">
        <div class="flex items-center">
          <a href="/" class="flex items-center">
            <div class="text-[#D4AF37] text-3xl mr-3">
              <i class="fas fa-comment-dollar"></i>
            </div>
            <span class="font-bold text-2xl gold-gradient">BoGuani</span>
          </a>
        </div>
        <div class="flex space-x-6">
          <a href="/" class="hover:text-[#D4AF37] transition-colors">Home</a>
          <a href="/guides" class="hover:text-[#D4AF37] transition-colors">Guides</a>
          <a href="/support" class="hover:text-[#D4AF37] transition-colors">Support</a>
          <a href="/auth" class="bg-[#D4AF37] text-[#2D1B4E] px-4 py-2 rounded-full font-semibold hover:bg-[#F2D675] transition-colors">Open Web App</a>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="pt-24 pb-16 px-6">
      <div class="container mx-auto max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-16">
          <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-[#D4AF37] to-[#F2D675] rounded-full flex items-center justify-center">
            <i class="fas fa-code text-3xl text-[#2D1B4E]"></i>
          </div>
          <h1 class="text-5xl md:text-6xl font-bold mb-6 gold-gradient">
            API Documentation
          </h1>
          <p class="text-xl text-gray-300 max-w-3xl mx-auto">
            Integrate BoGuani's secure messaging and payment features into your applications with our powerful API.
          </p>
        </div>

        <!-- Quick Start -->
        <div class="card-gradient p-8 rounded-2xl mb-12">
          <h2 class="text-3xl font-bold mb-6 gold-gradient">Quick Start</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="w-12 h-12 mx-auto mb-4 bg-[#D4AF37] rounded-full flex items-center justify-center">
                <span class="text-[#2D1B4E] font-bold">1</span>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-[#F2D675]">Get API Key</h3>
              <p class="text-gray-300 text-sm">Register your application and get your API credentials</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 mx-auto mb-4 bg-[#D4AF37] rounded-full flex items-center justify-center">
                <span class="text-[#2D1B4E] font-bold">2</span>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-[#F2D675]">Authenticate</h3>
              <p class="text-gray-300 text-sm">Use OAuth 2.0 or API key authentication</p>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 mx-auto mb-4 bg-[#D4AF37] rounded-full flex items-center justify-center">
                <span class="text-[#2D1B4E] font-bold">3</span>
              </div>
              <h3 class="text-lg font-semibold mb-2 text-[#F2D675]">Start Building</h3>
              <p class="text-gray-300 text-sm">Send messages and process payments programmatically</p>
            </div>
          </div>
        </div>

        <!-- API Endpoints -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          <!-- Messaging API -->
          <div class="card-gradient p-6 rounded-2xl">
            <div class="flex items-center mb-4">
              <i class="fas fa-comments text-[#D4AF37] text-2xl mr-3"></i>
              <h3 class="text-2xl font-bold text-[#F2D675]">Messaging API</h3>
            </div>
            <p class="text-gray-300 mb-4">Send and receive encrypted messages programmatically</p>
            <div class="space-y-3">
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-green-400">POST /api/v1/messages</code>
                <p class="text-gray-400 text-sm mt-1">Send a message</p>
              </div>
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-blue-400">GET /api/v1/messages</code>
                <p class="text-gray-400 text-sm mt-1">Retrieve messages</p>
              </div>
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-yellow-400">PUT /api/v1/messages/:id</code>
                <p class="text-gray-400 text-sm mt-1">Update message status</p>
              </div>
            </div>
          </div>

          <!-- Payments API -->
          <div class="card-gradient p-6 rounded-2xl">
            <div class="flex items-center mb-4">
              <i class="fas fa-credit-card text-[#D4AF37] text-2xl mr-3"></i>
              <h3 class="text-2xl font-bold text-[#F2D675]">Payments API</h3>
            </div>
            <p class="text-gray-300 mb-4">Process secure payments and transfers</p>
            <div class="space-y-3">
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-green-400">POST /api/v1/payments</code>
                <p class="text-gray-400 text-sm mt-1">Create payment</p>
              </div>
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-blue-400">GET /api/v1/payments/:id</code>
                <p class="text-gray-400 text-sm mt-1">Get payment status</p>
              </div>
              <div class="bg-[#2D1B4E] bg-opacity-50 p-3 rounded-lg">
                <code class="text-purple-400">POST /api/v1/payments/:id/refund</code>
                <p class="text-gray-400 text-sm mt-1">Refund payment</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Code Examples -->
        <div class="card-gradient p-8 rounded-2xl mb-12">
          <h2 class="text-3xl font-bold mb-6 gold-gradient">Code Examples</h2>
          
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- JavaScript Example -->
            <div>
              <h3 class="text-xl font-semibold mb-4 text-[#F2D675]">JavaScript</h3>
              <div class="bg-[#1E1E24] p-4 rounded-lg overflow-x-auto">
                <pre class="text-sm"><code class="text-gray-300">
<span class="text-blue-400">const</span> <span class="text-yellow-400">boguani</span> = <span class="text-green-400">require</span>(<span class="text-red-400">'boguani-sdk'</span>);

<span class="text-blue-400">const</span> <span class="text-yellow-400">client</span> = <span class="text-blue-400">new</span> <span class="text-green-400">boguani.Client</span>({
  <span class="text-purple-400">apiKey</span>: <span class="text-red-400">'your-api-key'</span>
});

<span class="text-gray-500">// Send a message</span>
<span class="text-blue-400">const</span> <span class="text-yellow-400">message</span> = <span class="text-blue-400">await</span> <span class="text-yellow-400">client</span>.<span class="text-green-400">messages</span>.<span class="text-green-400">send</span>({
  <span class="text-purple-400">to</span>: <span class="text-red-400">'+1234567890'</span>,
  <span class="text-purple-400">text</span>: <span class="text-red-400">'Hello from BoGuani!'</span>
});
                </code></pre>
              </div>
            </div>

            <!-- Python Example -->
            <div>
              <h3 class="text-xl font-semibold mb-4 text-[#F2D675]">Python</h3>
              <div class="bg-[#1E1E24] p-4 rounded-lg overflow-x-auto">
                <pre class="text-sm"><code class="text-gray-300">
<span class="text-blue-400">import</span> <span class="text-yellow-400">boguani</span>

<span class="text-yellow-400">client</span> = <span class="text-yellow-400">boguani</span>.<span class="text-green-400">Client</span>(
    <span class="text-purple-400">api_key</span>=<span class="text-red-400">'your-api-key'</span>
)

<span class="text-gray-500"># Send a payment</span>
<span class="text-yellow-400">payment</span> = <span class="text-yellow-400">client</span>.<span class="text-green-400">payments</span>.<span class="text-green-400">create</span>({
    <span class="text-red-400">'amount'</span>: <span class="text-orange-400">100.00</span>,
    <span class="text-red-400">'currency'</span>: <span class="text-red-400">'USD'</span>,
    <span class="text-red-400">'recipient'</span>: <span class="text-red-400">'+1234567890'</span>
})
                </code></pre>
              </div>
            </div>
          </div>
        </div>

        <!-- SDKs and Libraries -->
        <div class="card-gradient p-8 rounded-2xl mb-12">
          <h2 class="text-3xl font-bold mb-6 gold-gradient">SDKs & Libraries</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div class="text-center">
              <i class="fab fa-js-square text-4xl text-[#D4AF37] mb-3"></i>
              <h3 class="font-semibold text-[#F2D675]">JavaScript</h3>
              <p class="text-gray-400 text-sm">npm install boguani-sdk</p>
            </div>
            <div class="text-center">
              <i class="fab fa-python text-4xl text-[#D4AF37] mb-3"></i>
              <h3 class="font-semibold text-[#F2D675]">Python</h3>
              <p class="text-gray-400 text-sm">pip install boguani</p>
            </div>
            <div class="text-center">
              <i class="fab fa-php text-4xl text-[#D4AF37] mb-3"></i>
              <h3 class="font-semibold text-[#F2D675]">PHP</h3>
              <p class="text-gray-400 text-sm">composer require boguani/sdk</p>
            </div>
            <div class="text-center">
              <i class="fab fa-java text-4xl text-[#D4AF37] mb-3"></i>
              <h3 class="font-semibold text-[#F2D675]">Java</h3>
              <p class="text-gray-400 text-sm">Maven & Gradle</p>
            </div>
          </div>
        </div>

        <!-- Get Started -->
        <div class="text-center">
          <div class="card-gradient p-8 rounded-2xl max-w-2xl mx-auto">
            <h3 class="text-2xl font-bold mb-4 gold-gradient">Ready to Build?</h3>
            <p class="text-gray-300 mb-6">Get your API credentials and start integrating BoGuani today.</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/auth" class="bg-gradient-to-r from-[#D4AF37] to-[#F2D675] text-[#2D1B4E] px-8 py-4 rounded-full font-bold text-lg hover:from-[#F2D675] hover:to-[#D4AF37] transition-all">
                <i class="fas fa-key mr-2"></i>
                Get API Key
              </a>
              <a href="/guides" class="border-2 border-[#D4AF37] text-[#D4AF37] px-8 py-4 rounded-full font-bold text-lg hover:bg-[#D4AF37] hover:text-[#2D1B4E] transition-all">
                <i class="fas fa-book mr-2"></i>
                View Guides
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
