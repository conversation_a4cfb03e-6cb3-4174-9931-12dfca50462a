import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { env } from '$env/dynamic/private';
import jwt from 'jsonwebtoken';

export const POST: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    let userId: string;
    
    try {
      const decoded = jwt.verify(token, env.JWT_SECRET || 'boguani-secret-key') as any;
      userId = decoded.userId;
    } catch (error) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { contacts } = await request.json();

    if (!contacts || !Array.isArray(contacts)) {
      return json({ error: 'Invalid contacts data' }, { status: 400 });
    }

    // Process contacts and check which ones are BoGuani users
    const processedContacts = await processContacts(contacts);

    // In production, you'd save these to the user's contact list in the database
    console.log(`📱 Imported ${processedContacts.length} contacts for user ${userId}`);

    return json({
      success: true,
      contacts: processedContacts,
      boguaniUsers: processedContacts.filter(c => c.isBoGuaniUser).length,
      totalContacts: processedContacts.length
    });

  } catch (error) {
    console.error('Contact import error:', error);
    return json({ error: 'Failed to import contacts' }, { status: 500 });
  }
};

async function processContacts(contacts: any[]) {
  // In production, you'd:
  // 1. Normalize phone numbers
  // 2. Check against your user database to see who has BoGuani
  // 3. Get profile info for existing users
  // 4. Respect privacy settings

  const processedContacts = [];

  for (const contact of contacts) {
    if (!contact.phoneNumbers || contact.phoneNumbers.length === 0) {
      continue;
    }

    // Take the first phone number
    const phoneNumber = normalizePhoneNumber(contact.phoneNumbers[0].number);
    
    if (!phoneNumber) {
      continue;
    }

    // Check if this phone number is a BoGuani user
    const isBoGuaniUser = await checkIfBoGuaniUser(phoneNumber);
    
    const processedContact = {
      id: `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: contact.name || 'Unknown',
      phoneNumber,
      isBoGuaniUser,
      avatar: null,
      isOnline: isBoGuaniUser ? Math.random() > 0.5 : false, // Mock online status
      lastSeen: isBoGuaniUser ? new Date(Date.now() - Math.random() * 86400000).toISOString() : undefined
    };

    processedContacts.push(processedContact);
  }

  return processedContacts;
}

function normalizePhoneNumber(phoneNumber: string): string | null {
  // Remove all non-digit characters
  const digits = phoneNumber.replace(/\D/g, '');
  
  // Handle US numbers
  if (digits.length === 10) {
    return `+1${digits}`;
  } else if (digits.length === 11 && digits.startsWith('1')) {
    return `+${digits}`;
  } else if (digits.length > 10) {
    return `+${digits}`;
  }
  
  return null;
}

async function checkIfBoGuaniUser(phoneNumber: string): Promise<boolean> {
  // In production, you'd query your user database
  // For demo, we'll simulate some users being on BoGuani
  
  const mockBoGuaniUsers = [
    '+1234567891',
    '+1234567892', 
    '+1234567893',
    '+1555123456',
    '+1555654321'
  ];

  return mockBoGuaniUsers.includes(phoneNumber) || Math.random() > 0.7;
}

// GET endpoint to retrieve user's contacts
export const GET: RequestHandler = async ({ request }) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return json({ error: 'Authorization required' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    let userId: string;
    
    try {
      const decoded = jwt.verify(token, env.JWT_SECRET || 'boguani-secret-key') as any;
      userId = decoded.userId;
    } catch (error) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    // In production, you'd fetch from database
    // For demo, return mock contacts
    const mockContacts = [
      {
        id: 'contact_1',
        name: 'Maria Rodriguez',
        phoneNumber: '+1234567891',
        isBoGuaniUser: true,
        isOnline: true,
        avatar: null
      },
      {
        id: 'contact_2',
        name: 'Carlos Mendez', 
        phoneNumber: '+1234567892',
        isBoGuaniUser: true,
        isOnline: false,
        lastSeen: new Date(Date.now() - 3600000).toISOString(),
        avatar: null
      },
      {
        id: 'contact_3',
        name: 'Ana Delgado',
        phoneNumber: '+1234567893',
        isBoGuaniUser: false,
        avatar: null
      },
      {
        id: 'contact_4',
        name: 'Roberto Silva',
        phoneNumber: '+1555123456',
        isBoGuaniUser: true,
        isOnline: true,
        avatar: null
      }
    ];

    return json({
      success: true,
      contacts: mockContacts
    });

  } catch (error) {
    console.error('Get contacts error:', error);
    return json({ error: 'Failed to get contacts' }, { status: 500 });
  }
};
