@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Poppins', sans-serif;
  }
  
  body {
    @apply bg-dark-dark text-gray-200;
    height: 100vh;
    overflow: hidden;
  }
}

@layer components {
  .chat-bubble {
    @apply max-w-[75%] break-words;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  
  .chat-bubble.sent {
    @apply bg-primary bg-opacity-85 rounded-2xl rounded-tr-sm;
  }
  
  .chat-bubble.received {
    @apply bg-dark-light bg-opacity-75 rounded-2xl rounded-tl-sm;
  }
  
  .chat-time {
    @apply text-xs mt-0.5 opacity-70;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .glass-effect {
    background: rgba(26, 26, 26, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-effect-light {
    background: rgba(74, 74, 74, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
  
  .gold-gradient {
    background: linear-gradient(135deg, #d4af37 0%, #f0e3b5 100%);
  }
  
  .purple-gradient {
    background: linear-gradient(135deg, #4a2a80 0%, #9d7bde 100%);
  }
  
  .bg-gradient-main {
    background: linear-gradient(to right top, #2C003E, #7D3C98);
  }
}
