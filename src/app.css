@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    --color-primary: 46, 0, 62;
    --color-gold: 205, 164, 52;
  }

  html {
    font-family: 'Poppins', sans-serif;
    background-color: #0f0519;
    color: #f0e6ff;
  }
  
  body {
    background: linear-gradient(135deg, #0f0519 0%, #1a0a2a 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 9999px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(145deg, #FFE55C 0%, #CDA434 50%, #8A6F00 100%);
    border-radius: 9999px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(145deg, #FFE55C 0%, #E6C04D 50%, #B08C1E 100%);
  }
}

@layer components {
  .chat-bubble {
    max-width: 75%;
    word-break: break-word;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .metallic-gold {
    background: linear-gradient(145deg, #FFE55C 0%, #CDA434 50%, #8A6F00 100%);
    position: relative;
    overflow: hidden;
  }

  .metallic-gold::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    right: -50%;
    bottom: -50%;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    animation: shine 3s infinite;
  }

  @keyframes shine {
    0% { transform: translateX(-100%) rotate(30deg); }
    100% { transform: translateX(100%) rotate(30deg); }
  }

  .glass-effect {
    background: linear-gradient(135deg, rgba(46, 0, 62, 0.5) 0%, rgba(70, 0, 94, 0.4) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .glass-effect-light {
    background: linear-gradient(135deg, rgba(46, 0, 62, 0.3) 0%, rgba(70, 0, 94, 0.2) 100%);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.03);
  }
  
  .chat-bubble.sent {
    background-color: rgba(106, 61, 179, 0.85);
    border-radius: 1rem 1rem 0.25rem 1rem;
    margin-left: auto;
    color: white;
  }
  
  .chat-bubble.received {
    background-color: rgba(66, 66, 66, 0.75);
    border-radius: 1rem 1rem 1rem 0.25rem;
    margin-right: auto;
    color: #e0e0e0;
  }
  
  .chat-time {
    font-size: 0.75rem;
    margin-top: 0.125rem;
    opacity: 0.7;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .glass-effect {
    background: rgba(42, 21, 65, 0.7);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .glass-effect-light {
    background: rgba(90, 70, 130, 0.5);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modal-backdrop {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
  }
  
  .gold-gradient {
    background: linear-gradient(135deg, #d4af37 0%, #f0e3b5 100%);
  }
  
  .purple-gradient {
    background: linear-gradient(135deg, #4a2a80 0%, #9d7bde 100%);
  }
  
  .bg-gradient-main {
    background: linear-gradient(135deg, #2C003E 0%, #4a2a80 100%);
  }

  .font-serif {
    font-family: 'Playfair Display', serif;
  }
}
