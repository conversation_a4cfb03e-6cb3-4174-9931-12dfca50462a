

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NexusPay</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            light: '#9d7bde',
                            DEFAULT: '#6a3db3',
                            dark: '#4a2a80'
                        },
                        gold: {
                            light: '#f0e3b5',
                            DEFAULT: '#d4af37',
                            dark: '#b08c1e'
                        },
                        dark: {
                            light: '#4a4a4a',
                            DEFAULT: '#333333',
                            dark: '#1a1a1a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            height: 100vh;
            overflow: hidden;
            background-color: #1a1a1a;
        }
        .chat-bubble {
            max-width: 75%;
            word-wrap: break-word;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        .chat-bubble.sent {
            background-color: rgba(106, 61, 179, 0.85);
            border-radius: 1rem 0.25rem 1rem 1rem;
        }
        .chat-bubble.received {
            background-color: rgba(74, 74, 74, 0.75);
            border-radius: 0.25rem 1rem 1rem 1rem;
        }
        .chat-time {
            font-size: 0.7rem;
            margin-top: 2px;
            opacity: 0.7;
        }
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .glass-effect {
            background: rgba(26, 26, 26, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-effect-light {
            background: rgba(74, 74, 74, 0.5);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
        }
        .gold-gradient {
            background: linear-gradient(135deg, #d4af37 0%, #f0e3b5 100%);
        }
        .purple-gradient {
            background: linear-gradient(135deg, #4a2a80 0%, #9d7bde 100%);
        }
    </style>
</head>
<body class="bg-dark-dark text-gray-200">
    <div class="flex h-full">
        <!-- Sidebar -->
        <div class="w-1/4 glass-effect flex flex-col h-full border-r border-gray-700">
            <!-- App Header -->
            <div class="p-4 bg-primary-dark bg-opacity-90 text-white flex justify-between items-center border-b border-gray-700">
                <h1 class="text-xl font-semibold flex items-center">
                    <span class="text-gold-light mr-2">Nexus</span>Pay
                </h1>
                <div class="flex space-x-3">
                    <button id="newChatBtn" class="focus:outline-none hover:text-gold-light transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                    </button>
                    <button class="focus:outline-none hover:text-gold-light transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Search -->
            <div class="p-3 bg-dark-DEFAULT bg-opacity-70">
                <div class="relative">
                    <input type="text" placeholder="Search or start new chat" class="w-full py-2 px-4 bg-dark-light bg-opacity-50 rounded-full pl-10 focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
            </div>
            
            <!-- Chat List -->
            <div class="flex-1 overflow-y-auto scrollbar-hide">
                <!-- Chat Item 1 -->
                <div class="flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer chat-item active bg-dark-light bg-opacity-30" data-chat="1">
                    <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">JD</div>
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">John Doe</h3>
                            <span class="text-xs text-gray-400">10:30 AM</span>
                        </div>
                        <p class="text-sm text-gray-400 truncate">Hey, can you send me $20 for lunch?</p>
                    </div>
                </div>
                
                <!-- Chat Item 2 -->
                <div class="flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer chat-item" data-chat="2">
                    <div class="w-12 h-12 rounded-full bg-primary-light flex items-center justify-center text-white font-semibold mr-3">AS</div>
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Alice Smith</h3>
                            <span class="text-xs text-gray-400">Yesterday</span>
                        </div>
                        <p class="text-sm text-gray-400 truncate">Thanks for the payment!</p>
                    </div>
                </div>
                
                <!-- Chat Item 3 -->
                <div class="flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer chat-item" data-chat="3">
                    <div class="w-12 h-12 rounded-full gold-gradient flex items-center justify-center text-dark-dark font-semibold mr-3">RJ</div>
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Robert Johnson</h3>
                            <span class="text-xs text-gray-400">Monday</span>
                        </div>
                        <p class="text-sm text-gray-400 truncate">Meeting at 3pm tomorrow?</p>
                    </div>
                </div>
                
                <!-- Chat Item 4 -->
                <div class="flex items-center p-3 border-b border-gray-700 hover:bg-dark-light hover:bg-opacity-30 cursor-pointer chat-item" data-chat="4">
                    <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center text-white font-semibold mr-3">EW</div>
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <h3 class="font-semibold">Emma Wilson</h3>
                            <span class="text-xs text-gray-400">Sunday</span>
                        </div>
                        <p class="text-sm text-gray-400 truncate">I'll send you the money tonight</p>
                    </div>
                </div>
            </div>
            
            <!-- Banking Section -->
            <div class="p-4 glass-effect border-t border-gray-700">
                <h2 class="text-lg font-semibold mb-2 flex items-center">
                    <span class="text-gold-light">Banking</span>
                    <span class="ml-1 text-xs bg-primary-light px-1.5 py-0.5 rounded-full">Premium</span>
                </h2>
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm opacity-70">Balance</p>
                        <p class="font-bold text-lg text-gold-light">$1,245.67</p>
                    </div>
                    <button id="connectPlaidBtn" class="bg-primary bg-opacity-80 hover:bg-primary-dark text-white px-3 py-1.5 rounded-lg text-sm font-medium transition flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Connect Bank
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Chat Area -->
        <div class="w-3/4 flex flex-col h-full">
            <!-- Chat Header -->
            <div class="p-3 glass-effect border-b border-gray-700 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">JD</div>
                    <div>
                        <h2 class="font-semibold">John Doe</h2>
                        <p class="text-xs text-gray-400 flex items-center">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-1.5"></span>
                            Online
                        </p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <button class="focus:outline-none text-gray-400 hover:text-white transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                    </button>
                    <button class="focus:outline-none text-gray-400 hover:text-white transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </button>
                    <button id="sendMoneyBtn" class="focus:outline-none text-gold-light hover:text-gold transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                    <button class="focus:outline-none text-gray-400 hover:text-white transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Messages Area -->
            <div id="messagesArea" class="flex-1 p-4 overflow-y-auto bg-gradient-to-b from-dark-DEFAULT to-dark-dark scrollbar-hide">
                <!-- Day Divider -->
                <div class="flex justify-center mb-4">
                    <span class="bg-dark-light bg-opacity-50 text-gray-300 text-xs px-3 py-1 rounded-full">Today</span>
                </div>
                
                <!-- Received Message -->
                <div class="flex mb-4">
                    <div class="chat-bubble received p-3 shadow-lg">
                        <p>Hey there! How's it going?</p>
                        <div class="chat-time text-right text-gray-300">10:15 AM</div>
                    </div>
                </div>
                
                <!-- Sent Message -->
                <div class="flex justify-end mb-4">
                    <div class="chat-bubble sent p-3 shadow-lg">
                        <p class="text-white">Hi! I'm good, thanks. Just working on some projects.</p>
                        <div class="chat-time text-right text-gray-200">10:17 AM</div>
                    </div>
                </div>
                
                <!-- Received Message -->
                <div class="flex mb-4">
                    <div class="chat-bubble received p-3 shadow-lg">
                        <p>Cool! Hey, can you send me $20 for lunch? I forgot my wallet.</p>
                        <div class="chat-time text-right text-gray-300">10:20 AM</div>
                    </div>
                </div>
                
                <!-- Sent Message -->
                <div class="flex justify-end mb-4">
                    <div class="chat-bubble sent p-3 shadow-lg">
                        <p class="text-white">Sure, no problem. I'll send it right away.</p>
                        <div class="chat-time text-right text-gray-200">10:22 AM</div>
                    </div>
                </div>
                
                <!-- Payment Message -->
                <div class="flex justify-end mb-4">
                    <div class="chat-bubble sent p-3 shadow-lg">
                        <div class="glass-effect-light rounded-lg p-3 mb-1 border border-primary-light">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gold-light">Payment Sent</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="text-lg font-bold text-white">$20.00</div>
                            <div class="text-xs text-gray-300">To: John Doe</div>
                        </div>
                        <div class="chat-time text-right text-gray-200">10:25 AM</div>
                    </div>
                </div>
                
                <!-- Received Message -->
                <div class="flex mb-4">
                    <div class="chat-bubble received p-3 shadow-lg">
                        <p>Thanks a lot! Got it. 👍</p>
                        <div class="chat-time text-right text-gray-300">10:26 AM</div>
                    </div>
                </div>
            </div>
            
            <!-- Message Input -->
            <div class="p-3 glass-effect border-t border-gray-700">
                <div class="flex items-center">
                    <button class="p-2 text-gray-400 hover:text-white focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-white focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                    </button>
                    <input type="text" id="messageInput" placeholder="Type a message" class="flex-1 py-2 px-4 bg-dark-light bg-opacity-50 rounded-full mx-2 focus:outline-none focus:ring-1 focus:ring-primary-light text-gray-200 placeholder-gray-400">
                    <button id="sendMessageBtn" class="p-2 bg-primary hover:bg-primary-dark text-white rounded-full focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Plaid Connection Modal -->
    <div id="plaidModal" class="modal-backdrop fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="glass-effect rounded-xl shadow-2xl w-full max-w-md mx-4 border border-gray-700">
            <div class="p-5 border-b border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-white flex items-center">
                        <span class="text-gold-light mr-2">Connect</span> Your Bank
                    </h3>
                    <button id="closePlaidModal" class="text-gray-400 hover:text-white focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-5">
                <p class="text-gray-300 mb-4">Connect your bank account securely with Plaid to enable money transfers within chats.</p>
                
                <div class="space-y-3 mb-5">
                    <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition border border-gray-700">
                        <div class="w-10 h-10 bg-primary-light bg-opacity-30 rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Chase</h4>
                            <p class="text-xs text-gray-400">Personal Banking</p>
                        </div>
                    </div>
                    
                    <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition border border-gray-700">
                        <div class="w-10 h-10 bg-red-900 bg-opacity-30 rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Bank of America</h4>
                            <p class="text-xs text-gray-400">Personal Banking</p>
                        </div>
                    </div>
                    
                    <div class="glass-effect-light rounded-lg p-3 flex items-center hover:bg-dark-light hover:bg-opacity-70 cursor-pointer transition border border-gray-700">
                        <div class="w-10 h-10 bg-primary-dark bg-opacity-30 rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="font-medium text-white">Wells Fargo</h4>
                            <p class="text-xs text-gray-400">Personal Banking</p>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button id="searchBanksBtn" class="text-primary-light hover:text-primary hover:underline focus:outline-none transition">
                        Search for more banks
                    </button>
                </div>
            </div>
            <div class="p-4 bg-dark-DEFAULT bg-opacity-70 rounded-b-xl border-t border-gray-700">
                <div class="flex justify-between items-center">
                    <p class="text-xs text-gray-400">Secured by Plaid</p>
                    <button id="connectBankBtn" class="bg-primary hover:bg-primary-dark text-white px-4 py-2 rounded-lg focus:outline-none transition">
                        Continue
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Modal -->
    <div id="paymentModal" class="modal-backdrop fixed inset-0 flex items-center justify-center z-50 hidden">
        <div class="glass-effect rounded-xl shadow-2xl w-full max-w-md mx-4 border border-gray-700">
            <div class="p-5 border-b border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-white flex items-center">
                        <span class="text-gold-light mr-2">Send</span> Money
                    </h3>
                    <button id="closePaymentModal" class="text-gray-400 hover:text-white focus:outline-none transition">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-5">
                <div class="flex items-center mb-5">
                    <div class="w-12 h-12 rounded-full purple-gradient flex items-center justify-center text-white font-semibold mr-3">JD</div>
                    <div>
                        <h4 class="font-medium text-white">John Doe</h4>
                        <p class="text-xs text-gray-400">@johndoe</p>
                    </div>
                </div>
                
                <div class="mb-5">
                    <label class="block text-gray-300 text-sm font-medium mb-2">Amount</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gold-light text-lg">$</span>
                        </div>
                        <input type="number" id="amountInput" class="pl-8 block w-full rounded-lg bg-dark-light bg-opacity-50 border border-gray-700 p-2.5 focus:outline-none focus:ring-1 focus:ring-primary-light focus:border-primary-light text-white" placeholder="0.00" value="20.00">
                    </div>
                </div>
                
                <div class="mb-5">
                    <label class="block text-gray-300 text-sm font-medium mb-2">From</label>
                    <div class="glass-effect-light rounded-lg p-3 flex items-center border border-gray-700">
                        <div class="w-8 h-8 bg-primary-light bg-opacity-30 rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-light" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm text-white">Chase Checking</h4>
                            <p class="text-xs text-gray-400">**** 4567</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                
                <div class="mb-5">
                    <label class="block text-gray-300 text-sm font-medium mb-2">Note (optional)</label>
                    <input type="text" class="block w-full rounded-lg bg-dark-light bg-opacity-50 border border-gray-700 p-2.5 focus:outline-none focus:ring-1 focus:ring-primary-light focus:border-primary-light text-white" placeholder="What's it for?" value="Lunch">
                </div>
            </div>
            <div class="p-4 bg-dark-DEFAULT bg-opacity-70 rounded-b-xl border-t border-gray-700">
                <button id="confirmPaymentBtn" class="w-full gold-gradient text-dark-dark py-2.5 rounded-lg hover:opacity-90 focus:outline-none transition font-medium">
                    Send $20.00
                </button>
            </div>
        </div>
    </div>
    
    <script>
        // DOM Elements
        const connectPlaidBtn = document.getElementById('connectPlaidBtn');
        const plaidModal = document.getElementById('plaidModal');
        const closePlaidModal = document.getElementById('closePlaidModal');
        const connectBankBtn = document.getElementById('connectBankBtn');
        
        const sendMoneyBtn = document.getElementById('sendMoneyBtn');
        const paymentModal = document.getElementById('paymentModal');
        const closePaymentModal = document.getElementById('closePaymentModal');
        const confirmPaymentBtn = document.getElementById('confirmPaymentBtn');
        
        const messageInput = document.getElementById('messageInput');
        const sendMessageBtn = document.getElementById('sendMessageBtn');
        const messagesArea = document.getElementById('messagesArea');
        
        const chatItems = document.querySelectorAll('.chat-item');
        
        // Event Listeners
        connectPlaidBtn.addEventListener('click', () => {
            plaidModal.classList.remove('hidden');
        });
        
        closePlaidModal.addEventListener('click', () => {
            plaidModal.classList.add('hidden');
        });
        
        connectBankBtn.addEventListener('click', () => {
            plaidModal.classList.add('hidden');
            // Show success notification
            showNotification('Bank connected successfully!');
        });
        
        sendMoneyBtn.addEventListener('click', () => {
            paymentModal.classList.remove('hidden');
        });
        
        closePaymentModal.addEventListener('click', () => {
            paymentModal.classList.add('hidden');
        });
        
        confirmPaymentBtn.addEventListener('click', () => {
            const amount = document.getElementById('amountInput').value;
            paymentModal.classList.add('hidden');
            
            // Add payment message
            const paymentMessage = `
                <div class="flex justify-end mb-4">
                    <div class="chat-bubble sent p-3 shadow-lg">
                        <div class="glass-effect-light rounded-lg p-3 mb-1 border border-primary-light">
                            <div class="flex items-center justify-between">
                                <span class="font-semibold text-gold-light">Payment Sent</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="text-lg font-bold text-white">$${amount}</div>
                            <div class="text-xs text-gray-300">To: John Doe</div>
                        </div>
                        <div class="chat-time text-right text-gray-200">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            messagesArea.insertAdjacentHTML('beforeend', paymentMessage);
            messagesArea.scrollTop = messagesArea.scrollHeight;
            
            // Show success notification
            showNotification(`$${amount} sent successfully!`);
        });
        
        sendMessageBtn.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        chatItems.forEach(item => {
            item.addEventListener('click', () => {
                // Remove active class from all items
                chatItems.forEach(i => i.classList.remove('active', 'bg-dark-light', 'bg-opacity-30'));
                // Add active class to clicked item
                item.classList.add('active', 'bg-dark-light', 'bg-opacity-30');
            });
        });
        
        // Functions
        function sendMessage() {
            const message = messageInput.value.trim();
            if (message) {
                const newMessage = `
                    <div class="flex justify-end mb-4">
                        <div class="chat-bubble sent p-3 shadow-lg">
                            <p class="text-white">${message}</p>
                            <div class="chat-time text-right text-gray-200">${getCurrentTime()}</div>
                        </div>
                    </div>
                `;
                messagesArea.insertAdjacentHTML('beforeend', newMessage);
                messageInput.value = '';
                messagesArea.scrollTop = messagesArea.scrollHeight;
            }
        }
        
        function getCurrentTime() {
            const now = new Date();
            let hours = now.getHours();
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'
            return `${hours}:${minutes} ${ampm}`;
        }
        
        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 glass-effect border border-gold-light text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300 flex items-center';
            
            // Add gold icon
            const icon = document.createElement('span');
            icon.className = 'text-gold-light mr-2';
            icon.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            `;
            notification.appendChild(icon);
            
            // Add message text
            const text = document.createElement('span');
            text.textContent = message;
            notification.appendChild(text);
            
            // Add to body
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // Initialize
        messagesArea.scrollTop = messagesArea.scrollHeight;
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'956f1ef205c5a04f',t:'MTc1MTEzNDAxNi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
