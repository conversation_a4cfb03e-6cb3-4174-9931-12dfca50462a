<!DOCTYPE html>
<html>
<head>
    <title>BoGuani Icon Generator</title>
</head>
<body>
    <canvas id="canvas" style="display: none;"></canvas>
    <div id="status">Generating icons...</div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        
        function generateIcon(size) {
            canvas.width = size;
            canvas.height = size;
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#D4AF37');
            gradient.addColorStop(1, '#B8860B');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            ctx.roundRect(0, 0, size, size, size * 0.2);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Add Taíno sun symbol
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.25;
            
            // Sun center
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius * 0.6, 0, 2 * Math.PI);
            ctx.fill();
            
            // Sun rays
            ctx.strokeStyle = '#FFFFFF';
            ctx.lineWidth = size * 0.02;
            
            for (let i = 0; i < 8; i++) {
                const angle = (i * Math.PI) / 4;
                const startX = centerX + Math.cos(angle) * radius * 0.8;
                const startY = centerY + Math.sin(angle) * radius * 0.8;
                const endX = centerX + Math.cos(angle) * radius * 1.2;
                const endY = centerY + Math.sin(angle) * radius * 1.2;
                
                ctx.beginPath();
                ctx.moveTo(startX, startY);
                ctx.lineTo(endX, endY);
                ctx.stroke();
            }
            
            // Add "B" letter
            ctx.fillStyle = '#1a1a1a';
            ctx.font = `bold ${size * 0.3}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('B', centerX, centerY);
            
            return canvas.toDataURL('image/png');
        }
        
        function downloadIcon(dataUrl, size) {
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = dataUrl;
            link.click();
        }
        
        // Generate all icons
        sizes.forEach((size, index) => {
            setTimeout(() => {
                const dataUrl = generateIcon(size);
                downloadIcon(dataUrl, size);
                
                if (index === sizes.length - 1) {
                    document.getElementById('status').textContent = 'All icons generated! Check your downloads folder.';
                }
            }, index * 100);
        });
    </script>
</body>
</html>
