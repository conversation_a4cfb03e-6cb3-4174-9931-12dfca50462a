version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: nexuspay-db
    environment:
      POSTGRES_DB: nexuspay
      POSTGRES_USER: nexuspay
      POSTGRES_PASSWORD: nexuspay_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - nexuspay-network

  # Redis for session storage and caching
  redis:
    image: redis:7-alpine
    container_name: nexuspay-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nexuspay-network

  # NexusPay Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nexuspay-app
    ports:
      - "5173:5173"
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*****************************************************/nexuspay
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - nexuspay-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  nexuspay-network:
    driver: bridge
