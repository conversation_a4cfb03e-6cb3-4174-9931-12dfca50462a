import adapter from '@sveltejs/adapter-node';
import { vitePreprocess } from '@sveltejs/vite-plugin-svelte';

/** @type {import('@sveltejs/kit').Config} */
const config = {
  // Enable TypeScript with Svelte
  preprocess: [
    vitePreprocess({
      // This enables TypeScript in Svelte files
      typescript: {
        compilerOptions: {
          // Your TypeScript compiler options
          target: 'es2020',
          module: 'es2020',
          moduleResolution: 'node',
          strict: true,
          skipLibCheck: true,
          noImplicitAny: false,
          resolveJsonModule: true,
          allowSyntheticDefaultImports: true,
          esModuleInterop: true,
          sourceMap: true,
          baseUrl: '.',
          paths: {
            '$lib/*': ['src/lib/*']
          }
        },
        // Enable type checking in Svelte files
        tsconfigFile: './tsconfig.json',
        transpileOnly: false
      }
    })
  ],

  kit: {
    // Use Node.js adapter for Heroku deployment
    adapter: adapter({
      out: 'build',
      precompress: false,
      envPrefix: ''
    })
  }
};

export default config;
