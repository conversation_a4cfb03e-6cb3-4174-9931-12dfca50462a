rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 🔐 Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.token.phone_number == userId;
    }
    
    // 🔐 Chats collection - users can only access chats they participate in
    match /chats/{chatId} {
      allow read, write: if request.auth != null && 
        request.auth.token.phone_number in resource.data.participants;
    }
    
    // 🔐 Messages subcollection - users can only access messages in chats they participate in
    match /chats/{chatId}/messages/{messageId} {
      allow read, write: if request.auth != null && 
        request.auth.token.phone_number in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }
    
    // 🔐 Bank accounts - users can only access their own bank accounts
    match /bankAccounts/{userId} {
      allow read, write: if request.auth != null && request.auth.token.phone_number == userId;
    }
    
    // 🔐 Payments - users can only access payments they're involved in
    match /payments/{paymentId} {
      allow read, write: if request.auth != null && 
        (request.auth.token.phone_number == resource.data.senderId || 
         request.auth.token.phone_number == resource.data.recipientId);
    }
  }
}
