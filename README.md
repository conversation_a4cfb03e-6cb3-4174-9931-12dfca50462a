# NexusPay - Secure Chat & Money Transfer App

A production-grade full-stack application built with SvelteKit, TypeScript, and TailwindCSS that combines secure messaging with seamless money transfers - like a mix of WhatsApp and Cash App.

## Features

- 🔐 **End-to-End Encrypted Messaging** using libsodium
- 💰 **Integrated Money Transfers** via Plaid API
- 📱 **Phone Number Authentication** with SMS OTP
- 🔄 **Real-time Chat** with WebSocket support
- 🎨 **Beautiful UI** with purple/gold theme and glass effects
- ✨ **Smooth Animations** using @motionone/svelte
- 📱 **Mobile-First Design** responsive across all devices
- 🏦 **Bank Account Integration** secure Plaid connection
- 🔒 **Security First** JWT sessions, CSRF protection

## Tech Stack

- **Frontend**: SvelteKit + TypeScript + TailwindCSS
- **Backend**: SvelteKit API routes + Node.js
- **Database**: Supabase (PostgreSQL)
- **Real-time**: Socket.IO WebSockets
- **Authentication**: JWT + SMS OTP (Twilio)
- **Payments**: Plaid API
- **Encryption**: libsodium for E2E encryption
- **Deployment**: Heroku

## Quick Start

### Prerequisites

- Node.js 18+ and npm 8+
- Supabase account (free tier available)
- Twilio account for SMS
- Plaid account for banking integration

### Local Development

1. **Clone and install dependencies**
   ```bash
   git clone <your-repo>
   cd nexuspay
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual API keys
   ```

3. **Set up Supabase database**
   - Create a new Supabase project
   - Run the SQL schema from `supabase-schema.sql`
   - Add your Supabase URL and keys to `.env`

4. **Start development server**
   ```bash
   npm run dev
   ```

   The app will be available at `http://localhost:5173`

## Heroku Deployment

### 1. Create Heroku App

```bash
# Install Heroku CLI if you haven't already
npm install -g heroku

# Login to Heroku
heroku login

# Create new app
heroku create your-nexuspay-app

# Add Node.js buildpack
heroku buildpacks:set heroku/nodejs
```

### 2. Set Environment Variables

```bash
# Required environment variables
heroku config:set JWT_SECRET=your-super-secret-jwt-key
heroku config:set PLAID_CLIENT_ID=your-plaid-client-id
heroku config:set PLAID_SECRET=your-plaid-secret-key
heroku config:set PLAID_ENCRYPTION_KEY=your-encryption-key
heroku config:set TWILIO_ACCOUNT_SID=your-twilio-sid
heroku config:set TWILIO_AUTH_TOKEN=your-twilio-token
heroku config:set TWILIO_PHONE_NUMBER=your-twilio-phone
heroku config:set SUPABASE_URL=your-supabase-url
heroku config:set SUPABASE_ANON_KEY=your-supabase-anon-key
heroku config:set NODE_ENV=production
```

### 3. Deploy

```bash
# Deploy to Heroku
git add .
git commit -m "Initial deployment"
git push heroku main

# Open your app
heroku open
```

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `JWT_SECRET` | Secret key for JWT tokens | Yes |
| `PLAID_CLIENT_ID` | Plaid API client ID | Yes |
| `PLAID_SECRET` | Plaid API secret key | Yes |
| `PLAID_ENCRYPTION_KEY` | Key for encrypting Plaid tokens | Yes |
| `TWILIO_ACCOUNT_SID` | Twilio account SID for SMS | Yes |
| `TWILIO_AUTH_TOKEN` | Twilio auth token | Yes |
| `TWILIO_PHONE_NUMBER` | Twilio phone number | Yes |
| `SUPABASE_URL` | Supabase project URL | Yes |
| `SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `NODE_ENV` | Environment (development/production) | No |

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run check` - Type checking

## License

MIT License - see LICENSE file for details
