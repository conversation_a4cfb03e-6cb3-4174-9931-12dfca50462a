{"name": "@firebase/messaging-compat", "version": "0.2.22", "license": "Apache-2.0", "description": "", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "typings": "dist/src/index.d.ts", "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:deps": "lerna run --scope @firebase/'messaging-compat' --include-dependencies build", "build:release": "yarn build && yarn add-compat-overloads", "dev": "rollup -c -w", "test": "run-p --npm-path npm test:karma", "test:ci": "node ../../scripts/run_tests_in_ci.js", "test:karma": "karma start", "test:debug": "karma start --browsers=Chrome --auto-watch", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "type-check": "tsc --noEmit", "add-compat-overloads": "ts-node-script ../../scripts/build/create-overloads.ts -i ../messaging/dist/index-public.d.ts -o dist/src/index.d.ts -a -r Messaging:MessagingCompat -r FirebaseApp:FirebaseAppCompat --moduleToEnhance @firebase/messaging"}, "peerDependencies": {"@firebase/app-compat": "0.x"}, "dependencies": {"@firebase/messaging": "0.12.22", "@firebase/component": "0.6.18", "@firebase/util": "1.12.1", "tslib": "^2.1.0"}, "devDependencies": {"@firebase/app-compat": "0.4.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-typescript2": "0.36.0", "ts-essentials": "9.4.2", "typescript": "5.5.4"}, "repository": {"directory": "packages/messaging", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}}