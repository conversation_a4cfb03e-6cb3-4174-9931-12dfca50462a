{"name": "@firebase/app-check-compat", "version": "0.3.26", "description": "A compat App Check package for new firebase packages", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "exports": {".": {"types": "./dist/src/index.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:release": "yarn build && yarn add-compat-overloads", "build:deps": "lerna run --scope @firebase/app-check-compat --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:browser", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:browser", "test:browser": "karma start --nocache", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "add-compat-overloads": "ts-node-script ../../scripts/build/create-overloads.ts -i ../app-check/dist/app-check-public.d.ts -o dist/src/index.d.ts -a -r AppCheck:FirebaseAppCheck -r FirebaseApp:FirebaseAppCompat --moduleToEnhance @firebase/app-check"}, "peerDependencies": {"@firebase/app-compat": "0.x"}, "dependencies": {"@firebase/app-check": "0.10.1", "@firebase/app-check-types": "0.5.3", "@firebase/logger": "0.4.4", "@firebase/util": "1.12.1", "@firebase/component": "0.6.18", "tslib": "^2.1.0"}, "license": "Apache-2.0", "devDependencies": {"@firebase/app-compat": "0.4.2", "rollup": "2.79.2", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4"}, "repository": {"directory": "packages/app-check", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "dist/src/index.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}, "engines": {"node": ">=18.0.0"}}