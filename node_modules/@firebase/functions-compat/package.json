{"name": "@firebase/functions-compat", "version": "0.3.26", "description": "", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "exports": {".": {"types": "./dist/src/index.d.ts", "node": {"require": "./dist/index.cjs.js", "import": "./dist/esm/index.esm2017.js"}, "browser": {"require": "./dist/index.cjs.js", "import": "./dist/esm/index.esm2017.js"}, "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "license": "Apache-2.0", "peerDependencies": {"@firebase/app-compat": "0.x"}, "devDependencies": {"@firebase/app-compat": "0.4.2", "rollup": "2.79.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4"}, "repository": {"directory": "packages/functions-compat", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c", "build:deps": "lerna run --scope @firebase/functions-compat --include-dependencies build", "build:release": "rollup -c rollup.config.release.js && yarn add-compat-overloads", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:all": "run-p --npm-path npm test:browser test:node", "test:browser": "karma start", "test:browser:debug": "karma start --browsers=Chrome --auto-watch", "test:node": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' nyc --reporter lcovonly -- mocha 'src/{,!(browser)/**/}*.test.ts' --config ../../config/mocharc.node.js", "test:emulator": "env FIREBASE_FUNCTIONS_HOST=http://localhost FIREBASE_FUNCTIONS_PORT=5005 run-p --npm-path npm test:node", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "add-compat-overloads": "ts-node-script ../../scripts/build/create-overloads.ts -i ../functions/dist/functions-public.d.ts -o dist/src/index.d.ts -a -r Functions:types.FirebaseFunctions -r FirebaseApp:FirebaseAppCompat --moduleToEnhance @firebase/functions"}, "typings": "dist/src/index.d.ts", "dependencies": {"@firebase/component": "0.6.18", "@firebase/functions": "0.12.9", "@firebase/functions-types": "0.6.3", "@firebase/util": "1.12.1", "tslib": "^2.1.0"}, "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}, "engines": {"node": ">=18.0.0"}}