/**
 * {{{description}}}
 * @export
 * @interface {{classname}}
 */
{{! Edited by Plaid, line 8. remove index signature }}
export interface {{classname}} {{#parent}}extends {{{parent}}} {{/parent}}{
{{#vars}}
    /**
     * {{{description}}}
     * @type {{=<% %>=}}{<%&datatype%>}<%={{ }}=%>
     * @memberof {{classname}}
    {{#deprecated}}
     * @deprecated
    {{/deprecated}}
     */
    {{name}}{{^required}}?{{/required}}: {{#isEnum}}{{{datatypeWithEnum}}}{{/isEnum}}{{^isEnum}}{{{dataType}}}{{#isNullable}} | null{{/isNullable}}{{/isEnum}};
{{/vars}}
}{{#hasEnums}}

{{#vars}}
{{#isEnum}}
/**
    * @export
    * @enum {string}
    */
export enum {{enumName}} {
{{#allowableValues}}
    {{#enumVars}}
    {{#enumDescription}}
    /**
    * {{enumDescription}}
    */
    {{/enumDescription}}
    {{{name}}} = {{{value}}}{{^-last}},{{/-last}}
    {{/enumVars}}
{{/allowableValues}}
}
{{/isEnum}}
{{/vars}}
{{/hasEnums}}
