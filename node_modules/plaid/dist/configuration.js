"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * The Plaid API
 * The Plaid REST API. Please see https://plaid.com/docs/api for more details.
 *
 * The version of the OpenAPI document: 2020-09-14_1.645.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Configuration = exports.PlaidEnvironments = void 0;
exports.PlaidEnvironments = {
    production: 'https://production.plaid.com',
    sandbox: 'https://sandbox.plaid.com',
};
class Configuration {
    constructor(param = {}) {
        this.apiKey = param.apiKey;
        this.username = param.username;
        this.password = param.password;
        this.accessToken = param.accessToken;
        this.basePath = param.basePath;
        this.baseOptions = param.baseOptions;
        this.formDataCtor = param.formDataCtor;
        if (!this.baseOptions)
            this.baseOptions = {};
        if (!this.baseOptions.headers)
            this.baseOptions.headers = {};
        this.baseOptions.headers = Object.assign({ ['User-Agent']: `Plaid Node v36.0.0`, ['Plaid-Version']: '2020-09-14' }, this.baseOptions.headers);
    }
    /**
     * Check if the given MIME is a JSON MIME.
     * JSON MIME examples:
     *   application/json
     *   application/json; charset=UTF8
     *   APPLICATION/JSON
     *   application/vnd.company+json
     * @param mime - MIME (Multipurpose Internet Mail Extensions)
     * @return True if the given MIME is JSON, false otherwise.
     */
    isJsonMime(mime) {
        const jsonMime = new RegExp('^(application\/json|[^;/ \t]+\/[^;/ \t]+[+]json)[ \t]*(;.*)?$', 'i');
        return mime !== null && (jsonMime.test(mime) || mime.toLowerCase() === 'application/json-patch+json');
    }
}
exports.Configuration = Configuration;
//# sourceMappingURL=data:application/json;base64,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