{"name": "plaid", "version": "36.0.0", "description": "A node.js client for the Plaid API", "keywords": ["plaid", "plaid.com"], "repository": {"type": "git", "url": "**************:plaid/plaid-node.git"}, "engines": {"node": ">=10.0.0"}, "bugs": {"url": "https://github.com/plaid/plaid-node/issues"}, "main": "dist/index.js", "license": "MIT", "types": "dist/index.d.ts", "dependencies": {"axios": "^1.7.4"}, "publishConfig": {"registry": "https://registry.npmjs.com"}, "scripts": {"build": "tsc --outDir dist/"}, "devDependencies": {"@types/chai": "^4.2.14", "@types/mocha": "^8.2.0", "@types/node": "^14.14.14", "chai": "^4.2.0", "expect.js": "0.3.x", "jshint": "^2.13.6", "mocha": "^10.2.0", "ts-node": "^9.1.1", "typescript": "4.1.3"}}