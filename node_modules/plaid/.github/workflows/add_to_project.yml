name: Auto Assign to CLIB Project

on:
  issues:
    types: [opened]
  pull_request:
    types: [opened]
env:
  MY_GITHUB_TOKEN: ${{ secrets.CLIB_AUTOMATION_TOKEN }}

jobs:
  assign_one_project:
    runs-on: ubuntu-latest
    name: Assign to One Project
    steps:
    - name: Assign NEW issues and NEW pull requests to project 1
      uses: srggrs/assign-one-project-github-action@1.2.0
      if: github.event.action == 'opened'
      with:
        project: 'https://github.com/orgs/plaid/projects/1'
        column_name: 'Needs Investigation'
