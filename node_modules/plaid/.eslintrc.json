{"parserOptions": {"ecmaVersion": 6}, "rules": {"no-empty": [2, {"allowEmptyCatch": true}], "no-implicit-coercion": [2, {"boolean": true, "string": true, "number": true}], "indent": [2, 2], "brace-style": [2, "1tbs", {"allowSingleLine": true}], "no-mixed-spaces-and-tabs": 2, "no-multi-str": 2, "key-spacing": [2, {"beforeColon": false, "afterColon": true}], "space-unary-ops": [2, {"words": false, "nonwords": false}], "space-before-function-paren": [2, "never"], "array-bracket-spacing": [2, "never", {"singleValue": false}], "space-in-parens": [2, "never"], "no-trailing-spaces": 2, "yoda": [2, "never"], "max-len": [2, 79], "comma-style": [2, "last"], "curly": [2, "all"], "dot-notation": 2, "eol-last": 2, "wrap-iife": 2, "space-infix-ops": 2, "keyword-spacing": [2, {"overrides": {"else": {"before": true}, "catch": {"before": true}, "finally": {"before": true}}}], "spaced-comment": [2, "always"], "space-before-blocks": [2, "always"], "linebreak-style": [2, "unix"], "quotes": [2, "single", {"avoidEscape": true}]}}