function t(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const e=(t,e,n)=>n>e?e:n<t?t:n;const n={},s=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function i(t){return"object"==typeof t&&null!==t}const r=t=>/^0[^.\s]+$/u.test(t);function a(t){let e;return()=>(void 0===e&&(e=t()),e)}const o=t=>t,l=(t,e)=>n=>e(t(n)),u=(...t)=>t.reduce(l),h=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class c{constructor(){this.subscriptions=[]}add(e){var n,s;return n=this.subscriptions,s=e,-1===n.indexOf(s)&&n.push(s),()=>t(this.subscriptions,e)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let i=0;i<s;i++){const s=this.subscriptions[i];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const p=t=>1e3*t,d=t=>t/1e3;function m(t,e){return e?t*(1e3/e):0}const f=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function y(t,e,n,s){if(t===e&&n===s)return o;const i=e=>function(t,e,n,s,i){let r,a,o=0;do{a=e+(n-e)/2,r=f(a,s,i)-t,r>0?n=a:e=a}while(Math.abs(r)>1e-7&&++o<12);return a}(e,0,1,t,n);return t=>0===t||1===t?t:f(i(t),e,s)}const g=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,v=t=>e=>1-t(1-e),b=y(.33,1.53,.69,.99),w=v(b),T=g(w),M=t=>(t*=2)<1?.5*w(t):.5*(2-Math.pow(2,-10*(t-1))),V=t=>1-Math.sin(Math.acos(t)),A=v(V),S=g(V),x=y(.42,0,1,1),k=y(0,0,.58,1),C=y(.42,0,.58,1),F=t=>Array.isArray(t)&&"number"!=typeof t[0];function P(t,e){return F(t)?t[((t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t})(0,t.length,e)]:t}const O=t=>Array.isArray(t)&&"number"==typeof t[0],E={linear:o,easeIn:x,easeInOut:C,easeOut:k,circIn:V,circInOut:S,circOut:A,backIn:w,backInOut:T,backOut:b,anticipate:M},D=t=>{if(O(t)){t.length;const[e,n,s,i]=t;return y(e,n,s,i)}return"string"==typeof t?E[t]:t},I=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],R={value:null,addProjectionMetrics:null};function B(t,e){let s=!1,i=!0;const r={delta:0,timestamp:0,isProcessing:!1},a=()=>s=!0,o=I.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,i=!1,r=!1;const a=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){a.has(e)&&(h.schedule(e),t()),l++,e(o)}const h={schedule:(t,e=!1,r=!1)=>{const o=r&&i?n:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{o=t,i?r=!0:(i=!0,[n,s]=[s,n],n.forEach(u),e&&R.value&&R.value.frameloop[e].push(l),l=0,n.clear(),i=!1,r&&(r=!1,h.process(t)))}};return h}(a,e?n:void 0),t),{}),{setup:l,read:u,resolveKeyframes:h,preUpdate:c,update:p,preRender:d,render:m,postRender:f}=o,y=()=>{const a=n.useManualTiming?r.timestamp:performance.now();s=!1,n.useManualTiming||(r.delta=i?1e3/60:Math.max(Math.min(a-r.timestamp,40),1)),r.timestamp=a,r.isProcessing=!0,l.process(r),u.process(r),h.process(r),c.process(r),p.process(r),d.process(r),m.process(r),f.process(r),r.isProcessing=!1,s&&e&&(i=!1,t(y))};return{schedule:I.reduce((e,n)=>{const a=o[n];return e[n]=(e,n=!1,o=!1)=>(s||(s=!0,i=!0,r.isProcessing||t(y)),a.schedule(e,n,o)),e},{}),cancel:t=>{for(let e=0;e<I.length;e++)o[I[e]].cancel(t)},state:r,steps:o}}const{schedule:N,cancel:K,state:j,steps:W}=B("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:o,!0);let Y;function $(){Y=void 0}const L={now:()=>(void 0===Y&&L.set(j.isProcessing||n.useManualTiming?j.timestamp:performance.now()),Y),set:t=>{Y=t,queueMicrotask($)}},U=t=>e=>"string"==typeof e&&e.startsWith(t),X=U("--"),q=U("var(--"),z=t=>!!q(t)&&Z.test(t.split("/*")[0].trim()),Z=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,H={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},_={...H,transform:t=>e(0,1,t)},G={...H,default:1},J=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const tt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,et=(t,e)=>n=>Boolean("string"==typeof n&&tt.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),nt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[i,r,a,o]=s.match(Q);return{[t]:parseFloat(i),[e]:parseFloat(r),[n]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},st={...H,transform:t=>Math.round((t=>e(0,255,t))(t))},it={test:et("rgb","red"),parse:nt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+st.transform(t)+", "+st.transform(e)+", "+st.transform(n)+", "+J(_.transform(s))+")"};const rt={test:et("#"),parse:function(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}},transform:it.transform},at=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ot=at("deg"),lt=at("%"),ut=at("px"),ht=at("vh"),ct=at("vw"),pt=(()=>({...lt,parse:t=>lt.parse(t)/100,transform:t=>lt.transform(100*t)}))(),dt={test:et("hsl","hue"),parse:nt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+lt.transform(J(e))+", "+lt.transform(J(n))+", "+J(_.transform(s))+")"},mt={test:t=>it.test(t)||rt.test(t)||dt.test(t),parse:t=>it.test(t)?it.parse(t):dt.test(t)?dt.parse(t):rt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?it.transform(t):dt.transform(t),getAnimatableNone:t=>{const e=mt.parse(t);return e.alpha=0,mt.transform(e)}},ft=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const yt="number",gt="color",vt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function bt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let r=0;const a=e.replace(vt,t=>(mt.test(t)?(s.color.push(r),i.push(gt),n.push(mt.parse(t))):t.startsWith("var(")?(s.var.push(r),i.push("var"),n.push(t)):(s.number.push(r),i.push(yt),n.push(parseFloat(t))),++r,"${}")).split("${}");return{values:n,split:a,indexes:s,types:i}}function wt(t){return bt(t).values}function Tt(t){const{split:e,types:n}=bt(t),s=e.length;return t=>{let i="";for(let r=0;r<s;r++)if(i+=e[r],void 0!==t[r]){const e=n[r];i+=e===yt?J(t[r]):e===gt?mt.transform(t[r]):t[r]}return i}}const Mt=t=>"number"==typeof t?0:mt.test(t)?mt.getAnimatableNone(t):t;const Vt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(ft)?.length||0)>0},parse:wt,createTransformer:Tt,getAnimatableNone:function(t){const e=wt(t);return Tt(t)(e.map(Mt))}};function At(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function St(t,e){return n=>n>0?e:t}const xt=(t,e,n)=>t+(e-t)*n,kt=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Ct=[rt,it,dt];function Ft(t){const e=(n=t,Ct.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===dt&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let i=0,r=0,a=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,o=2*n-s;i=At(o,s,t+1/3),r=At(o,s,t),a=At(o,s,t-1/3)}else i=r=a=n;return{red:Math.round(255*i),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(s)),s}const Pt=(t,e)=>{const n=Ft(t),s=Ft(e);if(!n||!s)return St(t,e);const i={...n};return t=>(i.red=kt(n.red,s.red,t),i.green=kt(n.green,s.green,t),i.blue=kt(n.blue,s.blue,t),i.alpha=xt(n.alpha,s.alpha,t),it.transform(i))},Ot=new Set(["none","hidden"]);function Et(t,e){return n=>xt(t,e,n)}function Dt(t){return"number"==typeof t?Et:"string"==typeof t?z(t)?St:mt.test(t)?Pt:Bt:Array.isArray(t)?It:"object"==typeof t?mt.test(t)?Pt:Rt:St}function It(t,e){const n=[...t],s=n.length,i=t.map((t,n)=>Dt(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=i[e](t);return n}}function Rt(t,e){const n={...t,...e},s={};for(const i in n)void 0!==t[i]&&void 0!==e[i]&&(s[i]=Dt(t[i])(t[i],e[i]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const Bt=(t,e)=>{const n=Vt.createTransformer(e),s=bt(t),i=bt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ot.has(t)&&!i.values.length||Ot.has(e)&&!s.values.length?function(t,e){return Ot.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):u(It(function(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const r=e.types[i],a=t.indexes[r][s[r]],o=t.values[a]??0;n[i]=o,s[r]++}return n}(s,i),i.values),n):St(t,e)};function Nt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return xt(t,e,n);return Dt(t)(t,e)}const Kt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>N.update(e,t),stop:()=>K(e),now:()=>j.isProcessing?j.timestamp:L.now()}},jt=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let e=0;e<i;e++)s+=Math.round(1e4*t(e/(i-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},Wt=2e4;function Yt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Wt;)e+=50,n=t.next(e);return e>=Wt?1/0:e}function $t(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Yt(s),Wt);return{type:"keyframes",ease:t=>s.next(i*t).value/e,duration:d(i)}}function Lt(t,e,n){const s=Math.max(e-5,0);return m(n-t(s),e-s)}const Ut=100,Xt=10,qt=1,zt=0,Zt=800,Ht=.3,_t=.3,Gt={granular:.01,default:2},Jt={granular:.005,default:.5},Qt=.01,te=10,ee=.05,ne=1,se=.001;function ie({duration:t=Zt,bounce:n=Ht,velocity:s=zt,mass:i=qt}){let r,a,o=1-n;o=e(ee,ne,o),t=e(Qt,te,d(t)),o<1?(r=e=>{const n=e*o,i=n*t,r=n-s,a=ae(e,o),l=Math.exp(-i);return se-r/a*l},a=e=>{const n=e*o*t,i=n*s+s,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),u=ae(Math.pow(e,2),o);return(-r(e)+se>0?-1:1)*((i-a)*l)/u}):(r=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,a=e=>Math.exp(-e*t)*(t*t*(s-e)));const l=function(t,e,n){let s=n;for(let n=1;n<re;n++)s-=t(s)/e(s);return s}(r,a,5/t);if(t=p(t),isNaN(l))return{stiffness:Ut,damping:Xt,duration:t};{const e=Math.pow(l,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}const re=12;function ae(t,e){return t*Math.sqrt(1-e*e)}const oe=["duration","bounce"],le=["stiffness","damping","mass"];function ue(t,e){return e.some(e=>void 0!==t[e])}function he(t=_t,n=Ht){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:n}:t;let{restSpeed:i,restDelta:r}=s;const a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:u,damping:h,mass:c,duration:m,velocity:f,isResolvedFromDuration:y}=function(t){let n={velocity:zt,stiffness:Ut,damping:Xt,mass:qt,isResolvedFromDuration:!1,...t};if(!ue(t,le)&&ue(t,oe))if(t.visualDuration){const s=t.visualDuration,i=2*Math.PI/(1.2*s),r=i*i,a=2*e(.05,1,1-(t.bounce||0))*Math.sqrt(r);n={...n,mass:qt,stiffness:r,damping:a}}else{const e=ie(t);n={...n,...e,mass:qt},n.isResolvedFromDuration=!0}return n}({...s,velocity:-d(s.velocity||0)}),g=f||0,v=h/(2*Math.sqrt(u*c)),b=o-a,w=d(Math.sqrt(u/c)),T=Math.abs(b)<5;let M;if(i||(i=T?Gt.granular:Gt.default),r||(r=T?Jt.granular:Jt.default),v<1){const t=ae(w,v);M=e=>{const n=Math.exp(-v*w*e);return o-n*((g+v*w*b)/t*Math.sin(t*e)+b*Math.cos(t*e))}}else if(1===v)M=t=>o-Math.exp(-w*t)*(b+(g+w*b)*t);else{const t=w*Math.sqrt(v*v-1);M=e=>{const n=Math.exp(-v*w*e),s=Math.min(t*e,300);return o-n*((g+v*w*b)*Math.sinh(s)+t*b*Math.cosh(s))/t}}const V={calculatedDuration:y&&m||null,next:t=>{const e=M(t);if(y)l.done=t>=m;else{let n=0===t?g:0;v<1&&(n=0===t?p(g):Lt(M,t,e));const s=Math.abs(n)<=i,a=Math.abs(o-e)<=r;l.done=s&&a}return l.value=l.done?o:e,l},toString:()=>{const t=Math.min(Yt(V),Wt),e=jt(e=>V.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return V}function ce({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],p={done:!1,value:c},d=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l;let m=n*e;const f=c+m,y=void 0===a?f:a(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/s),v=t=>y+g(t),b=t=>{const e=g(t),n=v(t);p.done=Math.abs(e)<=u,p.value=p.done?y:n};let w,T;const M=t=>{var e;(e=p.value,void 0!==o&&e<o||void 0!==l&&e>l)&&(w=t,T=he({keyframes:[p.value,d(p.value)],velocity:Lt(v,t,p.value),damping:i,stiffness:r,restDelta:u,restSpeed:h}))};return M(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==w||(e=!0,b(t),M(t)),void 0!==w&&t>=w?T.next(t-w):(!e&&b(t),p)}}}function pe(t,s,{clamp:i=!0,ease:r,mixer:a}={}){const l=t.length;if(s.length,1===l)return()=>s[0];if(2===l&&s[0]===s[1])return()=>s[1];const c=t[0]===t[1];t[0]>t[l-1]&&(t=[...t].reverse(),s=[...s].reverse());const p=function(t,e,s){const i=[],r=s||n.mix||Nt,a=t.length-1;for(let n=0;n<a;n++){let s=r(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||o:e;s=u(t,s)}i.push(s)}return i}(s,r,a),d=p.length,m=e=>{if(c&&e<t[0])return s[0];let n=0;if(d>1)for(;n<t.length-2&&!(e<t[n+1]);n++);const i=h(t[n],t[n+1],e);return p[n](i)};return i?n=>m(e(t[0],t[l-1],n)):m}function de(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=h(0,e,s);t.push(xt(n,1,i))}}function me(t){const e=[0];return de(e,t.length-1),e}function fe({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=F(s)?s.map(D):D(s),r={done:!1,value:e[0]},a=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:me(e),t),o=pe(a,e,{ease:Array.isArray(i)?i:(l=e,u=i,l.map(()=>u||C).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}he.applyToOptions=t=>{const e=$t(t,100,he);return t.ease=e.ease,t.duration=p(e.duration),t.type="keyframes",t};const ye=t=>null!==t;function ge(t,{repeat:e,repeatType:n="loop"},s,i=1){const r=t.filter(ye),a=i<0||e&&"loop"!==n&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}const ve={decay:ce,inertia:ce,tween:fe,keyframes:fe,spring:he};function be(t){"string"==typeof t.type&&(t.type=ve[t.type])}class we{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Te=t=>t/100;class Me extends we{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==L.now()&&this.tick(L.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;be(t);const{type:e=fe,repeat:n=0,repeatDelay:s=0,repeatType:i,velocity:r=0}=t;let{keyframes:a}=t;const o=e||fe;o!==fe&&"number"!=typeof a[0]&&(this.mixKeyframes=u(Te,Nt(a[0],a[1])),a=[0,100]);const l=o({...t,keyframes:a});"mirror"===i&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=Yt(l));const{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,n=!1){const{generator:s,totalDuration:i,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return s.next(0);const{delay:u=0,keyframes:h,repeat:c,repeatType:p,repeatDelay:d,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let b=this.currentTime,w=s;if(c){const t=Math.min(this.currentTime,i)/o;let n=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&n--,n=Math.min(n,c+1);Boolean(n%2)&&("reverse"===p?(s=1-s,d&&(s-=d/o)):"mirror"===p&&(w=a)),b=e(0,1,s)*o}const T=v?{done:!1,value:h[0]}:w.next(b);r&&(T.value=r(T.value));let{done:M}=T;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const V=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return V&&m!==ce&&(T.value=ge(h,this.options,y,this.speed)),f&&f(T.value),V&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return d(this.calculatedDuration)}get time(){return d(this.currentTime)}set time(t){t=p(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(L.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=d(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Kt,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(L.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const Ve=t=>180*t/Math.PI,Ae=t=>{const e=Ve(Math.atan2(t[1],t[0]));return xe(e)},Se={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Ae,rotateZ:Ae,skewX:t=>Ve(Math.atan(t[1])),skewY:t=>Ve(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},xe=t=>((t%=360)<0&&(t+=360),t),ke=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ce=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Fe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ke,scaleY:Ce,scale:t=>(ke(t)+Ce(t))/2,rotateX:t=>xe(Ve(Math.atan2(t[6],t[5]))),rotateY:t=>xe(Ve(Math.atan2(-t[2],t[0]))),rotateZ:Ae,rotate:Ae,skewX:t=>Ve(Math.atan(t[4])),skewY:t=>Ve(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Pe(t){return t.includes("scale")?1:0}function Oe(t,e){if(!t||"none"===t)return Pe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=Fe,i=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Se,i=e}if(!i)return Pe(e);const r=s[e],a=i[1].split(",").map(Ee);return"function"==typeof r?r(a):a[r]}function Ee(t){return parseFloat(t.trim())}const De=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Ie=(()=>new Set(De))(),Re=t=>t===H||t===ut,Be=new Set(["x","y","z"]),Ne=De.filter(t=>!Be.has(t));const Ke={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Oe(e,"x"),y:(t,{transform:e})=>Oe(e,"y")};Ke.translateX=Ke.x,Ke.translateY=Ke.y;const je=new Set;let We=!1,Ye=!1,$e=!1;function Le(){if(Ye){const t=Array.from(je).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Ne.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Ye=!1,We=!1,je.forEach(t=>t.complete($e)),je.clear()}function Ue(){je.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Ye=!0)})}class Xe{constructor(t,e,n,s,i,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=i,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(je.add(this),We||(We=!0,N.read(Ue),N.resolveKeyframes(Le))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const i=s?.get(),r=t[t.length-1];if(void 0!==i)t[0]=i;else if(n&&e){const s=n.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===i&&s.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),je.delete(this)}cancel(){"scheduled"===this.state&&(je.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const qe=a(()=>void 0!==window.ScrollTimeline),ze={};function Ze(t,e){const n=a(t);return()=>ze[e]??n()}const He=Ze(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),_e=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Ge={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:_e([0,.65,.55,1]),circOut:_e([.55,0,1,.45]),backIn:_e([.31,.01,.66,-.59]),backOut:_e([.33,1.53,.69,.99])};function Je(t,e){return t?"function"==typeof t?He()?jt(t,e):"ease-out":O(t)?_e(t):Array.isArray(t)?t.map(t=>Je(t,e)||Ge.easeOut):Ge[t]:void 0}function Qe(t,e,n,{delay:s=0,duration:i=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=Je(o,i);Array.isArray(c)&&(h.easing=c);const p={delay:s,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};u&&(p.pseudoElement=u);return t.animate(h,p)}function tn(t){return"function"==typeof t&&"applyToOptions"in t}class en extends we{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:i,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=Boolean(i),this.allowFlatten=r,this.options=t,t.type;const l=function({type:t,...e}){return tn(t)&&He()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=Qe(e,n,s,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){const t=ge(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return d(Number(t))}get time(){return d(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=p(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&qe()?(this.animation.timeline=t,o):e(this)}}const nn={anticipate:M,backInOut:T,circInOut:S};function sn(t){"string"==typeof t.ease&&t.ease in nn&&(t.ease=nn[t.ease])}class rn extends en{constructor(t){sn(t),be(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:i,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const a=new Me({...r,autoplay:!1}),o=p(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}const an=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Vt.test(t)&&"0"!==t||t.startsWith("url(")));const on=new Set(["opacity","clipPath","filter","transform"]),ln=a(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function un(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:r,damping:a,type:o}=t;if(l=e?.owner?.current,!i(l)||!("offsetHeight"in l))return!1;var l;const{onUpdate:u,transformTemplate:h}=e.owner.getProps();return ln()&&n&&on.has(n)&&("transform"!==n||!h)&&!u&&!s&&"mirror"!==r&&0!==a&&"inertia"!==o}class hn extends we{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=L.now();const c={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:i,repeatType:r,name:o,motionValue:l,element:u,...h},p=u?.KeyframeResolver||Xe;this.keyframeResolver=new p(a,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,s,i){this.keyframeResolver=void 0;const{name:r,type:a,velocity:l,delay:u,isHandoff:h,onUpdate:c}=s;this.resolvedAt=L.now(),function(t,e,n,s){const i=t[0];if(null===i)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],a=an(i,e),o=an(r,e);return!(!a||!o)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||tn(n))&&s)}(t,r,a,l)||(!n.instantAnimations&&u||c?.(ge(t,s,e)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const p={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...s,keyframes:t},d=!h&&un(p)?new rn({...p,element:p.motionValue.owner.current}):new Me(p);d.finished.then(()=>this.notifyFinished()).catch(o),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),$e=!0,Ue(),Le(),$e=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class cn{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class pn extends cn{then(t,e){return this.finished.finally(t).then(()=>{})}}const dn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function mn(t,e,n=1){const[i,r]=function(t){const e=dn.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}(t);if(!i)return;const a=window.getComputedStyle(e).getPropertyValue(i);if(a){const t=a.trim();return s(t)?parseFloat(t):t}return z(r)?mn(r,e,n+1):r}function fn(t,e){return t?.[e]??t?.default??t}const yn=new Set(["width","height","top","left","right","bottom",...De]),gn=t=>e=>e.test(t),vn=[H,ut,lt,ot,ct,ht,{test:t=>"auto"===t,parse:t=>t}],bn=t=>vn.find(gn(t));function wn(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||r(t))}const Tn=new Set(["brightness","contrast","saturate","opacity"]);function Mn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(Q)||[];if(!s)return t;const i=n.replace(s,"");let r=Tn.has(e)?1:0;return s!==n&&(r*=100),e+"("+r+i+")"}const Vn=/\b([a-z-]*)\(.*?\)/gu,An={...Vt,getAnimatableNone:t=>{const e=t.match(Vn);return e?e.map(Mn).join(" "):t}},Sn={...H,transform:Math.round},xn={borderWidth:ut,borderTopWidth:ut,borderRightWidth:ut,borderBottomWidth:ut,borderLeftWidth:ut,borderRadius:ut,radius:ut,borderTopLeftRadius:ut,borderTopRightRadius:ut,borderBottomRightRadius:ut,borderBottomLeftRadius:ut,width:ut,maxWidth:ut,height:ut,maxHeight:ut,top:ut,right:ut,bottom:ut,left:ut,padding:ut,paddingTop:ut,paddingRight:ut,paddingBottom:ut,paddingLeft:ut,margin:ut,marginTop:ut,marginRight:ut,marginBottom:ut,marginLeft:ut,backgroundPositionX:ut,backgroundPositionY:ut,...{rotate:ot,rotateX:ot,rotateY:ot,rotateZ:ot,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ot,skewX:ot,skewY:ot,distance:ut,translateX:ut,translateY:ut,translateZ:ut,x:ut,y:ut,z:ut,perspective:ut,transformPerspective:ut,opacity:_,originX:pt,originY:pt,originZ:ut},zIndex:Sn,fillOpacity:_,strokeOpacity:_,numOctaves:Sn},kn={...xn,color:mt,backgroundColor:mt,outlineColor:mt,fill:mt,stroke:mt,borderColor:mt,borderTopColor:mt,borderRightColor:mt,borderBottomColor:mt,borderLeftColor:mt,filter:An,WebkitFilter:An},Cn=t=>kn[t];function Fn(t,e){let n=Cn(t);return n!==An&&(n=Vt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Pn=new Set(["auto","none","0"]);class On extends Xe{constructor(t,e,n,s,i){super(t,e,n,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),z(s))){const i=mn(s,e.current);void 0!==i&&(t[n]=i),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!yn.has(n)||2!==t.length)return;const[s,i]=t,r=bn(s),a=bn(i);if(r!==a)if(Re(r)&&Re(a))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ke[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||wn(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,i=0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!Pn.has(e)&&bt(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Fn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ke[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const i=n.length-1,r=n[i];n[i]=Ke[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const En=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class Dn{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=L.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=L.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new c);const n=this.events[t].add(e);return"change"===t?()=>{n(),N.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=L.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return m(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function In(t,e){return new Dn(t,e)}function Rn(t){return i(t)&&"ownerSVGElement"in t}const Bn=t=>Boolean(t&&t.getVelocity),Nn=[...vn,mt,Vt];function Kn(t){return"object"==typeof t&&!Array.isArray(t)}function jn(t,e,n,s){return"string"==typeof t&&Kn(e)?function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const i=n?.[t]??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function Wn(t,e,n){return t*(e+1)}function Yn(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):s.get(e)??t}function $n(e,n,s,i,r,a){!function(e,n,s){for(let i=0;i<e.length;i++){const r=e[i];r.at>n&&r.at<s&&(t(e,r),i--)}}(e,r,a);for(let t=0;t<n.length;t++)e.push({value:n[t],at:xt(r,a,i[t]),easing:P(s,t)})}function Ln(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Un(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function Xn(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function qn(t,e){return e[t]||(e[t]=[]),e[t]}function zn(t){return Array.isArray(t)?t:[t]}function Zn(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Hn=t=>"number"==typeof t,_n=t=>t.every(Hn),Gn=new WeakMap;function Jn(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Qn(t,e,n,s){if("function"==typeof e){const[i,r]=Jn(s);e=e(void 0!==n?n:t.custom,i,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[i,r]=Jn(s);e=e(void 0!==n?n:t.custom,i,r)}return e}function ts(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,In(n))}function es(t){return(t=>Array.isArray(t))(t)?t[t.length-1]||0:t}function ns(t,e){const n=function(t,e,n){const s=t.getProps();return Qn(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:i={},...r}=n||{};r={...r,...s};for(const e in r){ts(t,e,es(r[e]))}}function ss(t,e){const s=t.getValue("willChange");if(i=s,Boolean(Bn(i)&&i.add))return s.add(e);if(!s&&n.WillChange){const s=new n.WillChange("auto");t.addValue("willChange",s),s.add(e)}var i}const is=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),rs="data-"+is("framerAppearId");function as(t){return t.props[rs]}const os=t=>null!==t;const ls={type:"spring",stiffness:500,damping:25,restSpeed:10},us={type:"keyframes",duration:.8},hs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},cs=(t,{keyframes:e})=>e.length>2?us:Ie.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:ls:hs;const ps=(t,e,s,i={},r,a)=>o=>{const l=fn(i,t)||{},u=l.delay||i.delay||0;let{elapsed:h=0}=i;h-=p(u);const c={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:a?void 0:r};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(l)||Object.assign(c,cs(t,c)),c.duration&&(c.duration=p(c.duration)),c.repeatDelay&&(c.repeatDelay=p(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let d=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(d=!0)),(n.instantAnimations||n.skipAnimations)&&(d=!0,c.duration=0,c.delay=0),c.allowFlatten=!l.type&&!l.ease,d&&!a&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(os),r=e&&"loop"!==n&&e%2==1?0:i.length-1;return r&&void 0!==s?s:i[r]}(c.keyframes,l);if(void 0!==t)return void N.update(()=>{c.onUpdate(t),c.onComplete()})}return l.isSync?new Me(c):new hn(c)};function ds({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function ms(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;s&&(r=s);const l=[],u=i&&t.animationState&&t.animationState.getState()[i];for(const e in o){const s=t.getValue(e,t.latestValues[e]??null),i=o[e];if(void 0===i||u&&ds(u,e))continue;const a={delay:n,...fn(r||{},e)},h=s.get();if(void 0!==h&&!s.isAnimating&&!Array.isArray(i)&&i===h&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const n=as(t);if(n){const t=window.MotionHandoffAnimation(n,e,N);null!==t&&(a.startTime=t,c=!0)}}ss(t,e),s.start(ps(e,s,i,t.shouldReduceMotion&&yn.has(e)?{type:!1}:a,t,c));const p=s.animation;p&&l.push(p)}return a&&Promise.all(l).then(()=>{N.update(()=>{a&&ns(t,a)})}),l}const fs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ys={};for(const t in fs)ys[t]={isEnabled:e=>fs[t].some(t=>!!e[t])};const gs=()=>({x:{min:0,max:0},y:{min:0,max:0}}),vs="undefined"!=typeof window,bs={current:null},ws={current:!1};const Ts=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function Ms(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||Ts.some(e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e]));var e}const Vs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class As{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:i,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Xe,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=L.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,N.render(this.render,!1,!0))};const{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=Boolean(i),this.isControllingVariants=Ms(e),this.isVariantNode=function(t){return Boolean(Ms(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==o[t]&&Bn(e)&&e.set(o[t],!1)}}mount(t){this.current=t,Gn.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ws.current||function(){if(ws.current=!0,vs)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>bs.current=t.matches;t.addListener(e),e()}else bs.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||bs.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),K(this.notifyUpdate),K(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Ie.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&N.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),i=e.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i(),r&&r(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ys){const e=ys[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Vs.length;e++){const n=Vs[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const i=e[s],r=n[s];if(Bn(i))t.addValue(s,i);else if(Bn(r))t.addValue(s,In(i,{owner:t}));else if(r!==i)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(i):e.hasAnimated||e.set(i)}else{const e=t.getStaticValue(s);t.addValue(s,In(void 0!==e?e:i,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=In(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(s(n)||r(n))?n=parseFloat(n):(i=n,!Nn.find(gn(i))&&Vt.test(e)&&(n=Fn(t,e))),this.setBaseTarget(t,Bn(n)?n.get():n)),Bn(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=Qn(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Bn(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new c),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Ss extends As{constructor(){super(...arguments),this.KeyframeResolver=On}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Bn(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}const xs={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ks=De.length;function Cs(t,e,n){const{style:s,vars:i,transformOrigin:r}=t;let a=!1,o=!1;for(const t in e){const n=e[t];if(Ie.has(t))a=!0;else if(X(t))i[t]=n;else{const e=En(n,xn[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(e.transform||(a||n?s.transform=function(t,e,n){let s="",i=!0;for(let r=0;r<ks;r++){const a=De[r],o=t[a];if(void 0===o)continue;let l=!0;if(l="number"==typeof o?o===(a.startsWith("scale")?1:0):0===parseFloat(o),!l||n){const t=En(o,xn[a]);l||(i=!1,s+=`${xs[a]||a}(${t}) `),n&&(e[a]=t)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),o){const{originX:t="50%",originY:e="50%",originZ:n=0}=r;s.transformOrigin=`${t} ${e} ${n}`}}function Fs(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}const Ps={};function Os(t,{layout:e,layoutId:n}){return Ie.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ps[t]||"opacity"===t)}function Es(t,e,n){const{style:s}=t,i={};for(const r in s)(Bn(s[r])||e.style&&Bn(e.style[r])||Os(r,t)||void 0!==n?.getValue(r)?.liveStyle)&&(i[r]=s[r]);return i}class Ds extends Ss{constructor(){super(...arguments),this.type="html",this.renderInstance=Fs}readValueFromInstance(t,e){if(Ie.has(e))return this.projection?.isProjecting?Pe(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Oe(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),i=(X(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){Cs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Es(t,e,n)}}class Is extends As{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}const Rs={offset:"stroke-dashoffset",array:"stroke-dasharray"},Bs={offset:"strokeDashoffset",array:"strokeDasharray"};function Ns(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:r=1,pathOffset:a=0,...o},l,u,h){if(Cs(t,o,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:p}=t;c.transform&&(p.transform=c.transform,delete c.transform),(p.transform||c.transformOrigin)&&(p.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),p.transform&&(p.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==s&&(c.scale=s),void 0!==i&&function(t,e,n=1,s=0,i=!0){t.pathLength=1;const r=i?Rs:Bs;t[r.offset]=ut.transform(-s);const a=ut.transform(e),o=ut.transform(n);t[r.array]=`${a} ${o}`}(c,i,r,a,!1)}const Ks=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class js extends Ss{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=gs}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Ie.has(e)){const t=Cn(e);return t&&t.default||0}return e=Ks.has(e)?e:is(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=Es(t,e,n);for(const n in t)(Bn(t[n])||Bn(e[n]))&&(s[-1!==De.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}build(t,e,n){Ns(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){Fs(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(Ks.has(n)?n:is(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}function Ws(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Rn(t)&&!function(t){return Rn(t)&&"svg"===t.tagName}(t)?new js(e):new Ds(e);n.mount(t),Gn.set(t,n)}function Ys(t){const e=new Is({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),Gn.set(t,e)}function $s(t,e,n,s){const i=[];if(function(t,e){return Bn(t)||"number"==typeof t||"string"==typeof t&&!Kn(e)}(t,e))i.push(function(t,e,n){const s=Bn(t)?t:In(t);return s.start(ps("",s,e,n)),s.animation}(t,Kn(e)&&e.default||e,n&&n.default||n));else{const r=jn(t,e,s),a=r.length;for(let t=0;t<a;t++){const s=r[t],o=s instanceof Element?Ws:Ys;Gn.has(s)||o(s);const l=Gn.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,a)),i.push(...ms(l,{...e,transition:u},{}))}}return i}function Ls(t,e,n){const s=[],i=function(t,{defaultTransition:e={},...n}={},s,i){const r=e.duration||.3,a=new Map,o=new Map,l={},u=new Map;let c=0,d=0,m=0;for(let n=0;n<t.length;n++){const a=t[n];if("string"==typeof a){u.set(a,d);continue}if(!Array.isArray(a)){u.set(a.name,Yn(d,a.at,c,u));continue}let[h,f,y={}]=a;void 0!==y.at&&(d=Yn(d,y.at,c,u));let g=0;const v=(t,n,s,a=0,o=0)=>{const l=zn(t),{delay:u=0,times:h=me(l),type:c="keyframes",repeat:f,repeatType:y,repeatDelay:v=0,...b}=n;let{ease:w=e.ease||"easeOut",duration:T}=n;const M="function"==typeof u?u(a,o):u,V=l.length,A=tn(c)?c:i?.[c||"keyframes"];if(V<=2&&A){let t=100;if(2===V&&_n(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...b};void 0!==T&&(e.duration=p(T));const n=$t(e,t,A);w=n.ease,T=n.duration}T??(T=r);const S=d+M;1===h.length&&0===h[0]&&(h[1]=1);const x=h.length-l.length;if(x>0&&de(h,x),1===l.length&&l.unshift(null),f){T=Wn(T,f);const t=[...l],e=[...h];w=Array.isArray(w)?[...w]:[w];const n=[...w];for(let s=0;s<f;s++){l.push(...t);for(let i=0;i<t.length;i++)h.push(e[i]+(s+1)),w.push(0===i?"linear":P(n,i-1))}Ln(h,f)}const k=S+T;$n(s,l,w,h,S,k),g=Math.max(M+T,g),m=Math.max(k,m)};if(Bn(h))v(f,y,qn("default",Xn(h,o)));else{const t=jn(h,f,s,l),e=t.length;for(let n=0;n<e;n++){const s=Xn(t[n],o);for(const t in f)v(f[t],Zn(y,t),qn(t,s),n,e)}}c=d,d+=g}return o.forEach((t,s)=>{for(const i in t){const r=t[i];r.sort(Un);const o=[],l=[],u=[];for(let t=0;t<r.length;t++){const{at:e,value:n,easing:s}=r[t];o.push(n),l.push(h(0,m,e)),u.push(s||"easeOut")}0!==l[0]&&(l.unshift(0),o.unshift(o[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),o.push(null)),a.has(s)||a.set(s,{keyframes:{},transition:{}});const c=a.get(s);c.keyframes[i]=o,c.transition[i]={...e,duration:m,ease:u,times:l,...n}}}),a}(t,e,n,{spring:he});return i.forEach(({keyframes:t,transition:e},n)=>{s.push(...$s(n,t,e))}),s}function Us(e){return function(n,s,i){let r=[];var a;a=n,r=Array.isArray(a)&&a.some(Array.isArray)?Ls(n,s,e):$s(n,s,i,e);const o=new pn(r);return e&&(e.animations.push(o),o.finished.then(()=>{t(e.animations,o)})),o}}const Xs=Us();export{Xs as animate,Us as createScopedAnimate};
