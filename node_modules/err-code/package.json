{"name": "err-code", "version": "3.0.1", "description": "Create an error with a code", "main": "index.js", "types": "dist/index.d.ts", "scripts": {"lint": "eslint '{./*.js,test/**/*.js}'", "check": "tsc --noEmit", "prepare": "tsc --emitDeclarationOnly --declarationDir dist", "test": "mocha --bail", "prepublishOnly": "browserify -s err-code index.js > index.umd.js"}, "bugs": {"url": "https://github.com/IndigoUnited/js-err-code/issues/"}, "repository": {"type": "git", "url": "git://github.com/IndigoUnited/js-err-code.git"}, "keywords": ["error", "err", "code", "properties", "property"], "author": "IndigoUnited <<EMAIL>> (http://indigounited.com)", "license": "MIT", "devDependencies": {"@satazor/eslint-config": "^3.0.0", "@types/expect.js": "0.3.29", "@types/mocha": "8.2.0", "browserify": "^17.0.0", "eslint": "^7.19.0", "expect.js": "^0.3.1", "mocha": "^8.2.1", "typescript": "^4.1.3"}, "files": ["index.js", "index.umd.js", "dist/index.d.ts"]}