{"name": "@types/simple-peer", "version": "9.11.8", "description": "TypeScript definitions for simple-peer", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/simple-peer", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tlaziuk"}, {"name": "xWiiLLz", "githubUsername": "xWiiLLz", "url": "https://github.com/xWiiLLz"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/simple-peer"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "def9fe00f6b805a9f2134ed7f2a0265f25b081fbaf3df2c70f4d09eef279bb13", "typeScriptVersion": "4.5"}