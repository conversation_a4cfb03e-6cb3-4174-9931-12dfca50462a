!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,function(t){"use strict";function e(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const s=(t,e,n)=>n>e?e:n<t?t:n;let r=()=>{};const i={},o=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function a(t){return"object"==typeof t&&null!==t}const l=t=>/^0[^.\s]+$/u.test(t);function u(t){let e;return()=>(void 0===e&&(e=t()),e)}const c=t=>t,h=(t,e)=>n=>e(t(n)),d=(...t)=>t.reduce(h),p=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class f{constructor(){this.subscriptions=[]}add(t){return e(this.subscriptions,t),()=>n(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let r=0;r<s;r++){const s=this.subscriptions[r];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const m=t=>1e3*t,g=t=>t/1e3;function y(t,e){return e?t*(1e3/e):0}const v=new Set;const w=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t},b=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function T(t,e,n,s){if(t===e&&n===s)return c;const r=e=>function(t,e,n,s,r){let i,o,a=0;do{o=e+(n-e)/2,i=b(o,s,r)-t,i>0?n=o:e=o}while(Math.abs(i)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:b(r(t),e,s)}const x=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,V=t=>e=>1-t(1-e),M=T(.33,1.53,.69,.99),A=V(M),S=x(A),k=t=>(t*=2)<1?.5*A(t):.5*(2-Math.pow(2,-10*(t-1))),E=t=>1-Math.sin(Math.acos(t)),P=V(E),C=x(E),O=T(.42,0,1,1),F=T(0,0,.58,1),R=T(.42,0,.58,1);const B=t=>Array.isArray(t)&&"number"!=typeof t[0];function D(t,e){return B(t)?t[w(0,t.length,e)]:t}const L=t=>Array.isArray(t)&&"number"==typeof t[0],W={linear:c,easeIn:O,easeInOut:R,easeOut:F,circIn:E,circInOut:C,circOut:P,backIn:A,backInOut:S,backOut:M,anticipate:k},I=t=>{if(L(t)){t.length;const[e,n,s,r]=t;return T(e,n,s,r)}return"string"==typeof t?W[t]:t},N=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],j={value:null,addProjectionMetrics:null};function K(t,e){let n=!1,s=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,a=N.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,r=!1,i=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,i=!1)=>{const a=i&&r?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,r?i=!0:(r=!0,[n,s]=[s,n],n.forEach(u),e&&j.value&&j.value.frameloop[e].push(l),l=0,n.clear(),r=!1,i&&(i=!1,c.process(t)))}};return c}(o,e?n:void 0),t),{}),{setup:l,read:u,resolveKeyframes:c,preUpdate:h,update:d,preRender:p,render:f,postRender:m}=a,g=()=>{const o=i.useManualTiming?r.timestamp:performance.now();n=!1,i.useManualTiming||(r.delta=s?1e3/60:Math.max(Math.min(o-r.timestamp,40),1)),r.timestamp=o,r.isProcessing=!0,l.process(r),u.process(r),c.process(r),h.process(r),d.process(r),p.process(r),f.process(r),m.process(r),r.isProcessing=!1,n&&e&&(s=!1,t(g))};return{schedule:N.reduce((e,i)=>{const o=a[i];return e[i]=(e,i=!1,a=!1)=>(n||(n=!0,s=!0,r.isProcessing||t(g)),o.schedule(e,i,a)),e},{}),cancel:t=>{for(let e=0;e<N.length;e++)a[N[e]].cancel(t)},state:r,steps:a}}const{schedule:$,cancel:U,state:Y,steps:z}=K("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:c,!0);let X;function H(){X=void 0}const q={now:()=>(void 0===X&&q.set(Y.isProcessing||i.useManualTiming?Y.timestamp:performance.now()),X),set:t=>{X=t,queueMicrotask(H)}},G={layout:0,mainThread:0,waapi:0},Z=t=>e=>"string"==typeof e&&e.startsWith(t),_=Z("--"),J=Z("var(--"),Q=t=>!!J(t)&&tt.test(t.split("/*")[0].trim()),tt=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,et={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},nt={...et,transform:t=>s(0,1,t)},st={...et,default:1},rt=t=>Math.round(1e5*t)/1e5,it=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ot=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,at=(t,e)=>n=>Boolean("string"==typeof n&&ot.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),lt=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[r,i,o,a]=s.match(it);return{[t]:parseFloat(r),[e]:parseFloat(i),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ut={...et,transform:t=>Math.round((t=>s(0,255,t))(t))},ct={test:at("rgb","red"),parse:lt("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+ut.transform(t)+", "+ut.transform(e)+", "+ut.transform(n)+", "+rt(nt.transform(s))+")"};const ht={test:at("#"),parse:function(t){let e="",n="",s="",r="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,n+=n,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:ct.transform},dt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),pt=dt("deg"),ft=dt("%"),mt=dt("px"),gt=dt("vh"),yt=dt("vw"),vt=(()=>({...ft,parse:t=>ft.parse(t)/100,transform:t=>ft.transform(100*t)}))(),wt={test:at("hsl","hue"),parse:lt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ft.transform(rt(e))+", "+ft.transform(rt(n))+", "+rt(nt.transform(s))+")"},bt={test:t=>ct.test(t)||ht.test(t)||wt.test(t),parse:t=>ct.test(t)?ct.parse(t):wt.test(t)?wt.parse(t):ht.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ct.transform(t):wt.transform(t),getAnimatableNone:t=>{const e=bt.parse(t);return e.alpha=0,bt.transform(e)}},Tt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const xt="number",Vt="color",Mt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function At(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},r=[];let i=0;const o=e.replace(Mt,t=>(bt.test(t)?(s.color.push(i),r.push(Vt),n.push(bt.parse(t))):t.startsWith("var(")?(s.var.push(i),r.push("var"),n.push(t)):(s.number.push(i),r.push(xt),n.push(parseFloat(t))),++i,"${}")).split("${}");return{values:n,split:o,indexes:s,types:r}}function St(t){return At(t).values}function kt(t){const{split:e,types:n}=At(t),s=e.length;return t=>{let r="";for(let i=0;i<s;i++)if(r+=e[i],void 0!==t[i]){const e=n[i];r+=e===xt?rt(t[i]):e===Vt?bt.transform(t[i]):t[i]}return r}}const Et=t=>"number"==typeof t?0:bt.test(t)?bt.getAnimatableNone(t):t;const Pt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(it)?.length||0)+(t.match(Tt)?.length||0)>0},parse:St,createTransformer:kt,getAnimatableNone:function(t){const e=St(t);return kt(t)(e.map(Et))}};function Ct(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Ot({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let r=0,i=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;r=Ct(a,s,t+1/3),i=Ct(a,s,t),o=Ct(a,s,t-1/3)}else r=i=o=n;return{red:Math.round(255*r),green:Math.round(255*i),blue:Math.round(255*o),alpha:s}}function Ft(t,e){return n=>n>0?e:t}const Rt=(t,e,n)=>t+(e-t)*n,Bt=(t,e,n)=>{const s=t*t,r=n*(e*e-s)+s;return r<0?0:Math.sqrt(r)},Dt=[ht,ct,wt];function Lt(t){const e=(n=t,Dt.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===wt&&(s=Ot(s)),s}const Wt=(t,e)=>{const n=Lt(t),s=Lt(e);if(!n||!s)return Ft(t,e);const r={...n};return t=>(r.red=Bt(n.red,s.red,t),r.green=Bt(n.green,s.green,t),r.blue=Bt(n.blue,s.blue,t),r.alpha=Rt(n.alpha,s.alpha,t),ct.transform(r))},It=new Set(["none","hidden"]);function Nt(t,e){return It.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function jt(t,e){return n=>Rt(t,e,n)}function Kt(t){return"number"==typeof t?jt:"string"==typeof t?Q(t)?Ft:bt.test(t)?Wt:Yt:Array.isArray(t)?$t:"object"==typeof t?bt.test(t)?Wt:Ut:Ft}function $t(t,e){const n=[...t],s=n.length,r=t.map((t,n)=>Kt(t)(t,e[n]));return t=>{for(let e=0;e<s;e++)n[e]=r[e](t);return n}}function Ut(t,e){const n={...t,...e},s={};for(const r in n)void 0!==t[r]&&void 0!==e[r]&&(s[r]=Kt(t[r])(t[r],e[r]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const Yt=(t,e)=>{const n=Pt.createTransformer(e),s=At(t),r=At(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?It.has(t)&&!r.values.length||It.has(e)&&!s.values.length?Nt(t,e):d($t(function(t,e){const n=[],s={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const i=e.types[r],o=t.indexes[i][s[i]],a=t.values[o]??0;n[r]=a,s[i]++}return n}(s,r),r.values),n):Ft(t,e)};function zt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Rt(t,e,n);return Kt(t)(t,e)}const Xt=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>$.update(e,t),stop:()=>U(e),now:()=>Y.isProcessing?Y.timestamp:q.now()}},Ht=(t,e,n=10)=>{let s="";const r=Math.max(Math.round(e/n),2);for(let e=0;e<r;e++)s+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},qt=2e4;function Gt(t){let e=0;let n=t.next(e);for(;!n.done&&e<qt;)e+=50,n=t.next(e);return e>=qt?1/0:e}function Zt(t,e=100,n){const s=n({...t,keyframes:[0,e]}),r=Math.min(Gt(s),qt);return{type:"keyframes",ease:t=>s.next(r*t).value/e,duration:g(r)}}function _t(t,e,n){const s=Math.max(e-5,0);return y(n-t(s),e-s)}const Jt=100,Qt=10,te=1,ee=0,ne=800,se=.3,re=.3,ie={granular:.01,default:2},oe={granular:.005,default:.5},ae=.01,le=10,ue=.05,ce=1,he=.001;function de({duration:t=ne,bounce:e=se,velocity:n=ee,mass:r=te}){let i,o,a=1-e;a=s(ue,ce,a),t=s(ae,le,g(t)),a<1?(i=e=>{const s=e*a,r=s*t,i=s-n,o=fe(e,a),l=Math.exp(-r);return he-i/o*l},o=e=>{const s=e*a*t,r=s*n+n,o=Math.pow(a,2)*Math.pow(e,2)*t,l=Math.exp(-s),u=fe(Math.pow(e,2),a);return(-i(e)+he>0?-1:1)*((r-o)*l)/u}):(i=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const l=function(t,e,n){let s=n;for(let n=1;n<pe;n++)s-=t(s)/e(s);return s}(i,o,5/t);if(t=m(t),isNaN(l))return{stiffness:Jt,damping:Qt,duration:t};{const e=Math.pow(l,2)*r;return{stiffness:e,damping:2*a*Math.sqrt(r*e),duration:t}}}const pe=12;function fe(t,e){return t*Math.sqrt(1-e*e)}const me=["duration","bounce"],ge=["stiffness","damping","mass"];function ye(t,e){return e.some(e=>void 0!==t[e])}function ve(t=re,e=se){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:r,restDelta:i}=n;const o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:p,isResolvedFromDuration:f}=function(t){let e={velocity:ee,stiffness:Jt,damping:Qt,mass:te,isResolvedFromDuration:!1,...t};if(!ye(t,ge)&&ye(t,me))if(t.visualDuration){const n=t.visualDuration,r=2*Math.PI/(1.2*n),i=r*r,o=2*s(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:te,stiffness:i,damping:o}}else{const n=de(t);e={...e,...n,mass:te},e.isResolvedFromDuration=!0}return e}({...n,velocity:-g(n.velocity||0)}),y=p||0,v=c/(2*Math.sqrt(u*h)),w=a-o,b=g(Math.sqrt(u/h)),T=Math.abs(w)<5;let x;if(r||(r=T?ie.granular:ie.default),i||(i=T?oe.granular:oe.default),v<1){const t=fe(b,v);x=e=>{const n=Math.exp(-v*b*e);return a-n*((y+v*b*w)/t*Math.sin(t*e)+w*Math.cos(t*e))}}else if(1===v)x=t=>a-Math.exp(-b*t)*(w+(y+b*w)*t);else{const t=b*Math.sqrt(v*v-1);x=e=>{const n=Math.exp(-v*b*e),s=Math.min(t*e,300);return a-n*((y+v*b*w)*Math.sinh(s)+t*w*Math.cosh(s))/t}}const V={calculatedDuration:f&&d||null,next:t=>{const e=x(t);if(f)l.done=t>=d;else{let n=0===t?y:0;v<1&&(n=0===t?m(y):_t(x,t,e));const s=Math.abs(n)<=r,o=Math.abs(a-e)<=i;l.done=s&&o}return l.value=l.done?a:e,l},toString:()=>{const t=Math.min(Gt(V),qt),e=Ht(e=>V.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return V}function we({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let f=n*e;const m=h+f,g=void 0===o?m:o(m);g!==m&&(f=g-h);const y=t=>-f*Math.exp(-t/s),v=t=>g+y(t),w=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let b,T;const x=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(b=t,T=ve({keyframes:[d.value,p(d.value)],velocity:_t(v,t,d.value),damping:r,stiffness:i,restDelta:u,restSpeed:c}))};return x(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==b||(e=!0,w(t),x(t)),void 0!==b&&t>=b?T.next(t-b):(!e&&w(t),d)}}}function be(t,e,{clamp:n=!0,ease:r,mixer:o}={}){const a=t.length;if(e.length,1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];const l=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const u=function(t,e,n){const s=[],r=n||i.mix||zt,o=t.length-1;for(let n=0;n<o;n++){let i=r(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||c:e;i=d(t,i)}s.push(i)}return s}(e,r,o),h=u.length,f=n=>{if(l&&n<t[0])return e[0];let s=0;if(h>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const r=p(t[s],t[s+1],n);return u[s](r)};return n?e=>f(s(t[0],t[a-1],e)):f}function Te(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const r=p(0,e,s);t.push(Rt(n,1,r))}}function xe(t){const e=[0];return Te(e,t.length-1),e}function Ve(t,e){return t.map(t=>t*e)}function Me(t,e){return t.map(()=>e||R).splice(0,t.length-1)}function Ae({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const r=B(s)?s.map(I):I(s),i={done:!1,value:e[0]},o=be(Ve(n&&n.length===e.length?n:xe(e),t),e,{ease:Array.isArray(r)?r:Me(e,r)});return{calculatedDuration:t,next:e=>(i.value=o(e),i.done=e>=t,i)}}ve.applyToOptions=t=>{const e=Zt(t,100,ve);return t.ease=e.ease,t.duration=m(e.duration),t.type="keyframes",t};const Se=t=>null!==t;function ke(t,{repeat:e,repeatType:n="loop"},s,r=1){const i=t.filter(Se),o=r<0||e&&"loop"!==n&&e%2==1?0:i.length-1;return o&&void 0!==s?s:i[o]}const Ee={decay:we,inertia:we,tween:Ae,keyframes:Ae,spring:ve};function Pe(t){"string"==typeof t.type&&(t.type=Ee[t.type])}class Ce{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Oe=t=>t/100;class Fe extends Ce{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==q.now()&&this.tick(q.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},G.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Pe(t);const{type:e=Ae,repeat:n=0,repeatDelay:s=0,repeatType:r,velocity:i=0}=t;let{keyframes:o}=t;const a=e||Ae;a!==Ae&&"number"!=typeof o[0]&&(this.mixKeyframes=d(Oe,zt(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-i})),null===l.calculatedDuration&&(l.calculatedDuration=Gt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(n+1)-s,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:r,mixKeyframes:i,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return n.next(0);const{delay:u=0,keyframes:c,repeat:h,repeatType:d,repeatDelay:p,type:f,onUpdate:m,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let w=this.currentTime,b=n;if(h){const t=Math.min(this.currentTime,r)/a;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/a)):"mirror"===d&&(b=o)),w=s(0,1,n)*a}const T=v?{done:!1,value:c[0]}:b.next(w);i&&(T.value=i(T.value));let{done:x}=T;v||null===l||(x=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);const V=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return V&&f!==we&&(T.value=ke(c,this.options,g,this.speed)),m&&m(T.value),V&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return g(this.calculatedDuration)}get time(){return g(this.currentTime)}set time(t){t=m(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(q.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=g(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Xt,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(q.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,G.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function Re(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const Be=t=>180*t/Math.PI,De=t=>{const e=Be(Math.atan2(t[1],t[0]));return We(e)},Le={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:De,rotateZ:De,skewX:t=>Be(Math.atan(t[1])),skewY:t=>Be(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},We=t=>((t%=360)<0&&(t+=360),t),Ie=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ne=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),je={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ie,scaleY:Ne,scale:t=>(Ie(t)+Ne(t))/2,rotateX:t=>We(Be(Math.atan2(t[6],t[5]))),rotateY:t=>We(Be(Math.atan2(-t[2],t[0]))),rotateZ:De,rotate:De,skewX:t=>Be(Math.atan(t[4])),skewY:t=>Be(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Ke(t){return t.includes("scale")?1:0}function $e(t,e){if(!t||"none"===t)return Ke(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,r;if(n)s=je,r=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=Le,r=e}if(!r)return Ke(e);const i=s[e],o=r[1].split(",").map(Ye);return"function"==typeof i?i(o):o[i]}const Ue=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return $e(n,e)};function Ye(t){return parseFloat(t.trim())}const ze=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Xe=(()=>new Set(ze))(),He=t=>t===et||t===mt,qe=new Set(["x","y","z"]),Ge=ze.filter(t=>!qe.has(t));const Ze={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>$e(e,"x"),y:(t,{transform:e})=>$e(e,"y")};Ze.translateX=Ze.x,Ze.translateY=Ze.y;const _e=new Set;let Je=!1,Qe=!1,tn=!1;function en(){if(Qe){const t=Array.from(_e).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Ge.forEach(n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Qe=!1,Je=!1,_e.forEach(t=>t.complete(tn)),_e.clear()}function nn(){_e.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Qe=!0)})}function sn(){tn=!0,nn(),en(),tn=!1}class rn{constructor(t,e,n,s,r,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=r,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(_e.add(this),Je||(Je=!0,$.read(nn),$.resolveKeyframes(en))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;if(null===t[0]){const r=s?.get(),i=t[t.length-1];if(void 0!==r)t[0]=r;else if(n&&e){const s=n.readValue(e,i);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=i),s&&void 0===r&&s.set(t[0])}Re(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),_e.delete(this)}cancel(){"scheduled"===this.state&&(_e.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const on=t=>t.startsWith("--");function an(t,e,n){on(e)?t.style.setProperty(e,n):t.style[e]=n}const ln=u(()=>void 0!==window.ScrollTimeline),un={};function cn(t,e){const n=u(t);return()=>un[e]??n()}const hn=cn(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),dn=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,pn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:dn([0,.65,.55,1]),circOut:dn([.55,0,1,.45]),backIn:dn([.31,.01,.66,-.59]),backOut:dn([.33,1.53,.69,.99])};function fn(t,e){return t?"function"==typeof t?hn()?Ht(t,e):"ease-out":L(t)?dn(t):Array.isArray(t)?t.map(t=>fn(t,e)||pn.easeOut):pn[t]:void 0}function mn(t,e,n,{delay:s=0,duration:r=300,repeat:i=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=fn(a,r);Array.isArray(h)&&(c.easing=h),j.value&&G.waapi++;const d={delay:s,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);const p=t.animate(c,d);return j.value&&p.finished.finally(()=>{G.waapi--}),p}function gn(t){return"function"==typeof t&&"applyToOptions"in t}function yn({type:t,...e}){return gn(t)&&hn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class vn extends Ce{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:s,pseudoElement:r,allowFlatten:i=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(r),this.allowFlatten=i,this.options=t,t.type;const l=yn(t);this.animation=mn(e,n,s,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const t=ke(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):an(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return g(Number(t))}get time(){return g(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=m(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ln()?(this.animation.timeline=t,c):e(this)}}const wn={anticipate:k,backInOut:S,circInOut:C};function bn(t){"string"==typeof t.ease&&t.ease in wn&&(t.ease=wn[t.ease])}class Tn extends vn{constructor(t){bn(t),Pe(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:s,element:r,...i}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Fe({...i,autoplay:!1}),a=m(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const xn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Pt.test(t)&&"0"!==t||t.startsWith("url(")));function Vn(t){return a(t)&&"offsetHeight"in t}const Mn=new Set(["opacity","clipPath","filter","transform"]),An=u(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Sn(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:r,damping:i,type:o}=t;if(!Vn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return An()&&n&&Mn.has(n)&&("transform"!==n||!l)&&!a&&!s&&"mirror"!==r&&0!==i&&"inertia"!==o}class kn extends Ce{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:i="loop",keyframes:o,name:a,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=q.now();const h={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:r,repeatType:i,name:a,motionValue:l,element:u,...c},d=u?.KeyframeResolver||rn;this.keyframeResolver=new d(o,(t,e,n)=>this.onKeyframesResolved(t,e,h,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,s){this.keyframeResolver=void 0;const{name:r,type:o,velocity:a,delay:l,isHandoff:u,onUpdate:h}=n;this.resolvedAt=q.now(),function(t,e,n,s){const r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;const i=t[t.length-1],o=xn(r,e),a=xn(i,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||gn(n))&&s)}(t,r,o,a)||(!i.instantAnimations&&l||h?.(ke(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const d={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},p=!u&&Sn(d)?new Tn({...d,element:d.motionValue.owner.current}):new Fe(d);p.finished.then(()=>this.notifyFinished()).catch(c),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),sn()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class En{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t){const e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Pn extends En{then(t,e){return this.finished.finally(t).then(()=>{})}}class Cn extends vn{constructor(t){super(),this.animation=t,t.onfinish=()=>{this.finishedTime=this.time,this.notifyFinished()}}}const On=new WeakMap,Fn=(t,e="")=>`${t}:${e}`;function Rn(t){const e=On.get(t)||new Map;return On.set(t,e),e}const Bn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Dn(t){const e=Bn.exec(t);if(!e)return[,];const[,n,s,r]=e;return[`--${n??s}`,r]}function Ln(t,e,n=1){const[s,r]=Dn(t);if(!s)return;const i=window.getComputedStyle(e).getPropertyValue(s);if(i){const t=i.trim();return o(t)?parseFloat(t):t}return Q(r)?Ln(r,e,n+1):r}function Wn(t,e){return t?.[e]??t?.default??t}const In=new Set(["width","height","top","left","right","bottom",...ze]),Nn=t=>e=>e.test(t),jn=[et,mt,ft,pt,yt,gt,{test:t=>"auto"===t,parse:t=>t}],Kn=t=>jn.find(Nn(t));function $n(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||l(t))}const Un=new Set(["brightness","contrast","saturate","opacity"]);function Yn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(it)||[];if(!s)return t;const r=n.replace(s,"");let i=Un.has(e)?1:0;return s!==n&&(i*=100),e+"("+i+r+")"}const zn=/\b([a-z-]*)\(.*?\)/gu,Xn={...Pt,getAnimatableNone:t=>{const e=t.match(zn);return e?e.map(Yn).join(" "):t}},Hn={...et,transform:Math.round},qn={rotate:pt,rotateX:pt,rotateY:pt,rotateZ:pt,scale:st,scaleX:st,scaleY:st,scaleZ:st,skew:pt,skewX:pt,skewY:pt,distance:mt,translateX:mt,translateY:mt,translateZ:mt,x:mt,y:mt,z:mt,perspective:mt,transformPerspective:mt,opacity:nt,originX:vt,originY:vt,originZ:mt},Gn={borderWidth:mt,borderTopWidth:mt,borderRightWidth:mt,borderBottomWidth:mt,borderLeftWidth:mt,borderRadius:mt,radius:mt,borderTopLeftRadius:mt,borderTopRightRadius:mt,borderBottomRightRadius:mt,borderBottomLeftRadius:mt,width:mt,maxWidth:mt,height:mt,maxHeight:mt,top:mt,right:mt,bottom:mt,left:mt,padding:mt,paddingTop:mt,paddingRight:mt,paddingBottom:mt,paddingLeft:mt,margin:mt,marginTop:mt,marginRight:mt,marginBottom:mt,marginLeft:mt,backgroundPositionX:mt,backgroundPositionY:mt,...qn,zIndex:Hn,fillOpacity:nt,strokeOpacity:nt,numOctaves:Hn},Zn={...Gn,color:bt,backgroundColor:bt,outlineColor:bt,fill:bt,stroke:bt,borderColor:bt,borderTopColor:bt,borderRightColor:bt,borderBottomColor:bt,borderLeftColor:bt,filter:Xn,WebkitFilter:Xn},_n=t=>Zn[t];function Jn(t,e){let n=_n(t);return n!==Xn&&(n=Pt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Qn=new Set(["auto","none","0"]);class ts extends rn{constructor(t,e,n,s,r){super(t,e,n,s,r,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),Q(s))){const r=Ln(s,e.current);void 0!==r&&(t[n]=r),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!In.has(n)||2!==t.length)return;const[s,r]=t,i=Kn(s),o=Kn(r);if(i!==o)if(He(i)&&He(o))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else Ze[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)(null===t[e]||$n(t[e]))&&n.push(e);n.length&&function(t,e,n){let s,r=0;for(;r<t.length&&!s;){const e=t[r];"string"==typeof e&&!Qn.has(e)&&At(e).values.length&&(s=t[r]),r++}if(s&&n)for(const r of e)t[r]=Jn(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ze[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const r=n.length-1,i=n[r];n[r]=Ze[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==i&&void 0===this.finalKeyframe&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const es=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);function ns(t,e){for(let n=0;n<t.length;n++)"number"==typeof t[n]&&es.has(e)&&(t[n]=t[n]+"px")}const ss=u(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),rs=new Set(["opacity","clipPath","filter","transform"]);function is(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const r=n?.[t]??s.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}function os(t){return(e,n)=>{const s=is(e),r=[];for(const e of s){const s=t(e,n);r.push(s)}return()=>{for(const t of r)t()}}}const as=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class ls{constructor(){this.latest={},this.values=new Map}set(t,e,n,s,r=!0){const i=this.values.get(t);i&&i.onRemove();const o=()=>{const s=e.get();this.latest[t]=r?as(s,Gn[t]):s,n&&$.render(n)};o();const a=e.on("change",o);s&&e.addDependent(s);const l=()=>{a(),n&&U(n),this.values.delete(t),s&&e.removeDependent(s)};return this.values.set(t,{value:e,onRemove:l}),l}get(t){return this.values.get(t)?.value}destroy(){for(const t of this.values.values())t.onRemove()}}function us(t){const e=new WeakMap,n=[];return(s,r)=>{const i=e.get(s)??new ls;e.set(s,i);for(const e in r){const o=r[e],a=t(s,i,e,o);n.push(a)}return()=>{for(const t of n)t()}}}const cs=(t,e,n,s)=>{const r=function(t,e){if(!(e in t))return!1;const n=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(t),e)||Object.getOwnPropertyDescriptor(t,e);return n&&"function"==typeof n.set}(t,n),i=r?n:n.startsWith("data")||n.startsWith("aria")?n.replace(/([A-Z])/g,t=>`-${t.toLowerCase()}`):n;const o=r?()=>{t[i]=e.latest[n]}:()=>{const s=e.latest[n];null==s?t.removeAttribute(i):t.setAttribute(i,String(s))};return e.set(n,s,o)},hs=os(us(cs)),ds=us((t,e,n,s)=>e.set(n,s,()=>{t[n]=e.latest[n]},void 0,!1)),ps={current:void 0};class fs{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=q.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new f);const n=this.events[t].add(e);return"change"===t?()=>{n(),$.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return ps.current&&ps.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return y(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ms(t,e){return new fs(t,e)}const gs={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"};const ys=new Set(["originX","originY","originZ"]),vs=(t,e,n,s)=>{let r,i;return Xe.has(n)?(e.get("transform")||(Vn(t)||e.get("transformBox")||vs(t,e,"transformBox",new fs("fill-box")),e.set("transform",new fs("none"),()=>{t.style.transform=function(t){let e="",n=!0;for(let s=0;s<ze.length;s++){const r=ze[s],i=t.latest[r];if(void 0===i)continue;let o=!0;o="number"==typeof i?i===(r.startsWith("scale")?1:0):0===parseFloat(i),o||(n=!1,e+=`${gs[r]||r}(${t.latest[r]}) `)}return n?"none":e.trim()}(e)})),i=e.get("transform")):ys.has(n)?(e.get("transformOrigin")||e.set("transformOrigin",new fs(""),()=>{const n=e.latest.originX??"50%",s=e.latest.originY??"50%",r=e.latest.originZ??0;t.style.transformOrigin=`${n} ${s} ${r}`}),i=e.get("transformOrigin")):r=on(n)?()=>{t.style.setProperty(n,e.latest[n])}:()=>{t.style[n]=e.latest[n]},e.set(n,s,r,i)},ws=os(us(vs)),bs=mt.transform;const Ts=os(us((t,e,n,s)=>{if(n.startsWith("path"))return function(t,e,n,s){return $.render(()=>t.setAttribute("pathLength","1")),"pathOffset"===n?e.set(n,s,()=>t.setAttribute("stroke-dashoffset",bs(-e.latest[n]))):(e.get("stroke-dasharray")||e.set("stroke-dasharray",new fs("1 1"),()=>{const{pathLength:n=1,pathSpacing:s}=e.latest;t.setAttribute("stroke-dasharray",`${bs(n)} ${bs(s??1-Number(n))}`)}),e.set(n,s,void 0,e.get("stroke-dasharray")))}(t,e,n,s);if(n.startsWith("attr"))return cs(t,e,function(t){return t.replace(/^attr([A-Z])/,(t,e)=>e.toLowerCase())}(n),s);return(n in t.style?vs:cs)(t,e,n,s)}));const{schedule:xs,cancel:Vs}=K(queueMicrotask,!1),Ms={x:!1,y:!1};function As(){return Ms.x||Ms.y}function Ss(t,e){const n=is(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function ks(t){return!("touch"===t.pointerType||As())}const Es=(t,e)=>!!e&&(t===e||Es(t,e.parentElement)),Ps=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Cs=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Os=new WeakSet;function Fs(t){return e=>{"Enter"===e.key&&t(e)}}function Rs(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Bs(t){return Ps(t)&&!As()}function Ds(t,e){const n=window.getComputedStyle(t);return on(e)?n.getPropertyValue(e):n[e]}function Ls(t){return a(t)&&"ownerSVGElement"in t}const Ws=new WeakMap;let Is;const Ns=(t,e,n)=>(s,r)=>r&&r[0]?r[0][t+"Size"]:Ls(s)&&"getBBox"in s?s.getBBox()[e]:s[n],js=Ns("inline","width","offsetWidth"),Ks=Ns("block","height","offsetHeight");function $s({target:t,borderBoxSize:e}){Ws.get(t)?.forEach(n=>{n(t,{get width(){return js(t,e)},get height(){return Ks(t,e)}})})}function Us(t){t.forEach($s)}function Ys(t,e){Is||"undefined"!=typeof ResizeObserver&&(Is=new ResizeObserver(Us));const n=is(t);return n.forEach(t=>{let n=Ws.get(t);n||(n=new Set,Ws.set(t,n)),n.add(e),Is?.observe(t)}),()=>{n.forEach(t=>{const n=Ws.get(t);n?.delete(e),n?.size||Is?.unobserve(t)})}}const zs=new Set;let Xs;function Hs(t){return zs.add(t),Xs||(Xs=()=>{const t={get width(){return window.innerWidth},get height(){return window.innerHeight}};zs.forEach(e=>e(t))},window.addEventListener("resize",Xs)),()=>{zs.delete(t),zs.size||"function"!=typeof Xs||(window.removeEventListener("resize",Xs),Xs=void 0)}}function qs(t,e){return"function"==typeof t?Hs(t):Ys(t,e)}function Gs(t,e){let n;const s=()=>{const{currentTime:s}=e,r=(null===s?0:s.value)/100;n!==r&&t(r),n=r};return $.preUpdate(s,!0),()=>U(s)}function Zs(){const{value:t}=j;null!==t?(t.frameloop.rate.push(Y.delta),t.animations.mainThread.push(G.mainThread),t.animations.waapi.push(G.waapi),t.animations.layout.push(G.layout)):U(Zs)}function _s(t){return t.reduce((t,e)=>t+e,0)/t.length}function Js(t,e=_s){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}const Qs=t=>Math.round(1e3/t);function tr(){j.value=null,j.addProjectionMetrics=null}function er(){const{value:t}=j;if(!t)throw new Error("Stats are not being measured");tr(),U(Zs);const e={frameloop:{setup:Js(t.frameloop.setup),rate:Js(t.frameloop.rate),read:Js(t.frameloop.read),resolveKeyframes:Js(t.frameloop.resolveKeyframes),preUpdate:Js(t.frameloop.preUpdate),update:Js(t.frameloop.update),preRender:Js(t.frameloop.preRender),render:Js(t.frameloop.render),postRender:Js(t.frameloop.postRender)},animations:{mainThread:Js(t.animations.mainThread),waapi:Js(t.animations.waapi),layout:Js(t.animations.layout)},layoutProjection:{nodes:Js(t.layoutProjection.nodes),calculatedTargetDeltas:Js(t.layoutProjection.calculatedTargetDeltas),calculatedProjections:Js(t.layoutProjection.calculatedProjections)}},{rate:n}=e.frameloop;return n.min=Qs(n.min),n.max=Qs(n.max),n.avg=Qs(n.avg),[n.min,n.max]=[n.max,n.min],e}function nr(t){return Ls(t)&&"svg"===t.tagName}function sr(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],r=be(t[1+n],t[2+n],t[3+n]);return e?r(s):r}function rr(t){const e=[];ps.current=e;const n=t();ps.current=void 0;const s=ms(n);return function(t,e,n){const s=()=>e.set(n()),r=()=>$.preRender(s,!1,!0),i=t.map(t=>t.on("change",r));e.on("destroy",()=>{i.forEach(t=>t()),U(s)})}(e,s,t),s}const ir=t=>Boolean(t&&t.getVelocity);function or(t,e,n){const s=t.get();let r,i=null,o=s;const a="string"==typeof s?s.replace(/[\d.-]/g,""):void 0,l=()=>{i&&(i.stop(),i=null)},u=()=>{l(),i=new Fe({keyframes:[lr(t.get()),lr(o)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:r})};let c;return t.attach((e,n)=>(o=e,r=t=>n(ar(t,a)),$.postRender(u),t.get()),l),ir(e)&&(c=e.on("change",e=>t.set(ar(e,a))),t.on("destroy",c)),c}function ar(t,e){return e?t+e:t}function lr(t){return"number"==typeof t?t:parseFloat(t)}const ur=[...jn,bt,Pt],cr=t=>ur.find(Nn(t));function hr(t){return"layout"===t?"group":"enter"===t||"new"===t?"new":"exit"===t||"old"===t?"old":"group"}let dr={},pr=null;const fr=(t,e)=>{dr[t]=e},mr=()=>{pr||(pr=document.createElement("style"),pr.id="motion-view");let t="";for(const e in dr){const n=dr[e];t+=`${e} {\n`;for(const[e,s]of Object.entries(n))t+=`  ${e}: ${s};\n`;t+="}\n"}pr.textContent=t,document.head.appendChild(pr),dr={}},gr=()=>{pr&&pr.parentElement&&pr.parentElement.removeChild(pr)};function yr(t){const e=t.match(/::view-transition-(old|new|group|image-pair)\((.*?)\)/);return e?{layer:e[2],type:e[1]}:null}function vr(t){const{effect:e}=t;return!!e&&(e.target===document.documentElement&&e.pseudoElement?.startsWith("::view-transition"))}const wr=["layout","enter","exit","new","old"];function br(t){const{update:e,targets:n,options:s}=t;if(!document.startViewTransition)return new Promise(async t=>{await e(),t(new En([]))});(function(t,e){return e.has(t)&&Object.keys(e.get(t)).length>0})("root",n)||fr(":root",{"view-transition-name":"none"}),fr("::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)",{"animation-timing-function":"linear !important"}),mr();const r=document.startViewTransition(async()=>{await e()});return r.finished.finally(()=>{gr()}),new Promise(t=>{r.ready.then(()=>{const e=document.getAnimations().filter(vr),r=[];n.forEach((t,e)=>{for(const n of wr){if(!t[n])continue;const{keyframes:i,options:o}=t[n];for(let[t,a]of Object.entries(i)){if(!a)continue;const i={...Wn(s,t),...Wn(o,t)},l=hr(n);if("opacity"===t&&!Array.isArray(a)){a=["new"===l?0:1,a]}"function"==typeof i.delay&&(i.delay=i.delay(0,1)),i.duration&&(i.duration=m(i.duration)),i.delay&&(i.delay=m(i.delay));const u=new vn({...i,element:document.documentElement,name:t,pseudoElement:`::view-transition-${l}(${e})`,keyframes:a});r.push(u)}}});for(const t of e){if("finished"===t.playState)continue;const{effect:e}=t;if(!(e&&e instanceof KeyframeEffect))continue;const{pseudoElement:i}=e;if(!i)continue;const o=yr(i);if(!o)continue;const a=n.get(o.layer);if(a)Tr(a,"enter")&&Tr(a,"exit")&&e.getKeyframes().some(t=>t.mixBlendMode)?r.push(new Cn(t)):t.cancel();else{const n="group"===o.type?"layout":"";let i={...Wn(s,n)};i.duration&&(i.duration=m(i.duration)),i=yn(i);const a=fn(i.ease,i.duration);e.updateTiming({delay:m(i.delay??0),duration:i.duration,easing:a}),r.push(new Cn(t))}}t(new En(r))})})}function Tr(t,e){return t?.[e]?.keyframes.opacity}let xr=[],Vr=null;function Mr(){Vr=null;const[t]=xr;var e;t&&(n(xr,e=t),Vr=e,br(e).then(t=>{e.notifyReady(t),t.finished.finally(Mr)}))}function Ar(){for(let t=xr.length-1;t>=0;t--){const e=xr[t],{interrupt:n}=e.options;if("immediate"===n){const n=xr.slice(0,t+1).map(t=>t.update),s=xr.slice(t+1);e.update=()=>{n.forEach(t=>t())},xr=[e,...s];break}}Vr&&"immediate"!==xr[0]?.options.interrupt||Mr()}class Sr{constructor(t,e={}){var n;this.currentTarget="root",this.targets=new Map,this.notifyReady=c,this.readyPromise=new Promise(t=>{this.notifyReady=t}),this.update=t,this.options={interrupt:"wait",...e},n=this,xr.push(n),xs.render(Ar)}get(t){return this.currentTarget=t,this}layout(t,e){return this.updateTarget("layout",t,e),this}new(t,e){return this.updateTarget("new",t,e),this}old(t,e){return this.updateTarget("old",t,e),this}enter(t,e){return this.updateTarget("enter",t,e),this}exit(t,e){return this.updateTarget("exit",t,e),this}crossfade(t){return this.updateTarget("enter",{opacity:1},t),this.updateTarget("exit",{opacity:0},t),this}updateTarget(t,e,n={}){const{currentTarget:s,targets:r}=this;r.has(s)||r.set(s,{});r.get(s)[t]={keyframes:e,options:n}}then(t,e){return this.readyPromise.then(t,e)}}const kr=$,Er=N.reduce((t,e)=>(t[e]=t=>U(t),t),{});function Pr(t){return"object"==typeof t&&!Array.isArray(t)}function Cr(t,e,n,s){return"string"==typeof t&&Pr(e)?is(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function Or(t,e,n){return t*(e+1)}function Fr(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:e.startsWith("<")?Math.max(0,n+parseFloat(e.slice(1))):s.get(e)??t}function Rr(t,e,s,r,i,o){!function(t,e,s){for(let r=0;r<t.length;r++){const i=t[r];i.at>e&&i.at<s&&(n(t,i),r--)}}(t,i,o);for(let n=0;n<e.length;n++)t.push({value:e[n],at:Rt(i,o,r[n]),easing:D(s,n)})}function Br(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Dr(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function Lr(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Wr(t,e){return e[t]||(e[t]=[]),e[t]}function Ir(t){return Array.isArray(t)?t:[t]}function Nr(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const jr=t=>"number"==typeof t,Kr=t=>t.every(jr),$r=new WeakMap;function Ur(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Yr(t,e,n,s){if("function"==typeof e){const[r,i]=Ur(s);e=e(void 0!==n?n:t.custom,r,i)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[r,i]=Ur(s);e=e(void 0!==n?n:t.custom,r,i)}return e}function zr(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ms(n))}function Xr(t){return(t=>Array.isArray(t))(t)?t[t.length-1]||0:t}function Hr(t,e){const n=function(t,e,n){const s=t.getProps();return Yr(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:r={},...i}=n||{};i={...i,...s};for(const e in i){zr(t,e,Xr(i[e]))}}function qr(t,e){const n=t.getValue("willChange");if(s=n,Boolean(ir(s)&&s.add))return n.add(e);if(!n&&i.WillChange){const n=new i.WillChange("auto");t.addValue("willChange",n),n.add(e)}var s}const Gr=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Zr="data-"+Gr("framerAppearId");function _r(t){return t.props[Zr]}const Jr=t=>null!==t;const Qr={type:"spring",stiffness:500,damping:25,restSpeed:10},ti={type:"keyframes",duration:.8},ei={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ni=(t,{keyframes:e})=>e.length>2?ti:Xe.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Qr:ei;const si=(t,e,n,s={},r,o)=>a=>{const l=Wn(s,t)||{},u=l.delay||s.delay||0;let{elapsed:c=0}=s;c-=m(u);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:o?void 0:r};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:r,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(l)||Object.assign(h,ni(t,h)),h.duration&&(h.duration=m(h.duration)),h.repeatDelay&&(h.repeatDelay=m(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),(i.instantAnimations||i.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!l.type&&!l.ease,d&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"},s){const r=t.filter(Jr),i=e&&"loop"!==n&&e%2==1?0:r.length-1;return i&&void 0!==s?s:r[i]}(h.keyframes,l);if(void 0!==t)return void $.update(()=>{h.onUpdate(t),h.onComplete()})}return l.isSync?new Fe(h):new kn(h)};function ri({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function ii(t,e,{delay:n=0,transitionOverride:s,type:r}={}){let{transition:i=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(i=s);const l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&ri(u,e))continue;const o={delay:n,...Wn(i||{},e)},c=s.get();if(void 0!==c&&!s.isAnimating&&!Array.isArray(r)&&r===c&&!o.velocity)continue;let h=!1;if(window.MotionHandoffAnimation){const n=_r(t);if(n){const t=window.MotionHandoffAnimation(n,e,$);null!==t&&(o.startTime=t,h=!0)}}qr(t,e),s.start(si(e,s,r,t.shouldReduceMotion&&In.has(e)?{type:!1}:o,t,h));const d=s.animation;d&&l.push(d)}return o&&Promise.all(l).then(()=>{$.update(()=>{o&&Hr(t,o)})}),l}const oi={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ai={};for(const t in oi)ai[t]={isEnabled:e=>oi[t].some(t=>!!e[t])};const li=()=>({x:{min:0,max:0},y:{min:0,max:0}}),ui="undefined"!=typeof window,ci={current:null},hi={current:!1};const di=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function pi(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||di.some(e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e]));var e}const fi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class mi{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=rn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,$.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(r),this.isControllingVariants=pi(e),this.isVariantNode=function(t){return Boolean(pi(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in c){const e=c[t];void 0!==a[t]&&ir(e)&&e.set(a[t],!1)}}mount(t){this.current=t,$r.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),hi.current||function(){if(hi.current=!0,ui)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ci.current=t.matches;t.addListener(e),e()}else ci.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ci.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),U(this.notifyUpdate),U(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Xe.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&$.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ai){const e=ai[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<fi.length;e++){const n=fi[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const r=e[s],i=n[s];if(ir(r))t.addValue(s,r);else if(ir(i))t.addValue(s,ms(r,{owner:t}));else if(i!==r)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{const e=t.getStaticValue(s);t.addValue(s,ms(void 0!==e?e:r,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=ms(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(o(n)||l(n))?n=parseFloat(n):!cr(n)&&Pt.test(e)&&(n=Jn(t,e)),this.setBaseTarget(t,ir(n)?n.get():n)),ir(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=Yr(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||ir(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new f),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class gi extends mi{constructor(){super(...arguments),this.KeyframeResolver=ts}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ir(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}const yi={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},vi=ze.length;function wi(t,e,n){const{style:s,vars:r,transformOrigin:i}=t;let o=!1,a=!1;for(const t in e){const n=e[t];if(Xe.has(t))o=!0;else if(_(t))r[t]=n;else{const e=as(n,Gn[t]);t.startsWith("origin")?(a=!0,i[t]=e):s[t]=e}}if(e.transform||(o||n?s.transform=function(t,e,n){let s="",r=!0;for(let i=0;i<vi;i++){const o=ze[i],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=as(a,Gn[o]);l||(r=!1,s+=`${yi[o]||o}(${t}) `),n&&(e[o]=t)}}return s=s.trim(),n?s=n(e,r?"":s):r&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=i;s.transformOrigin=`${t} ${e} ${n}`}}function bi(t,{style:e,vars:n},s,r){Object.assign(t.style,e,r&&r.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}const Ti={};function xi(t,{layout:e,layoutId:n}){return Xe.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ti[t]||"opacity"===t)}function Vi(t,e,n){const{style:s}=t,r={};for(const i in s)(ir(s[i])||e.style&&ir(e.style[i])||xi(i,t)||void 0!==n?.getValue(i)?.liveStyle)&&(r[i]=s[i]);return r}class Mi extends gi{constructor(){super(...arguments),this.type="html",this.renderInstance=bi}readValueFromInstance(t,e){if(Xe.has(e))return this.projection?.isProjecting?Ke(e):Ue(t,e);{const s=(n=t,window.getComputedStyle(n)),r=(_(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof r?r.trim():r}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){wi(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Vi(t,e,n)}}class Ai extends mi{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}const Si={offset:"stroke-dashoffset",array:"stroke-dasharray"},ki={offset:"strokeDashoffset",array:"strokeDasharray"};function Ei(t,{attrX:e,attrY:n,attrScale:s,pathLength:r,pathSpacing:i=1,pathOffset:o=0,...a},l,u,c){if(wi(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==n&&(h.y=n),void 0!==s&&(h.scale=s),void 0!==r&&function(t,e,n=1,s=0,r=!0){t.pathLength=1;const i=r?Si:ki;t[i.offset]=mt.transform(-s);const o=mt.transform(e),a=mt.transform(n);t[i.array]=`${o} ${a}`}(h,r,i,o,!1)}const Pi=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class Ci extends gi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=li}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Xe.has(e)){const t=_n(e);return t&&t.default||0}return e=Pi.has(e)?e:Gr(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=Vi(t,e,n);for(const n in t)(ir(t[n])||ir(e[n]))&&(s[-1!==ze.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}build(t,e,n){Ei(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,s){!function(t,e,n,s){bi(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(Pi.has(n)?n:Gr(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}function Oi(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Ls(t)&&!nr(t)?new Ci(e):new Mi(e);n.mount(t),$r.set(t,n)}function Fi(t){const e=new Ai({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),$r.set(t,e)}function Ri(t,e,n,s){const r=[];if(function(t,e){return ir(t)||"number"==typeof t||"string"==typeof t&&!Pr(e)}(t,e))r.push(function(t,e,n){const s=ir(t)?t:ms(t);return s.start(si("",s,e,n)),s.animation}(t,Pr(e)&&e.default||e,n&&n.default||n));else{const i=Cr(t,e,s),o=i.length;for(let t=0;t<o;t++){const s=i[t],a=s instanceof Element?Oi:Fi;$r.has(s)||a(s);const l=$r.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),r.push(...ii(l,{...e,transition:u},{}))}}return r}function Bi(t,e,n){const s=[],r=function(t,{defaultTransition:e={},...n}={},s,r){const i=e.duration||.3,o=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){u.set(o,h);continue}if(!Array.isArray(o)){u.set(o.name,Fr(h,o.at,c,u));continue}let[p,f,g={}]=o;void 0!==g.at&&(h=Fr(h,g.at,c,u));let y=0;const v=(t,n,s,o=0,a=0)=>{const l=Ir(t),{delay:u=0,times:c=xe(l),type:p="keyframes",repeat:f,repeatType:g,repeatDelay:v=0,...w}=n;let{ease:b=e.ease||"easeOut",duration:T}=n;const x="function"==typeof u?u(o,a):u,V=l.length,M=gn(p)?p:r?.[p||"keyframes"];if(V<=2&&M){let t=100;if(2===V&&Kr(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...w};void 0!==T&&(e.duration=m(T));const n=Zt(e,t,M);b=n.ease,T=n.duration}T??(T=i);const A=h+x;1===c.length&&0===c[0]&&(c[1]=1);const S=c.length-l.length;if(S>0&&Te(c,S),1===l.length&&l.unshift(null),f){T=Or(T,f);const t=[...l],e=[...c];b=Array.isArray(b)?[...b]:[b];const n=[...b];for(let s=0;s<f;s++){l.push(...t);for(let r=0;r<t.length;r++)c.push(e[r]+(s+1)),b.push(0===r?"linear":D(n,r-1))}Br(c,f)}const k=A+T;Rr(s,l,b,c,A,k),y=Math.max(x+T,y),d=Math.max(k,d)};if(ir(p))v(f,g,Wr("default",Lr(p,a)));else{const t=Cr(p,f,s,l),e=t.length;for(let n=0;n<e;n++){const s=Lr(t[n],a);for(const t in f)v(f[t],Nr(g,t),Wr(t,s),n,e)}}c=h,h+=y}return a.forEach((t,s)=>{for(const r in t){const i=t[r];i.sort(Dr);const a=[],l=[],u=[];for(let t=0;t<i.length;t++){const{at:e,value:n,easing:s}=i[t];a.push(n),l.push(p(0,d,e)),u.push(s||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),o.has(s)||o.set(s,{keyframes:{},transition:{}});const c=o.get(s);c.keyframes[r]=a,c.transition[r]={...e,duration:d,ease:u,times:l,...n}}}),o}(t,e,n,{spring:ve});return r.forEach(({keyframes:t,transition:e},n)=>{s.push(...Ri(n,t,e))}),s}function Di(t){return function(e,n,s){let r=[];var i;i=e,r=Array.isArray(i)&&i.some(Array.isArray)?Bi(e,n,t):Ri(e,n,s,t);const o=new Pn(r);return t&&t.animations.push(o),o}}const Li=Di();const Wi=t=>function(e,n,s){return new Pn(function(t,e,n,s){const r=is(t,s),i=r.length,o=[];for(let t=0;t<i;t++){const s=r[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,i));for(const t in e){let n=e[t];Array.isArray(n)||(n=[n]);const r={...Wn(a,t)};r.duration&&(r.duration=m(r.duration)),r.delay&&(r.delay=m(r.delay));const i=Rn(s),l=Fn(t,r.pseudoElement||""),u=i.get(l);u&&u.stop(),o.push({map:i,key:l,unresolvedKeyframes:n,options:{...r,element:s,name:t,allowFlatten:!a.type&&!a.ease}})}}for(let t=0;t<o.length;t++){const{unresolvedKeyframes:e,options:n}=o[t],{element:s,name:r,pseudoElement:i}=n;i||null!==e[0]||(e[0]=Ds(s,r)),Re(e),ns(e,r),!i&&e.length<2&&e.unshift(Ds(s,r)),n.keyframes=e}const a=[];for(let t=0;t<o.length;t++){const{map:e,key:n,options:s}=o[t],r=new vn(s);e.set(n,r),r.finished.finally(()=>e.delete(n)),a.push(r)}return a}(e,n,s,t))},Ii=Wi(),Ni={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ji(t,e,n,s){const r=n[e],{length:i,position:o}=Ni[e],a=r.current,l=n.time;r.current=t[`scroll${o}`],r.scrollLength=t[`scroll${i}`]-t[`client${i}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=p(0,r.scrollLength,r.current);const u=s-l;r.velocity=u>50?0:y(r.current-a,u)}const Ki={start:0,center:.5,end:1};function $i(t,e,n=0){let s=0;if(t in Ki&&(t=Ki[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?s=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?s=e/100*document.documentElement.clientWidth:t.endsWith("vh")?s=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(s=e*t),n+s}const Ui=[0,0];function Yi(t,e,n,s){let r=Array.isArray(t)?t:Ui,i=0,o=0;return"number"==typeof t?r=[t,t]:"string"==typeof t&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,Ki[t]?t:"0"]),i=$i(r[0],n,s),o=$i(r[1],e),i-o}const zi={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Xi={x:0,y:0};function Hi(t,e,n){const{offset:r=zi.All}=n,{target:i=t,axis:o="y"}=n,a="y"===o?"height":"width",l=i!==t?function(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(Vn(s))n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){const t=s.getBoundingClientRect();s=s.parentElement;const e=s.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(s instanceof SVGGraphicsElement))break;{const{x:t,y:e}=s.getBBox();n.x+=t,n.y+=e;let r=null,i=s.parentNode;for(;!r;)"svg"===i.tagName&&(r=i),i=s.parentNode;s=r}}return n}(i,t):Xi,u=i===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(i),c={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let h=!e[o].interpolate;const d=r.length;for(let t=0;t<d;t++){const n=Yi(r[t],c[a],u[a],l[o]);h||n===e[o].interpolatorOffsets[t]||(h=!0),e[o].offset[t]=n}h&&(e[o].interpolate=be(e[o].offset,xe(r),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=s(0,1,e[o].interpolate(e[o].current))}function qi(t,e,n,s={}){return{measure:e=>{!function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,s.target,n),function(t,e,n){ji(t,"x",e,n),ji(t,"y",e,n),e.time=n}(t,n,e),(s.offset||s.target)&&Hi(t,n,s)},notify:()=>e(n)}}const Gi=new WeakMap,Zi=new WeakMap,_i=new WeakMap,Ji=t=>t===document.scrollingElement?window:t;function Qi(t,{container:e=document.scrollingElement,...n}={}){if(!e)return c;let s=_i.get(e);s||(s=new Set,_i.set(e,s));const r=qi(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(s.add(r),!Gi.has(e)){const t=()=>{for(const t of s)t.measure(Y.timestamp);$.preUpdate(n)},n=()=>{for(const t of s)t.notify()},r=()=>$.read(t);Gi.set(e,r);const i=Ji(e);window.addEventListener("resize",r,{passive:!0}),e!==document.documentElement&&Zi.set(e,qs(e,r)),i.addEventListener("scroll",r,{passive:!0}),r()}const i=Gi.get(e);return $.read(i,!1,!0),()=>{U(i);const t=_i.get(e);if(!t)return;if(t.delete(r),t.size)return;const n=Gi.get(e);Gi.delete(e),n&&(Ji(e).removeEventListener("scroll",n),Zi.get(e)?.(),window.removeEventListener("resize",n))}}const to=new Map;function eo({source:t,container:e,...n}){const{axis:s}=n;t&&(e=t);const r=to.get(e)??new Map;to.set(e,r);const i=n.target??"self",o=r.get(i)??{},a=s+(n.offset??[]).join(",");return o[a]||(o[a]=!n.target&&ln()?new ScrollTimeline({source:e,axis:s}):function(t){const e={value:0},n=Qi(n=>{e.value=100*n[t.axis].progress},t);return{currentTime:e,cancel:n}}({container:e,...n})),o[a]}const no={some:0,all:1};const so=(t,e)=>Math.abs(t-e);t.AsyncMotionValueAnimation=kn,t.DOMKeyframesResolver=ts,t.GroupAnimation=En,t.GroupAnimationWithThen=Pn,t.JSAnimation=Fe,t.KeyframeResolver=rn,t.MotionGlobalConfig=i,t.MotionValue=fs,t.NativeAnimation=vn,t.NativeAnimationExtended=Tn,t.NativeAnimationWrapper=Cn,t.SubscriptionManager=f,t.ViewTransitionBuilder=Sr,t.acceleratedValues=rs,t.activeAnimations=G,t.addAttrValue=cs,t.addStyleValue=vs,t.addUniqueItem=e,t.alpha=nt,t.analyseComplexValue=At,t.animate=Li,t.animateMini=Ii,t.animateValue=function(t){return new Fe(t)},t.animateView=function(t,e={}){return new Sr(t,e)},t.animationMapKey=Fn,t.anticipate=k,t.applyPxDefaults=ns,t.attachSpring=or,t.attrEffect=hs,t.backIn=A,t.backInOut=S,t.backOut=M,t.calcGeneratorDuration=Gt,t.cancelFrame=U,t.cancelMicrotask=Vs,t.cancelSync=Er,t.circIn=E,t.circInOut=C,t.circOut=P,t.clamp=s,t.collectMotionValues=ps,t.color=bt,t.complex=Pt,t.convertOffsetToTimes=Ve,t.createGeneratorEasing=Zt,t.createRenderBatcher=K,t.createScopedAnimate=Di,t.cubicBezier=T,t.cubicBezierAsString=dn,t.defaultEasing=Me,t.defaultOffset=xe,t.defaultTransformValue=Ke,t.defaultValueTypes=Zn,t.degrees=pt,t.delay=function(t,e){return function(t,e){const n=q.now(),s=({timestamp:r})=>{const i=r-n;i>=e&&(U(s),t(i-e))};return $.setup(s,!0),()=>U(s)}(t,m(e))},t.dimensionValueTypes=jn,t.distance=so,t.distance2D=function(t,e){const n=so(t.x,e.x),s=so(t.y,e.y);return Math.sqrt(n**2+s**2)},t.easeIn=O,t.easeInOut=R,t.easeOut=F,t.easingDefinitionToFunction=I,t.fillOffset=Te,t.fillWildcards=Re,t.findDimensionValueType=Kn,t.findValueType=cr,t.flushKeyframeResolvers=sn,t.frame=$,t.frameData=Y,t.frameSteps=z,t.generateLinearEasing=Ht,t.getAnimatableNone=Jn,t.getAnimationMap=Rn,t.getComputedStyle=Ds,t.getDefaultValueType=_n,t.getEasingForSegment=D,t.getMixer=Kt,t.getValueAsType=as,t.getValueTransition=Wn,t.getVariableValue=Ln,t.hasWarned=function(t){return v.has(t)},t.hex=ht,t.hover=function(t,e,n={}){const[s,r,i]=Ss(t,n),o=t=>{if(!ks(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const i=t=>{ks(t)&&(s(t),n.removeEventListener("pointerleave",i))};n.addEventListener("pointerleave",i,r)};return s.forEach(t=>{t.addEventListener("pointerenter",o,r)}),i},t.hsla=wt,t.hslaToRgba=Ot,t.inView=function(t,e,{root:n,margin:s,amount:r="some"}={}){const i=is(t),o=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=o.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?o.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),o.delete(t.target))})},{root:n,rootMargin:s,threshold:"number"==typeof r?r:no[r]});return i.forEach(t=>a.observe(t)),()=>a.disconnect()},t.inertia=we,t.interpolate=be,t.invariant=r,t.invisibleValues=It,t.isBezierDefinition=L,t.isCSSVariableName=_,t.isCSSVariableToken=Q,t.isDragActive=As,t.isDragging=Ms,t.isEasingArray=B,t.isGenerator=gn,t.isHTMLElement=Vn,t.isMotionValue=ir,t.isNodeOrChild=Es,t.isNumericalString=o,t.isObject=a,t.isPrimaryPointer=Ps,t.isSVGElement=Ls,t.isSVGSVGElement=nr,t.isWaapiSupportedEasing=function t(e){return Boolean("function"==typeof e&&hn()||!e||"string"==typeof e&&(e in pn||hn())||L(e)||Array.isArray(e)&&e.every(t))},t.isZeroValueString=l,t.keyframes=Ae,t.mapEasingToNativeEasing=fn,t.mapValue=function(t,e,n,s){const r=sr(e,n,s);return rr(()=>r(t.get()))},t.maxGeneratorDuration=qt,t.memo=u,t.microtask=xs,t.millisecondsToSeconds=g,t.mirrorEasing=x,t.mix=zt,t.mixArray=$t,t.mixColor=Wt,t.mixComplex=Yt,t.mixImmediate=Ft,t.mixLinearColor=Bt,t.mixNumber=Rt,t.mixObject=Ut,t.mixVisibility=Nt,t.motionValue=ms,t.moveItem=function([...t],e,n){const s=e<0?t.length+e:e;if(s>=0&&s<t.length){const s=n<0?t.length+n:n,[r]=t.splice(e,1);t.splice(s,0,r)}return t},t.noop=c,t.number=et,t.numberValueTypes=Gn,t.observeTimeline=Gs,t.parseCSSVariable=Dn,t.parseValueFromTransform=$e,t.percent=ft,t.pipe=d,t.positionalKeys=In,t.press=function(t,e,n={}){const[s,r,i]=Ss(t,n),o=t=>{const s=t.currentTarget;if(!Bs(t))return;Os.add(s);const i=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),Os.has(s)&&Os.delete(s),Bs(t)&&"function"==typeof i&&i(t,{success:e})},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||Es(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return s.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),Vn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=Fs(()=>{if(Os.has(n))return;Rs(n,"down");const t=Fs(()=>{Rs(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Rs(n,"cancel"),e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)})(t,r)),e=t,Cs.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),i},t.progress=p,t.progressPercentage=vt,t.propEffect=ds,t.px=mt,t.readTransformValue=Ue,t.recordStats=function(){if(j.value)throw tr(),new Error("Stats are already being measured");const t=j;return t.value={frameloop:{setup:[],rate:[],read:[],resolveKeyframes:[],preUpdate:[],update:[],preRender:[],render:[],postRender:[]},animations:{mainThread:[],waapi:[],layout:[]},layoutProjection:{nodes:[],calculatedTargetDeltas:[],calculatedProjections:[]}},t.addProjectionMetrics=e=>{const{layoutProjection:n}=t.value;n.nodes.push(e.nodes),n.calculatedTargetDeltas.push(e.calculatedTargetDeltas),n.calculatedProjections.push(e.calculatedProjections)},$.postRender(Zs,!0),er},t.removeItem=n,t.resize=qs,t.resolveElements=is,t.reverseEasing=V,t.rgbUnit=ut,t.rgba=ct,t.scale=st,t.scroll=function(t,{axis:e="y",container:n=document.scrollingElement,...s}={}){if(!n)return c;const r={axis:e,container:n,...s};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?Qi(n=>{t(n[e.axis].progress,n)},e):Gs(t,eo(e))}(t,r):function(t,e){const n=eo(e);return t.attachTimeline({timeline:e.target?void 0:n,observe:t=>(t.pause(),Gs(e=>{t.time=t.duration*e},n))})}(t,r)},t.scrollInfo=Qi,t.secondsToMilliseconds=m,t.setDragLock=function(t){return"x"===t||"y"===t?Ms[t]?null:(Ms[t]=!0,()=>{Ms[t]=!1}):Ms.x||Ms.y?null:(Ms.x=Ms.y=!0,()=>{Ms.x=Ms.y=!1})},t.setStyle=an,t.spring=ve,t.springValue=function(t,e){const n=ms(ir(t)?t.get():t);return or(n,t,e),n},t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:s}={}){return(r,i)=>{const o="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,i),a=Math.abs(o-r);let l=t*a;if(s){const e=i*t;l=I(s)(l/e)*e}return e+l}},t.startWaapiAnimation=mn,t.statsBuffer=j,t.steps=function(t,e="end"){return n=>{const r=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,i="end"===e?Math.floor(r):Math.ceil(r);return s(0,1,i/t)}},t.styleEffect=ws,t.supportedWaapiEasing=pn,t.supportsBrowserAnimation=Sn,t.supportsFlags=un,t.supportsLinearEasing=hn,t.supportsPartialKeyframes=ss,t.supportsScrollTimeline=ln,t.svgEffect=Ts,t.sync=kr,t.testValueType=Nn,t.time=q,t.transform=sr,t.transformPropOrder=ze,t.transformProps=Xe,t.transformValue=rr,t.transformValueTypes=qn,t.velocityPerSecond=y,t.vh=gt,t.vw=yt,t.warnOnce=function(t,e,n){t||v.has(e)||(console.warn(e),n&&console.warn(n),v.add(e))},t.warning=()=>{},t.wrap=w});
