{"name": "twi<PERSON>", "description": "A <PERSON>wi<PERSON> helper library", "version": "5.7.1", "author": "API Team <<EMAIL>>", "contributors": [{"name": "twilio-node contributors", "url": "https://github.com/twilio/twilio-node/graphs/contributors"}], "keywords": ["twi<PERSON>", "sms", "rest", "api"], "repository": {"type": "git", "url": "https://github.com/twilio/twilio-node.git"}, "dependencies": {"axios": "^1.8.3", "dayjs": "^1.11.9", "https-proxy-agent": "^5.0.0", "jsonwebtoken": "^9.0.2", "qs": "^6.9.4", "scmp": "^2.1.0", "xmlbuilder": "^13.0.2"}, "devDependencies": {"@babel/preset-env": "^7.23.0", "@babel/preset-typescript": "^7.18.6", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.2", "@types/node": "22.9.0", "@types/qs": "^6.9.7", "babel-plugin-replace-ts-export-assignment": "^0.0.2", "eslint": "^8.31.0", "express": "^4.17.1", "jest": "^29.5.5", "jshint": "^2.11.0", "mock-fs": "^5.2.0", "nock": "^13.2.9", "node-mocks-http": "^1.8.1", "prettier": "^2.7.1", "ts-jest": "^29.1.1", "typescript": "5.0.4", "typedoc": "^0.23.21"}, "scripts": {"test": "npm run test:javascript && npm run test:typescript", "test:javascript": "jest spec --coverage --detectOpenHandles --testPathIgnorePatterns=spec/cluster", "test:typescript": "tsc --noEmit", "jshint": "jshint src/rest/** src/base/** src/http/**", "jscs": "eslint src/base/**/**.js src/http/**/**.js --fix", "prepublish": "npm run build", "build": "tsc", "check": "npm run jshint && npm run jscs", "ci": "npm run test && npm run nsp && npm run prettier-check", "nsp": "npm audit --production", "prettier": "prettier --write .", "prettier-check": "prettier --check .", "typedoc": "typedoc --entryPointStrategy expand src --out docs --logLevel Error"}, "files": ["lib", "index.js", "index.d.ts"], "main": "./lib", "types": "./index.d.ts", "engines": {"node": ">=14.0"}, "license": "MIT"}