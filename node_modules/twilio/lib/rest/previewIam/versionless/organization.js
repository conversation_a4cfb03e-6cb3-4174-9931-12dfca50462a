"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Organization Public API
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationListInstance = exports.OrganizationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const account_1 = require("./organization/account");
const roleAssignment_1 = require("./organization/roleAssignment");
const user_1 = require("./organization/user");
class OrganizationContextImpl {
    constructor(_version, organizationSid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(organizationSid)) {
            throw new Error("Parameter 'organizationSid' is not valid.");
        }
        this._solution = { organizationSid };
        this._uri = `/${organizationSid}`;
    }
    get accounts() {
        this._accounts =
            this._accounts ||
                (0, account_1.AccountListInstance)(this._version, this._solution.organizationSid);
        return this._accounts;
    }
    get roleAssignments() {
        this._roleAssignments =
            this._roleAssignments ||
                (0, roleAssignment_1.RoleAssignmentListInstance)(this._version, this._solution.organizationSid);
        return this._roleAssignments;
    }
    get users() {
        this._users =
            this._users ||
                (0, user_1.UserListInstance)(this._version, this._solution.organizationSid);
        return this._users;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.OrganizationContextImpl = OrganizationContextImpl;
function OrganizationListInstance(version) {
    const instance = ((organizationSid) => instance.get(organizationSid));
    instance.get = function get(organizationSid) {
        return new OrganizationContextImpl(version, organizationSid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.OrganizationListInstance = OrganizationListInstance;
