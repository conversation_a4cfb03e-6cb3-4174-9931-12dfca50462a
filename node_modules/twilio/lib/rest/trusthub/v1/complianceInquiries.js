"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceInquiriesListInstance = exports.ComplianceInquiriesInstance = exports.ComplianceInquiriesContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class ComplianceInquiriesContextImpl {
    constructor(_version, customerId) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(customerId)) {
            throw new Error("Parameter 'customerId' is not valid.");
        }
        this._solution = { customerId };
        this._uri = `/ComplianceInquiries/Customers/${customerId}/Initialize`;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["primaryProfileSid"] === null ||
            params["primaryProfileSid"] === undefined) {
            throw new Error("Required parameter \"params['primaryProfileSid']\" missing.");
        }
        let data = {};
        data["PrimaryProfileSid"] = params["primaryProfileSid"];
        if (params["themeSetId"] !== undefined)
            data["ThemeSetId"] = params["themeSetId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ComplianceInquiriesInstance(operationVersion, payload, instance._solution.customerId));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ComplianceInquiriesContextImpl = ComplianceInquiriesContextImpl;
class ComplianceInquiriesInstance {
    constructor(_version, payload, customerId) {
        this._version = _version;
        this.inquiryId = payload.inquiry_id;
        this.inquirySessionToken = payload.inquiry_session_token;
        this.customerId = payload.customer_id;
        this.url = payload.url;
        this._solution = { customerId: customerId || this.customerId };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ComplianceInquiriesContextImpl(this._version, this._solution.customerId);
        return this._context;
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            inquiryId: this.inquiryId,
            inquirySessionToken: this.inquirySessionToken,
            customerId: this.customerId,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ComplianceInquiriesInstance = ComplianceInquiriesInstance;
function ComplianceInquiriesListInstance(version) {
    const instance = ((customerId) => instance.get(customerId));
    instance.get = function get(customerId) {
        return new ComplianceInquiriesContextImpl(version, customerId);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/ComplianceInquiries/Customers/Initialize`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["primaryProfileSid"] === null ||
            params["primaryProfileSid"] === undefined) {
            throw new Error("Required parameter \"params['primaryProfileSid']\" missing.");
        }
        let data = {};
        data["PrimaryProfileSid"] = params["primaryProfileSid"];
        if (params["notificationEmail"] !== undefined)
            data["NotificationEmail"] = params["notificationEmail"];
        if (params["themeSetId"] !== undefined)
            data["ThemeSetId"] = params["themeSetId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ComplianceInquiriesInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ComplianceInquiriesListInstance = ComplianceInquiriesListInstance;
