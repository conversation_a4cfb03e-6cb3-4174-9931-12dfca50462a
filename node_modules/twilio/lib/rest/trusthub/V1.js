"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trusthub
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const complianceInquiries_1 = require("./v1/complianceInquiries");
const complianceRegistrationInquiries_1 = require("./v1/complianceRegistrationInquiries");
const complianceTollfreeInquiries_1 = require("./v1/complianceTollfreeInquiries");
const customerProfiles_1 = require("./v1/customerProfiles");
const endUser_1 = require("./v1/endUser");
const endUserType_1 = require("./v1/endUserType");
const policies_1 = require("./v1/policies");
const supportingDocument_1 = require("./v1/supportingDocument");
const supportingDocumentType_1 = require("./v1/supportingDocumentType");
const trustProducts_1 = require("./v1/trustProducts");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Trusthub
     *
     * @param domain - The Twilio (Twilio.Trusthub) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for complianceInquiries resource */
    get complianceInquiries() {
        this._complianceInquiries =
            this._complianceInquiries || (0, complianceInquiries_1.ComplianceInquiriesListInstance)(this);
        return this._complianceInquiries;
    }
    /** Getter for complianceRegistrationInquiries resource */
    get complianceRegistrationInquiries() {
        this._complianceRegistrationInquiries =
            this._complianceRegistrationInquiries ||
                (0, complianceRegistrationInquiries_1.ComplianceRegistrationInquiriesListInstance)(this);
        return this._complianceRegistrationInquiries;
    }
    /** Getter for complianceTollfreeInquiries resource */
    get complianceTollfreeInquiries() {
        this._complianceTollfreeInquiries =
            this._complianceTollfreeInquiries ||
                (0, complianceTollfreeInquiries_1.ComplianceTollfreeInquiriesListInstance)(this);
        return this._complianceTollfreeInquiries;
    }
    /** Getter for customerProfiles resource */
    get customerProfiles() {
        this._customerProfiles =
            this._customerProfiles || (0, customerProfiles_1.CustomerProfilesListInstance)(this);
        return this._customerProfiles;
    }
    /** Getter for endUsers resource */
    get endUsers() {
        this._endUsers = this._endUsers || (0, endUser_1.EndUserListInstance)(this);
        return this._endUsers;
    }
    /** Getter for endUserTypes resource */
    get endUserTypes() {
        this._endUserTypes = this._endUserTypes || (0, endUserType_1.EndUserTypeListInstance)(this);
        return this._endUserTypes;
    }
    /** Getter for policies resource */
    get policies() {
        this._policies = this._policies || (0, policies_1.PoliciesListInstance)(this);
        return this._policies;
    }
    /** Getter for supportingDocuments resource */
    get supportingDocuments() {
        this._supportingDocuments =
            this._supportingDocuments || (0, supportingDocument_1.SupportingDocumentListInstance)(this);
        return this._supportingDocuments;
    }
    /** Getter for supportingDocumentTypes resource */
    get supportingDocumentTypes() {
        this._supportingDocumentTypes =
            this._supportingDocumentTypes || (0, supportingDocumentType_1.SupportingDocumentTypeListInstance)(this);
        return this._supportingDocumentTypes;
    }
    /** Getter for trustProducts resource */
    get trustProducts() {
        this._trustProducts =
            this._trustProducts || (0, trustProducts_1.TrustProductsListInstance)(this);
        return this._trustProducts;
    }
}
exports.default = V1;
