"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Pricing
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CountryPage = exports.CountryListInstance = exports.CountryInstance = exports.CountryContextImpl = exports.PricingV2TrunkingCountryInstanceTerminatingPrefixPrices = exports.PricingV2TrunkingCountryInstanceOriginatingCallPrices = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class PricingV2TrunkingCountryInstanceOriginatingCallPrices {
}
exports.PricingV2TrunkingCountryInstanceOriginatingCallPrices = PricingV2TrunkingCountryInstanceOriginatingCallPrices;
class PricingV2TrunkingCountryInstanceTerminatingPrefixPrices {
}
exports.PricingV2TrunkingCountryInstanceTerminatingPrefixPrices = PricingV2TrunkingCountryInstanceTerminatingPrefixPrices;
class CountryContextImpl {
    constructor(_version, isoCountry) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(isoCountry)) {
            throw new Error("Parameter 'isoCountry' is not valid.");
        }
        this._solution = { isoCountry };
        this._uri = `/Trunking/Countries/${isoCountry}`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CountryInstance(operationVersion, payload, instance._solution.isoCountry));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryContextImpl = CountryContextImpl;
class CountryInstance {
    constructor(_version, payload, isoCountry) {
        this._version = _version;
        this.country = payload.country;
        this.isoCountry = payload.iso_country;
        this.terminatingPrefixPrices = payload.terminating_prefix_prices;
        this.originatingCallPrices = payload.originating_call_prices;
        this.priceUnit = payload.price_unit;
        this.url = payload.url;
        this._solution = { isoCountry: isoCountry || this.isoCountry };
    }
    get _proxy() {
        this._context =
            this._context ||
                new CountryContextImpl(this._version, this._solution.isoCountry);
        return this._context;
    }
    /**
     * Fetch a CountryInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed CountryInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            country: this.country,
            isoCountry: this.isoCountry,
            terminatingPrefixPrices: this.terminatingPrefixPrices,
            originatingCallPrices: this.originatingCallPrices,
            priceUnit: this.priceUnit,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryInstance = CountryInstance;
function CountryListInstance(version) {
    const instance = ((isoCountry) => instance.get(isoCountry));
    instance.get = function get(isoCountry) {
        return new CountryContextImpl(version, isoCountry);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Trunking/Countries`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new CountryPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new CountryPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.CountryListInstance = CountryListInstance;
class CountryPage extends Page_1.default {
    /**
     * Initialize the CountryPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of CountryInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new CountryInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.CountryPage = CountryPage;
