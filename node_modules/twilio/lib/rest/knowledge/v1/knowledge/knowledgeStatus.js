"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Knowledge
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.KnowledgeStatusListInstance = exports.KnowledgeStatusInstance = exports.KnowledgeStatusContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class KnowledgeStatusContextImpl {
    constructor(_version, id) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(id)) {
            throw new Error("Parameter 'id' is not valid.");
        }
        this._solution = { id };
        this._uri = `/Knowledge/${id}/Status`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new KnowledgeStatusInstance(operationVersion, payload, instance._solution.id));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.KnowledgeStatusContextImpl = KnowledgeStatusContextImpl;
class KnowledgeStatusInstance {
    constructor(_version, payload, id) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.status = payload.status;
        this.lastStatus = payload.last_status;
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this._solution = { id };
    }
    get _proxy() {
        this._context =
            this._context ||
                new KnowledgeStatusContextImpl(this._version, this._solution.id);
        return this._context;
    }
    /**
     * Fetch a KnowledgeStatusInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed KnowledgeStatusInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            status: this.status,
            lastStatus: this.lastStatus,
            dateUpdated: this.dateUpdated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.KnowledgeStatusInstance = KnowledgeStatusInstance;
function KnowledgeStatusListInstance(version, id) {
    if (!(0, utility_1.isValidPathParam)(id)) {
        throw new Error("Parameter 'id' is not valid.");
    }
    const instance = (() => instance.get());
    instance.get = function get() {
        return new KnowledgeStatusContextImpl(version, id);
    };
    instance._version = version;
    instance._solution = { id };
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.KnowledgeStatusListInstance = KnowledgeStatusListInstance;
