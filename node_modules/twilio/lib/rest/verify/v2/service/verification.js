"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationListInstance = exports.VerificationInstance = exports.VerificationContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
class VerificationContextImpl {
    constructor(_version, serviceSid, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(serviceSid)) {
            throw new Error("Parameter 'serviceSid' is not valid.");
        }
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { serviceSid, sid };
        this._uri = `/Services/${serviceSid}/Verifications/${sid}`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["status"] === null || params["status"] === undefined) {
            throw new Error("Required parameter \"params['status']\" missing.");
        }
        let data = {};
        data["Status"] = params["status"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationInstance(operationVersion, payload, instance._solution.serviceSid, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationContextImpl = VerificationContextImpl;
class VerificationInstance {
    constructor(_version, payload, serviceSid, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.serviceSid = payload.service_sid;
        this.accountSid = payload.account_sid;
        this.to = payload.to;
        this.channel = payload.channel;
        this.status = payload.status;
        this.valid = payload.valid;
        this.lookup = payload.lookup;
        this.amount = payload.amount;
        this.payee = payload.payee;
        this.sendCodeAttempts = payload.send_code_attempts;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.sna = payload.sna;
        this.url = payload.url;
        this._solution = { serviceSid, sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new VerificationContextImpl(this._version, this._solution.serviceSid, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a VerificationInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed VerificationInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            serviceSid: this.serviceSid,
            accountSid: this.accountSid,
            to: this.to,
            channel: this.channel,
            status: this.status,
            valid: this.valid,
            lookup: this.lookup,
            amount: this.amount,
            payee: this.payee,
            sendCodeAttempts: this.sendCodeAttempts,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            sna: this.sna,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationInstance = VerificationInstance;
function VerificationListInstance(version, serviceSid) {
    if (!(0, utility_1.isValidPathParam)(serviceSid)) {
        throw new Error("Parameter 'serviceSid' is not valid.");
    }
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new VerificationContextImpl(version, serviceSid, sid);
    };
    instance._version = version;
    instance._solution = { serviceSid };
    instance._uri = `/Services/${serviceSid}/Verifications`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["to"] === null || params["to"] === undefined) {
            throw new Error("Required parameter \"params['to']\" missing.");
        }
        if (params["channel"] === null || params["channel"] === undefined) {
            throw new Error("Required parameter \"params['channel']\" missing.");
        }
        let data = {};
        data["To"] = params["to"];
        data["Channel"] = params["channel"];
        if (params["customFriendlyName"] !== undefined)
            data["CustomFriendlyName"] = params["customFriendlyName"];
        if (params["customMessage"] !== undefined)
            data["CustomMessage"] = params["customMessage"];
        if (params["sendDigits"] !== undefined)
            data["SendDigits"] = params["sendDigits"];
        if (params["locale"] !== undefined)
            data["Locale"] = params["locale"];
        if (params["customCode"] !== undefined)
            data["CustomCode"] = params["customCode"];
        if (params["amount"] !== undefined)
            data["Amount"] = params["amount"];
        if (params["payee"] !== undefined)
            data["Payee"] = params["payee"];
        if (params["rateLimits"] !== undefined)
            data["RateLimits"] = serialize.object(params["rateLimits"]);
        if (params["channelConfiguration"] !== undefined)
            data["ChannelConfiguration"] = serialize.object(params["channelConfiguration"]);
        if (params["appHash"] !== undefined)
            data["AppHash"] = params["appHash"];
        if (params["templateSid"] !== undefined)
            data["TemplateSid"] = params["templateSid"];
        if (params["templateCustomSubstitutions"] !== undefined)
            data["TemplateCustomSubstitutions"] =
                params["templateCustomSubstitutions"];
        if (params["deviceIp"] !== undefined)
            data["DeviceIp"] = params["deviceIp"];
        if (params["enableSnaClientToken"] !== undefined)
            data["EnableSnaClientToken"] = serialize.bool(params["enableSnaClientToken"]);
        if (params["riskCheck"] !== undefined)
            data["RiskCheck"] = params["riskCheck"];
        if (params["tags"] !== undefined)
            data["Tags"] = params["tags"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationInstance(operationVersion, payload, instance._solution.serviceSid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.VerificationListInstance = VerificationListInstance;
