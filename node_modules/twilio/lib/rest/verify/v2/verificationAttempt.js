"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationAttemptPage = exports.VerificationAttemptListInstance = exports.VerificationAttemptInstance = exports.VerificationAttemptContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class VerificationAttemptContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Attempts/${sid}`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationAttemptInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationAttemptContextImpl = VerificationAttemptContextImpl;
class VerificationAttemptInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.serviceSid = payload.service_sid;
        this.verificationSid = payload.verification_sid;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.conversionStatus = payload.conversion_status;
        this.channel = payload.channel;
        this.price = payload.price;
        this.channelData = payload.channel_data;
        this.url = payload.url;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context ||
                new VerificationAttemptContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Fetch a VerificationAttemptInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed VerificationAttemptInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sid: this.sid,
            accountSid: this.accountSid,
            serviceSid: this.serviceSid,
            verificationSid: this.verificationSid,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            conversionStatus: this.conversionStatus,
            channel: this.channel,
            price: this.price,
            channelData: this.channelData,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationAttemptInstance = VerificationAttemptInstance;
function VerificationAttemptListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new VerificationAttemptContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Attempts`;
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["dateCreatedAfter"] !== undefined)
            data["DateCreatedAfter"] = serialize.iso8601DateTime(params["dateCreatedAfter"]);
        if (params["dateCreatedBefore"] !== undefined)
            data["DateCreatedBefore"] = serialize.iso8601DateTime(params["dateCreatedBefore"]);
        if (params["channelData.to"] !== undefined)
            data["ChannelData.To"] = params["channelData.to"];
        if (params["country"] !== undefined)
            data["Country"] = params["country"];
        if (params["channel"] !== undefined)
            data["Channel"] = params["channel"];
        if (params["verifyServiceSid"] !== undefined)
            data["VerifyServiceSid"] = params["verifyServiceSid"];
        if (params["verificationSid"] !== undefined)
            data["VerificationSid"] = params["verificationSid"];
        if (params["status"] !== undefined)
            data["Status"] = params["status"];
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationAttemptPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new VerificationAttemptPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.VerificationAttemptListInstance = VerificationAttemptListInstance;
class VerificationAttemptPage extends Page_1.default {
    /**
     * Initialize the VerificationAttemptPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of VerificationAttemptInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new VerificationAttemptInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationAttemptPage = VerificationAttemptPage;
