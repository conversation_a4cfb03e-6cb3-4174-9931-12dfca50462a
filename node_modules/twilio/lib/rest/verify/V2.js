"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const form_1 = require("./v2/form");
const safelist_1 = require("./v2/safelist");
const service_1 = require("./v2/service");
const template_1 = require("./v2/template");
const verificationAttempt_1 = require("./v2/verificationAttempt");
const verificationAttemptsSummary_1 = require("./v2/verificationAttemptsSummary");
class V2 extends Version_1.default {
    /**
     * Initialize the V2 version of Verify
     *
     * @param domain - The Twilio (Twilio.Verify) domain
     */
    constructor(domain) {
        super(domain, "v2");
    }
    /** Getter for forms resource */
    get forms() {
        this._forms = this._forms || (0, form_1.FormListInstance)(this);
        return this._forms;
    }
    /** Getter for safelist resource */
    get safelist() {
        this._safelist = this._safelist || (0, safelist_1.SafelistListInstance)(this);
        return this._safelist;
    }
    /** Getter for services resource */
    get services() {
        this._services = this._services || (0, service_1.ServiceListInstance)(this);
        return this._services;
    }
    /** Getter for templates resource */
    get templates() {
        this._templates = this._templates || (0, template_1.TemplateListInstance)(this);
        return this._templates;
    }
    /** Getter for verificationAttempts resource */
    get verificationAttempts() {
        this._verificationAttempts =
            this._verificationAttempts || (0, verificationAttempt_1.VerificationAttemptListInstance)(this);
        return this._verificationAttempts;
    }
    /** Getter for verificationAttemptsSummary resource */
    get verificationAttemptsSummary() {
        this._verificationAttemptsSummary =
            this._verificationAttemptsSummary ||
                (0, verificationAttemptsSummary_1.VerificationAttemptsSummaryListInstance)(this);
        return this._verificationAttemptsSummary;
    }
}
exports.default = V2;
