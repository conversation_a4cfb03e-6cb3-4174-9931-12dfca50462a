"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportListInstance = exports.ExportInstance = exports.ExportContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const day_1 = require("./export/day");
const exportCustomJob_1 = require("./export/exportCustomJob");
const job_1 = require("./export/job");
class ExportContextImpl {
    constructor(_version, resourceType) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(resourceType)) {
            throw new Error("Parameter 'resourceType' is not valid.");
        }
        this._solution = { resourceType };
        this._uri = `/Exports/${resourceType}`;
    }
    get days() {
        this._days =
            this._days || (0, day_1.DayListInstance)(this._version, this._solution.resourceType);
        return this._days;
    }
    get exportCustomJobs() {
        this._exportCustomJobs =
            this._exportCustomJobs ||
                (0, exportCustomJob_1.ExportCustomJobListInstance)(this._version, this._solution.resourceType);
        return this._exportCustomJobs;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ExportInstance(operationVersion, payload, instance._solution.resourceType));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ExportContextImpl = ExportContextImpl;
class ExportInstance {
    constructor(_version, payload, resourceType) {
        this._version = _version;
        this.resourceType = payload.resource_type;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { resourceType: resourceType || this.resourceType };
    }
    get _proxy() {
        this._context =
            this._context ||
                new ExportContextImpl(this._version, this._solution.resourceType);
        return this._context;
    }
    /**
     * Fetch a ExportInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed ExportInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    /**
     * Access the days.
     */
    days() {
        return this._proxy.days;
    }
    /**
     * Access the exportCustomJobs.
     */
    exportCustomJobs() {
        return this._proxy.exportCustomJobs;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            resourceType: this.resourceType,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ExportInstance = ExportInstance;
function ExportListInstance(version) {
    const instance = ((resourceType) => instance.get(resourceType));
    instance.get = function get(resourceType) {
        return new ExportContextImpl(version, resourceType);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Exports`;
    Object.defineProperty(instance, "jobs", {
        get: function jobs() {
            if (!instance._jobs) {
                instance._jobs = (0, job_1.JobListInstance)(instance._version);
            }
            return instance._jobs;
        },
    });
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ExportListInstance = ExportListInstance;
