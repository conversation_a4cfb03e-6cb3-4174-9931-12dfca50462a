"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Bulkexports
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExportCustomJobPage = exports.ExportCustomJobInstance = exports.ExportCustomJobListInstance = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../../base/Page"));
const deserialize = require("../../../../base/deserialize");
const serialize = require("../../../../base/serialize");
const utility_1 = require("../../../../base/utility");
function ExportCustomJobListInstance(version, resourceType) {
    if (!(0, utility_1.isValidPathParam)(resourceType)) {
        throw new Error("Parameter 'resourceType' is not valid.");
    }
    const instance = {};
    instance._version = version;
    instance._solution = { resourceType };
    instance._uri = `/Exports/${resourceType}/Jobs`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["startDay"] === null || params["startDay"] === undefined) {
            throw new Error("Required parameter \"params['startDay']\" missing.");
        }
        if (params["endDay"] === null || params["endDay"] === undefined) {
            throw new Error("Required parameter \"params['endDay']\" missing.");
        }
        if (params["friendlyName"] === null ||
            params["friendlyName"] === undefined) {
            throw new Error("Required parameter \"params['friendlyName']\" missing.");
        }
        let data = {};
        data["StartDay"] = params["startDay"];
        data["EndDay"] = params["endDay"];
        data["FriendlyName"] = params["friendlyName"];
        if (params["webhookUrl"] !== undefined)
            data["WebhookUrl"] = params["webhookUrl"];
        if (params["webhookMethod"] !== undefined)
            data["WebhookMethod"] = params["webhookMethod"];
        if (params["email"] !== undefined)
            data["Email"] = params["email"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ExportCustomJobInstance(operationVersion, payload, instance._solution.resourceType));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new ExportCustomJobPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new ExportCustomJobPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.ExportCustomJobListInstance = ExportCustomJobListInstance;
class ExportCustomJobInstance {
    constructor(_version, payload, resourceType) {
        this._version = _version;
        this.friendlyName = payload.friendly_name;
        this.resourceType = payload.resource_type;
        this.startDay = payload.start_day;
        this.endDay = payload.end_day;
        this.webhookUrl = payload.webhook_url;
        this.webhookMethod = payload.webhook_method;
        this.email = payload.email;
        this.jobSid = payload.job_sid;
        this.details = payload.details;
        this.jobQueuePosition = payload.job_queue_position;
        this.estimatedCompletionTime = payload.estimated_completion_time;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            friendlyName: this.friendlyName,
            resourceType: this.resourceType,
            startDay: this.startDay,
            endDay: this.endDay,
            webhookUrl: this.webhookUrl,
            webhookMethod: this.webhookMethod,
            email: this.email,
            jobSid: this.jobSid,
            details: this.details,
            jobQueuePosition: this.jobQueuePosition,
            estimatedCompletionTime: this.estimatedCompletionTime,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ExportCustomJobInstance = ExportCustomJobInstance;
class ExportCustomJobPage extends Page_1.default {
    /**
     * Initialize the ExportCustomJobPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of ExportCustomJobInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new ExportCustomJobInstance(this._version, payload, this._solution.resourceType);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.ExportCustomJobPage = ExportCustomJobPage;
