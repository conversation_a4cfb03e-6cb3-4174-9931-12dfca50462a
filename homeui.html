

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BoGuani - Messenger of Value</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const scrollLinks = document.querySelectorAll('.scroll-link');
            scrollLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'dark-purple': '#2D1B4E',
                        'medium-purple': '#3D2A5F',
                        'light-purple': '#4E3A70',
                        'dark-gray': '#1E1E24',
                        'medium-gray': '#2D2D34',
                        'gold': '#D4AF37',
                        'gold-light': '#F2D675'
                    },
                    fontFamily: {
                        'sans': ['Montserrat', 'sans-serif'],
                        'serif': ['Georgia', 'serif']
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');
        
        body {
            background: linear-gradient(135deg, #1E1E24 0%, #2D1B4E 100%);
            font-family: 'Montserrat', sans-serif;
        }
        
        .gold-gradient {
            background: linear-gradient(90deg, #D4AF37 0%, #F2D675 50%, #D4AF37 100%);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        .gold-border {
            border: 2px solid transparent;
            background: linear-gradient(#2D1B4E, #2D1B4E) padding-box,
                        linear-gradient(90deg, #D4AF37, #F2D675) border-box;
        }
        
        .feature-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
        }
        
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }
        
        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="text-white min-h-screen">
    <div class="hero-pattern">
        <!-- Navigation -->
        <nav class="bg-dark-gray bg-opacity-80 backdrop-blur-md fixed w-full z-10">
            <div class="container mx-auto px-6 py-3 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="text-gold text-3xl mr-2">
                        <i class="fas fa-comment-dollar"></i>
                    </div>
                    <span class="font-bold text-2xl gold-gradient">BoGuani</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#features" class="hover:text-gold transition-colors scroll-link">Features</a>
                    <a href="#about" class="hover:text-gold transition-colors scroll-link">About</a>
                    <a href="#download" class="hover:text-gold transition-colors scroll-link">Download</a>
                    <a href="/support" class="hover:text-gold transition-colors">Support</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-white focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            <!-- Mobile menu -->
            <div id="mobile-menu" class="md:hidden hidden bg-medium-gray">
                <div class="container mx-auto px-6 py-3 flex flex-col space-y-4">
                    <a href="#features" class="hover:text-gold transition-colors scroll-link">Features</a>
                    <a href="#about" class="hover:text-gold transition-colors scroll-link">About</a>
                    <a href="#download" class="hover:text-gold transition-colors scroll-link">Download</a>
                    <a href="/support" class="hover:text-gold transition-colors">Support</a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <header class="pt-28 pb-20 px-6">
            <div class="container mx-auto flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4">
                        <span class="gold-gradient">BoGuani</span>
                    </h1>
                    <h2 class="text-2xl md:text-3xl font-semibold mb-2">Messenger of Value</h2>
                    <p class="text-xl mb-6 text-gray-300 italic">Where Words Carry Worth</p>
                    <p class="mb-8 text-gray-300 max-w-lg">
                        Inspired by ancient Taíno wisdom, BoGuani transforms communication into value exchange. 
                        Send messages that matter, share moments that count, and transfer value instantly - 
                        all protected by sacred-level encryption.
                    </p>
                    <div class="mb-8">
                        <p class="text-xl font-semibold gold-gradient">"Speak Gold. Share Value."</p>
                    </div>
                    <div class="flex flex-wrap gap-4">
                        <a href="/auth" class="bg-gold text-dark-gray px-6 py-3 rounded-full font-semibold transition-all btn-hover flex items-center">
                            <i class="fas fa-globe mr-2"></i> Open BoGuani Web Version
                        </a>
                        <a href="/downloads" class="gold-border bg-transparent px-6 py-3 rounded-full font-semibold transition-all btn-hover flex items-center">
                            <i class="fas fa-play mr-2 text-gold"></i> <span class="text-gold">Watch Demo</span>
                        </a>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative w-80 h-80">
                        <!-- App mockup -->
                        <div class="absolute inset-0 bg-medium-purple rounded-3xl gold-border shadow-2xl overflow-hidden">
                            <div class="bg-dark-purple p-4 border-b border-gold border-opacity-30">
                                <div class="flex justify-between items-center">
                                    <div class="text-gold text-xl">
                                        <i class="fas fa-comment-dollar"></i>
                                    </div>
                                    <p class="font-semibold">BoGuani</p>
                                    <div class="text-gold">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 h-full">
                                <div class="flex flex-col h-full">
                                    <div class="bg-light-purple bg-opacity-30 p-3 rounded-lg mb-3 self-start max-w-[70%]">
                                        <p class="text-sm">Hey! Can you send me 20 for dinner tonight?</p>
                                    </div>
                                    <div class="bg-gold bg-opacity-20 p-3 rounded-lg mb-3 self-end max-w-[70%]">
                                        <p class="text-sm">Sure! Sending it now with a special message.</p>
                                    </div>
                                    <div class="bg-gold bg-opacity-20 p-3 rounded-lg mb-3 self-end max-w-[70%]">
                                        <div class="flex items-center">
                                            <div class="text-gold mr-2">
                                                <i class="fas fa-coins"></i>
                                            </div>
                                            <p class="text-sm">20.00 sent - Enjoy dinner!</p>
                                        </div>
                                    </div>
                                    <div class="bg-light-purple bg-opacity-30 p-3 rounded-lg self-start max-w-[70%]">
                                        <p class="text-sm">Thanks! Value received. 🙏</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Decorative elements -->
                        <div class="absolute -bottom-5 -right-5 w-40 h-40 bg-gold opacity-10 rounded-full blur-xl"></div>
                        <div class="absolute -top-5 -left-5 w-20 h-20 bg-gold opacity-10 rounded-full blur-md"></div>
                    </div>
                </div>
            </div>
        </header>
    </div>

    <!-- Features Section -->
    <section id="features" class="py-16 bg-dark-gray">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4">Key Features</h2>
                <div class="w-20 h-1 bg-gold mx-auto"></div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card bg-medium-purple p-6 rounded-xl gold-border">
                    <div class="text-gold text-4xl mb-4">
                        <i class="fas fa-lock"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">256-bit End-to-End Encryption</h3>
                    <p class="text-gray-300">Your messages and transactions are protected with sacred-level security. No one but you and your recipient can access your communications.</p>
                </div>
                
                <div class="feature-card bg-medium-purple p-6 rounded-xl gold-border">
                    <div class="text-gold text-4xl mb-4">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Instant Money Transfers</h3>
                    <p class="text-gray-300">Send value along with your words. Transfer money instantly to anyone in your contacts, making every conversation potentially valuable.</p>
                </div>
                
                <div class="feature-card bg-medium-purple p-6 rounded-xl gold-border">
                    <div class="text-gold text-4xl mb-4">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">24/7 Global Support</h3>
                    <p class="text-gray-300">Our dedicated team is always available to assist you with any questions or issues, ensuring a seamless experience around the clock.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="py-16 bg-gradient-to-b from-dark-gray to-dark-purple">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4">Get BoGuani Now</h2>
                <p class="text-gray-300 max-w-2xl mx-auto">Experience the revolution in value-based messaging. Available on all major platforms.</p>
                <div class="w-20 h-1 bg-gold mx-auto mt-4"></div>
            </div>
            
            <div class="flex flex-wrap justify-center gap-6">
                <a href="/downloads" class="bg-medium-purple hover:bg-light-purple transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                    <i class="fab fa-apple text-3xl mr-4 text-gold"></i>
                    <div>
                        <p class="text-xs">Download for</p>
                        <p class="font-semibold">iOS</p>
                    </div>
                </a>

                <a href="/downloads" class="bg-medium-purple hover:bg-light-purple transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                    <i class="fab fa-android text-3xl mr-4 text-gold"></i>
                    <div>
                        <p class="text-xs">Download for</p>
                        <p class="font-semibold">Android</p>
                    </div>
                </a>

                <a href="/downloads" class="bg-medium-purple hover:bg-light-purple transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                    <i class="fas fa-desktop text-3xl mr-4 text-gold"></i>
                    <div>
                        <p class="text-xs">Download</p>
                        <p class="font-semibold">Desktop App</p>
                    </div>
                </a>

                <a href="/auth" class="bg-medium-purple hover:bg-light-purple transition-colors px-8 py-4 rounded-xl flex items-center gold-border btn-hover">
                    <i class="fas fa-globe text-3xl mr-4 text-gold"></i>
                    <div>
                        <p class="text-xs">Open</p>
                        <p class="font-semibold">Web Version</p>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-16 bg-dark-purple">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <div class="relative">
                        <svg class="w-full max-w-md mx-auto" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                            <path fill="#D4AF37" fill-opacity="0.2" d="M45.7,-51.9C59.9,-41.5,72.3,-27.7,76.3,-11.5C80.3,4.7,75.9,23.4,65.1,36.6C54.3,49.8,37.1,57.5,19.3,63.3C1.6,69.1,-16.8,73,-32.5,67.5C-48.2,62,-61.3,47.1,-68.3,29.8C-75.3,12.5,-76.2,-7.2,-69.8,-23.6C-63.3,-40,-49.5,-53.1,-34.7,-63C-19.9,-72.9,-3.9,-79.7,9.9,-76.8C23.8,-73.9,31.5,-62.3,45.7,-51.9Z" transform="translate(100 100)" />
                            <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="#D4AF37" font-size="24" font-weight="bold">Taíno Wisdom</text>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-gold text-6xl opacity-20">
                                <i class="fas fa-comment-dollar"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 md:pl-10">
                    <h2 class="text-3xl font-bold mb-6">Our Story</h2>
                    <div class="w-20 h-1 bg-gold mb-6"></div>
                    <p class="mb-4 text-gray-300">
                        BoGuani draws inspiration from the ancient Taíno civilization, where communication and value exchange were intrinsically linked. The name "BoGuani" combines elements from their language representing both "message" and "worth."
                    </p>
                    <p class="mb-6 text-gray-300">
                        We've reimagined this ancient wisdom for the digital age, creating a platform where every message can carry not just meaning, but tangible value. Our mission is to transform how people connect, share, and exchange value in a secure and meaningful way.
                    </p>
                    <div class="flex items-center">
                        <div class="h-px bg-gold flex-grow"></div>
                        <p class="px-4 text-gold italic">"Speak Gold. Share Value."</p>
                        <div class="h-px bg-gold flex-grow"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-16 bg-dark-gray">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold mb-4">What Our Users Say</h2>
                <div class="w-20 h-1 bg-gold mx-auto"></div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-medium-purple p-6 rounded-xl relative">
                    <div class="text-gold text-4xl absolute -top-5 -left-2">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-4 mb-6 text-gray-300">BoGuani has completely changed how I think about messaging. Being able to send value along with my words makes every conversation more meaningful.</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gold flex items-center justify-center text-dark-purple font-bold">M</div>
                        <div class="ml-3">
                            <p class="font-semibold">Maria L.</p>
                            <p class="text-xs text-gray-400">Entrepreneur</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-medium-purple p-6 rounded-xl relative">
                    <div class="text-gold text-4xl absolute -top-5 -left-2">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-4 mb-6 text-gray-300">The security features are impressive. I feel confident sending money through BoGuani knowing that my transactions are protected by top-tier encryption.</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gold flex items-center justify-center text-dark-purple font-bold">J</div>
                        <div class="ml-3">
                            <p class="font-semibold">James T.</p>
                            <p class="text-xs text-gray-400">Security Analyst</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-medium-purple p-6 rounded-xl relative">
                    <div class="text-gold text-4xl absolute -top-5 -left-2">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <p class="mt-4 mb-6 text-gray-300">I use BoGuani daily for both personal and business communications. The ability to instantly transfer value has streamlined so many of my interactions.</p>
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gold flex items-center justify-center text-dark-purple font-bold">S</div>
                        <div class="ml-3">
                            <p class="font-semibold">Sarah K.</p>
                            <p class="text-xs text-gray-400">Digital Nomad</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-gradient-to-b from-dark-purple to-dark-gray">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Ready to Transform Your Communications?</h2>
            <p class="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">Join thousands of users who are already experiencing the power of value-based messaging with BoGuani.</p>
            
            <div class="flex flex-wrap justify-center gap-6">
                <a href="/auth" class="bg-gold text-dark-gray px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i class="fas fa-globe mr-2"></i> Open Web Version
                </a>
                <a href="/downloads" class="gold-border bg-transparent px-8 py-4 rounded-full font-semibold transition-all btn-hover flex items-center">
                    <i class="fas fa-download mr-2 text-gold"></i> <span class="text-gold">Download App</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark-gray py-10">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center">
                        <div class="text-gold text-2xl mr-2">
                            <i class="fas fa-comment-dollar"></i>
                        </div>
                        <span class="font-bold text-xl gold-gradient">BoGuani</span>
                    </div>
                    <p class="text-gray-400 mt-2">Messenger of Value</p>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-6 md:mb-0">
                    <div>
                        <h3 class="font-semibold mb-3 text-gold">Product</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><a href="#features" class="hover:text-gold transition-colors">Features</a></li>
                            <li><a href="/security" class="hover:text-gold transition-colors">Security</a></li>
                            <li><a href="/pricing" class="hover:text-gold transition-colors">Pricing</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold mb-3 text-gold">Company</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><a href="#about" class="hover:text-gold transition-colors">About</a></li>
                            <li><a href="/careers" class="hover:text-gold transition-colors">Careers</a></li>
                            <li><a href="/blog" class="hover:text-gold transition-colors">Blog</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold mb-3 text-gold">Resources</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><a href="/support" class="hover:text-gold transition-colors">Help Center</a></li>
                            <li><a href="/guides" class="hover:text-gold transition-colors">Guides</a></li>
                            <li><a href="/api-docs" class="hover:text-gold transition-colors">API Docs</a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold mb-3 text-gold">Legal</h3>
                        <ul class="space-y-2 text-gray-400">
                            <li><a href="/privacy" class="hover:text-gold transition-colors">Privacy</a></li>
                            <li><a href="/terms" class="hover:text-gold transition-colors">Terms</a></li>
                            <li><a href="/security" class="hover:text-gold transition-colors">Security</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="flex space-x-4">
                    <a href="https://twitter.com/boguani" target="_blank" class="w-10 h-10 rounded-full bg-medium-purple flex items-center justify-center hover:bg-light-purple transition-colors">
                        <i class="fab fa-twitter text-gold"></i>
                    </a>
                    <a href="https://facebook.com/boguani" target="_blank" class="w-10 h-10 rounded-full bg-medium-purple flex items-center justify-center hover:bg-light-purple transition-colors">
                        <i class="fab fa-facebook-f text-gold"></i>
                    </a>
                    <a href="https://instagram.com/boguani" target="_blank" class="w-10 h-10 rounded-full bg-medium-purple flex items-center justify-center hover:bg-light-purple transition-colors">
                        <i class="fab fa-instagram text-gold"></i>
                    </a>
                    <a href="https://linkedin.com/company/boguani" target="_blank" class="w-10 h-10 rounded-full bg-medium-purple flex items-center justify-center hover:bg-light-purple transition-colors">
                        <i class="fab fa-linkedin-in text-gold"></i>
                    </a>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2023 BoGuani. All rights reserved.</p>
                <div class="flex space-x-6 text-gray-400 text-sm">
                    <a href="/privacy" class="hover:text-gold transition-colors">Privacy Policy</a>
                    <a href="/terms" class="hover:text-gold transition-colors">Terms of Service</a>
                    <a href="/security" class="hover:text-gold transition-colors">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'957daa900278a04f',t:'MTc1MTI4NjUzNS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
